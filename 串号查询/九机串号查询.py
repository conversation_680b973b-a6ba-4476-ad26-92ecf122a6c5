import requests
import tkinter as tk
from tkinter import messagebox, ttk
from tkinter.scrolledtext import ScrolledText

# 查询串号信息
def query_imei(imei):
    url = f'https://moa.9ji.com/cloudapi_nc/afterservice/api/smallpro/selectProductInfoListByImei/v1'
    params = {
        'imei': imei
    }
    headers = {
        'xservicename': 'oa-afterservice'
    }
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求出错: {e}")
        return None

# 提交查询
def on_submit():
    imei = imei_entry.get()
    result = query_imei(imei)
    
    # 清空结果显示区域
    result_text.delete(1.0, tk.END)

    if result and result.get('success'):
        data = result.get('data')
        if data:
            # 格式化显示数据
            message = f"查询结果：\n\nIMEI: {data.get('imei', '无')}\n产品ID: {data.get('productId', '无')}"
            result_text.insert(tk.END, message)
        else:
            result_text.insert(tk.END, "未找到相关数据")
    else:
        error_msg = result.get('userMsg', '请求失败，请检查输入') if result else '请求失败，请检查输入'
        result_text.insert(tk.END, f"错误: {error_msg}")

# 创建主窗口
root = tk.Tk()
root.title("串号查询")
root.geometry("400x300")  # 设置窗口大小

# 创建框架
frame = ttk.Frame(root, padding="10")
frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

# 创建输入框和标签
ttk.Label(frame, text="串号 (IMEI):").grid(row=0, column=0, sticky=tk.W, pady=5)
imei_entry = ttk.Entry(frame, width=30)
imei_entry.grid(row=0, column=1, sticky=tk.W, pady=5)

# 创建提交按钮
submit_button = ttk.Button(frame, text="查询", command=on_submit)
submit_button.grid(row=1, column=0, columnspan=2, pady=10)

# 创建结果显示区域
result_text = ScrolledText(frame, width=40, height=10, wrap=tk.WORD)
result_text.grid(row=2, column=0, columnspan=2, pady=5)

# 配置网格权重
root.columnconfigure(0, weight=1)
root.rowconfigure(0, weight=1)
frame.columnconfigure(1, weight=1)

# 运行主循环
root.mainloop()
(['C:\\aiwork\\串号查询\\九机串号查询.py'],
 ['C:\\aiwork\\串号查询'],
 [],
 [('C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\pygame\\__pyinstaller',
   0),
  ('C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.13.0b3 (tags/v3.13.0b3:7b41395, Jun 27 2024, 16:08:48) [MSC v.1940 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('九机串号查询', 'C:\\aiwork\\串号查询\\九机串号查询.py', 'PYSOURCE')],
 [('inspect',
   'C:\\Development_Workspace\\python\\environment\\Lib\\inspect.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv',
   'C:\\Development_Workspace\\python\\environment\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string',
   'C:\\Development_Workspace\\python\\environment\\Lib\\string.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('copy',
   'C:\\Development_Workspace\\python\\environment\\Lib\\copy.py',
   'PYMODULE'),
  ('random',
   'C:\\Development_Workspace\\python\\environment\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Development_Workspace\\python\\environment\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Development_Workspace\\python\\environment\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Development_Workspace\\python\\environment\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Development_Workspace\\python\\environment\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Development_Workspace\\python\\environment\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('struct',
   'C:\\Development_Workspace\\python\\environment\\Lib\\struct.py',
   'PYMODULE'),
  ('threading',
   'C:\\Development_Workspace\\python\\environment\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Development_Workspace\\python\\environment\\Lib\\bisect.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Development_Workspace\\python\\environment\\Lib\\calendar.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Development_Workspace\\python\\environment\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Development_Workspace\\python\\environment\\Lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Development_Workspace\\python\\environment\\Lib\\gettext.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('socket',
   'C:\\Development_Workspace\\python\\environment\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Development_Workspace\\python\\environment\\Lib\\selectors.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Development_Workspace\\python\\environment\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Development_Workspace\\python\\environment\\Lib\\quopri.py',
   'PYMODULE'),
  ('typing',
   'C:\\Development_Workspace\\python\\environment\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tempfile.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Development_Workspace\\python\\environment\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Development_Workspace\\python\\environment\\Lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Development_Workspace\\python\\environment\\Lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Development_Workspace\\python\\environment\\Lib\\bz2.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Development_Workspace\\python\\environment\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\contextlib.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Development_Workspace\\python\\environment\\Lib\\textwrap.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Development_Workspace\\python\\environment\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Development_Workspace\\python\\environment\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('glob',
   'C:\\Development_Workspace\\python\\environment\\Lib\\glob.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('email',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('json',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Development_Workspace\\python\\environment\\Lib\\__future__.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Development_Workspace\\python\\environment\\Lib\\argparse.py',
   'PYMODULE'),
  ('token',
   'C:\\Development_Workspace\\python\\environment\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tokenize.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis',
   'C:\\Development_Workspace\\python\\environment\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Development_Workspace\\python\\environment\\Lib\\opcode.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('ast',
   'C:\\Development_Workspace\\python\\environment\\Lib\\ast.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Development_Workspace\\python\\environment\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Development_Workspace\\python\\environment\\Lib\\signal.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Development_Workspace\\python\\environment\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_colorize.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('requests',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Development_Workspace\\python\\environment\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Development_Workspace\\python\\environment\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\netrc.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Development_Workspace\\python\\environment\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Development_Workspace\\python\\environment\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Development_Workspace\\python\\environment\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Development_Workspace\\python\\environment\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Development_Workspace\\python\\environment\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Development_Workspace\\python\\environment\\Lib\\hmac.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('queue',
   'C:\\Development_Workspace\\python\\environment\\Lib\\queue.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Development_Workspace\\python\\environment\\Lib\\ssl.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE')],
 [('python313.dll',
   'C:\\Development_Workspace\\python\\environment\\python313.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Development_Workspace\\python\\environment\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Development_Workspace\\python\\environment\\python3.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\ucrtbase.dll',
   'BINARY'),
  ('zlib1.dll',
   'C:\\Development_Workspace\\python\\environment\\DLLs\\zlib1.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Development_Workspace\\openjdk17\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\aiwork\\串号查询\\build\\九机串号查询\\base_library.zip',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.7.tm',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8\\8.5\\tcltest-2.5.7.tm',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Development_Workspace\\python\\environment\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-44.0.0.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography-44.0.0.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-44.0.0.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography-44.0.0.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-44.0.0.dist-info\\INSTALLER',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography-44.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-44.0.0.dist-info\\METADATA',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography-44.0.0.dist-info\\METADATA',
   'DATA'),
  ('cryptography-44.0.0.dist-info\\WHEEL',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography-44.0.0.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-44.0.0.dist-info\\licenses\\LICENSE',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography-44.0.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-44.0.0.dist-info\\RECORD',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography-44.0.0.dist-info\\RECORD',
   'DATA')])

('C:\\aiwork\\串号查询\\build\\九机串号查询\\PYZ-00.pyz',
 [('OpenSSL',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Development_Workspace\\python\\environment\\Lib\\__future__.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_compression.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Development_Workspace\\python\\environment\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Development_Workspace\\python\\environment\\Lib\\ast.py',
   'PYMODULE'),
  ('base64',
   'C:\\Development_Workspace\\python\\environment\\Lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Development_Workspace\\python\\environment\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Development_Workspace\\python\\environment\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Development_Workspace\\python\\environment\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Development_Workspace\\python\\environment\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Development_Workspace\\python\\environment\\Lib\\copy.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv',
   'C:\\Development_Workspace\\python\\environment\\Lib\\csv.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Development_Workspace\\python\\environment\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Development_Workspace\\python\\environment\\Lib\\decimal.py',
   'PYMODULE'),
  ('dis',
   'C:\\Development_Workspace\\python\\environment\\Lib\\dis.py',
   'PYMODULE'),
  ('email',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Development_Workspace\\python\\environment\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Development_Workspace\\python\\environment\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Development_Workspace\\python\\environment\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Development_Workspace\\python\\environment\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Development_Workspace\\python\\environment\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Development_Workspace\\python\\environment\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Development_Workspace\\python\\environment\\Lib\\hmac.py',
   'PYMODULE'),
  ('http',
   'C:\\Development_Workspace\\python\\environment\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Development_Workspace\\python\\environment\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Development_Workspace\\python\\environment\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Development_Workspace\\python\\environment\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('idna',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Development_Workspace\\python\\environment\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Development_Workspace\\python\\environment\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Development_Workspace\\python\\environment\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Development_Workspace\\python\\environment\\Lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Development_Workspace\\python\\environment\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Development_Workspace\\python\\environment\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Development_Workspace\\python\\environment\\Lib\\opcode.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\py_compile.py',
   'PYMODULE'),
  ('queue',
   'C:\\Development_Workspace\\python\\environment\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Development_Workspace\\python\\environment\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Development_Workspace\\python\\environment\\Lib\\random.py',
   'PYMODULE'),
  ('requests',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Development_Workspace\\python\\environment\\Lib\\selectors.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Development_Workspace\\python\\environment\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Development_Workspace\\python\\environment\\Lib\\signal.py',
   'PYMODULE'),
  ('socket',
   'C:\\Development_Workspace\\python\\environment\\Lib\\socket.py',
   'PYMODULE'),
  ('socks',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Development_Workspace\\python\\environment\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Development_Workspace\\python\\environment\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Development_Workspace\\python\\environment\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Development_Workspace\\python\\environment\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Development_Workspace\\python\\environment\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Development_Workspace\\python\\environment\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Development_Workspace\\python\\environment\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'C:\\Development_Workspace\\python\\environment\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'C:\\Development_Workspace\\python\\environment\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Development_Workspace\\python\\environment\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Development_Workspace\\python\\environment\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Development_Workspace\\python\\environment\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])

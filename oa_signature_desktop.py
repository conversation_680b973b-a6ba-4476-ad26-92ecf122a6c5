#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA签名生成器桌面应用
基于Java OaVerifyUtil类的Python实现
支持多种签名算法
"""

import hashlib
import json
import time
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from typing import Dict, Any, List
import threading
from datetime import datetime
import re

# 常量 - 与Java代码完全一致
SALT = "9ji.com"

class OASignatureGenerator:
    """OA签名生成器核心类"""
    
    @staticmethod
    def get_map_by_oa_req_api(obj: Dict[str, Any], secret: str, version: str, timestamp: int) -> Dict[str, Any]:
        """
        与Java代码完全匹配的getMapByOaReqApi实现
        """
        table = {}
        table["secret"] = secret
        table["version"] = version
        table["timestamp"] = timestamp
        
        # 确保JSON数据的键也是按照字母序排序的，同时排除null值，但保留空字符串
        sorted_obj = {}
        for key in sorted(obj.keys()):
            # 只添加非null值到sorted_obj中，但要保留空字符串
            if obj[key] is not None:
                # 处理日期时间格式，将空格替换为T，以匹配ISO 8601格式
                if isinstance(obj[key], str) and re.match(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', obj[key]):
                    sorted_obj[key] = obj[key].replace(' ', 'T')
                else:
                    sorted_obj[key] = obj[key]
        
        # 使用不带null值的排序对象生成JSON，确保中文字符不被转义
        table["jsonData"] = json.dumps(sorted_obj, separators=(',', ':'), ensure_ascii=False)  # 紧凑JSON格式，键按字母序排序，保持中文原样
        return table

    @staticmethod
    def java_style_sort(key_list: List[str]) -> List[str]:
        """
        模拟Java的Collections.sort()实现
        Java中的String比较是按照字符的Unicode值进行比较的
        """
        # 直接使用Python的sorted函数，它的行为与Java的Collections.sort对字符串排序一致
        # 两者都是按照字典序（lexicographical order）排序
        return sorted(key_list)

    @staticmethod
    def get_oa_sign_standard(secret: str, obj: Dict[str, Any], timestamp: int) -> str:
        """
        标准的OA签名实现（基于Java代码）
        """
        # 获取参数字典
        params = OASignatureGenerator.get_map_by_oa_req_api(obj, secret, "1.0", timestamp)
        
        # 构建签名前的明文字符串
        laws_builder = secret + SALT
        
        # Key按照字母序进行排序 - 使用Java风格排序
        key_set = params.keys()
        key_list = list(key_set)  # 转换为列表，模拟Java的ArrayList<>(keySet)
        key_list = OASignatureGenerator.java_style_sort(key_list)  # 模拟Collections.sort(keyList)
        
        # 构建签名前的明文字符串
        for key in key_list:
            value = params.get(key)
            # 跳过null值和byte数组，完全按照Java代码逻辑
            if value is None or isinstance(value, bytes):
                continue
            # 追加key和value（与Java中的append方法相同）
            laws_builder += key + str(value)
        
        # 创建SHA-1哈希
        message_digest = hashlib.sha1(laws_builder.encode('utf-8'))
        # 转换为小写十六进制字符串
        sign = message_digest.hexdigest().lower()
        
        return sign

    @staticmethod
    def get_oa_api_vo(secret: str, obj: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成完整的OA API VO对象
        """
        # 获取当前时间戳（秒）
        timestamp = int(time.time())
        
        # 生成签名
        sign = OASignatureGenerator.get_oa_sign_standard(secret, obj, timestamp)
        
        return {
            "sign": sign,
            "data": obj,
            "timeStamp": timestamp
        }

class OASignatureApp:
    """OA签名生成器桌面应用"""
    
    def __init__(self, root):
        self.root = root
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("OA签名生成器 v2.0")
        self.root.geometry("900x800")
        self.root.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 密钥输入
        ttk.Label(main_frame, text="密钥 (Secret):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.secret_var = tk.StringVar()
        secret_entry = ttk.Entry(main_frame, textvariable=self.secret_var, width=60)
        secret_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 数据对象输入
        ttk.Label(main_frame, text="数据对象 (JSON):").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=5)
        self.data_text = scrolledtext.ScrolledText(main_frame, height=8, width=70)
        self.data_text.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=(10, 0))
        main_frame.rowconfigure(1, weight=1)
        
        # 时间戳选项
        timestamp_frame = ttk.Frame(main_frame)
        timestamp_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        self.timestamp_mode = tk.StringVar(value="auto")
        ttk.Radiobutton(timestamp_frame, text="自动生成时间戳", variable=self.timestamp_mode, 
                       value="auto").pack(side=tk.LEFT)
        ttk.Radiobutton(timestamp_frame, text="手动输入时间戳", variable=self.timestamp_mode, 
                       value="manual").pack(side=tk.LEFT, padx=(20, 0))
        
        self.timestamp_var = tk.StringVar()
        self.timestamp_entry = ttk.Entry(timestamp_frame, textvariable=self.timestamp_var, width=15)
        self.timestamp_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="生成签名", command=self.generate_signature).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="生成完整VO", command=self.generate_full_vo).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空", command=self.clear_all).pack(side=tk.LEFT, padx=5)
        
        # 结果显示
        ttk.Label(main_frame, text="结果:").grid(row=5, column=0, sticky=(tk.W, tk.N), pady=(10, 5))
        self.result_text = scrolledtext.ScrolledText(main_frame, height=15, width=70)
        self.result_text.grid(row=5, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 5), padx=(10, 0))
        main_frame.rowconfigure(5, weight=2)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def generate_signature(self):
        """生成签名"""
        try:
            self.status_var.set("正在生成签名...")
            self.root.update()
            
            # 获取输入
            secret = self.secret_var.get().strip()
            data_json = self.data_text.get("1.0", tk.END).strip()
            
            if not secret:
                messagebox.showerror("错误", "请输入密钥")
                return
                
            if not data_json:
                messagebox.showerror("错误", "请输入数据对象")
                return
            
            # 解析JSON
            try:
                data_obj = json.loads(data_json)
                
                # 如果输入是完整的VO对象，则提取data部分
                if isinstance(data_obj, dict) and "data" in data_obj and "sign" in data_obj and "timeStamp" in data_obj:
                    # 设置timestamp
                    if self.timestamp_mode.get() == "manual":
                        self.timestamp_var.set(str(data_obj["timeStamp"]))
                    
                    data_obj = data_obj["data"]
                    self.data_text.delete("1.0", tk.END)
                    self.data_text.insert("1.0", json.dumps(data_obj, indent=2, ensure_ascii=False))
                    
            except json.JSONDecodeError as e:
                messagebox.showerror("错误", f"JSON格式错误: {e}")
                return
            
            # 获取时间戳
            if self.timestamp_mode.get() == "auto":
                timestamp = int(time.time())
            else:
                timestamp_str = self.timestamp_var.get().strip()
                if not timestamp_str:
                    messagebox.showerror("错误", "请输入时间戳")
                    return
                try:
                    timestamp = int(timestamp_str)
                except ValueError:
                    messagebox.showerror("错误", "时间戳必须是整数")
                    return
            
            # 生成签名
            signature = OASignatureGenerator.get_oa_sign_standard(secret, data_obj, timestamp)
            
            # 显示结果
            result = f"签名生成成功!\n\n"
            result += f"时间戳: {timestamp}\n"
            result += f"时间: {datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')}\n"
            result += f"签名: {signature}\n\n"
            
            # 显示调试信息
            # 重新计算laws_builder以显示最终值
            # 获取参数字典
            params = OASignatureGenerator.get_map_by_oa_req_api(data_obj, secret, "1.0", timestamp)
            
            # 构建签名前的明文字符串
            laws_builder = secret + SALT
            
            # Key按照字母序进行排序 - 使用Java风格排序
            key_set = params.keys()
            key_list = list(key_set)  # 转换为列表，模拟Java的ArrayList<>(keySet)
            key_list = OASignatureGenerator.java_style_sort(key_list)  # 模拟Collections.sort(keyList)
            
            # 构建签名前的明文字符串（完全按照Java代码逻辑）
            for key in key_list:
                value = params.get(key)
                # 跳过null值和byte数组
                if value is None or isinstance(value, bytes):
                    continue
                # 追加key和value（与Java中的append方法相同）
                laws_builder += key + str(value)

            result += "调试信息:\n"
            result += f"最终laws_builder: {laws_builder}\n"
            
            self.result_text.delete("1.0", tk.END)
            self.result_text.insert("1.0", result)
            
            self.status_var.set("签名生成完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"生成签名时发生错误: {e}")
            self.status_var.set("生成失败")
    
    def generate_full_vo(self):
        """生成完整的VO对象"""
        try:
            self.status_var.set("正在生成完整VO...")
            self.root.update()
            
            # 获取输入
            secret = self.secret_var.get().strip()
            data_json = self.data_text.get("1.0", tk.END).strip()
            
            if not secret:
                messagebox.showerror("错误", "请输入密钥")
                return
                
            if not data_json:
                messagebox.showerror("错误", "请输入数据对象")
                return
            
            # 解析JSON
            try:
                data_obj = json.loads(data_json)
                
                # 如果输入是完整的VO对象，则提取data部分
                if isinstance(data_obj, dict) and "data" in data_obj and "sign" in data_obj and "timeStamp" in data_obj:
                    data_obj = data_obj["data"]
                    self.data_text.delete("1.0", tk.END)
                    self.data_text.insert("1.0", json.dumps(data_obj, indent=2, ensure_ascii=False))
            except json.JSONDecodeError as e:
                messagebox.showerror("错误", f"JSON格式错误: {e}")
                return
            
            # 生成完整VO
            oa_api_vo = OASignatureGenerator.get_oa_api_vo(secret, data_obj)
            
            # 显示结果
            result = f"完整OA API VO对象:\n\n"
            result += json.dumps(oa_api_vo, indent=2, ensure_ascii=False)
            
            self.result_text.delete("1.0", tk.END)
            self.result_text.insert("1.0", result)
            
            self.status_var.set("完整VO生成完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"生成VO时发生错误: {e}")
            self.status_var.set("生成失败")
    
    def clear_all(self):
        """清空所有输入和输出"""
        self.secret_var.set("")
        self.data_text.delete("1.0", tk.END)
        self.timestamp_var.set("")
        self.result_text.delete("1.0", tk.END)
        self.status_var.set("已清空")

def main():
    """主函数"""
    root = tk.Tk()
    app = OASignatureApp(root)
    
    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
    
    # 启动应用
    root.mainloop()

if __name__ == "__main__":
    main()

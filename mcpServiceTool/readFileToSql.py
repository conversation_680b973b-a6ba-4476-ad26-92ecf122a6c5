#!/usr/bin/env python
from mcp   import context, tools
import pandas as pd
import json
import os

@tools.tool
def excel_to_sql(excel_data: str = None, field_mapping: dict = None) -> str:
    """
    将Excel数据(JSON字符串格式)转换为SQL更新语句
    
    参数:
        excel_data (str): Excel数据的JSON字符串表示
        field_mapping (dict, optional): 字段映射配置，包含id_field、key_field和table
    
    返回:
        str: 生成的SQL更新语句列表
    """
    # 解析输入的JSON数据
    if not excel_data:
        return "错误: 未提供Excel数据"
    
    try:
        data = json.loads(excel_data)
    except json.JSONDecodeError:
        return "错误: Excel数据不是有效的JSON格式"
    
    # 设置默认字段映射
    if not field_mapping:
        field_mapping = {
            'id_field': 'product_sn_id',
            'key_field': 'product_sn',
            'table': 'product_sn_log'
        }
    
    # 生成SQL语句
    sql_statements = []
    
    for record in data:
        id_value = record.get(field_mapping['id_field'])
        key_value = record.get(field_mapping['key_field'])
        
        if id_value is not None and key_value is not None:
            # 处理字符串值，添加引号；数值不需要引号
            if isinstance(id_value, str):
                id_value = f"'{id_value}'"
            
            # 确保字符串值正确转义
            if isinstance(key_value, str):
                key_value = f"'{key_value}'"
            
            sql = f"UPDATE {field_mapping['table']} SET {field_mapping['id_field']} = {id_value} WHERE {field_mapping['key_field']} = {key_value} AND is_deleted = 0;"
            sql_statements.append(sql)
    
    return "\n".join(sql_statements)

@tools.tool
def read_excel_file(file_path: str) -> str:
    """
    读取Excel文件并将内容转换为JSON字符串
    
    参数:
        file_path (str): Excel文件的路径
        
    返回:
        str: 包含Excel数据的JSON字符串
    """
    try:
        if not os.path.exists(file_path):
            return json.dumps({"error": f"文件不存在: {file_path}"})
            
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 转换为记录列表
        records = df.to_dict(orient='records')
        
        # 转换为JSON字符串
        return json.dumps(records, ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({"error": f"处理Excel文件时出错: {str(e)}"})

# 使用MCP初始化和配置工具
tools_description = """
Excel到SQL转换工具:
- excel_to_sql: 将Excel数据转换为SQL更新语句
- read_excel_file: 读取Excel文件并将其转换为JSON字符串
"""

# 创建MCP上下文并注册工具
mcp_context = context.Context(
    name="excel_sql_converter",
    description=tools_description
)

# 注册工具到MCP上下文
mcp_context.add_tool(excel_to_sql)
mcp_context.add_tool(read_excel_file)

# 启动MCP服务
if __name__ == "__main__":
    host = os.environ.get('MCP_HOST', 'localhost')
    port = int(os.environ.get('MCP_PORT', 8080))
    mcp_context.serve(host=host, port=port)
    print(f"MCP服务已启动，监听 {host}:{port}")
=== SimpleService.processData Call Chain ===

DOT源代码:
// SimpleService.processData Call Chain
digraph {
	dpi=300 rankdir=TB size="12,8"
	node [fillcolor=lightblue fontname=Arial fontsize=10 shape=box style="rounded,filled"]
	edge [color=black fontname=Arial fontsize=9]
	"SimpleService.processData" [label="SimpleService\nprocessData(String)" fillcolor=lightblue]
	"SimpleService.formatOutput" [label="SimpleService\nformatOutput(String)" fillcolor=lightblue]
	"SimpleService.processData" -> "SimpleService.formatOutput" [label="" style=solid]
}


注意: 需要安装Graphviz系统工具才能生成图像文件
安装说明: https://graphviz.org/download/

import pyuac
import subprocess
import sys
import os
import winreg as reg
import psutil

def get_chrome_path():
    try:
        # 打开注册表路径，这里查找 Chrome 的安装路径
        registry_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"
        registry_key = reg.<PERSON><PERSON>ey(reg.HKEY_LOCAL_MACHINE, registry_path)
        chrome_path, _ = reg.QueryValueEx(registry_key, "")
        reg.<PERSON><PERSON><PERSON>(registry_key)

        # 返回 Chrome 的安装路径
        return chrome_path
    except Exception as e:
        print(f"Error: {e}")
        return None
chrome_path = get_chrome_path()

# 确保 chrome_path 被引号包围
if chrome_path:
    remote_debugging = '--remote-debugging-port=9222'
    command = f'"{chrome_path}" {remote_debugging}'  # 用引号包围路径

    # 执行命令
    subprocess.run(command, shell=True)
else:
    print("未找到 Chrome 的安装路径。")


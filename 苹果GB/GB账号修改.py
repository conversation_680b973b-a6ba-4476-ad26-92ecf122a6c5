from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException
from selenium.webdriver.support.ui import WebDriverWait
import requests
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Optional, Callable
import json


class AreaUpdater:
    def __init__(self):
        self.base_url = 'https://moa.9ji.com/cloudapi_nc/oa-stock/api'
        self.session = requests.Session()

    def update_area(self, area: str, account: str, password: str, authorization: str) -> Optional[dict]:
        url = f'{self.base_url}/user/area/updateGba'
        params = {
            'xservicename': 'oa-stock',
            'area': area,
            'account': account,
            'password': password
        }
        headers = {'Authorization': authorization}

        try:
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException:
            messagebox.showerror("错误", "网络请求失败，请检查网络连接")
            return None
        except json.JSONDecodeError:
            messagebox.showerror("错误", "服务器响应格式错误")
            return None


def get_pc_token(callback: Callable[[str], None]) -> None:
    chrome_options = Options()
    chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
    driver = None

    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("https://oa.9ji.com/staticpc/#/")

        # 等待页面加载完成
        WebDriverWait(driver, 10).until(
            lambda d: d.get_cookies()
        )

        cookies = driver.get_cookies()
        pc_oa_token = next(
            (cookie['value'] for cookie in cookies if cookie['name'] == 'pcOaToken'),
            None
        )

        if pc_oa_token:
            callback(pc_oa_token)
        else:
            messagebox.showerror("错误", "未能获取到token，请确保已登录OA系统")

    except WebDriverException:
        messagebox.showerror("错误", "浏览器操作失败，请确保Chrome浏览器已启动且开启调试模式")
    finally:
        if driver:
            driver.quit()


class AreaUpdateGUI:
    def __init__(self, authorization: str):
        self.root = tk.Tk()
        self.root.title("区域信息更新工具")
        self.authorization = authorization
        self.area_updater = AreaUpdater()
        self._setup_ui()

    def _setup_ui(self):
        # 使用Frame作为容器
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 创建输入框和标签
        labels = ['区域:', '账户:', '密码:']
        self.entries = {}

        for i, label in enumerate(labels):
            ttk.Label(main_frame, text=label).grid(row=i, column=0, padx=5, pady=5, sticky=tk.W)
            entry = ttk.Entry(main_frame, width=30)
            entry.grid(row=i, column=1, padx=5, pady=5, sticky=(tk.W, tk.E))
            self.entries[label] = entry

        # 提交按钮
        submit_btn = ttk.Button(main_frame, text="提交", command=self._on_submit)
        submit_btn.grid(row=len(labels), column=0, columnspan=2, pady=10)

        # 配置窗口大小和位置
        self.root.update_idletasks()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = 400
        window_height = 200
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f'{window_width}x{window_height}+{x}+{y}')

    def _on_submit(self):
        # 获取输入值
        area = self.entries['区域:'].get().strip()
        account = self.entries['账户:'].get().strip()
        password = self.entries['密码:'].get().strip()

        # 输入验证
        if not all([area, account, password]):
            messagebox.showwarning("警告", "请填写所有字段")
            return

        result = self.area_updater.update_area(
            area=area,
            account=account,
            password=password,
            authorization=self.authorization
        )

        if result:
            messagebox.showinfo("成功", f"更新成功: {json.dumps(result, ensure_ascii=False)}")

    def run(self):
        self.root.mainloop()


def main():
    get_pc_token(lambda token: AreaUpdateGUI(token).run())


if __name__ == "__main__":
    main()
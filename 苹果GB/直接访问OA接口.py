from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import requests

def get_pc_token():
    chrome_options = Options()
    chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")

    # 连接到已经启动的浏览器实例
    driver = webdriver.Chrome(options=chrome_options)

    # 访问你要抓取的页面
    driver.get("https://oa.9ji.com/staticpc/#/")

    # 获取 cookies
    cookies = driver.get_cookies()

    # 查找 pcOaToken
    pcOaToken = None
    for cookie in cookies:
        if cookie['name'] == 'pcOaToken':
            pcOaToken = cookie['value']
            break



    if pcOaToken:
        driver.get("https://oa.9ji.com/cloudapi_nc/oa-stock/api/ok3wqudao/get-enum")
        driver.quit()
        return pcOaToken
    else:
        print("未找到 pcOaToken。")
        driver.quit()
        return None

def call_api_with_token(pcOaToken):
    url = 'https://oa.9ji.com/cloudapi_nc/oa-stock/api/ok3wqudao/page/v2'
    headers = {
        'accept': 'application/json, text/plain, */*',
        'authorization': pcOaToken,  # 使用获取到的 pcOaToken
        'content-type': 'application/json',
        'xservicename': 'oa-stock'
    }
    data = {
        "page": {
            "current": 1,
            "size": 10
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查请求是否成功
        print("API 响应:", response.json())  # 打印 API 响应
    except requests.exceptions.RequestException as e:
        print(f"请求出错: {e}")

if __name__ == "__main__":
    pcOaToken = get_pc_token()  # 获取 pcOaToken
    if pcOaToken:
        call_api_with_token(pcOaToken)  # 调用 API


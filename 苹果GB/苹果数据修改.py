import requests
import tkinter as tk
from tkinter import messagebox

def update_area(area, account, password):
    url = f'https://moa.9ji.com/cloudapi_nc/oa-stock/api/user/area/updateGba'
    params = {
        'xservicename': 'oa-stock',
        'area': area,
        'account': account,
        'password': password
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()  # 检查请求是否成功
        return response.json()  # 返回 JSON 响应
    except requests.exceptions.RequestException as e:
        print(f"请求出错: {e}")
        return None

def on_submit():
    area = area_entry.get()
    account = account_entry.get()
    password = password_entry.get()
    
    result = update_area(area, account, password)
    
    if result:
        messagebox.showinfo("成功", f"响应: {result}")
    else:
        messagebox.showerror("错误", "请求失败，请检查输入。")

# 创建主窗口
root = tk.Tk()
root.title("更新区域信息")

# 创建输入框和标签
tk.Label(root, text="区域 (area):").grid(row=0, column=0)
area_entry = tk.Entry(root)
area_entry.grid(row=0, column=1)

tk.Label(root, text="账户 (account):").grid(row=1, column=0)
account_entry = tk.Entry(root)
account_entry.grid(row=1, column=1)

tk.Label(root, text="密码 (password):").grid(row=2, column=0)
# 设置 show='' 以显示明文输入
password_entry = tk.Entry(root, show='')
password_entry.grid(row=2, column=1)

# 创建提交按钮
submit_button = tk.Button(root, text="提交", command=on_submit)
submit_button.grid(row=3, columnspan=2)

# 运行主循环
root.mainloop() 
import pandas as pd
import json

def excel_to_json(file_path):
    try:
        # 读取 Excel 文件
        df = pd.read_excel(file_path)
        # 将 DataFrame 转换为 JSON 字符串
        json_str = df.to_json(orient='records', force_ascii=False)
        return json_str
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

# 示例用法
if __name__ == "__main__":
    file_path = "C:/Users/<USER>/Desktop/苹果数据修改.xls"  # 替换为您的 Excel 文件路径
    json_result = excel_to_json(file_path)
    if json_result:
        print(json_result)
    else:
        print("未能生成 JSON 结果。")
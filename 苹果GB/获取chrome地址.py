import os
import winreg as reg

def get_chrome_path():
    try:
        # 打开注册表路径，这里查找 Chrome 的安装路径
        registry_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"
        registry_key = reg.<PERSON>Key(reg.HKEY_LOCAL_MACHINE, registry_path)
        chrome_path, _ = reg.QueryValueEx(registry_key, "")
        reg.<PERSON><PERSON>ey(registry_key)

        # 返回 Chrome 的安装路径
        return chrome_path
    except Exception as e:
        print(f"Error: {e}")
        return None

chrome_path = get_chrome_path()
if chrome_path:
    print(f"Chrome executable path: {chrome_path}")

    # 创建一个新的批处理文件来启动 Chrome
    batch_file_path = os.path.join(os.path.dirname(chrome_path), "start_chrome_with_debugging.bat")
    with open(batch_file_path, 'w') as batch_file:
        batch_file.write(f'@echo off\n"{chrome_path}" --remote-debugging-port=9222\n')

    print(f"创建了批处理文件: {batch_file_path}")
else:
    print("Chrome not found.")

import pyuac
import subprocess
import winreg as reg
import psutil

def get_chrome_path():
    try:
        # 打开注册表路径，这里查找 Chrome 的安装路径
        registry_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"
        registry_key = reg.<PERSON>ey(reg.HKEY_LOCAL_MACHINE, registry_path)
        chrome_path, _ = reg.QueryValueEx(registry_key, "")
        reg.CloseKey(registry_key)

        # 返回 Chrome 的安装路径
        return chrome_path
    except Exception as e:
        print(f"Error: {e}")
        return None

def kill_chrome():
    # 遍历所有正在运行的进程
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            # 如果进程名称为 chrome.exe，则杀死该进程
            if proc.info['name'].lower() == 'chrome.exe':
                print(f"Killing process {proc.info['name']} with PID {proc.info['pid']}")
                proc.terminate()  # 尝试结束进程
                proc.wait()  # 等待进程终止
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            # 处理无效的进程或没有权限的情况
            pass


def open_chrome():
    # 检查是否以管理员权限运行，如果不是则重新启动脚本以管理员权限运行
    if not pyuac.isUserAdmin():
        pyuac.runAsAdmin()

    # 调用函数来杀死所有 Chrome 进程
    kill_chrome()

    chrome_path = get_chrome_path()

    # 确保 chrome_path 被引号包围
    if chrome_path:
        remote_debugging = '--remote-debugging-port=9222'
        command = f'"{chrome_path}" {remote_debugging}'  # 用引号包围路径

        # 执行命令
        subprocess.run(command, shell=True)
    else:
        print("未找到 Chrome 的安装路径。")



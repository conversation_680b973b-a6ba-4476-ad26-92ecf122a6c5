import requests
from bs4 import BeautifulSoup
import re
import time
import json
import os
import markdown
from markdown.extensions.toc import TocExtension
from markdown.extensions.tables import TableExtension
from markdown.extensions.fenced_code import FencedCodeExtension
from markdown.extensions.codehilite import CodeHiliteExtension
import html2text
import pypandoc
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def fetch_direct_request(url):
    """Try to fetch the document using direct HTTP request with appropriate headers"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()  # Raise an exception for bad status codes
        return response.text
    except requests.exceptions.RequestException as e:
        print(f"Error with direct request: {e}")
        return None

def extract_feishu_api_data(url):
    """Try to extract document ID and access an API endpoint"""
    doc_id_match = re.search(r'docx/([A-Za-z0-9]+)', url)
    if not doc_id_match:
        print("Could not extract document ID from URL")
        return None
    
    doc_id = doc_id_match.group(1)
    api_url = f"https://ry6uq5vtyu.feishu.cn/space/api/doc/meta/read?doc_token={doc_id}"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Referer': url,
        'X-Requested-With': 'XMLHttpRequest',
    }
    
    try:
        response = requests.get(api_url, headers=headers, timeout=30)
        if response.status_code == 200:
            try:
                return response.json()
            except json.JSONDecodeError:
                print("Could not parse JSON response from API")
        else:
            print(f"API request failed with status code: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"Error with API request: {e}")
    
    return None

def extract_feishu_content_api(url):
    """Try to extract content using a content-specific API endpoint"""
    doc_id_match = re.search(r'docx/([A-Za-z0-9]+)', url)
    if not doc_id_match:
        print("Could not extract document ID from URL")
        return None
    
    doc_id = doc_id_match.group(1)
    content_api_url = f"https://ry6uq5vtyu.feishu.cn/space/api/doc/content?doc_token={doc_id}"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Referer': url,
        'X-Requested-With': 'XMLHttpRequest',
    }
    
    try:
        response = requests.get(content_api_url, headers=headers, timeout=30)
        if response.status_code == 200:
            try:
                return response.json()
            except json.JSONDecodeError:
                print("Could not parse JSON response from content API")
        else:
            print(f"Content API request failed with status code: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"Error with content API request: {e}")
    
    return None

def fetch_feishu_doc(url):
    """Fetch Feishu document content using Selenium for full JS rendering"""
    print("Trying multiple methods to fetch the document content...")
    
    # First try direct HTTP request
    print("Method 1: Direct HTTP request...")
    direct_html = fetch_direct_request(url)
    if direct_html:
        print("Got response from direct HTTP request")
        direct_soup = BeautifulSoup(direct_html, 'html.parser')
    else:
        direct_soup = None
    
    # Try API endpoints
    print("Method 2: Feishu API request...")
    api_data = extract_feishu_api_data(url)
    
    print("Method 3: Feishu Content API request...")
    content_api_data = extract_feishu_content_api(url)
    
    # Finally, use Selenium as a fallback
    print("Method 4: Using Selenium for full JS rendering...")
    # Set up Chrome options for headless browsing
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920x1080")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    # Initialize the driver with automatic webdriver installation
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
    
    try:
        # Navigate to the URL
        print(f"Accessing {url}...")
        driver.get(url)
        
        # Wait for the content to load
        try:
            # First try to find if the document content has loaded
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".docx-viewer-content, .lark-doc-content, .docx-content"))
            )
            print("Content element found.")
        except:
            print("Timeout waiting for content element. Checking if document is viewable...")
            try:
                # Check if we need to handle a login wall or preview page
                if "请登录后查看" in driver.page_source or "Please login to view" in driver.page_source:
                    print("Login required to view this document.")
                    return None, "LOGIN_REQUIRED", None
            except:
                pass
            
            print("Proceeding with available page content...")
        
        # Give the page a moment to fully render
        time.sleep(10)
        
        # Try to get document data from any embedded JSON
        doc_data = extract_json_data(driver)
        
        # Save the rendered page for inspection
        with open("rendered_page.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("Saved rendered page to rendered_page.html for inspection")
        
        # Get the page source
        page_source = driver.page_source
        
        # Try to take screenshot for visual debugging
        try:
            driver.save_screenshot("feishu_screenshot.png")
            print("Saved screenshot to feishu_screenshot.png")
        except Exception as e:
            print(f"Failed to take screenshot: {e}")
        
        # Parse with BeautifulSoup
        selenium_soup = BeautifulSoup(page_source, 'html.parser')
        
        return selenium_soup, doc_data, {
            'direct_soup': direct_soup,
            'api_data': api_data,
            'content_api_data': content_api_data
        }
        
    finally:
        # Close the driver
        driver.quit()

def extract_json_data(driver):
    """Try to extract document data from JSON embedded in the page"""
    try:
        # Execute script to find all script tags with JSON data
        scripts = driver.execute_script("""
            var scripts = document.querySelectorAll('script');
            var jsonData = [];
            
            for (var i = 0; i < scripts.length; i++) {
                var script = scripts[i];
                var content = script.textContent;
                
                if (content && (content.includes('docContent') || content.includes('blocks') || content.includes('title'))) {
                    jsonData.push(content);
                }
            }
            
            return jsonData;
        """)
        
        if scripts:
            for script in scripts:
                # Try to find and extract JSON data
                json_matches = re.findall(r'({.*?"docContent".*?})', script)
                if not json_matches:
                    json_matches = re.findall(r'({.*?"blocks".*?})', script)
                if not json_matches:
                    json_matches = re.findall(r'({.*?"content".*?})', script)
                    
                if json_matches:
                    for json_str in json_matches:
                        try:
                            data = json.loads(json_str)
                            if "docContent" in data or "blocks" in data or "content" in data:
                                print("Found document content in JSON data")
                                return data
                        except:
                            pass
        
        print("No JSON document data found")
        return None
    except Exception as e:
        print(f"Error extracting JSON data: {e}")
        return None

def extract_content(soup):
    """Extract content from various possible containers in Feishu docs"""
    # Try different possible container selectors
    content_selectors = [
        '.docx-viewer-content',
        '.lark-doc-content',
        '.docx-content',
        '.lark-document-container',
        'div[data-role="doc-content"]',
        '.lark-open-doc-viewer',  # Another possible container
        '.lark-doc-renderer-content',  # Another possible content container
        '#docx-root',  # Root container
        '#container'  # Generic container
    ]
    
    content = None
    for selector in content_selectors:
        content = soup.select_one(selector)
        if content:
            print(f"Found content with selector: {selector}")
            break
    
    if not content:
        # If no specific content container found, get the body
        content = soup.body
        print("No specific content container found, using full body")
    
    return content

def process_api_data(api_data):
    """Process document content from API data"""
    if not api_data:
        return ""
    
    try:
        # Extract title if available
        title = ""
        if "data" in api_data and "title" in api_data["data"]:
            title = api_data["data"]["title"]
            
        markdown = f"# {title}\n\n" if title else ""
            
        # Try to find content blocks
        if "data" in api_data and "content" in api_data["data"]:
            content = api_data["data"]["content"]
            if isinstance(content, str):
                try:
                    content = json.loads(content)
                except:
                    markdown += content + "\n\n"
                    return markdown
                    
            # Process structured content
            if isinstance(content, dict) and "blocks" in content:
                blocks = content["blocks"]
                for block in blocks:
                    if "type" in block and "content" in block:
                        block_type = block["type"]
                        block_content = block["content"]
                        
                        if block_type == "heading":
                            level = block.get("level", 1)
                            markdown += "#" * level + " " + block_content + "\n\n"
                        elif block_type == "text" or block_type == "paragraph":
                            markdown += block_content + "\n\n"
                        elif block_type == "list":
                            list_type = block.get("listType", "bullet")
                            items = block_content if isinstance(block_content, list) else [block_content]
                            for i, item in enumerate(items):
                                if list_type == "bullet" or list_type == "unordered":
                                    markdown += "- " + item + "\n"
                                else:
                                    markdown += f"{i+1}. " + item + "\n"
                            markdown += "\n"
                        elif block_type == "code":
                            markdown += "```\n" + block_content + "\n```\n\n"
                        elif block_type == "image":
                            url = block.get("url", "")
                            alt = block.get("alt", "Image")
                            if url:
                                markdown += f"![{alt}]({url})\n\n"
        
        return markdown
    except Exception as e:
        print(f"Error processing API data: {e}")
        return ""

def process_json_content(doc_data):
    """Process document content from JSON data"""
    if not doc_data:
        return ""
    
    try:
        # Try different possible content structures
        content = None
        if "docContent" in doc_data:
            content = doc_data["docContent"]
        elif "content" in doc_data:
            content = doc_data["content"]
        elif "data" in doc_data and "content" in doc_data["data"]:
            content = doc_data["data"]["content"]
            
        if not content:
            print("No content field found in JSON data")
            return ""
            
        # Handle string content
        if isinstance(content, str):
            try:
                content = json.loads(content)
            except:
                # If it's not valid JSON, just return the text
                return content + "\n\n"
        
        # Extract title
        title = ""
        if "title" in content:
            title = content["title"]
        elif "title" in doc_data:
            title = doc_data["title"]
        
        # Extract blocks of content
        markdown = f"# {title}\n\n" if title else ""
        
        if "blocks" in content:
            blocks = content["blocks"]
            for block in blocks:
                if "type" in block and "content" in block:
                    block_type = block["type"]
                    block_content = block["content"]
                    
                    if block_type == "heading":
                        level = block.get("level", 1)
                        markdown += "#" * level + " " + block_content + "\n\n"
                    elif block_type == "text" or block_type == "paragraph":
                        markdown += block_content + "\n\n"
                    elif block_type == "list":
                        list_type = block.get("listType", "bullet")
                        items = block_content if isinstance(block_content, list) else [block_content]
                        for i, item in enumerate(items):
                            if list_type == "bullet" or list_type == "unordered":
                                markdown += "- " + item + "\n"
                            else:
                                markdown += f"{i+1}. " + item + "\n"
                        markdown += "\n"
                    elif block_type == "code":
                        markdown += "```\n" + block_content + "\n```\n\n"
                    elif block_type == "image":
                        url = block.get("url", "")
                        alt = block.get("alt", "Image")
                        if url:
                            markdown += f"![{alt}]({url})\n\n"
        
        return markdown
    except Exception as e:
        print(f"Error processing JSON content: {e}")
        return ""

def clean_markdown(markdown):
    """Clean up the markdown to remove duplicates and unnecessary content"""
    # Remove duplicate headings that are common in these docs
    lines = markdown.split('\n')
    unique_lines = []
    seen_headings = set()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Skip empty lines at the beginning
        if not line and not unique_lines:
            i += 1
            continue
        
        # Check for heading duplicates
        if line.startswith('#'):
            heading_text = line.strip()
            if heading_text in seen_headings:
                i += 1
                continue
            seen_headings.add(heading_text)
        
        # Add the line
        unique_lines.append(line)
        i += 1
    
    # Join lines back together with proper spacing
    cleaned = ""
    prev_is_heading = False
    
    for line in unique_lines:
        if not line:
            if not prev_is_heading:
                cleaned += "\n"
            prev_is_heading = False
            continue
            
        if line.startswith('#'):
            if cleaned and not cleaned.endswith('\n\n'):
                cleaned += "\n\n"
            cleaned += line + "\n"
            prev_is_heading = True
        else:
            if prev_is_heading:
                cleaned += "\n"
            cleaned += line + "\n"
            prev_is_heading = False
    
    # Clean up any excessive newlines
    return re.sub(r'\n{3,}', '\n\n', cleaned)

def convert_to_markdown(soup, doc_data=None, extra_data=None):
    """Convert the document content to Markdown, trying multiple sources"""
    markdown = ""
    
    # First check if we have any API data
    if extra_data and extra_data.get('api_data'):
        print("Trying to process API data...")
        api_markdown = process_api_data(extra_data['api_data'])
        if api_markdown:
            print("Successfully extracted content from API data")
            markdown += api_markdown
            
    # Then check content API data
    if extra_data and extra_data.get('content_api_data'):
        print("Trying to process Content API data...")
        content_api_markdown = process_api_data(extra_data['content_api_data'])
        if content_api_markdown:
            print("Successfully extracted content from Content API data")
            markdown += content_api_markdown
    
    # Try to process from JSON data if available
    if not markdown and doc_data:
        print("Trying to process JSON data...")
        markdown_from_json = process_json_content(doc_data)
        if markdown_from_json:
            print("Successfully extracted content from JSON data")
            markdown += markdown_from_json
    
    # Try direct HTTP response
    if not markdown and extra_data and extra_data.get('direct_soup'):
        print("Trying to process direct HTTP response...")
        direct_content = extract_content(extra_data['direct_soup'])
        if direct_content:
            direct_markdown = extract_html_content(direct_content, extra_data['direct_soup'])
            if direct_markdown:
                print("Successfully extracted content from direct HTTP response")
                markdown += direct_markdown
    
    # If nothing worked so far, try the Selenium-rendered HTML
    if not markdown and soup:
        print("Trying to process Selenium-rendered HTML...")
        content = extract_content(soup)
        
        if content:
            selenium_markdown = extract_html_content(content, soup)
            if selenium_markdown:
                print("Successfully extracted content from Selenium-rendered HTML")
                markdown += selenium_markdown
    
    # If we still have no content, return error message
    if not markdown:
        markdown = "# Could not extract content from this Feishu document\n\n"
        markdown += "This document may require login or have special access restrictions.\n"
    
    # Clean the markdown
    return clean_markdown(markdown)

def extract_html_content(content, soup):
    """Extract content from HTML elements"""
    markdown = ""
    
    # Extract title if available
    title_elem = soup.select_one('title') or soup.select_one('h1')
    if title_elem:
        title = title_elem.get_text().strip()
        # Strip common text in Feishu titles like "- 飞书云文档"
        title = re.sub(r'\s*[-—]\s*飞书.*?$', '', title)
        markdown += f"# {title}\n\n"
    
    # Process headings - look for both standard and Feishu specific heading classes
    headings = content.select('h1, h2, h3, h4, h5, h6, [class*="heading"], [class*="title"]')
    for heading in headings:
        # Skip navigation elements or UI elements
        if any(cls in str(heading.get('class', [])) for cls in ['nav', 'menu', 'header', 'footer']):
            continue
            
        # Try to determine heading level
        level = 1  # Default level
        
        if heading.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            level = int(heading.name[1])
        elif 'heading1' in str(heading.get('class', [])) or 'title1' in str(heading.get('class', [])):
            level = 1
        elif 'heading2' in str(heading.get('class', [])) or 'title2' in str(heading.get('class', [])):
            level = 2
        elif 'heading3' in str(heading.get('class', [])) or 'title3' in str(heading.get('class', [])):
            level = 3
        else:
            # Try to find heading level in class name
            for cls in heading.get('class', []):
                if re.match(r'heading\d+', cls) or re.match(r'title\d+', cls):
                    level_match = re.search(r'\d+', cls)
                    if level_match:
                        level = int(level_match.group(0))
                        break
        
        text = heading.get_text().strip()
        if text and not any(x in text.lower() for x in ['登录', '注册', '帮助中心', '效率指南']):
            # Skip common UI text that isn't content
            markdown += '#' * level + ' ' + text + '\n\n'
    
    # Process paragraphs and text blocks
    paragraphs = content.select('p, [class*="paragraph"], [class*="text-block"]')
    for paragraph in paragraphs:
        # Skip if the paragraph is inside header, footer, or navigation elements
        if paragraph.parent and any(x in str(paragraph.parent.get('class', [])) for x in ['header', 'footer', 'nav', 'menu']):
            continue
            
        text = paragraph.get_text().strip()
        if text:
            # Skip if this appears to be a heading we already processed or common UI elements
            if paragraph in headings or any(x in text.lower() for x in ['登录', '注册', '帮助中心', '效率指南']):
                continue
            markdown += text + '\n\n'
    
    # Process lists
    for ul in content.select('ul, [class*="unordered-list"]'):
        # Skip navigation menus
        if any(x in str(ul.get('class', [])) for x in ['nav', 'menu']):
            continue
            
        for li in ul.select('li, [class*="list-item"]'):
            text = li.get_text().strip()
            if text:
                markdown += "- " + text + '\n'
        markdown += '\n'
    
    for ol in content.select('ol, [class*="ordered-list"]'):
        for i, li in enumerate(ol.select('li, [class*="list-item"]')):
            text = li.get_text().strip()
            if text:
                markdown += f"{i+1}. " + text + '\n'
        markdown += '\n'
    
    # Process tables
    for table in content.select('table'):
        has_headers = False
        rows = table.select('tr')
        
        if rows:
            # Check if first row has th elements for header
            if rows[0].select('th'):
                has_headers = True
            
            for i, row in enumerate(rows):
                cells = row.select('td, th')
                if cells:
                    row_cells = [cell.get_text().strip() for cell in cells]
                    markdown += '| ' + ' | '.join(row_cells) + ' |\n'
                    
                    # Add separator after header row
                    if i == 0 and (has_headers or len(rows) > 1):
                        markdown += '| ' + ' | '.join(['---'] * len(cells)) + ' |\n'
            
            markdown += '\n'
    
    # Process images
    for img in content.select('img'):
        # Skip tiny icons and UI elements
        width = img.get('width', '')
        height = img.get('height', '')
        if width and height and (int(width) < 50 or int(height) < 50):
            continue
            
        alt_text = img.get('alt', 'Image')
        src = img.get('src', '')
        if src:
            markdown += f'![{alt_text}]({src})\n\n'
    
    # Process code blocks
    for code in content.select('pre code, [class*="code-block"]'):
        markdown += '```\n' + code.get_text() + '\n```\n\n'
    
    # Process inline code
    for code in content.select('code, [class*="inline-code"]'):
        if code.parent.name != 'pre' and not any('code-block' in str(cls) for cls in code.get('class', [])):
            inline_code = code.get_text().strip()
            if inline_code:
                markdown = markdown.replace(inline_code, f'`{inline_code}`')
    
    return markdown

def process_markdown_with_advanced_parser(markdown_text, output_format='html', output_file=None):
    """
    Process markdown text with advanced parsing features
    
    Args:
        markdown_text (str): The markdown text to process
        output_format (str): The desired output format ('html', 'pdf', 'docx', etc.)
        output_file (str): The output file path (without extension)
        
    Returns:
        str: The processed content in the desired format
    """
    print(f"Processing markdown with advanced parser to {output_format} format...")
    
    # Define the extensions to use
    extensions = [
        'markdown.extensions.extra',  # Includes tables, code blocks, etc.
        'markdown.extensions.sane_lists',
        'markdown.extensions.smarty',
        'markdown.extensions.wikilinks',
        FencedCodeExtension(),
        CodeHiliteExtension(linenums=True, css_class='highlight'),
        TableExtension()
    ]
    
    # Process TOC separately
    toc_extension = TocExtension(permalink=True, baselevel=1)
    extensions.append(toc_extension)
    
    # Process markdown to HTML with extensions
    html_content = markdown.markdown(markdown_text, extensions=extensions)
    
    if output_format == 'html':
        # Generate a table of contents manually
        toc_html = "<div class='toc'><h2>Table of Contents</h2><ul>"
        # Extract headings for TOC
        headings = re.findall(r'<h([1-6]).*?id="(.*?)".*?>(.*?)</h\1>', html_content)
        for level, id_attr, title in headings:
            # Create indentation based on heading level
            indent = '  ' * (int(level) - 1)
            toc_html += f"{indent}<li><a href='#{id_attr}'>{title}</a></li>\n"
        toc_html += "</ul></div>"
        
        # Add CSS for better styling
        styled_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Markdown Document</title>
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                    line-height: 1.6;
                    padding: 20px;
                    max-width: 900px;
                    margin: 0 auto;
                    color: #333;
                }}
                h1, h2, h3, h4, h5, h6 {{
                    color: #111;
                    margin-top: 24px;
                    margin-bottom: 16px;
                    font-weight: 600;
                    line-height: 1.25;
                }}
                h1 {{ font-size: 2em; padding-bottom: 0.3em; border-bottom: 1px solid #eaecef; }}
                h2 {{ font-size: 1.5em; padding-bottom: 0.3em; border-bottom: 1px solid #eaecef; }}
                img {{ max-width: 100%; }}
                pre {{ padding: 16px; overflow: auto; background-color: #f6f8fa; border-radius: 3px; }}
                code {{ font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace; }}
                blockquote {{ padding: 0 1em; color: #6a737d; border-left: 0.25em solid #dfe2e5; }}
                table {{ border-collapse: collapse; width: 100%; }}
                table, th, td {{ border: 1px solid #ddd; padding: 8px; }}
                tr:nth-child(even) {{ background-color: #f2f2f2; }}
                th {{ padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #4CAF50; color: white; }}
                .toc {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
                .toc ul {{ padding-left: 20px; }}
                .highlight {{ background-color: #f6f8fa; }}
            </style>
        </head>
        <body>
            {toc_html}
            {html_content}
        </body>
        </html>
        """
        
        if output_file:
            html_output_file = f"{output_file}.html"
            with open(html_output_file, 'w', encoding='utf-8') as f:
                f.write(styled_html)
            print(f"HTML file saved to {html_output_file}")
        
        return styled_html
    
    # Use pypandoc for other formats (like PDF, DOCX)
    elif output_format in ['pdf', 'docx', 'epub', 'odt', 'latex']:
        try:
            # Create temporary HTML file
            temp_html_file = 'temp_markdown.html'
            with open(temp_html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            output_file_with_ext = f"{output_file}.{output_format}"
            
            # Convert to the desired format
            pypandoc.convert_file(
                temp_html_file,
                output_format,
                outputfile=output_file_with_ext,
                extra_args=['--pdf-engine=xelatex'] if output_format == 'pdf' else []
            )
            
            # Clean up temp file
            if os.path.exists(temp_html_file):
                os.remove(temp_html_file)
                
            print(f"{output_format.upper()} file saved to {output_file_with_ext}")
            return f"Conversion to {output_format.upper()} completed successfully. File saved to {output_file_with_ext}"
            
        except Exception as e:
            print(f"Error converting to {output_format}: {e}")
            return html_content
    
    return html_content

def extract_markdown_structure(markdown_text):
    """
    Analyze the structure of the markdown document
    
    Args:
        markdown_text (str): The markdown text to analyze
        
    Returns:
        dict: Information about the document structure
    """
    structure = {
        'title': None,
        'headings': [],
        'images': [],
        'links': [],
        'tables': 0,
        'code_blocks': 0,
        'word_count': len(markdown_text.split())
    }
    
    # Extract title (first # heading)
    title_match = re.search(r'^# (.+)$', markdown_text, re.MULTILINE)
    if title_match:
        structure['title'] = title_match.group(1).strip()
    
    # Extract headings
    headings = re.findall(r'^(#{1,6}) (.+)$', markdown_text, re.MULTILINE)
    for level, text in headings:
        structure['headings'].append({
            'level': len(level),
            'text': text.strip()
        })
    
    # Extract images
    images = re.findall(r'!\[(.*?)\]\((.*?)\)', markdown_text)
    for alt, src in images:
        structure['images'].append({
            'alt': alt,
            'src': src
        })
    
    # Extract links
    links = re.findall(r'(?<!!)\[(.*?)\]\((.*?)\)', markdown_text)
    for text, url in links:
        structure['links'].append({
            'text': text,
            'url': url
        })
    
    # Count tables
    table_rows = re.findall(r'^\|(.+)\|$', markdown_text, re.MULTILINE)
    structure['tables'] = len(table_rows) // 3  # Assume at least 3 rows per table (header, separator, data)
    
    # Count code blocks
    code_blocks = re.findall(r'```', markdown_text)
    structure['code_blocks'] = len(code_blocks) // 2  # Start and end markers
    
    return structure

def clean_markdown_advanced(markdown_text):
    """
    Advanced cleaning of the markdown to remove duplicates and fix formatting issues
    
    Args:
        markdown_text (str): The markdown text to clean
        
    Returns:
        str: The cleaned markdown text
    """
    # Split text into paragraphs (splitting by empty lines)
    paragraphs = re.split(r'\n\s*\n', markdown_text)
    clean_paragraphs = []
    
    # Track seen paragraphs
    seen_paragraphs = set()
    seen_headings = set()
    
    for paragraph in paragraphs:
        # Skip empty paragraphs
        if not paragraph.strip():
            continue
            
        # Handle headings specially
        if re.match(r'^#+\s', paragraph):
            # If this is a heading and we haven't seen it before, add it
            heading = paragraph.strip()
            if heading not in seen_headings:
                seen_headings.add(heading)
                clean_paragraphs.append(paragraph)
            continue
        
        # Process normal paragraphs
        # Remove internal repetitions (same line repeated multiple times within a paragraph)
        lines = paragraph.split('\n')
        clean_lines = []
        
        i = 0
        while i < len(lines):
            current = lines[i].strip()
            if not current:
                i += 1
                continue
                
            # Check for repeating line pattern
            repetition_count = 1
            j = i + 1
            while j < len(lines) and lines[j].strip() == current:
                repetition_count += 1
                j += 1
                
            # Only add the line once, regardless of how many times it repeats
            clean_lines.append(current)
            i += repetition_count
        
        # Reconstruct paragraph without internal repetitions
        clean_paragraph = '\n'.join(clean_lines)
        
        # Skip if we've seen this exact paragraph before
        if clean_paragraph in seen_paragraphs:
            continue
            
        seen_paragraphs.add(clean_paragraph)
        clean_paragraphs.append(clean_paragraph)
    
    # Format the output with proper spacing
    result = []
    for i, para in enumerate(clean_paragraphs):
        # Add spacing before headings
        if re.match(r'^#+\s', para) and i > 0:
            result.append('')  # Empty line before heading
            
        result.append(para)
        
        # Add spacing after paragraphs
        if i < len(clean_paragraphs) - 1:
            result.append('')  # Empty line after paragraph
    
    return '\n'.join(result)

def save_markdown(markdown, output_file):
    """Save markdown to a file"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(markdown)
    print(f"Markdown saved to {output_file}")
    
    # Return the base filename without extension for further processing
    return os.path.splitext(output_file)[0]

def main():
    url = "https://ry6uq5vtyu.feishu.cn/docx/VRWOd0D61ocEZPxc7CrcQjvUnab?PdWaR8=1"
    output_file = "C:\\Users\\<USER>\\Desktop\\feishu_document.md"
    
    print("Fetching Feishu document...")
    soup, doc_data, extra_data = fetch_feishu_doc(url)
    
    if soup is None:
        print("Failed to fetch document content. Reason:", doc_data)
        return
    
    print("Converting to Markdown...")
    markdown = convert_to_markdown(soup, doc_data, extra_data)
    
    # Apply advanced cleaning
    print("Applying advanced Markdown cleaning...")
    markdown = clean_markdown_advanced(markdown)
    
    print("Saving Markdown...")
    base_output_file = save_markdown(markdown, output_file)
    
    # Analyze document structure
    print("\nAnalyzing document structure...")
    structure = extract_markdown_structure(markdown)
    print(f"Title: {structure['title']}")
    print(f"Headings: {len(structure['headings'])}")
    print(f"Images: {len(structure['images'])}")
    print(f"Tables: {structure['tables']}")
    print(f"Word count: {structure['word_count']}")
    
    # Convert to HTML with advanced processing
    print("\nConverting to HTML with advanced Markdown processing...")
    process_markdown_with_advanced_parser(markdown, 'html', base_output_file)
    
    # Try to convert to PDF if supported
    try:
        print("\nAttempting to convert to PDF...")
        process_markdown_with_advanced_parser(markdown, 'pdf', base_output_file)
    except Exception as e:
        print(f"PDF conversion not available: {e}")
    
    # Try to convert to DOCX if supported
    try:
        print("\nAttempting to convert to DOCX...")
        process_markdown_with_advanced_parser(markdown, 'docx', base_output_file)
    except Exception as e:
        print(f"DOCX conversion not available: {e}")
    
    print(f"\nDone! All processing completed.")
    
    # Print a sample of the output
    preview_lines = markdown.split('\n')[:20]
    print("\nPreview of the first 20 lines:")
    print('\n'.join(preview_lines))
    
    # Inform about the saved HTML
    if os.path.exists("rendered_page.html"):
        print("\nNote: The rendered HTML page was saved to 'rendered_page.html'")
        print("You can examine this file to see the original content structure.")

if __name__ == "__main__":
    main()

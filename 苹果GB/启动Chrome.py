import os
import subprocess
import sys

def start_chrome_with_debugging():
    # Chrome浏览器的可能路径
    chrome_paths = [
        r'C:\Program Files\Google\Chrome\Application\chrome.exe',
        r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
        os.path.expanduser('~') + r'\AppData\Local\Google\Chrome\Application\chrome.exe',
    ]
    
    # 查找Chrome可执行文件
    chrome_exe = None
    for path in chrome_paths:
        if os.path.exists(path):
            chrome_exe = path
            break
    
    if not chrome_exe:
        print("未找到Chrome浏览器，请确认Chrome是否安装")
        return False
    
    # 启动Chrome并开启远程调试
    cmd = [
        chrome_exe,
        '--remote-debugging-port=9222',
        '--user-data-dir="C:\\temp\\chrome_debug_temp"'
    ]
    
    try:
        subprocess.Popen(cmd)
        print("Chrome已启动，远程调试端口：9222")
        print("请保持此Chrome窗口打开，然后运行你的主程序")
        return True
    except Exception as e:
        print(f"启动Chrome时出错: {e}")
        return False

if __name__ == "__main__":
    start_chrome_with_debugging() 
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
#"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 执行改代码的时候需要先手动执行
# 设置 Chrome 浏览器的远程调试选项
chrome_options = Options()
chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")

# 连接到已经启动的浏览器实例
driver = webdriver.Chrome(options=chrome_options)

# 访问你要抓取的页面
driver.get("https://oa.9ji.com/staticpc/#/")

# 获取页面内容
page_source = driver.page_source

# 如果你想抓取特定的内容，可以使用 BeautifulSoup 进行解析
soup = BeautifulSoup(page_source, 'html.parser')

# 获取 cookies
cookies = driver.get_cookies()

# 打印所有 cookies
#print("所有 Cookies:", cookies)

# 查找 pcOaToken
pcOaToken = None
for cookie in cookies:
    if cookie['name'] == 'pcOaToken':
        pcOaToken = cookie['value']
        break

if pcOaToken:
    print(f"pcOaToken: {pcOaToken}")
else:
    print("未找到 pcOaToken。")

# 完成后记得关闭浏览器
driver.quit()

package com.jiuji.pick.web.order.controller;


import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.OrderLogTypeEnum;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.service.OrderInfoLogService;
import com.jiuji.pick.web.order.param.SaveLogParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 订单操作日志表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-20
 */
@RestController
@RequestMapping("/api/order/log")
public class OrderInfoLogController {

    @Resource
    private OrderInfoLogService orderInfoLogService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;


    /**
     * 合作伙伴保存订单操作日志
     *
     * @param param param
     * @return
     */
    @PostMapping("/saveLog/v1")
    public Result<String> saveLog(@RequestBody @Valid SaveLogParam param) {
        PartnerTokenInfo tokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if(tokenInfo==null){
            return Result.error("当前登录信息失效，请重新登录");
        }
        String loginName = tokenInfo.getLoginOAUserName();
        Long userId = tokenInfo.getLoginOAUserId();
        orderInfoLogService.saveOrderLog(param.getContent(),OrderLogTypeEnum.OTHER,param.getOrderId(),userId,loginName);
        return Result.success("添加成功");
    }

    /**
     * 供应商保存订单操作日志
     * @param param
     * @return
     */
    @PostMapping("/saveLog/v2")
    public Result<String> saveSupplierLog(@RequestBody @Valid SaveLogParam param) {
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();

        if(supplierTokenInfo==null){
            return Result.error("当前登录信息失效，请重新登录");
        }
        String loginName = supplierTokenInfo.getLoginName();
        Long userId = supplierTokenInfo.getId();
        orderInfoLogService.saveOrderLog(param.getContent(),OrderLogTypeEnum.OTHER,param.getOrderId(),userId,loginName);
        return Result.success("添加成功");
    }

}


package com.jiuji.pick.web.product.controller;


import com.ch999.common.util.vo.Result;
import com.google.common.collect.Lists;

import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.service.product.bo.AdmSkuBO;
import com.jiuji.pick.service.product.bo.ProductBO;
import com.jiuji.pick.service.product.entity.*;
import com.jiuji.pick.service.product.mapper.*;
import com.jiuji.pick.service.product.service.ProductCsService;
import com.jiuji.pick.service.product.service.ProductImageService;
import com.jiuji.pick.service.product.service.ProductPriceService;
import com.jiuji.pick.service.product.service.ProductService;
import com.jiuji.pick.service.product.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.jiuji.pick.common.utils.ProductSynchronizeUtils.callBack;

/**
 * @Author:wangkai
 * @Description 九机商品新增时同步到智乐方、华腾数据库
 * @Date 2019-11-5 19:33:23
 */
@Slf4j
@RequestMapping("/api/synchronize/product/data")
@RestController
public class ProductSynchronizeController {

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductPriceService productPriceService;

    @Resource
    private ProductStandardMapper productStandardMapper;

    @Resource
    private ProductStDetailMapper productStDetailMapper;

    @Resource
    private ProductCsBasicMapper productCsBasicMapper;

    @Resource
    private ParamValueMapper paramValueMapper;

    @Resource
    private ParamGroupMapper paramGroupMapper;

    @Autowired
    private ProductCsService productCsService;

    @Autowired
    private ProductImageService productImageService;


    /**
     * 同步spu
     * @param productBO
     * @return
     */
    @PostMapping("/spu")
    public Result<Boolean> saveProduct(@RequestBody ProductBO productBO) {
        // OA全部设置“未启用”
        productBO.setOaEnable(false);
        // 小型系统的商品添加方式跟九机不一致（详见管理后台 主要是sku结构不同）
        // 九机同步过来的数据结构中没有admSkuBOList 这里需要构建 否则不会添加sku
        boolean b = productService.addProduct(productBO, 0L, true);
        if (b) {
            //避免超时回调九机
            callBack();
        }
        return b ? Result.success(): Result.error("同步失败", "同步失败");
    }

    /**
     * 同步sku
     * @param admSkuBOList
     * @return
     */
    @PostMapping("/sku")
    public Result<Boolean> saveSku(@RequestBody List<AdmSkuBO> admSkuBOList) {
        Result<List<AdmSkuBO>> result= productPriceService.batchUpdateSku(admSkuBOList, true);
        if (result != null && result.getCode() == 0) {
            //避免超时回调九机
            callBack();
        }
        return result != null && result.getCode() == 0 ? Result.success() : Result.error("同步失败", result.getMsg());
    }

    /**
     * 同步商品的基础规格
     * @param productStandard
     * @return
     */
    @PostMapping("/productStandard")
    public Result<Boolean> saveProductStandard(@RequestBody ProductStandard productStandard) {
        int row;
        try {
            if(productStandard.getOrderId() == null){
                productStandard.setOrderId(0);
            }
            ProductStandard one = productStandardMapper.selectById(productStandard.getId());
            if(one == null){
                productStandard.setIsdel(false);
                row = productStandardMapper.addProductStandard(productStandard);
            } else {
                row = productStandardMapper.updateById(productStandard);
            }
        } catch (Exception e) {
            String fail = "同步多租户商品规格失败";
            log.error(fail,e);
            return Result.error(fail, fail);
        }
        if(row>0){
            //避免超时回调九机
            callBack();
        }
        return Result.success();
    }

    /**
     * 同步商品的规格明细
     * @param productStDetail
     * @return
     */
    @PostMapping("/productStDetail")
    public Result<Boolean> saveProductStDetail(@RequestBody ProductStDetail productStDetail) {
        int row;
        try {
            if(productStDetail.getOrderId() == null){
                productStDetail.setOrderId(0);
            }
            // 防止主键冲突 先查询
            ProductStDetail one = productStDetailMapper.selectById(productStDetail.getId() == null ? 0 : productStDetail.getId());
            if (one == null){
                row = productStDetailMapper.addProductStDetail(productStDetail);
            } else{
                row = productStDetailMapper.updateById(productStDetail);
            }
        } catch (Exception e) {
            log.error("同步多租户商品规格明细失败",e);
            return  Result.error("同步多租户商品规格明细失败", "同步多租户商品规格明细失败");
        }
        if(row>0){
            //避免超时回调九机
            callBack();
        }
        return Result.success();
    }


    /**
     * 同步参数组
     * @param paramGroupVO
     * @return
     */
    @PostMapping("/paramGroup")
    public Result<Boolean> saveParamGroup(@RequestBody CategoryParamGroupVO paramGroupVO) {
        ParamGroup existParamGroup = paramGroupMapper.selectById(paramGroupVO.getId());
        if(existParamGroup != null){
            return  Result.success();
        } else {
            ParamGroup paramGroup = new ParamGroup();
            paramGroup.setId(paramGroupVO.getId());
            paramGroup.setCateId(paramGroupVO.getCateId());
            paramGroup.setGroupName(paramGroupVO.getGroupName());
            paramGroup.setRank(paramGroupVO.getRank());
            paramGroup.setIsDel(paramGroupVO.getDel() ? 1 : 0);
            int result = paramGroupMapper.insertParamGroup(paramGroup);
            if (result>0) {
                //避免超时回调九机
                callBack();
            }
            return result>0 ? Result.success() : Result.error("同步多租户参数组失败", "同步多租户参数组失败");
        }
    }


    /**
     * 同步基础参数
     * @param productCsBasicVo
     * @return
     */
    @PostMapping("/productCsBasic")
    public Result<Boolean> saveProductCsBasic(@RequestBody ProductCsBasicVo productCsBasicVo) {
        ProductCsBasic existProductCsBasic = productCsBasicMapper.selectById(productCsBasicVo.getId());
        if(existProductCsBasic != null){
            return  Result.success();
        } else {
            ProductCsBasic productCsBasic = new ProductCsBasic();
            BeanUtils.copyProperties(productCsBasicVo,productCsBasic);
            productCsBasic.setIsDel(false);
            productCsBasic.setExcludePk(productCsBasicVo.getExcludePK());
            int result = productCsBasicMapper.insertProductCsBasic(productCsBasic);
            if (result>0) {
                //避免超时回调九机
                callBack();
            }
            return result>0 ? Result.success() : Result.error("同步多租户基础参数失败", "同步多租户基础参数失败");
        }
    }

    /**
     * 同步选项参数内容
     * @param paramValueVO
     * @return
     */
    @PostMapping("/paramValue")
    public Result<Boolean> saveParamValue(@RequestBody ParamValueVO paramValueVO) {
        ParamValue existParamValue = paramValueMapper.selectById(paramValueVO.getId());
        if(existParamValue != null){
            return  Result.success();
        } else {
            ParamValue paramValue = ParamValueVO.toSuper(paramValueVO);
            BeanUtils.copyProperties(paramValueVO,paramValue);
            int result = paramValueMapper.insertParamValue(paramValue);
            if (result>0) {
                //避免超时回调九机
                callBack();
            }
            return result>0 ? Result.success() : Result.error("同步多租户选项参数值失败", "同步多租户选项参数值失败");
        }
    }

    /**
     * 同步商品参数关系
     * @param updateProductParamVO
     * @return
     */
    @PostMapping("/productCs")
    public Result<Boolean> saveProductCs(@RequestBody UpdateProductParamVO updateProductParamVO) {
        Result<Boolean> result = productCsService.updateProductOrSkuParam(updateProductParamVO,true);
        if (result != null && result.getCode() == 0) {
            //避免超时回调九机
            callBack();
        }
        return result;
    }

    /**
     * 同步商品图片
     * @param param
     * @return
     */
    @PostMapping("/productImage")
    public Result productImage(@RequestBody ProductImageUpdateParam param){
        if(productImageService.updateProductImage(param,true)){
            callBack();
            return Result.success();
        }else {
            return Result.error("error","修改失败");
        }
    }

    /**
     * 更新同步barCode
     * @param barCodeVo
     * @return
     */
    @PostMapping("/updateBarCode")
    public Result<Boolean> updateBarCode(@RequestBody BarCodeVo barCodeVo) {
        //默认返回成功，采货王这里没有业务逻辑
        return Result.success();
    }
}
package com.jiuji.pick.web.product.controller;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.service.ProductInfoService;
import com.jiuji.pick.service.product.vo.ProductSpecVo;
import com.jiuji.pick.service.product.vo.QueryProduct4AddVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @function:
 * @description: ProductInfoController.java
 * @date: 2021/04/29
 * @author: sunfayun
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/product")
public class ProductInfoController {

    @Resource
    private ProductInfoService productInfoService;

    /**
     * 添加商品查询基础信息
     * @param ppid
     * @return
     */
    @GetMapping("/queryProduct4AddInfo/v1")
    public Result<QueryProduct4AddVo> queryProduct4AddInfo(Long ppid) {
        return productInfoService.queryProductInfo4Add(ppid);
    }

    @GetMapping("/getProductSpecInfo/v1")
    public Result<List<ProductSpecVo>> getProductSpecInfo(Long ppid) {
        return productInfoService.getProductSpecInfo(ppid);
    }
}

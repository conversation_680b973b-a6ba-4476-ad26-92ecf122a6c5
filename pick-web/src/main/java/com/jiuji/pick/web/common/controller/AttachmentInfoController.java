package com.jiuji.pick.web.common.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.service.AttachmentInfoService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @function:
 * @description: AttachmentInfoController.java
 * @date: 2021/05/14
 * @author: sunfayun
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/common")
public class AttachmentInfoController {

    @Resource
    private AttachmentInfoService attachmentInfoService;

    @GetMapping("/deleteTest")
    public Result<String> deleteAttachmentInfo(Long id) {
        LambdaUpdateWrapper<AttachmentInfo> updateWrapper = new LambdaUpdateWrapper<AttachmentInfo>();
        updateWrapper.set(AttachmentInfo::getDelFlag, Boolean.TRUE).eq(AttachmentInfo::getId, id);
        attachmentInfoService.update(updateWrapper);
        return Result.success();
    }

    /***
     * @description: 获取供应商合同模板信息
     * @Param: []
     * @author: Lbj
     * @date: 2021/5/17 10:44
     */
    @GetMapping("/supplierContractTemplate")
    public Result<AttachmentInfo> getSupplierContractTemplate(){
        return Result.success(attachmentInfoService.getSupplierContractTemplate());
    }

}

package com.jiuji.pick.web.user.controller;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.common.utils.BeanCopyUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.NotifyLogInfo;
import com.jiuji.pick.service.common.service.NewOperateLogFactory;
import com.jiuji.pick.service.common.service.NotifyLogInfoService;
import com.jiuji.pick.service.common.vo.PartnerUserParam;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.query.PartnerUserQuery;
import com.jiuji.pick.service.user.service.PartnerUserService;
import com.jiuji.pick.service.user.vo.OAPartnerInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 合作伙伴信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
@Slf4j
@RestController
@RequestMapping("/api/user/partner")
public class PartnerUserController {

    @Resource
    private PartnerUserService partnerUserInfoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private NotifyLogInfoService notifyLogInfoService;
    @Resource
    private NewOperateLogFactory logFactory;

    /**
     * 合作伙伴名称
     */
    private static final String LIST_BY_NAME = "1";
    /**
     * 合作伙伴ID
     */
    private static final String LIST_BY_ID = "2";
    /**
     * 联系电话
     */
    private static final String LIST_BY_PHONE = "3";
    /**
     * 严选负责人
     */
    private static final String LIST_HEAD_PERSON = "4";

    /***
     * @description: 添加
     * @Param: [partnerUserParam]
     * @author: Lbj
     * @date: 2021/4/29 16:46
     */
    @PostMapping("/add/v1")
    public Result save(@RequestBody PartnerUserParam partnerUserParam){
        partnerUserInfoService.saveOrUpdateByXtenantAndCheck(BeanCopyUtil.copy(partnerUserParam, PartnerUser.class));
        return Result.success();
    }

    /***
     * @description: 详情
     * @author: Lbj
     * @date: 2021/4/29 16:46
     */
    @GetMapping("/detail/v1/{id}")
    public Result<PartnerUser> detail(@PathVariable("id") Long id){
        return Result.success(partnerUserInfoService.getById(id));
    }

    /***
     * 该接口的参数修改的时候记得修改pick-service部分相同的参数
     */
    @PostMapping("/update/v1")
    public Result update(@RequestBody PartnerUserParam partnerUserParam){
        PartnerUser partnerUser = BeanCopyUtil.copy(partnerUserParam, PartnerUser.class);
        partnerUser.setXtenant(partnerUserParam.getXtenant());
        partnerUserInfoService.saveOrUpdateByXtenantAndCheck(partnerUser);
        if(partnerUserParam.getParamOld()!=null){
            logFactory.systemSaveLog(partnerUserParam,NewOperateLogInfoTypeEnum.PARTNER_LIST.getCode());
        }
        return Result.success();
    }


    /***
     * @description: 当前[登陆]合作伙伴详情
     * @author: Lbj
     * @date: 2021/4/29 16:46
     */
    @GetMapping("/current/detail/v1")
    public Result<PartnerUser> detailCurrent(){
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if(null == partnerTokenInfo){
            return Result.error("请登录！");
        }
        return Result.success(partnerUserInfoService.getById(partnerTokenInfo.getId()));
    }

    /***
     * @description: 编辑当前[登陆]合作伙伴
     * @Param: [partnerUserParam]
     * @author: Lbj
     * @date: 2021/4/29 16:46
     */
    @PostMapping("/current/update/v1")
    public Result updateCurrent(@RequestBody PartnerUserParam partnerUserParam){
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if(null == partnerTokenInfo){
            return Result.error("请登录！");
        }
        partnerUserParam.setId(partnerTokenInfo.getId());
        PartnerUser partnerUser = BeanCopyUtil.copy(partnerUserParam, PartnerUser.class);
        partnerUser.setXtenant(partnerUserParam.getXtenant());
        partnerUserInfoService.saveOrUpdateByXtenantAndCheck(partnerUser);
        return Result.success();
    }


    /***
     * @description: 启用/禁用
     * @author: Lbj
     * @date: 2021/4/29 16:46
     */
    @PostMapping("/change/status/v1")
    public Result changeStatus(@RequestBody PartnerUser partner){
        if(partner.getStatus()){
            partnerUserInfoService.forbid(partner.getId());
        }else {
            partnerUserInfoService.enable(partner.getId());
        }
        logFactory.systemSaveLog(partner,NewOperateLogInfoTypeEnum.PARTNER_LIST_ORDER.getCode());
        return Result.success();
    }

    /***
     * @description: 合作伙伴列表
     * @Param: [query]
     * @author: Lbj
     * @date: 2021/4/30 11:51
     */
    @PostMapping("/list/page/v1")
    public Result<IPage<PartnerUser>> listPageQuery(@RequestBody PartnerUserQuery query){
        if(StringUtils.isNotEmpty(query.getSearchType()) && StringUtils.isNotEmpty(query.getKeyWord())){
            if(LIST_BY_NAME.equals(query.getSearchType())){
                query.setName(query.getKeyWord());
            }
            if(LIST_BY_ID.equals(query.getSearchType())){
                query.setId(Long.parseLong(query.getKeyWord()));
            }
            if(LIST_BY_PHONE.equals(query.getSearchType())){
                query.setPhone(query.getKeyWord());
            }
            if(LIST_HEAD_PERSON.equals(query.getSearchType())){
                query.setHeadPerson(query.getKeyWord());
            }
        }
        return Result.success(partnerUserInfoService.listPageQuery(query));
    }

    /***
     * @description: 合作伙伴登陆跳转，OA
     * @Param: [partnerUserLoginInfo]
     * @author: Lbj
     * @date: 2021/5/6 9:36
     */
    @GetMapping("/skip/v1")
    public Result<String> skip(@RequestParam("token") String token, @RequestParam("xtenant") String xtenant){
        log.info("OA跳转采货王平台，token:{}, xtenant:{}", token, xtenant);
        return partnerUserInfoService.loginAndSaveOAPartner(token, xtenant);
    }

    /**
     * 跳转v2
     */
    @GetMapping("/skip/v2")
    public Result<String> newSkip(@RequestParam("token") String token, @RequestParam("xtenant") String xtenant,
                                  @RequestParam("loginUserId") Long loginUserId, @RequestParam("loginUserName") String loginUserName){
        if(StringUtils.isEmpty(token)){
            return Result.error("token不能为空");
        }
        if(StringUtils.isEmpty(xtenant)){
            return Result.error("xtenant不能为空");
        }
        if(loginUserId==null){
            return Result.error("loginUserId不能为空");
        }
        if(StringUtils.isEmpty(loginUserName)){
            return Result.error("loginUserName不能为空");
        }
        String param = String.format("OA跳转采货王平台，token:%s, xtenant:%s,loginUserId:%s,loginUserName:%s", token, xtenant, loginUserId, loginUserName);
        NotifyLogInfo notifyLogInfo = new NotifyLogInfo();
        notifyLogInfo.setNotifyName("采货王系统跳转")
                .setXTenant(Long.valueOf(xtenant))
                .setNotifyParam(param)
                .setCreateTime(LocalDateTime.now())
                .setNotifyType(2);
        Result<String> stringResult = partnerUserInfoService.loginAndSavePartnerInfo(token, xtenant, loginUserId, loginUserName);
        notifyLogInfo.setNotifyResult(JSONUtil.toJsonStr(stringResult))
                .setUpdateTime(LocalDateTime.now());
        notifyLogInfoService.saveNotifyLogInfo(notifyLogInfo);
        return stringResult;
    }

    /**
     * 跳转商城，NEO
     * @param token token
     * @param xtenant 域名
     * @return 跳转url
     */
    @GetMapping("/skip4Neo/v1")
    public Result<String> skip4Neo(@RequestParam("token") String token, @RequestParam("xtenant") String xtenant,@RequestParam(value = "oaHost",required = false) String oaHost) {
        if (StringUtils.isEmpty(token) || StringUtils.isEmpty(xtenant)) {
            return Result.error("请求参数不能为空");
        }
        String param = String.format("小型跳转采货王平台，token:%s, xtenant:%s,oaHost:%s", token, xtenant, oaHost);
        NotifyLogInfo notifyLogInfo = new NotifyLogInfo();
        notifyLogInfo.setNotifyName("小型跳转采货王平台")
                .setNotifyParam(param)
                .setCreateTime(LocalDateTime.now())
                .setNotifyType(2);
        Result<String> stringResult = partnerUserInfoService.loginAndSaveNeoPartner(token, xtenant, oaHost);
        PartnerTokenInfo partnerTokenInfoWithoutCheck = Optional.ofNullable(currentRequestComponent.getPartnerTokenInfoWithoutCheck())
                .orElse(new PartnerTokenInfo());
        notifyLogInfo.setNotifyResult(JSONUtil.toJsonStr(stringResult))
                .setXTenant(partnerTokenInfoWithoutCheck.getXtenant())
                .setUpdateTime(LocalDateTime.now());
        notifyLogInfoService.saveNotifyLogInfo(notifyLogInfo);
        log.info("小型跳转采货王平台，token:{}, xtenant:{},oaHost:{}", token, xtenant,oaHost);
        return stringResult;
    }



    /***
     * @description: 退出登陆
     * @author: Lbj
     * @date: 2021/5/6 9:36
     */
    @GetMapping("/logout/v1")
    public Result<String> logout(){
        return partnerUserInfoService.logout(currentRequestComponent.getAuthorization());
    }


    /**
     * 登录信息缓存清除
     * @return
     */
    @GetMapping("/clearLogout/v1")
    public Result<String> clearLogout(@RequestParam Integer type){
        partnerUserInfoService.clearLogout(type);
        return Result.success("登录信息清除成功");
    }



    /***
     * @description: 合作伙伴登陆信息
     * @Param: [loginInfo]
     * @author: Lbj
     * @date: 2021/5/8 11:04
     */
    @GetMapping("/login/info/v1")
    public Result<OAPartnerInfo> loginInfo(){
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if(Objects.isNull(partnerTokenInfo)){
            return Result.error("请登录");
        }
        OAPartnerInfo partnerUser = new OAPartnerInfo();
        partnerUser.setId(partnerTokenInfo.getId());
        partnerUser.setName(partnerTokenInfo.getName());
        partnerUser.setShortName(partnerTokenInfo.getShortName());
        partnerUser.setLoginUserId(partnerTokenInfo.getLoginOAUserId());
        partnerUser.setXtenant(partnerTokenInfo.getXtenant());
        partnerUser.setLoginUserName(partnerTokenInfo.getLoginOAUserName());
        return Result.success(partnerUser);
    }
}


package com.jiuji.pick.web.product.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.annotation.AuthorizationAdmin;
import com.jiuji.pick.common.param.BasePageParam;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.entity.ProductRuleConfig;
import com.jiuji.pick.service.product.param.EditProductWhiteConfigParam;
import com.jiuji.pick.service.product.param.SaveProductWhiteConfigParam;
import com.jiuji.pick.service.product.param.SearchProductWhiteConfigParam;
import com.jiuji.pick.service.product.service.ProductRuleConfigService;
import com.jiuji.pick.service.product.vo.QueryIndexWhiteConfigVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@RestController
@RequestMapping("/productWhiteConfig")
public class ProductRuleConfigController {


    @Resource
    private ProductRuleConfigService ruleConfigService;

    @AuthorizationAdmin
    @PostMapping("/saveProductWhiteConfig/v1")
    public Result saveProductWhiteConfig(@RequestBody @Valid SaveProductWhiteConfigParam param) {
        return ruleConfigService.saveProductWhiteConfig(param);
    }

    @PostMapping("/removeWhiteConfig/v1/{id}")
    public Result<IPage<ProductRuleConfig>> removeWhiteConfig(@PathVariable Long id) {
        return ruleConfigService.removeWhiteConfig(id);
    }

    @PostMapping("/editSaveWhiteConfig/v1")
    public Result<IPage<ProductRuleConfig>> editSaveWhiteConfig(@RequestBody EditProductWhiteConfigParam param) {
        return ruleConfigService.editSaveWhiteConfig(param);
    }

    @PostMapping("/conditionSearch/v1")
    public Result<Page<QueryIndexWhiteConfigVo>> conditionSearch(@RequestBody SearchProductWhiteConfigParam param) {
        return ruleConfigService.conditionSearch(param);
    }

    @GetMapping("/detailWhiteConfig/v1")
    public Result<ProductRuleConfig> detailWhiteConfig(Long id) {
        return ruleConfigService.getProductRuleConfigDetail(id);
    }
}


package com.jiuji.pick.web.product.controller;


import com.ch999.common.util.vo.Result;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.service.product.service.ProductSearchService;
import com.jiuji.pick.service.product.vo.ProductSearchRequestVo;
import com.jiuji.pick.service.product.vo.RecommendSearchVo;
import com.jiuji.pick.service.product.vo.SearchResultVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:zuofei
 * @Description
 * @Date 2020-08-10 商品搜索
 */
@RestController
@RequestMapping("/api/product")
public class ProductSearchApi {

    @Autowired
    private CurrentRequestComponent currentRequestComponent;
    @Autowired
    private ProductSearchService productSearchService;

    /**
     * 商品搜索
     */
    @PostMapping("/search")
    public Result<SearchResultVo> search(@RequestBody ProductSearchRequestVo productSearchRequestVo) {

        Long userId = currentRequestComponent.getPartnerId();

        if (StringUtils.isNotBlank(productSearchRequestVo.getKeyword())) {
            productSearchService.addSearchHistory(userId, productSearchRequestVo.getKeyword());
        }
        SearchResultVo searchResultVo = productSearchService.searchProduct(productSearchRequestVo);
        return Result.success("请求成功", "请求成功", searchResultVo);
    }

    /**
     * 删除指定浏览记录
     * @param param 空-删除该用户全部浏览记录  不为空-删除该用户指定浏览记录
     * @return
     */
    @PostMapping("/deleteHistory")
    public Result deleteHistory(@RequestBody List<String> param) {
        Long userId = currentRequestComponent.getPartnerId();
        if (userId == null) {
            return new Result<>(Result.SUCCESS, "操作成功", "", null);
        }
        // 不传戴表全部删除
        if(CollectionUtils.isNotEmpty(param)){
            productSearchService.deleteHistorySearch(userId.toString(),param);
        }else {
            productSearchService.deleteHistorySearch(userId.toString());
        }
        return Result.success("SUCCESS", "请求成功", null);
    }

    @GetMapping("/recommendSearch")
    public Result getRecommendSearch(@RequestParam String keyword, @RequestParam(required = false,defaultValue = "10") Integer count) {
        List<RecommendSearchVo> list = productSearchService.getRecommendSearch(keyword, count);
        return Result.success(list);
    }


    @GetMapping("/recommendSearch/v1")
    public Result getRecommendSearch(@RequestParam String keyword) {
        List<String> recommendSearch = productSearchService.getRecommendSearch(keyword);
        return Result.success(recommendSearch);
    }

    @GetMapping("/historySearch")
    public Result<List<String>> HistorySearch() {
        // 只有登录后才会返回数据
        Long userId = currentRequestComponent.getPartnerId();
        if (userId == null) {
            return new Result<>(Result.SUCCESS, "操作成功", "", null);
        }
        List<String> historySearchList = productSearchService.getHistorySearch(String.valueOf(userId));
        return new Result<>(Result.SUCCESS, "操作成功", "", historySearchList);
    }


}

package com.jiuji.pick.web.product.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jiuji.pick.service.product.entity.CategoryDiy;
import lombok.Data;

@Data
public class CategoryDiyVo {
    private Integer id;
    private String name;
    private String link;
    private Integer parentId;
    private String fid;
    private Integer sort;
    /**
     * 0--普通商品 1--特卖商品 2--热销榜单
     */
    private Integer productType;
    /**
     * 广告位id
     */
    @TableField("ad_id")
    private String adId;

    private Integer childStyle;

    private Boolean hotProductAutoChange;

    public CategoryDiyVo(CategoryDiy categoryDiy) {
        this.id = categoryDiy.getId();
        this.name = categoryDiy.getName();
        this.link = categoryDiy.getLink();
        this.fid = categoryDiy.getFid();
        this.sort = categoryDiy.getSort();
        this.parentId=categoryDiy.getParentId();
        this.productType=categoryDiy.getProductType();
        this.adId=categoryDiy.getAdId();
        this.childStyle=categoryDiy.getStyle();
        this.hotProductAutoChange=categoryDiy.getHotProductAutoChange();
    }
}

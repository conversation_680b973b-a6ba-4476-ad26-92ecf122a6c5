package com.jiuji.pick.web.product.controller;

import com.alibaba.fastjson.JSON;
import com.ch999.common.util.tenant.Namespaces;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.service.product.service.BrandService;
import com.jiuji.pick.service.product.vo.BrandDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.jiuji.pick.common.utils.ProductSynchronizeUtils.callBack;


/**
 * 品牌同步API
 */
@Slf4j
@RequestMapping("/api/synchronize/brand/data")
@RestController
public class BrandSynchronizeApi {

    @Autowired
    public BrandService brandService;

    @Autowired
    public CurrentRequestComponent currentRequestComponent;


    /**
     * 九机品牌信息同步到其它租户
     * @param brandDetailVO
     * @return
     */
    @PostMapping("/save")
    public Result<Boolean> saveBrand(@RequestBody BrandDetailVO brandDetailVO){
        try {
            Result<Boolean> result = brandService.saveBrand(brandDetailVO, true);
            //如果成功 尝试删除九机主库因为超时等其它原因，再次尝试的数据
            if (result != null && result.getCode() == 0  && result.getData() != null && result.getData()){
                callBack();
            }
            return result;
        }catch (Exception e){
            log.error("分类信息同步保存失败,报文：{},错误信息:{}", JSON.toJSONString(brandDetailVO),e);
            return Result.error(e.getMessage(),"分类信息同步保存失败");
        }
    }

}

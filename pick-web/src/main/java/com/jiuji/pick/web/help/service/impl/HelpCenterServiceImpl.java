package com.jiuji.pick.web.help.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ch999.common.util.secure.Base64Utils;
import com.ch999.common.util.secure.RSAUtils;
import com.ch999.common.util.tenant.Namespaces;
import com.ch999.common.util.tenantconfig.TenantConfigUtil;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.properties.JiuXunAdminProperties;
import com.jiuji.pick.common.utils.HttpClientUtils;
import com.jiuji.pick.web.help.constant.JiuXunAdminConstant;
import com.jiuji.pick.web.help.service.HelpCenterService;
import com.jiuji.pick.web.help.vo.HelpCenterShowVO;
import com.jiuji.pick.web.help.vo.HelpSearchEsVO;
import com.jiuji.pick.web.help.vo.HelpUserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-09-30 16:25
 */

@Slf4j
@Service
public class HelpCenterServiceImpl implements HelpCenterService {


    private final CurrentRequestComponent currentRequestComponent;
    private final JiuXunAdminProperties jiuXunAdminProperties;
    private final ApplicationContext applicationContext;

    public HelpCenterServiceImpl(CurrentRequestComponent currentRequestComponent, JiuXunAdminProperties jiuXunAdminProperties, ApplicationContext applicationContext) {
        this.currentRequestComponent = currentRequestComponent;
        this.jiuXunAdminProperties = jiuXunAdminProperties;
        this.applicationContext = applicationContext;
    }


    @Override
    public String rsa() {
        /* 优先取员工ID */
        Integer userId = currentRequestComponent.getStaffUserId();
        Long tempUserId = null;
        String userName = currentRequestComponent.getStaffName();
        if (Objects.isNull(userId)) {
            /* 再尝试合作伙伴ID */
            tempUserId = currentRequestComponent.getPartnerId();
            if (Objects.isNull(tempUserId)) {
                /* 再尝试提供商 */
                SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
                if (Objects.nonNull(supplierTokenInfo)) {
                    tempUserId = supplierTokenInfo.getId();
                    userName = supplierTokenInfo.getLoginName();
                }
            } else {
                userName = currentRequestComponent.getPartnerTokenInfoWithoutCheck().getLoginOAUserName();
            }
        }

        if (Objects.isNull(tempUserId) && Objects.isNull(userId)) {
            throw new BizException("rsa加密串，获取用户信息失败");
        }

        userId = Objects.isNull(tempUserId) ? userId : tempUserId.intValue();
        HelpUserVO userVO = new HelpUserVO();
        userVO.setUserId(userId);
        userVO.setUserName(userName);
        userVO.setTenantOAHost(TenantConfigUtil.getWebmLong());
        userVO.setTenantId(String.valueOf(Namespaces.get()));
        userVO.setTenantType("1");

        String oaKey;
        try {

            oaKey = EncryptionBase64Util.encrypt(jiuXunAdminProperties.getPublicKey(), JSON.toJSONString(userVO));
//            byte[] decryptByPrivateKey = RSAUtils.encryptByPublicKey(JSON.toJSONString(userVO).getBytes(StandardCharsets.UTF_8), jiuXunAdminProperties.getPublicKey());
//            oaKey = Base64Utils.enCodeByte(decryptByPrivateKey);
//            oaKey = URLEncoder.encode(oaKey, "UTF-8");
        } catch (Exception e) {
            String error = "RSA加密失败";
            log.error(error, e);
            throw new BizException(error, e);
        }

        return oaKey;
    }

    @Override
    public HelpCenterShowVO relation(String systemType, String pageName) {
        String requestUrl;
        String url = String.format("/jiuxunadmin/jiuxun/helpCenter/find/relation/v1?systemType=%s&pageName=%s&t=%s",
                systemType, pageName, System.nanoTime());
        requestUrl = (isDev() ? JiuXunAdminConstant.TEST_DOMAIN : JiuXunAdminConstant.DOMAIN) + url;

        String result;
        result = HttpClientUtils.get(requestUrl);

        return JSON.parseObject(JSON.toJSONString(JSON.parseObject(result, Result.class).getData()), HelpCenterShowVO.class);
    }

    @Override
    public List<HelpSearchEsVO> search(String systemType, String key) {
        String requestUrl;
        String url = String.format("/jiuxunadmin/jiuxun/helpCenter/search/adm/v1?systemType=%s&keyword=%s&t=%s",
                systemType, key, System.nanoTime());
        requestUrl = (isDev() ? JiuXunAdminConstant.TEST_DOMAIN : JiuXunAdminConstant.DOMAIN) + url;

        String result;
        result = HttpClientUtils.get(requestUrl);

        return JSON.parseArray(JSON.toJSONString(JSON.parseObject(result, Result.class).getData()), HelpSearchEsVO.class);
    }

    /**
     * 判断是否dev环境
     *
     * @return bool
     */
    public boolean isDev() {
        return "test".equals(applicationContext.getEnvironment().getActiveProfiles()[0]);
    }
}

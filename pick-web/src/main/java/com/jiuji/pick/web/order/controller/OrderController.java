package com.jiuji.pick.web.order.controller;

import com.jiuji.pick.common.annotation.RepeatSubmitCheck;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.param.CartOrderParam;
import com.jiuji.pick.service.order.service.CartInfoService;
import com.jiuji.pick.service.order.service.OrderService;
import com.jiuji.pick.service.order.vo.ConfirmOrderInfoVO;
import com.jiuji.pick.service.order.vo.SalesReportFormSearchReq;
import com.jiuji.pick.service.order.vo.UpdateOrderPriceVo;
import com.jiuji.tc.common.vo.R;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 * 订单操作 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-06
 */
@RestController
@RequestMapping("/api/order")
public class OrderController {

    @Resource
    private OrderService orderService;
    @Resource
    private CartInfoService cartInfoService;




    @GetMapping("/getAllDomainsByOrder/v1")
    public Result<String> getAllDomainsByOrder(Long orderNumber){
        return orderService.getAllDomainsByOrder(orderNumber);
    }

    /**
     * 获取订单确认页信息
     *
     * @param cartIds 购物车id集合
     * @return
     */
    @GetMapping("/confirmOrder/cart/v1")
    public Result<ConfirmOrderInfoVO> confirmOrderByCartIds(String cartIds) {
        // 获取购物车里的数据
        List<Long> cartIdList = CommonUtil.covertIdStr2Long(cartIds);
        if (CollectionUtils.isEmpty(cartIdList)) {
            return Result.noData();
        }
        return cartInfoService.confirmOrderByCartIds(cartIdList);
    }

    /**
     * 提交订单
     *
     * @param cartOrderParam param
     * @return
     */
    @RepeatSubmitCheck
    @PostMapping("/submitOrder/cart/v1")
    public Result submitOrderByCart(@RequestBody @Valid CartOrderParam cartOrderParam) {
        List<CartOrderParam.CartOrderData> cartOrderDataList = cartOrderParam.getCartOrderDataList();
        if (CollectionUtils.isEmpty(cartOrderDataList)) {
            return Result.error("请选择要下单的商品");
        }
        for (CartOrderParam.CartOrderData cartOrderData : cartOrderDataList) {
            if (cartOrderData.getCartId() == null || cartOrderData.getProductCount() <= 0) {
                return Result.error("要下单的商品有问题，请核实");
            }
        }
        return orderService.submitOrderByCart(cartOrderParam);
    }


    /**
     * 订单价格修改
     * @return
     */
    @PostMapping("/updateOrderPrice/v1")
    public Result updateOrderPrice(@RequestBody UpdateOrderPriceVo updateOrderPriceVoList) {
        if(updateOrderPriceVoList==null){
            return Result.error("改价信息不能为空");
        }
        return orderService.updateOrderPrice(updateOrderPriceVoList);
    }

    /**
     * 用户订单收货
     * @param orderIdList
     * @return
     */
    @PostMapping("/orderReceive4User/v1")
    public Result<String> orderReceive4User(@RequestBody List<Long> orderIdList) {
        if(CollectionUtils.isEmpty(orderIdList)) {
            return Result.errorInfo("收货订单不能为空");
        }
        return orderService.orderReceive4User(orderIdList);
    }

    /**
     * 系统自动收货
     * @return
     */
    @GetMapping("/orderReceive4System/v1")
    public Result<String> orderReceive4System() {
        CompletableFuture.runAsync(() -> orderService.orderReceive4System());
        return Result.success();
    }


    /**
     * 系统自动收货
     * @return
     */
    @GetMapping("/automaticPickup/v1")
    public Result<String> automaticPickup() {
        String result = orderService.automaticPickup();
        return Result.success(result);
    }
    /**
     * 通过订单号获取严选单号
     * @param saleNo
     * @return
     */
    @GetMapping("/getOrderIdBySaleNo/v1")
    public Result<Long> getOrderIdBySaleNo(Integer saleNo){
        return orderService.getOrderIdBySaleNo(saleNo);
    }

    /***
     * @description: 后台站内信列表
     * @Param: []
     * @date: 2021/5/28 11:47
     */
    @PostMapping("/sales-report-form/v1")
    public R getSalesReportFormPageList(@RequestBody SalesReportFormSearchReq query){
        return R.success( orderService.getSalesReportFormPageList(query));
    }
}

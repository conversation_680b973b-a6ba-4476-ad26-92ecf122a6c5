package com.jiuji.pick.web.common.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.param.*;
import com.jiuji.pick.service.common.service.MessageInfoService;
import com.jiuji.pick.service.common.vo.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-08
 */
@RestController
@RequestMapping("/api/common")
public class MessageInfoController {

    @Resource
    private MessageInfoService messageInfoService;

    @GetMapping("/noticeSign/v1")
    public Result<QueryMessageStatusVo> noticeSign(){
        QueryMessageStatusVo resultInfoSign = messageInfoService.noticeSign();
        return Result.success(resultInfoSign);
    }

    /***
     * @description: 保存
     * @Param: [param]
     * @date: 2021/5/28 10:19
     */
    @PostMapping("/saveMessageInfo/v1")
    public Result saveMessageInfo(@RequestBody  MessageInfoSaveParam param){
        messageInfoService.saveMessageInfo(param);
        return Result.success("保存成功");
    }

    /***
     * @description: 详情
     * @Param: [id]
     * @date: 2021/5/28 10:19
     */
    @GetMapping("/queryMessageDetails/v1")
    public Result<QueryMessageInfoDetailsVo> queryMessageDetails(@RequestParam("id") Long id){
        return messageInfoService.queryMessageDetails(id);
    }

    /**
     * 查询用户消息
     * @param param
     * @return
     */
    @PostMapping("/queryUserMessageInfo/v1")
    public Result<Page<UserMessageInfoVo>> queryUserMessageInfo(@RequestBody @Valid QueryUserMessageParam param) {
        return messageInfoService.queryUserMessageInfo(param);
    }

    /**
     * 查询未读消息记录条数
     * @param type
     * @return
     */
    @GetMapping("/queryUnreadMessageCount/v1/{type}")
    public Result<UserCountInfoReq> getUnreadMessageCount(@PathVariable(name = "type") Integer type) {
        return messageInfoService.getUnreadMessageCount(type);
    }

    /**
     * 标记消息已读、全部已读
     * @param param
     * @return
     */
    @PostMapping("/userMessageDeal/v1")
    public Result<String> userMessageDeal(@RequestBody @Valid UserMessageDealParam param) {
        return messageInfoService.userMessageDeal(param);
    }

    /***
     * @description: 后台站内信列表
     * @Param: []
     * @date: 2021/5/28 11:47
     */
    @PostMapping("/admin/list/v1")
    public Result<Page<MessageInfoVO>> pageList(@RequestBody MessageInfoQuery query){

        return Result.success(messageInfoService.pageList(query));
    }
}


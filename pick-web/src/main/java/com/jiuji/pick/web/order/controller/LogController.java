package com.jiuji.pick.web.order.controller;


import com.jiuji.pick.common.enums.EnumV0;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.common.vo.SaveLogVo;
import com.jiuji.pick.service.common.vo.SelectLogVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/LogController")
public class LogController {

    @Resource
    private NewOperateLogInfoService logInfoService;

    /**
     * 操作日志保存
     * @param saveLogVo
     * @return
     */
    @PostMapping("/saveLog/v1")
    public Result saveLog(@RequestBody @Valid SaveLogVo saveLogVo) {
        logInfoService.saveLog(saveLogVo);
        return Result.success();
    }

    /**
     * 操作日志枚举类型
     * @return
     */
    @GetMapping("/getLogEnum/v1")
    public Result<Map<String, List<EnumV0>>> getLogEnum() {
        HashMap<String, List<EnumV0>> map = new HashMap<>(2);
        map.put("NewOperateLogInfoTypeEnum", NewOperateLogInfoTypeEnum.getList());
        map.put("LogShowTypeEnum", LogShowTypeEnum.getList());
        return Result.success(map);
    }

    /**
     * 操作日志查询
     * @param saveLogVo
     * @return
     */
    @PostMapping("/selectLog/v1")
    public Result<List<NewOperateLogInfo>> selectLog(@RequestBody @Valid SelectLogVo saveLogVo) {
       return logInfoService.selectLog(saveLogVo);
    }


}

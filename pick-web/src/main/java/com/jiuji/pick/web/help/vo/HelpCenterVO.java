package com.jiuji.pick.web.help.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description HelpCenterVO
 * @since 2021-01-26 16:00
 */
@Data
public class HelpCenterVO {

    private Integer id;

    /**
     * 目录名称
     */
    private String folderName;

    /**
     * 父目录ID
     */
    private Integer parentId;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 编辑时间提示
     */
    private String editTips;

    /**
     * 修改时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 修改人ID
     */
    private Integer updateUserId;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 文章内容字数
     */
    private Integer wordCount;

    /**
     * 关联的菜单
     */
    private String privileges;

    /**
     * 类型：document文档、video视频、directory目录
     */
    private String type;

    /**
     * 简介
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 视频封面
     */
    private String videoPoster;

    /**
     * 资源前缀
     */
    private String imgPrefix;

    /**
     * 投放平台 -1 全部 0 九机 1 中型 2 大型
     * 多个逗号分隔
     */
    private String putPlatform;
}

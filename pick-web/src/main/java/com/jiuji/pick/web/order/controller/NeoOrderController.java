package com.jiuji.pick.web.order.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.NotifyLogInfo;
import com.jiuji.pick.service.common.service.NotifyLogInfoService;
import com.jiuji.pick.service.order.param.NeoOrderParam;
import com.jiuji.pick.service.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description neo订单控制器
 * <AUTHOR>
 * @Date 2021/11/2
 */
@RestController
@RequestMapping("/api/order/neo")
@Slf4j
public class NeoOrderController {

    @Resource
    private OrderService orderService;
    @Resource
    private NotifyLogInfoService notifyLogInfoService;

    /**
     * neo取消订单，同步状态
     */
    @PostMapping("/cancel/v1")
    public Result<Boolean> neoOrderCancel(@RequestBody NeoOrderParam param) {
        log.info("Neo调用采货王取消订单，param:{}", JSONUtil.toJsonStr(param));
        if (ObjectUtil.isNull(param.getOrderNo()) || ObjectUtil.isNull(param.getXtenant())) {
            return Result.errorInfo("请求参数不能为空");
        }
        Result<Boolean> result = null;
        try {
            result = orderService.neoCancelOrder(param.getOrderNo(), param.getXtenant());
            return result;
        } catch (Exception e) {
            return Result.errorInfo("NEO订单取消处理异常");
        } finally {
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(param.getXtenant()).orderNo(param.getOrderNo()).notifyType(2)
                    .notifyName("NEO通知取消订单").notifyParam(JSONUtil.toJsonStr(param)).notifyResult(JSONUtil.toJsonStr(result)).build());
        }
    }

    /**
     * neo入库订单，同步状态
     */
    @PostMapping("/finish/v1")
    public Result<Boolean> neoOrderFinish(@RequestBody NeoOrderParam param) {
        log.info("NEO调用采货王订单入库，param:{}", JSONUtil.toJsonStr(param));
        if (ObjectUtil.isNull(param.getOrderNo()) || ObjectUtil.isNull(param.getXtenant())) {
            return Result.errorInfo("采购单号和租户ID不能为空");
        }
        if (ObjectUtil.isNull(param.getInStockCount())) {
            return Result.errorInfo("已入库数量不能为空");
        }
        Result<Boolean> result = null;
        try {
            result = orderService.neoOrderFinish(param.getOrderNo(), param.getXtenant(), param.getInStockCount());
            return result;
        } catch (Exception e) {
            log.error("NEO调用订单入库处理异常，param:{}, exception:", JSONUtil.toJsonStr(param), e);
            return Result.errorInfo("NEO订单入库处理异常");
        } finally {
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(param.getXtenant()).orderNo(param.getOrderNo()).notifyType(2)
                    .notifyName("NEO通知订单完成").notifyParam(JSONUtil.toJsonStr(param)).notifyResult(JSONUtil.toJsonStr(result)).build());
        }
    }

}

package com.jiuji.pick.web.product.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.entity.CommodityAssociation;
import com.jiuji.pick.service.product.service.CommodityAssociationService;
import com.jiuji.pick.service.product.vo.AssociationTerms;
import com.jiuji.pick.service.product.vo.AssociationVo;
import com.jiuji.pick.service.product.vo.DeleteInfoVo;
import com.jiuji.pick.service.product.vo.PageInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */

@Slf4j
@RequestMapping("/api/commodityAssociation")
@RestController
public class CommodityAssociationController {


    @Resource
    private CommodityAssociationService commodityAssociationService;


    /**
     * 新增或者修改商品版本关联
     *
     * @param associationVo
     * @return
     */
    @PostMapping("/saveOrModify/v1")
    public Result<String> saveOrModify(@RequestBody @Valid AssociationVo associationVo) {
        return commodityAssociationService.saveOrModify(associationVo);
    }


    /**
     * 编辑查询
     *
     * @param id
     * @return
     */
    @GetMapping("/getInfoById")
    public Result<AssociationVo> getInfoById(@RequestParam(value = "id") Long id) {
        if (id == null) {
            throw new BizException("查询id不能为空");
        }
        CommodityAssociation commodityAssociation = commodityAssociationService.getById(id);
        AssociationVo associationVo = new AssociationVo();
        if (commodityAssociation != null) {
            BeanUtils.copyProperties(commodityAssociation, associationVo);
        }
        return Result.success(associationVo);
    }

    /**
     * 删除信息
     *
     * @param deleteInfoVo
     * @return
     */
    @PostMapping("/deleteInfo/v1")
    public Result<String> deleteInfo(@RequestBody DeleteInfoVo deleteInfoVo) {
        if (CollectionUtils.isEmpty(deleteInfoVo.getAssociationIdList()) && CollectionUtils.isEmpty(deleteInfoVo.getIdList())) {
            throw new BizException("删除关联标识为空");
        }
        return commodityAssociationService.deleteInfo(deleteInfoVo);
    }

    /**
     * 分页查询
     * @param pageInfoVo
     * @return
     */
    @PostMapping("/pageInfo/v1")
    public Result<IPage<CommodityAssociation>> pageInfo(@RequestBody PageInfoVo pageInfoVo) {
        return commodityAssociationService.pageInfo(pageInfoVo);
    }


    /**
     * 联想词搜索
     * @param key
     * @return
     */
    @GetMapping("/getAssociationTerms/v1")
    public Result<List<AssociationTerms>>getAssociationTerms(@RequestParam(name = "key") String key){
        return commodityAssociationService.getAssociationTerms(key);
    }


    /**
     * 联想词搜索
     * @param key
     * @return
     */
    @GetMapping("/getAssociationTerms/v2")
    public Result<List<AssociationTerms>>getAssociationTermsV2(@RequestParam(name = "key") String key){
        return commodityAssociationService.getAssociationTermsV2(key);
    }

}

package com.jiuji.pick.web.user.controller;


import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.user.service.FavoriteService;
import com.jiuji.pick.service.user.vo.FavoriteVO;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 收藏夹 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
@RestController
@RequestMapping("/api/user/favorite")
public class FavoriteController {


    @Resource
    private FavoriteService favoriteService;

    @PostMapping("/add/v1")
    public Result<Boolean> add(@RequestParam Integer supplierProductId) {
        return favoriteService.add(supplierProductId);
    }

    @PostMapping("/del/v1")
    public Result<Boolean> del(@RequestParam String ids) {
        return favoriteService.del(ids);
    }

    @GetMapping("/list/v1")
    public Result<FavoriteVO> getList(){
        return favoriteService.getList();
    }

}


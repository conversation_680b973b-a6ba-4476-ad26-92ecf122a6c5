package com.jiuji.pick.web.common.controller;


import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.TopicConfig;
import com.jiuji.pick.service.common.service.TopicConfigService;
import com.jiuji.pick.service.common.vo.TopicConfigVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 专题配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@RestController
@RequestMapping("/adm/common/topicConfig")
public class TopicConfigController {

    @Resource
    private TopicConfigService topicConfigService;

    @GetMapping("/list/v1")
    public Result<List<TopicConfigVO>> list(@RequestParam(value = "enabled", required = false) Boolean enabled, @RequestParam(value = "name", required = false) String name) {
        return topicConfigService.getList(enabled,name);
    }

    @PostMapping("/edit/v1")
    public Result<Boolean> edit(@RequestBody TopicConfig config) {
        return topicConfigService.edit(config);
    }

    @PostMapping("/del/v1")
    public Result<Boolean> del(@RequestParam Integer id) {
        return topicConfigService.del(id);
    }

    @PostMapping("/enabled/v1")
    public Result<Boolean> enabled(@RequestParam Integer id){
        return topicConfigService.enabled(id);
    }

    @GetMapping("/get/v1")
    public Result<TopicConfig> get(@RequestParam Integer id){
        return Result.success(topicConfigService.getById(id));
    }

}


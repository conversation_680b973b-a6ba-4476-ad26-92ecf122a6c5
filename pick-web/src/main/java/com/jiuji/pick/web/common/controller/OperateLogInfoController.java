package com.jiuji.pick.web.common.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.annotation.AuthorizationAdmin;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.param.ConditionalQueryLogInfoParam;
import com.jiuji.pick.service.common.service.OperateLogInfoService;
import com.jiuji.pick.service.common.vo.QueryOperateLogVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
@RestController
@RequestMapping("/api/common")
public class OperateLogInfoController {

    @Resource
    private OperateLogInfoService operateLogInfoService;

    @PostMapping("/queryAdmLogInfo/v1")
    @AuthorizationAdmin
    public Result<Page<QueryOperateLogVo>> queryAdmLogInfo(@RequestBody @Valid ConditionalQueryLogInfoParam param) {
        return operateLogInfoService.queryAdmLogInfo(param);
    }

    @PostMapping("/querySupplierLogInfo/v1")
    public Result<Page<QueryOperateLogVo>> querySupplierLogInfo(@RequestBody @Valid ConditionalQueryLogInfoParam param) {
        return operateLogInfoService.querySupplierLogInfo(param);
    }

}


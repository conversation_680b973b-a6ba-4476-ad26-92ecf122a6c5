package com.jiuji.pick.web.user.controller;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.user.service.SupplierChannelService;
import com.jiuji.pick.service.user.vo.SupplierChannelVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description：
 * @date ：2021/5/19 11:55
 */
@RestController
@RequestMapping("/api/user/supplier/channel")
public class SupplierChannelController {

    @Resource
    private SupplierChannelService supplierChannelService;

    /***
     * @description: 获取供应商下的渠道列表
     * @Param: [supplierId]
     * @author: Lbj
     * @date: 2021/5/19 14:15
     */
    @GetMapping("/channel/list/v1")
    public Result<List<SupplierChannelVO>> listBySupplierId(@RequestParam("supplierId") Long supplierId){
        return Result.success(supplierChannelService.listBySupplierId(supplierId));
    }
}

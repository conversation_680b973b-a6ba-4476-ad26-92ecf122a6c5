package com.jiuji.pick.web.common.controller;

import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.EnumV0;
import com.jiuji.pick.common.enums.KeyTypeEnum;
import com.jiuji.pick.common.enums.OrderPriceTypeEnum;
import com.jiuji.pick.common.utils.EnumUtil;
import com.jiuji.pick.common.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description：
 * @date ：2021/5/12 14:53
 */
@Slf4j
@RestController
@RequestMapping("/api/common/enum")
public class EnumController {

    @Resource
    private CurrentRequestComponent currentRequestComponent;

    /***
     * @description: 根据枚举类名字 获取枚举信息
     * @Param: [className]
     * @author: Lbj
     * @date: 2021/5/12 14:55
     */
    @GetMapping("/one")
    public Result getEnumByClassName(@RequestParam("className") String className) throws Exception{
        return Result.success(EnumUtil.getEnumByClassName(className));
    }

    /***
     * @description: 根据枚举类名字 获取枚举信息
     * @Param: [className]
     * @author: Lbj
     * @date: 2021/5/12 14:55
     */
    @PostMapping("/many")
    public Result getEnumByClassName(@RequestBody List<String> classNameList) throws Exception{
        return Result.success(EnumUtil.getEnumByClassNameList(classNameList));
    }


    @GetMapping("/getEnum")
    public Result<Map<String, List<EnumV0>>> getEnum(){
        HashMap<String, List<EnumV0>> map = new HashMap<>(16);
        SupplierTokenInfo supplierTokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck())
                .orElse(new SupplierTokenInfo());
        Long id = supplierTokenInfo.getId();
        Map<String, List<EnumV0>> mapEnum = KeyTypeEnum.getMapEnum(id);
        for (Map.Entry<String, List<EnumV0>> item :mapEnum.entrySet()) {
            map.put(item.getKey(), item.getValue());
        }
        List<EnumV0> orderPriceTypeEnum = OrderPriceTypeEnum.getList();
        map.put("orderPriceTypeEnum", orderPriceTypeEnum);
        return Result.success(map);
    }
}


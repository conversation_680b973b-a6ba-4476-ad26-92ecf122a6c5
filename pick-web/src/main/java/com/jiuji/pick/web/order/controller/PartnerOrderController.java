package com.jiuji.pick.web.order.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.annotation.RepeatSubmitCheck;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.param.OrderOperatingParam;
import com.jiuji.pick.service.order.param.OrderSearchParam;
import com.jiuji.pick.service.order.service.OrderGetInfoService;
import com.jiuji.pick.service.order.service.OrderService;
import com.jiuji.pick.service.order.vo.OrderAreaInfoVO;
import com.jiuji.pick.service.order.vo.OrderDetailVO;
import com.jiuji.pick.service.order.vo.OrderListVO;
import com.jiuji.pick.service.order.vo.VendorCancelReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 合作伙伴订单操作 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@RestController
@RequestMapping("/api/order/partner")
public class PartnerOrderController {

    @Resource
    private OrderService orderService;
    @Resource
    private OrderGetInfoService orderGetInfoService;

    /**
     * 获取所有门店信息
     *
     * @return
     */
    @GetMapping("/getAreaInfo/v1")
    public Result<List<OrderAreaInfoVO>> getAreaInfo() {
        return orderService.getAreaInfo();
    }

    /**
     * 取消订单
     *
     * @param param param
     * @return
     */
    @PostMapping("/cancel/v1")
    public Result cancelOrder(@RequestBody OrderOperatingParam param) {
        if (param.getOrderId() == null) {
            return Result.error("请选择要取消的订单");
        }
        return orderService.cancelOrder(param.getOrderId());
    }


    /**
     * 供应商取消
     * @param param
     * @return
     */
    @PostMapping("/vendorCancel/v1")
    public Result vendorCancel(@RequestBody VendorCancelReq param) {
        Long orderId = param.getOrderId();
        if(ObjectUtil.isNull(orderId)){
            return Result.error("订单id不能为空");
        }
        String vendorCancelReason = param.getVendorCancelReason();
        if(StringUtils.isEmpty(vendorCancelReason) || vendorCancelReason.length() > 100){
            return Result.error("取消原因不能为空且长度不能超过100");
        }
        return orderService.vendorCancel(param);
    }
    /**
     * 再次购买
     *
     * @param param param
     * @return
     */
    @RepeatSubmitCheck
    @PostMapping("/reBuy/v1")
    public Result<String> reBuy(@RequestBody OrderOperatingParam param) {
        if (param.getOrderId() == null) {
            return Result.error("请选择订单");
        }
        return orderService.reBuy(param.getOrderId());
    }

    /**
     * 合作伙伴订单列表
     *
     * @param param param
     * @return
     */
    @PostMapping("/list/v1")
    public Result<Page<OrderListVO>> list(@RequestBody OrderSearchParam param) {
        if (param.getCurrent() == null || param.getCurrent() < 1) {
            param.setCurrent(1);
        }
        if (param.getSize() == null || param.getSize() > MagicalValueConstant.INT_50) {
            param.setSize(MagicalValueConstant.INT_50);
        }
        if (StringUtils.isNotBlank(param.getStartTimeStr())) {
            LocalDateTime startTime = DateUtil.stringParseLocalDateTime(param.getStartTimeStr());
            param.setStartTime(startTime);
        }
        if (StringUtils.isNotBlank(param.getEndTimeStr())) {
            LocalDateTime endTime = DateUtil.stringParseLocalDateTime(param.getEndTimeStr());
            param.setEndTime(endTime);
        }
        return orderGetInfoService.getPartnerOrderList(param);
    }

    /**
     * 合作伙伴订单详情
     *
     * @param orderId 订单id
     * @return
     */
    @GetMapping("/detail/v1")
    public Result<OrderDetailVO> detail(Long orderId) {
        if (orderId == null) {
            return Result.error("请选择订单");
        }
        return orderGetInfoService.getPartnerOrderDetail(orderId);
    }


    /**
     * 合作伙伴订单详情
     *
     * @param orderId 订单id
     * @return
     */
    @PostMapping("/findProductStock/v1")
    public Result<OrderDetailVO> findProductStock(Long orderId) {
        if (orderId == null) {
            return Result.error("请选择订单");
        }
        return orderGetInfoService.getPartnerOrderDetail(orderId);
    }

}

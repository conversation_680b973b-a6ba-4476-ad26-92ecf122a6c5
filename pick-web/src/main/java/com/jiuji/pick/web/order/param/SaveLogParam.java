package com.jiuji.pick.web.order.param;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SaveLogParam {

    @NotNull(message = "订单id不能为空")
    private Long orderId;

    @NotNull(message = "操作内容不能为空")
    @Length(max = 100, message = "操作日志不能超过100字")
    private String content;
}

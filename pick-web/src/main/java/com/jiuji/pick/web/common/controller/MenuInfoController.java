package com.jiuji.pick.web.common.controller;


import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.service.MenuInfoService;
import com.jiuji.pick.service.common.vo.MenuInfoVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 菜单信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
@RestController
@RequestMapping("/api/common")
public class MenuInfoController {

    @Resource
    private MenuInfoService menuInfoService;

    @GetMapping("/getMenu/{type}")
    public Result<List<MenuInfoVo>> getMenuInfo(@PathVariable("type") Integer type) {
        return menuInfoService.getMenuInfo(type);
    }


    @GetMapping("/buildMenuInfoVoList")
    public Result<List<MenuInfoVo>> buildMenuInfoVoList(@RequestParam("key") String  key) {
        return Result.success(menuInfoService.buildMenuInfoVoList(key));
    }

}


package com.jiuji.pick.web.product.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.entity.SupplierProductDetail;
import com.jiuji.pick.service.product.param.QuerySupplierApplyProductParam;
import com.jiuji.pick.service.product.param.QuerySupplierProductDetailParam;
import com.jiuji.pick.service.product.param.QuerySupplierProductListParam;
import com.jiuji.pick.service.product.param.SupplierAddOrUpdateProductParam;
import com.jiuji.pick.service.product.service.SupplierProductDetailService;
import com.jiuji.pick.service.product.vo.QuerySupplierApplyProductListVo;
import com.jiuji.pick.service.product.vo.QuerySupplierProductListVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@RestController
@RequestMapping("/api/product")
public class SupplierProductDetailController {

    @Resource
    private SupplierProductDetailService supplierProductDetailService;



    @PostMapping("/supplier/addOrUpdateProduct/v1")
    public Result<String> addOrUpdateSupplierProduct(@RequestBody @Valid SupplierAddOrUpdateProductParam param) {
        CommonUtil.checkPrice(param.getBuyNoTaxPrice(),param.getBuyTaxPrice());
        return supplierProductDetailService.addOrUpdateSupplierProduct(param);
    }

    @PostMapping("/supplier/getProductList/v1")
    public Result<Page<QuerySupplierProductListVo>> querySupplierProductList(@RequestBody @Valid QuerySupplierProductListParam param) {
        return supplierProductDetailService.querySupplierProductList(param);
    }

    @PostMapping("/supplier/getApplyProductList/v1")
    public Result<Page<QuerySupplierApplyProductListVo>> querySupplierApplyProductList(@RequestBody @Valid QuerySupplierApplyProductParam param) {
        return supplierProductDetailService.querySupplierApplyProductList(param);
    }

    @PostMapping("/supplier/getProductDetail/v1")
    public Result<SupplierProductDetail> querySupplierProductDetail(@RequestBody @Valid QuerySupplierProductDetailParam param) {
        return supplierProductDetailService.querySupplierProductDetail(param);
    }

    @GetMapping("/syncSaleAndStockCount/v1")
    public Result<Boolean> syncSaleAndStockCount() {
        CompletableFuture.runAsync(() -> supplierProductDetailService.syncStockAndSaleCount());
        return Result.success();
    }
}


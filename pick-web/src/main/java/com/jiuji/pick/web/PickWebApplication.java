package com.jiuji.pick.web;

import com.jiuji.infra.lmstfy.anotation.EnableLmstfy;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableLmstfy
@Configuration
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.jiuji.pick", "com.jiuji.cloud.office.service","com.jiuji.oa.office","com.jiuji.cloud.stock.service"})
@EnableTransactionManagement
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@SpringBootApplication(scanBasePackages = {"com.jiuji.pick", "com.jiuji.fileservice.server.sdk", "cn.hutool.extra.spring"})
public class PickWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(PickWebApplication.class, args);
    }

    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}") String applicationName) {
        return registry -> registry.config().commonTags("application", applicationName);
    }

}

package com.jiuji.pick.web.help.controller;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.web.help.service.HelpCenterService;
import com.jiuji.pick.web.help.vo.HelpCenterShowVO;
import com.jiuji.pick.web.help.vo.HelpSearchEsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 帮助中心controller
 *
 * <AUTHOR>
 * @date 2021-09-30 16:24
 */

@Slf4j
@RestController
@RequestMapping("/api/help_center")
public class HelpCenterController {

    private final HelpCenterService helpCenterService;

    public HelpCenterController(HelpCenterService helpCenterService) {
        this.helpCenterService = helpCenterService;
    }

    /**
     * 获取加密串用于跳转九讯云
     *
     * @return rsa加密串
     */
    @GetMapping("/rsa")
    public Result<String> rsa() {
        return Result.success(helpCenterService.rsa());
    }

    /**
     * 根据页面获取帮助文档
     *
     * @return Result<PageContentVO>
     */
    @GetMapping("/find/relation/v1")
    public Result<HelpCenterShowVO> relation(String systemType, String pageName) {
        return Result.success(helpCenterService.relation(systemType, pageName));
    }

    /**
     * 搜索帮助文档
     *
     * @return Result<PageContentVO>
     */
    @GetMapping("/search/v1")
    public Result<List<HelpSearchEsVO>> search(String systemType, String key) {
        return Result.success(helpCenterService.search(systemType, key));
    }

}

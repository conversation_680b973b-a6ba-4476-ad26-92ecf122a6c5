package com.jiuji.pick.web.common.controller;

import com.jiuji.pick.common.vo.Result;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @function:
 * @description: CacheDealController.java
 * @date: 2021/10/08
 * @author: sunfayun
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/common/cache")
public class CacheDealController {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 删除门店缓存
     * @param xTenant
     * @return
     */
    @GetMapping("/delAreaCache/v1")
    public Result<String> delAreaCache(Long xTenant) {
        String cacheKey = "oa:area:info:" + xTenant;
        Boolean result = stringRedisTemplate.delete(cacheKey);
        if(result == null || !result) {
            return Result.errorInfo("删除失败");
        }
        return Result.successInfo("删除成功");
    }
}

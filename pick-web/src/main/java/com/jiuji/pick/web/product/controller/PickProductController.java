package com.jiuji.pick.web.product.controller;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.annotation.AuthorizationAdmin;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.bo.AddOrUpdateProductParam;
import com.jiuji.pick.service.product.entity.SupplierProductDetail;
import com.jiuji.pick.service.product.param.*;
import com.jiuji.pick.service.product.service.PickProductService;
import com.jiuji.pick.service.product.vo.QueryProductExamineListVo;
import com.jiuji.pick.service.product.vo.QueryProductListVo;
import com.jiuji.pick.service.product.vo.SynOtherProductVo;
import com.jiuji.tc.utils.constants.NumberConstant;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@RestController
@RequestMapping("/api/product")
public class PickProductController {

    @Resource
    private PickProductService pickProductService;


    @AuthorizationAdmin
    @PostMapping("/addOrUpdateProduct/v1")
    public Result<Boolean> addOrUpdateProduct(@RequestBody @Valid AddOrUpdateProductParam param) {
        Result<Boolean> booleanResult = pickProductService.addOrUpdateProduct(param);
        //同步其他商品
        if(booleanResult.isSucceed() && NumberConstant.ONE.equals(param.getSynOtherPpid())){
            SynOtherProductVo synOtherProductVo = new SynOtherProductVo();
            BeanUtil.copyProperties(param,synOtherProductVo);
            pickProductService.synOtherProduct(synOtherProductVo);
        }
        return booleanResult;
    }

    @AuthorizationAdmin
    @PostMapping("/getSupplierProductDetailInfo/v1")
    public Result<SupplierProductDetail> getSupplierProductDetailInfo(@RequestBody @Valid AdmQuerySupplierProductDetailParam param) {
        return pickProductService.getSupplierProductDetailInfo(param);
    }

    @AuthorizationAdmin
    @PostMapping("/updateSupplierProductDetailInfo/v1")
    public Result<String> updateSupplierProductDetailInfo(@RequestBody @Valid AdmUpdateSupplierProductDetailParam param) {
        CommonUtil.checkPrice(param.getBuyNoTaxPrice(),param.getBuyTaxPrice());
        return pickProductService.updateSupplierProductDetailInfo(param);
    }

    @AuthorizationAdmin
    @PostMapping("/getProductList/v1")
    public Result<Page<QueryProductListVo>> getProductList(@RequestBody @Valid QueryProductListParam param) {
        return pickProductService.queryProductListInfo(param);
    }

    @AuthorizationAdmin
    @PostMapping("/productUpOrDown/v1")
    public Result<String> productUpOrDown(@RequestBody @Valid ProductUpOrDownParam param) {
        return pickProductService.productUpOrDown(param);
    }



    @AuthorizationAdmin
    @PostMapping("/getProductExamineList/v1")
    public Result<Page<QueryProductExamineListVo>> getProductExamineList(@RequestBody @Valid QueryProductExamineListParam param) {
        return pickProductService.getProductExamineList(param);
    }

    @AuthorizationAdmin
    @PostMapping("/bindOrUnboundProduct/v1")
    public Result<String> bindOrUnboundProduct(@RequestBody @Valid ProductApplyDealParam param) {
        return pickProductService.bindOrUnboundProduct(param);
    }

    @GetMapping("/isProductWillDown/v1")
    public Result<Boolean> judgeProductDown4Unbound(Long ppid) {
        return pickProductService.judgeProductDown4Unbound(ppid);
    }
}


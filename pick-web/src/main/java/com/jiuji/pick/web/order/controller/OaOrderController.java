package com.jiuji.pick.web.order.controller;

import cn.hutool.json.JSONUtil;
import com.jiuji.pick.common.enums.OrderTypeEnum;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.NotifyLogInfo;
import com.jiuji.pick.service.common.service.NotifyLogInfoService;
import com.jiuji.pick.service.order.param.OaOrderParam;
import com.jiuji.pick.service.order.service.AssetsOrderService;
import com.jiuji.pick.service.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 购物车信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-12
 */
@RestController
@RequestMapping("/api/order/oa")
@Slf4j
public class OaOrderController {

    @Resource
    private OrderService orderService;
    @Resource
    private NotifyLogInfoService notifyLogInfoService;
    @Resource(name="AssetsOrderServiceImpl")
    private AssetsOrderService assetsOrderService;

    /**
     * 获取订单取消
     *
     * @return
     */
    @PostMapping("/cancel/v1")
    public Result<Boolean> cancel(@RequestBody OaOrderParam param) {
        log.info("OA调用采货王取消订单，param:{}", JSONUtil.toJsonStr(param));
        Result<Boolean> result = null;
        try {
            String checkParam = checkParam(param);
            if (StringUtils.isNotBlank(checkParam)) {
                return Result.error(checkParam);
            }
            result = orderService.oaCancelOrder(param.getOrderNo(), param.getXtenant());
            return result;
        } catch (Exception e) {
            log.error("取消订单通知处理异常，param:{},exception:", JSONUtil.toJsonStr(param), e);
            return Result.errorInfo("取消订单服务处理异常");
        } finally {
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(param.getXtenant()).orderNo(param.getOrderNo()).notifyType(2).notifyName("oa通知取消订单").notifyParam(JSONUtil.toJsonStr(param)).notifyResult(JSONUtil.toJsonStr(result)).build());
        }
    }

    /**
     * oa入库，同步订单状态 (小件)
     *
     * @return
     */
    @PostMapping("/warehousing/v1")
    public Result<Boolean> warehousing(@RequestBody OaOrderParam param) {
        log.info("OA调用采货王小件订单入库，param:{}", JSONUtil.toJsonStr(param));
        Result<Boolean> result = null;
        try {
            String checkParam = checkParam(param);
            if (StringUtils.isNotBlank(checkParam)) {
                return Result.error(checkParam);
            }
            result = orderService.finish(param.getOrderNo(), param.getXtenant(), param.getCount(), OrderTypeEnum.SMALL.getCode());
            return result;
        } catch (Exception e) {
            log.error("小件订单入库通知处理异常，param:{}, exception:", JSONUtil.toJsonStr(param), e);
            return Result.errorInfo("小件订单入库处理异常");
        } finally {
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(param.getXtenant()).orderNo(param.getOrderNo()).notifyType(2).notifyName("oa通知订单完成").notifyParam(JSONUtil.toJsonStr(param)).notifyResult(JSONUtil.toJsonStr(result)).build());
        }
    }

    /**
     * oa入库，同步订单状态 （大件）
     *
     * @return
     */
    @PostMapping("/bulky/warehousing/v1")
    public Result<Boolean> warehousing4Bulky(@RequestBody OaOrderParam param) {
        log.info("OA调用采货王大件订单入库，param:{}", JSONUtil.toJsonStr(param));
        Result<Boolean> result = null;
        try {
            String checkParam = checkParam(param);
            if (StringUtils.isNotBlank(checkParam)) {
                return Result.error(checkParam);
            }
            if(param.getCount() == null) {
                return Result.errorInfo("大件商品入库数量不能为空");
            }
            result = orderService.finish(param.getOrderNo(), param.getXtenant(), param.getCount(), OrderTypeEnum.BULKY.getCode());
            return result;
        } catch (Exception e) {
            log.error("大件订单入库通知处理异常，param:{}, exception:", JSONUtil.toJsonStr(param), e);
            return Result.errorInfo("大件订单入库处理异常");
        } finally {
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(param.getXtenant()).orderNo(param.getOrderNo()).notifyType(2).notifyName("oa通知订单完成").notifyParam(JSONUtil.toJsonStr(param)).notifyResult(JSONUtil.toJsonStr(result)).build());
        }
    }

    /**
     * oa 校验ppid 与商品类型是否一致
     * @param productType   商品类型 ProductTypeEnum
     * @return
     */
    @GetMapping("/check/productType/v1")
    public Result<Boolean> warehousing4Bulky(@RequestParam("ppid") Long ppid, @RequestParam("productType") Integer productType) {
        log.info("oa校验ppid与商品类型是否一致，ppid:{} productType:{}", ppid, productType);
        Result<Boolean> result = null;
        try {
            result = assetsOrderService.checkProductType(ppid, productType);
            return result;
        } catch (Exception e) {
            log.info("oa校验ppid与商品类型是否一致处理异常，ppid:{} productType:{},exception:", ppid, productType, e);
            return Result.errorInfo("商品类型校验处理异常");
        } finally {
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().notifyType(2).notifyName("oa校验ppid与商品类型是否一致").notifyParam(String.format("oa校验ppid与商品类型是否一致，ppid:%s productType:%s", ppid, productType)).notifyResult(JSONUtil.toJsonStr(result)).build());
        }
    }

    /**
     * 检查参数
     *
     * @param param
     * @return
     */
    private static String checkParam(OaOrderParam param) {
        String errorMsg = null;
        if (param.getOrderNo() == null) {
            errorMsg = "orderNo不能为空";
        }
        if (param.getXtenant() == null) {
            errorMsg = "xtenant不能为空";
        }
        return errorMsg;
    }

}

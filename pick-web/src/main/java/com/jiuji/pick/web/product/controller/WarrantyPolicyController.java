package com.jiuji.pick.web.product.controller;

import cn.hutool.core.util.ObjectUtil;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.param.WarrantPolicySetParam;
import com.jiuji.pick.service.product.service.WarrantPolicyService;
import com.jiuji.pick.service.product.vo.WarrantyPolicyVO;
import com.jiuji.pick.service.product.vo.WarrantyTypeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description 质保政策控制器
 * <AUTHOR>
 * @Date 2021/11/10
 */
@RestController
@RequestMapping("/api/product")
public class WarrantyPolicyController {

    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private WarrantPolicyService warrantPolicyService;

    @GetMapping("/warranty/now/v1")
    public Result<WarrantyPolicyVO> getNowWarrantyPolicy(@RequestParam @NotNull(message = "质保政策类型不能为空") Integer type) {
        WarrantyPolicyVO warrantyPolicy = warrantPolicyService.getWarrantyPolicy(type, false);
        if (ObjectUtil.isNull(warrantyPolicy)) {
            return Result.noData();
        }
        return Result.success(warrantyPolicy);
    }

    @GetMapping("/warranty/getType/v1")
    public Result<List<WarrantyTypeVO>> getAllWarrantyType() {
        List<WarrantyTypeVO> warrantyType = warrantPolicyService.getAllWarrantyType();
        if (CollectionUtils.isEmpty(warrantyType)) {
            return Result.noData();
        }
        return Result.success(warrantyType);
    }

    @PostMapping("/warranty/set/v1")
    public Result<Boolean> setWarrantyPolicy(@RequestBody @Valid WarrantPolicySetParam param) {
        OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
        if (ObjectUtil.isNull(oaTokenInfo) || ObjectUtil.isNull(oaTokenInfo.getId())) {
            return Result.notLoginError();
        }

        boolean setResult = warrantPolicyService.setWarrantyPolicy(param, oaTokenInfo);
        if (!setResult) {
            return Result.error("质保政策设置失败");
        }
        return Result.success();
    }

}

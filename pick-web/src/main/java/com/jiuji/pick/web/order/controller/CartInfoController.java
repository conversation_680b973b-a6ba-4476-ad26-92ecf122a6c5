package com.jiuji.pick.web.order.controller;


import com.jiuji.pick.common.annotation.RepeatSubmitCheck;
import com.jiuji.pick.common.constant.OrderTipConstant;
import com.jiuji.pick.common.enums.CartTypeEnum;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.service.CartInfoService;
import com.jiuji.pick.service.order.vo.CartInfoVO;
import com.jiuji.pick.web.order.param.CartInfoParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 购物车信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-06
 */
@RestController
@RequestMapping("/api/order/cart")
public class CartInfoController {

    @Resource
    private CartInfoService cartInfoService;

    /**
     * 添加购物车
     *
     * @param cartInfoParam cartInfoParam
     * @return
     */
    @RepeatSubmitCheck
    @PostMapping("/add/v1")
    public Result<Long> add(@RequestBody CartInfoParam cartInfoParam) {
        String errorMsg = null;
        if (cartInfoParam.getProductCount() == null || cartInfoParam.getProductCount() < 1) {
            errorMsg = OrderTipConstant.PRODUCT_NUMBER_ERROR;
        }
        if (cartInfoParam.getProductId() == null) {
            errorMsg = OrderTipConstant.NEED_SELECT_PRODUCT;
        }
        if (cartInfoParam.getSupplierUserId() == null) {
            errorMsg = OrderTipConstant.NEED_SELECT_SUPPLIER;
        }
        if(cartInfoParam.getPriceType()==null){
            errorMsg =OrderTipConstant.ERORR_PRICE_TYPE;
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(OrderTipConstant.ADD_ERROR, errorMsg);
        }

        return cartInfoService.add(cartInfoParam.getProductId(), cartInfoParam.getProductCount(),
                cartInfoParam.getSupplierUserId(), CartTypeEnum.NORMAL.getCode(),cartInfoParam.getPriceType());
    }

    /**
     * 添加购物车
     *
     * @param cartInfoParam cartInfoParam
     * @return
     */
    @PostMapping("/directBuy/v1")
    public Result<Long> directBuy(@RequestBody CartInfoParam cartInfoParam) {
        String errorMsg = null;
        if (cartInfoParam.getProductCount() == null || cartInfoParam.getProductCount() < 1) {
            errorMsg = OrderTipConstant.PRODUCT_NUMBER_ERROR;
        }
        if (cartInfoParam.getProductId() == null) {
            errorMsg = OrderTipConstant.NEED_SELECT_PRODUCT;
        }
        if (cartInfoParam.getSupplierUserId() == null) {
            errorMsg = OrderTipConstant.NEED_SELECT_SUPPLIER;
        }
        if(cartInfoParam.getPriceType()==null){
            errorMsg=OrderTipConstant.ERORR_PRICE_TYPE;
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(OrderTipConstant.ADD_ERROR, errorMsg);
        }
        return cartInfoService.directBuy(cartInfoParam.getProductId(), cartInfoParam.getProductCount(),
                cartInfoParam.getSupplierUserId(),cartInfoParam.getPriceType());
    }

    /**
     * 根据id删除购物车，多个用","分隔
     *
     * @param cartInfoParam cartInfoParam
     * @return
     */
    @PostMapping("/deleteByIds/v1")
    public Result<Boolean> deleteById(@RequestBody CartInfoParam cartInfoParam) {
        List<Long> idList = CommonUtil.covertIdStr2Long(cartInfoParam.getIds());
        if (CollectionUtils.isEmpty(idList)) {
            return Result.success();
        }
        return cartInfoService.deleteById(idList);
    }

    /**
     * 更新购物车数量
     *
     * @param cartInfoParam cartInfoParam
     * @return
     */
    @PostMapping("/update/v1")
    public Result<Boolean> updateCart(@RequestBody CartInfoParam cartInfoParam) {
        if (cartInfoParam.getId() == null) {
            return Result.success();
        }
        if (cartInfoParam.getProductCount() == null || cartInfoParam.getProductCount() < 1) {
            return Result.error(OrderTipConstant.UPDATE_ERROR, OrderTipConstant.SELECT_PRODUCT_NUMBER_ERROR);
        }
        return cartInfoService.updateCart(cartInfoParam.getId(), cartInfoParam.getProductCount());
    }

    /**
     * 查询购物车
     *
     * @return
     */
    @GetMapping("/listAll/v1")
    public Result<CartInfoVO> listAll() {
        return cartInfoService.listAll();
    }
}


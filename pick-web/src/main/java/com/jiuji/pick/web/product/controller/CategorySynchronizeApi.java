package com.jiuji.pick.web.product.controller;

import com.alibaba.fastjson.JSON;
import com.ch999.common.util.tenant.Namespaces;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.utils.ProductSynchronizeUtils;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.service.CategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 分类同步API
 * @Auther: MiaoShaoxuan
 * @Date 2020/12/24
 */
@Slf4j
@RequestMapping("/api/synchronize/category/data")
@RestController
public class CategorySynchronizeApi {

    @Autowired
    public CategoryService categoryService;

    @Autowired
    public CurrentRequestComponent currentRequestComponent;


    /**
     * 九机分类信息同步到其它租户
     * @param category
     * @return
     */
    @PostMapping("/save")
    public Result<Boolean> saveCategory(@RequestBody Category category){
        try {

            Result<Boolean> result = categoryService.checkAndSave(category, true);
            //如果成功 尝试删除九机主库因为超时等其它原因，再次尝试的数据
            if (result.getData() != null && result.getData()){
                ProductSynchronizeUtils.callBack();
            }
            return result;
        }catch (Exception e){
            log.error("分类信息同步保存失败,报文：{},错误信息:{}", JSON.toJSONString(category),e);
            return Result.error(e.getMessage() ,"分类信息同步保存失败", false);
        }
    }


}

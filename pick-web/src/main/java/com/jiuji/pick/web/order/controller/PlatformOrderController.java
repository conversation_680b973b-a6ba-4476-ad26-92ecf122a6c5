package com.jiuji.pick.web.order.controller;

import com.jiuji.pick.common.annotation.AuthorizationAdmin;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.vo.BasePageVO;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.param.OrderSearchParam;
import com.jiuji.pick.service.order.service.OrderGetInfoService;
import com.jiuji.pick.service.order.service.OrderService;
import com.jiuji.pick.service.order.vo.OrderDetailVO;
import com.jiuji.pick.service.order.vo.PlatformOrderListVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;

/**
 * <p>
 * 合作伙伴订单操作 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@RestController
@RequestMapping("/api/order/platform")
public class PlatformOrderController {

    @Resource
    private OrderService orderService;
    @Resource
    private OrderGetInfoService orderGetInfoService;

    /**
     * 合作伙伴订单列表
     *
     * @param param param
     * @return
     */
    @AuthorizationAdmin
    @PostMapping("/list/v1")
    public Result<BasePageVO<PlatformOrderListVO>> list(@RequestBody OrderSearchParam param) {
        if (param.getCurrent() == null || param.getCurrent() < 1) {
            param.setCurrent(1);
        }
        if (param.getSize() == null || param.getSize() > MagicalValueConstant.INT_50) {
            param.setSize(MagicalValueConstant.INT_50);
        }
        if (StringUtils.isNotBlank(param.getStartTimeStr())) {
            LocalDateTime startTime = DateUtil.stringParseLocalDateTime(param.getStartTimeStr());
            param.setStartTime(startTime);
        }
        if (StringUtils.isNotBlank(param.getEndTimeStr())) {
            LocalDateTime endTime = DateUtil.stringParseLocalDateTime(param.getEndTimeStr());
            param.setEndTime(endTime);
        }
        return orderGetInfoService.getPlatformOrderList(param);
    }

    /**
     * 合作伙伴订单详情
     *
     * @param orderId 订单id
     * @return
     */
    @AuthorizationAdmin
    @GetMapping("/detail/v1")
    public Result<OrderDetailVO> detail(Long orderId) {
        if (orderId == null) {
            return Result.error("请选择订单");
        }
        return orderGetInfoService.getPlatformOrderDetail(orderId);
    }

    /**
     * 导出合作伙伴订单列表
     *
     * @param param param
     * @return
     */
    @AuthorizationAdmin
    @GetMapping("/export/v1")
    public Result export(OrderSearchParam param, HttpServletResponse response) {
        return orderService.exportPlatformOrderList(param, response);
    }

}

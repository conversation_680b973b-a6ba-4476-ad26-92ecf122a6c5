package com.jiuji.pick.web.product.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.param.QueryAreaProductPageParam;
import com.jiuji.pick.service.product.param.QuerySortProductParam;
import com.jiuji.pick.service.product.param.UpdateAreaProductParam;
import com.jiuji.pick.service.product.service.IndexProductService;
import com.jiuji.pick.service.product.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/08
 * @Time 11:30
 */
@RestController
@RequestMapping("/api/product")
public class IndexProductController {

    @Resource
    private IndexProductService indexProductService;

    /**
     * 获取商品列表
     * @return
     */
    @GetMapping("/getIndexProductList/v1")
    public Result<IndexProductListVo> getIndexProductInfo() {
        return indexProductService.indexProductList();
    }

    /**
     * 获取更多
     *
     * @param querySortProductParam
     * @return
     */
    @PostMapping("/queryProductArea/v1")
    public Result<Page<IndexProductVo>> queryProductArea(@RequestBody @Valid QuerySortProductParam querySortProductParam) {
        if (querySortProductParam == null) {
            return Result.error("请求参数错误");
        }
        return indexProductService.queryProductArea(querySortProductParam);
    }

    /**
     * 获取商品详情
     * @param supplierProductId
     * @return
     */
    @GetMapping("/getIndexProductDetail/v1/{supplierProductId}")
    public Result<IndexProductDetailVo> getIndexProductDetailInfo(@PathVariable(name = "supplierProductId") Long supplierProductId) {
        return indexProductService.getIndexProductDetailInfo(supplierProductId);
    }

    @PostMapping("/getAreaProductList/v1")
    public Result<Page<AreaProductVO>> getAreaProduct(@RequestBody @Valid QueryAreaProductPageParam param) {
        Page<AreaProductVO> areaProductPage = indexProductService.pageAreaProductVO(param);
        return Result.success(areaProductPage);
    }

    @PostMapping("/setAreaProductSort/v1")
    public Result<Boolean> setAreaProductSort(@RequestParam("supplierProductId") Long supplierProductId, @RequestParam("sort") Integer sort) {
        if (supplierProductId == null || sort == null) {
            return Result.error("请求参数错误");
        }
        if (sort < 0) {
            return Result.error("排序值不能小于0");
        }
        boolean setSort = indexProductService.areaProductSort(supplierProductId, sort);
        if (!setSort) {
            return Result.error("设置供应商商品排序失败");
        }
        return Result.success();
    }

    @PostMapping("/updateAreaProduct/v1")
    public Result<Boolean> updateAreaProduct(@RequestBody UpdateAreaProductParam param) {
        if (CollectionUtils.isEmpty(param.getPpidList()) || CollectionUtils.isEmpty(param.getAreaCodeList())) {
            return Result.error("请求参数错误,商品或专区不能为空");
        }
        boolean setSort = indexProductService.updateAreaProduct(param);
        if (!setSort) {
            return Result.error("设置商品专区失败");
        }
        return Result.success();
    }
}

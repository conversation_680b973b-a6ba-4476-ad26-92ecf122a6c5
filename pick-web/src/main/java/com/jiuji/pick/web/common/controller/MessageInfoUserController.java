package com.jiuji.pick.web.common.controller;


import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.MessageInfoUser;
import com.jiuji.pick.service.common.service.MessageInfoUserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@RestController
@RequestMapping("/api/common/message/user")
public class MessageInfoUserController {

    @Resource
    private MessageInfoUserService messageInfoUserService;

    /***
     * @description: 根据messageId 查询列表
     * @Param: [messageId]
     * @date: 2021/5/28 10:28
     */
    @GetMapping("/msgId/v1")
    public Result<List<MessageInfoUser>> listByMessageId(@RequestParam("messageId") Long messageId){
        return Result.success(messageInfoUserService.listByMessageId(messageId));
    }
}


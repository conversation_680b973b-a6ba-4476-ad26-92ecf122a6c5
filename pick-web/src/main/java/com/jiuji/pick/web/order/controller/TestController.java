package com.jiuji.pick.web.order.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.utils.HostManageUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.user.service.SupplierChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 *
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController {
    @Resource
    private SupplierChannelService supplierChannelService;

    @RequestMapping("/test")
    public Result<String> test(Long xTenant, Long supplierId, String addressId, Integer authId, Integer orderType){
        String chanelId = supplierChannelService.getChannelIdTest(xTenant, supplierId, addressId, authId, orderType);
        return Result.success(chanelId);
    }

    @RequestMapping("/test2")
    public Result<String> test2( Long supplierId, String channelId, Integer authId, Integer orderType){
        String host = HostManageUtil.getHost(String.valueOf(0));
        if(StringUtils.isBlank(host)) {
            return null;
        }
        String url = host + "/cloudapi_nc/oa-stock/api/chw/check/v1?xservicename=oa-stock&channelId=%s&authorizeid=%s";
        url = String.format(url, channelId, authId);
        String ciphertext = DigestUtils.md5Hex(DateUtil.stringParseLocalDate(LocalDateTime.now()));
        String res = HttpRequest.get(url).header("token", ciphertext).execute().body();
        log.info("调用OA校验渠道， url:{}, res:{}", url, res);
        return Result.success(res);
    }


    @GetMapping("/test3")
    public Result<String> test3(){
        String res="{\"code\":5000,\"msg\":\"无操作权限\",\"userMsg\":\"无操作权限\",\"data\":{}}";
        Result<JSONObject> result = JSONObject.parseObject(res, Result.class);
        if(Result.SUCCESS!=result.getCode()){
            return Result.error(Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
        }

        return Result.success("chanelId");
    }


}

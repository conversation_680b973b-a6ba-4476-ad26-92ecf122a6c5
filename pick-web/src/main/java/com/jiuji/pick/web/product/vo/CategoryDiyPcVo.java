package com.jiuji.pick.web.product.vo;


import com.jiuji.pick.service.product.entity.CategoryDiy;
import com.jiuji.pick.service.product.param.CategoryDiy4PcTypeSaveParam;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class CategoryDiyPcVo extends CategoryDiyVo {
    private List<CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeBo> types;
    private List<CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeKeywords> keywords;
    public CategoryDiyPcVo(CategoryDiy categoryDiy) {
        super(categoryDiy);
    }
}

package com.jiuji.pick.web.user.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ch999.common.util.secure.MD5Util;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.common.enums.SupplierUserStatusEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.BeanCopyUtil;
import com.jiuji.pick.common.utils.RegexUtils;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.service.NewOperateLogFactory;
import com.jiuji.pick.service.product.service.PickProductService;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.query.SupplierUserCheckQuery;
import com.jiuji.pick.service.user.service.SupplierUserService;
import com.jiuji.pick.service.user.vo.SupplierUserDetailVO;
import com.jiuji.pick.service.user.vo.SupplierUserVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
@Slf4j
@RestController
@RequestMapping("/api/user/supplier")
public class SupplierUserController {

    @Resource
    private SupplierUserService supplierUserService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private PickProductService pickProductService;

    @Value("${supplier.default.password}")
    private String supplierDefaultPassword;

    private static final String LIST_BY_NAME = "1";
    private static final String LIST_BY_ID = "2";
    @Resource
    private NewOperateLogFactory logFactory;

    /***
     * @description:详情
     * @Param: [id]
     * @author: Lbj
     * @date: 2021/5/6 13:55
     */
    @GetMapping("/detail/v1/{id}")
    public Result<SupplierUserDetailVO> getDetail(@PathVariable("id") Long id){
        return Result.success(supplierUserService.getDetailInfo(id));
    }

    /***
     * @description:添加或者修改
     * @Param: [detailVO]
     * @author: Lbj
     * @date: 2021/5/6 13:55
     */
    @PostMapping("/v1")
    public Result<Boolean> updateDetail(@RequestBody SupplierUserDetailVO detailVO){
        if(Objects.isNull(detailVO.getSupplierUser())){
            return Result.error("入参为空，请输入供应商基本信息！");
        }

        try {
            Result checkResult = supplierUserService.hasRepeatData(detailVO.getSupplierUser().getId(), detailVO.getSupplierUser().getLoginName(), detailVO.getSupplierUser().getName());
            if(!checkResult.isSucceed()){
                return checkResult;
            }
            // 编辑不改变状态值
            detailVO.getSupplierUser().setStatus(null);
            supplierUserService.saveOrUpdateDetail(detailVO);
            //操作日志详情记录
            if(detailVO.getParamOld()!=null){
                Optional.ofNullable(detailVO.getSupplierUser()).ifPresent((SupplierUser item)->
                        logFactory.systemSaveLog(detailVO, NewOperateLogInfoTypeEnum.SUPPLIER_LIST.getCode()));
            }
            return Result.success();
        } catch (BizException be) {
            return Result.errorInfo(be.getMessage());
        } catch (Exception e) {
            log.error("添加或者膝盖供应商信息发生异常，exception:", e);
            return Result.errorInfo("服务异常，稍后再试");
        }
    }


    /***
     * @description:当前登陆供应商详情
     * @Param: [id]
     * @author: Lbj
     * @date: 2021/5/6 13:55
     */
    @GetMapping("/current/detail/v1")
    public Result<SupplierUserDetailVO> getCurrentDetail(){
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if(null == supplierTokenInfo){
            return Result.error("请登录！");
        }
        return Result.success(supplierUserService.getDetailInfo(supplierTokenInfo.getId()));
    }

    /***
     * @description:修改当前登陆供应商
     * @Param: [detailVO]
     * @author: Lbj
     * @date: 2021/5/6 13:55
     */
    @PostMapping("/current/update/v1")
    public Result<Boolean> updateCurrentDetail(@RequestBody SupplierUserDetailVO detailVO){
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if(null == supplierTokenInfo){
            return Result.error("请登录！");
        }

        try {
            Result checkResult = supplierUserService.hasRepeatData(supplierTokenInfo.getId(), detailVO.getSupplierUser().getLoginName(), detailVO.getSupplierUser().getName());
            if(!checkResult.isSucceed()){
                return checkResult;
            }
            if(Objects.isNull(detailVO.getSupplierUser())){
                return Result.error("入参为空，请输入供应商基本信息！");
            }
            // 因为是自己编辑信息，编辑过后将状态改成待审核
            detailVO.getSupplierUser().setStatus(SupplierUserStatusEnum.UNCHECKED.getCode());
            detailVO.getSupplierUser().setId(supplierTokenInfo.getId());
            supplierUserService.saveOrUpdateDetail(detailVO);
            return Result.success();
        } catch (BizException be) {
            return Result.errorInfo(be.getMessage());
        } catch (Exception e) {
            log.error("修改当前登录供应商信息发生异常，supplierId:{}, exception:", supplierTokenInfo.getId(), e);
            return Result.errorInfo("服务异常，稍后再试");
        }
    }

    /***
     * @description:修改当前登陆供应商
     * @Param: [detailVO]
     * @author: Lbj
     * @date: 2021/5/6 13:55
     */
    @PostMapping("/current/update/v2")
    public Result<Boolean> updateCurrentDetailV2(@RequestBody SupplierUserDetailVO detailVO){
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if(null == supplierTokenInfo){
            return Result.error("请登录！");
        }
        try {
            Result checkResult = supplierUserService.hasRepeatData(supplierTokenInfo.getId(), detailVO.getSupplierUser().getLoginName(), detailVO.getSupplierUser().getName());
            if(!checkResult.isSucceed()){
                return checkResult;
            }
            if(Objects.isNull(detailVO.getSupplierUser())){
                return Result.error("入参为空，请输入供应商基本信息！");
            }
            // 因为是自己编辑信息，编辑过后将状态改成待审核
            detailVO.getSupplierUser().setStatus(SupplierUserStatusEnum.UNCHECKED.getCode());
            detailVO.getSupplierUser().setId(supplierTokenInfo.getId());
            supplierUserService.saveOrUpdateDetail(detailVO);
            //操作日志详情记录
            if(detailVO.getParamOld()!=null){
                Optional.ofNullable(detailVO.getSupplierUser()).ifPresent((SupplierUser item)->
                        logFactory.systemSaveLog(detailVO, NewOperateLogInfoTypeEnum.SUPPLIER_INFORMATION.getCode()));
            }
            return Result.success();
        } catch (BizException be) {
            return Result.errorInfo(be.getMessage());
        } catch (Exception e) {
            log.error("修改当前登录供应商信息发生异常，supplierId:{}, exception:", supplierTokenInfo.getId(), e);
            return Result.errorInfo("服务异常，稍后再试");
        }
    }


    /***
     * @description:添加/注册
     * @Param: [detailVO]
     * @author: Lbj
     * @date: 2021/5/6 13:55
     */
    @PostMapping("/add/v1")
    public Result<String> getDetail(@RequestBody SupplierUserVO supplierUserVO){

        if(StringUtils.isEmpty(supplierUserVO.getLoginName())){
            return Result.error("请输入登录名");
        }

        if(StringUtils.isEmpty(supplierUserVO.getName())){
            return Result.error("请输入名称");
        }
        if(StringUtils.isNotEmpty(supplierUserVO.getLeaderPhone()) && !RegexUtils.checkMobile(supplierUserVO.getLeaderPhone())) {
            return Result.errorInfo("负责人手机号码不正确");
        }
        // 前后去空格
        supplierUserVO.setLoginName(supplierUserVO.getLoginName().trim());
        if(StringUtils.isNotEmpty(supplierUserVO.getPassword())){
            supplierUserVO.setPassword(supplierUserVO.getPassword().trim());
        }

        Result checkResult = supplierUserService.hasRepeatData(supplierUserVO.getId(), supplierUserVO.getLoginName(), supplierUserVO.getName());
        if(!checkResult.isSucceed()){
            return checkResult;
        }
        SupplierUser supplierUser = BeanCopyUtil.copy(supplierUserVO, SupplierUser.class);
        supplierUser.setStatus(SupplierUserStatusEnum.REGISTERED.getCode());
        if(StringUtils.isEmpty(supplierUserVO.getPassword())){
            supplierUser.setPassword(MD5Util.GetMD5Code(supplierDefaultPassword));
        }else {
            supplierUser.setPassword(MD5Util.GetMD5Code(supplierUserVO.getPassword()));
        }
        supplierUserService.save(supplierUser);
        return Result.success();
    }


    /***
     * @description: 审核
     * @Param: [supplierUser]
     * @author: Lbj
     * @date: 2021/5/6 14:23
     */
    @PostMapping("/check/v1")
    public Result<Boolean> check(@RequestBody SupplierUser supplierUser){
        Integer userId = currentRequestComponent.getStaffUserId();
        String userName = currentRequestComponent.getStaffName();
        if(null == userId){
            return Result.error("请登录！");
        }
        if(SupplierUserStatusEnum.PASS.getCode() == supplierUser.getStatus()){
            supplierUserService.pass(supplierUser.getId(), supplierUser.getRemark(), Long.parseLong(String.valueOf(userId)) , userName);
        }else {
            supplierUserService.reject(supplierUser.getId(), supplierUser.getRemark(), Long.parseLong(String.valueOf(userId)), userName);
        }
        return Result.success();
    }

    /***
     * @description: 修改密码
     * @Param: [supplierUser]
     * @author: Lbj
     * @date: 2021/5/6 14:23
     */
    @PostMapping("/user/change/password/v1")
    public Result<String> changePassword(@RequestBody ChangePasswordInfoClass changePasswordInfo){

        if(StringUtils.isEmpty(changePasswordInfo.getNewPassword()) || StringUtils.isEmpty(changePasswordInfo.getOldPassword())){
            return Result.error("新密码和旧密码不能为空");
        }
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if(null == supplierTokenInfo){
            return Result.error("请登录！");
        }
        return supplierUserService.changePassword(supplierTokenInfo.getId(), changePasswordInfo.getNewPassword().trim(), changePasswordInfo.getOldPassword().trim(), Long.parseLong(String.valueOf(supplierTokenInfo.getId())), supplierTokenInfo.getLoginName());
    }

    /***
     * @description: 删除
     * @Param: [supplierUser]
     * @author: Lbj
     * @date: 2021/5/6 14:23
     */
    @PostMapping("/delete/v1")
    public Result<Boolean> delete(@RequestBody SupplierUser supplierUser){
        Integer userId = currentRequestComponent.getStaffUserId();
        String userName = currentRequestComponent.getStaffName();
        if(null == userId){
            return Result.error("请登录！");
        }
        supplierUserService.delete(supplierUser.getId(), Long.parseLong(String.valueOf(userId)), userName);
        // 删除商品
        pickProductService.deleteProduct4DelSupplier(supplierUser.getId());
        return Result.success();
    }

    /***
     * @description: 审核列表
     * @Param: [id]
     * @author: Lbj
     * @date: 2021/5/6 14:55
     */
    @PostMapping("/check/list/v1")
    public Result<IPage<SupplierUserVO>> listPageCheck(@RequestBody SupplierUserCheckQuery checkQuery){
        if(StringUtils.isNotEmpty(checkQuery.getSearchType()) && StringUtils.isNotEmpty(checkQuery.getKeyWord())){
            if("1".equals(checkQuery.getSearchType())){
                checkQuery.setName(checkQuery.getKeyWord());
            }
            if("2".equals(checkQuery.getSearchType()) && StringUtils.isNotEmpty(checkQuery.getKeyWord())){
                checkQuery.setId(Long.parseLong(checkQuery.getKeyWord().trim()));
            }
        }
        return Result.success(supplierUserService.listPageCheck(checkQuery));
    }

    /***
     * @description: 后台供应商列表
     * @Param: [id]
     * @author: Lbj
     * @date: 2021/5/6 14:55
     */
    @PostMapping("/adm/list/v1")
    public Result<IPage<SupplierUserVO>> listPageAdmin(@RequestBody SupplierUserCheckQuery checkQuery){
        if(StringUtils.isNotEmpty(checkQuery.getSearchType()) && StringUtils.isNotEmpty(checkQuery.getKeyWord())){
            if(LIST_BY_NAME.equals(checkQuery.getSearchType())){
                checkQuery.setName(checkQuery.getKeyWord());
            }
            if(LIST_BY_ID.equals(checkQuery.getSearchType())){
                checkQuery.setId(Long.parseLong(checkQuery.getKeyWord()));
            }
        }
        return Result.success(supplierUserService.listPageAdmin(checkQuery));
    }


    /***
     * @description: 供应商登陆
     * @Param: [loginInfo]
     * @author: Lbj
     * @date: 2021/5/8 11:04
     */
    @PostMapping("/login/v1")
    public Result<String> login(@RequestBody LoginInfoClass loginInfo){
        if(StringUtils.isEmpty(loginInfo.getLoginName()) || StringUtils.isEmpty(loginInfo.getPassword())){
            return Result.error("请输入用户名密码！");
        }
        return supplierUserService.login(loginInfo.getLoginName().trim(), loginInfo.getPassword().trim());
    }

    /***
     * @description: 供应商退出登陆
     * @Param: [loginInfo]
     * @author: Lbj
     * @date: 2021/5/8 11:04
     */
    @PostMapping("/logout/v1")
    public Result<String> logout(){
        return supplierUserService.logout(currentRequestComponent.getAuthorization());
    }

    /***
     * @description: 查询供应商登陆
     * @Param: [loginInfo]
     * @author: Lbj
     * @date: 2021/5/8 11:04
     */
    @GetMapping("/login/info/v1")
    public Result<SupplierUser> loginInfo(){
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if(Objects.isNull(supplierTokenInfo)){
            return Result.error("请登录");
        }
        SupplierUser supplierUser = supplierUserService.getById(supplierTokenInfo.getId());
        return Result.success(supplierUser);
    }


    /***
     * @description: 根据id 导入供应商
     * @Param: [idStr]
     * @author: Lbj
     * @date: 2021/5/11 10:24
     */
    @GetMapping("/importSupplier/ids")
    public Result<String> importSupplierByIds(String idStr){
        return supplierUserService.importSupplierByIds(idStr);
    }


    /***
     * 登陆信息
     */
    @Data
    static class LoginInfoClass{
        private String loginName;
        private String password;
    }

    /***
     *  修改密码类
     */
    @Data
    static class ChangePasswordInfoClass{
        private Long id;
        private String newPassword;
        private String oldPassword;
    }
}


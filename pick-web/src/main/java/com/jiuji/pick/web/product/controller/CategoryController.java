package com.jiuji.pick.web.product.controller;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.service.CategoryService;
import com.jiuji.pick.service.product.vo.CategoryTreeVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @function:
 * @description: CategoryController.java
 * @date: 2021/04/28
 * @author: sunfayun
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/product")
public class CategoryController {

    @Resource
    private CategoryService categoryService;

    @GetMapping("/getCategoryTree/v1")
    public Result<List<CategoryTreeVo>> getCategoryTree(Integer categoryType) {
        List<CategoryTreeVo> categoryTreeVoList = categoryService.getCategoryTree(categoryType);
        if(CollectionUtils.isEmpty(categoryTreeVoList)) {
            return Result.noData();
        }
        return Result.success(categoryTreeVoList);
    }

}

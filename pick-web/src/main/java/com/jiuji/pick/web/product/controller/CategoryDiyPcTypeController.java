package com.jiuji.pick.web.product.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jiuji.pick.common.annotation.AuthorizationAdmin;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.entity.CategoryDiy;
import com.jiuji.pick.service.product.entity.CategoryDiyPcType;
import com.jiuji.pick.service.product.param.CategoryDiy4PcItemSaveParam;
import com.jiuji.pick.service.product.param.CategoryDiy4PcTypeSaveParam;
import com.jiuji.pick.service.product.param.CategoryDiySortSaveParam;
import com.jiuji.pick.service.product.service.CategoryDiyPcTypeService;
import com.jiuji.pick.service.product.service.CategoryDiyService;
import com.jiuji.pick.service.product.vo.MenuNevVo;
import com.jiuji.pick.web.product.vo.CategoryDiyPcVo;
import com.jiuji.pick.web.product.vo.CategoryDiyVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
@RestController
@RequestMapping("/categoryDiyPcType")
public class CategoryDiyPcTypeController {

    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private CategoryDiyService categoryDiyService;
    @Resource
    private CategoryDiyPcTypeService categoryDiyPcTypeService;

    /**
     * 添加主分类
     * @param diy4PcTypeSave
     * @return
     */
    @PostMapping("/saveByType/v1")
    @AuthorizationAdmin
    public Result<Boolean > saveByArea(@RequestBody @Valid CategoryDiy4PcTypeSaveParam diy4PcTypeSave) {
        Integer staffUserId = currentRequestComponent.getStaffUserId();
        if (staffUserId==null){
            return Result.error("无法获取当前用户ID");
        }
        boolean success=categoryDiyService.saveByPcType(diy4PcTypeSave,staffUserId);
        return success?Result.success():Result.error("保存失败") ;
    }

    /**
     * 添加子分类
     * @param diy4PcItemSave
     * @return
     */
    @PostMapping("/saveByItem/v1")
    @AuthorizationAdmin
    public Result<Boolean > saveByItem( @RequestBody @Valid CategoryDiy4PcItemSaveParam diy4PcItemSave) {
        if(diy4PcItemSave.getLevel() == WebConstant.CATEGORYDIY_LEVEL.Level_3.intValue() && StringUtils.isBlank(diy4PcItemSave.getLink())){
            return Result.error("请填写分类链接");
        }
        boolean success=categoryDiyService.saveByPcItem(diy4PcItemSave,currentRequestComponent.getStaffUserId());
        return success?Result.success():Result.error("保存失败") ;
    }

    /**
     * 查看子分类
     * @param level
     * @param parentId
     * @return
     */
    @GetMapping("/itemList/v1")
    public Result<List<CategoryDiyVo>> itemList(Integer level, Integer parentId ) {
        LambdaQueryWrapper<CategoryDiy> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategoryDiy::getLevel, level)
                .eq(CategoryDiy::getParentId,parentId)
                .eq(CategoryDiy::getPlatform, WebConstant.CATEGORYDIY_PLATFORM.PC)
                .orderByAsc( CategoryDiy::getSort);
        return Result.success(categoryDiyService.list(queryWrapper).stream().map(CategoryDiyVo::new).collect(Collectors.toList())) ;
    }

    /**
     * pc 查看分类列表
     * @return
     */
    @GetMapping("/list/v1")
    public Result<List<CategoryDiyPcVo>> list() {
        LambdaQueryWrapper<CategoryDiy> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategoryDiy::getLevel,WebConstant.CATEGORYDIY_LEVEL.Level_1)
                .eq(CategoryDiy::getPlatform, WebConstant.CATEGORYDIY_PLATFORM.PC)
                .eq(CategoryDiy::getDeleted,false)
                .orderByAsc( CategoryDiy::getSort);
        List<CategoryDiyPcVo> list = categoryDiyService.list(queryWrapper).stream().map(CategoryDiyPcVo::new).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)){
            return Result.noData();
        }
        List<Integer> ids = list.stream().map(CategoryDiyPcVo::getId).collect(Collectors.toList());
        //去查关键字和分类
        List<CategoryDiyPcType> typeAndKeyWords = categoryDiyPcTypeService.list(new LambdaQueryWrapper<CategoryDiyPcType>().eq(CategoryDiyPcType::getDeleted,0).in(CategoryDiyPcType::getCategoryDiyId, ids));
        if (CollectionUtils.isEmpty(typeAndKeyWords)){
            return Result.error("无法根据条件获取diy分类的信息");
        }
        Map<Integer, List<CategoryDiyPcType>> typeAndKeyWordsGroup = typeAndKeyWords.stream().collect(Collectors.groupingBy(CategoryDiyPcType::getKinds));
        if (typeAndKeyWordsGroup==null){
            return Result.error("无法kins条件获取diy分类的信息,");
        }
        List<CategoryDiyPcType> pcTypes = typeAndKeyWordsGroup.get(1);
        List<CategoryDiyPcType> keyWords  = typeAndKeyWordsGroup.get(2);
        if (CollectionUtils.isEmpty(keyWords)){
            for (CategoryDiyPcVo categoryDiyPcVo : list) {
                categoryDiyPcVo.setTypes(pcTypes.stream().filter(e->e.getCategoryDiyId().equals(categoryDiyPcVo.getId())).map(CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeBo::new).collect(Collectors.toList()));
                categoryDiyPcVo.setKeywords(Arrays.asList());
                categoryDiyPcVo.setName(pcTypes.stream().filter(e->e.getCategoryDiyId().equals(categoryDiyPcVo.getId())).map(CategoryDiyPcType::getName).collect(Collectors.joining("/")));
            }
            return Result.success(list) ;
        }
        for (CategoryDiyPcVo categoryDiyPcVo : list) {
            categoryDiyPcVo.setTypes(pcTypes.stream().filter(e->e.getCategoryDiyId().equals(categoryDiyPcVo.getId())).map(CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeBo::new).collect(Collectors.toList()));
            categoryDiyPcVo.setKeywords(keyWords.stream().filter(e->e.getCategoryDiyId().equals(categoryDiyPcVo.getId())).map(CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeKeywords::new).collect(Collectors.toList()));
            categoryDiyPcVo.setName(pcTypes.stream().filter(e->e.getCategoryDiyId().equals(categoryDiyPcVo.getId())).map(CategoryDiyPcType::getName).collect(Collectors.joining("/")));
        }
        return Result.success(list) ;
    }

    /**
     * 商城端
     * @return
     */
    @GetMapping("/getMenuNav/v1")
    public Result<List<MenuNevVo>> getAllClass() {
        Integer currentCityId = currentRequestComponent.getCurrentCityId();
        if (currentCityId==null){
            return Result.error("无法获取当前用户ID");
        }
        List<MenuNevVo> allClass = categoryDiyService.getAllClass(currentCityId);
        return Result.success(allClass);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/remove/{id}")
    @AuthorizationAdmin
    public Result<Boolean > remove(@PathVariable("id") Integer id) {
        CategoryDiy byId = categoryDiyService.getById(id);
        boolean success=  categoryDiyService.removeCascade(id,byId.getLevel().equals(WebConstant.CATEGORYDIY_LEVEL.Level_1)?WebConstant.CATEGORYDIY_SORT_OR_REMOVE_TYPE.PC_TYPE:WebConstant.CATEGORYDIY_SORT_OR_REMOVE_TYPE.OTHER);
        return success?Result.success():Result.error("删除失败") ;
    }

    /**
     * 保持楼层
     * @param categoryDiySortSaveParam
     * @return
     */
    @PostMapping("/saveSort")
    @AuthorizationAdmin
    public Result<Boolean > saveSort(@RequestBody @Valid List<CategoryDiySortSaveParam> categoryDiySortSaveParam) {
        final List<Integer> sequences = categoryDiySortSaveParam.stream().map(CategoryDiySortSaveParam::getSort).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sequences)) {
            final long count = sequences.stream().distinct().count();
            if (((int) (count)) != sequences.size()) {
                return Result.error( "排序字段出现相同数字");
            }
        }
        Integer currentCityId = currentRequestComponent.getCurrentCityId();
        if (currentCityId==null){
            return Result.error("无法获取当前用户ID");
        }
        boolean update = categoryDiyService.saveSort(categoryDiySortSaveParam, WebConstant.CATEGORYDIY_SORT_OR_REMOVE_TYPE.OTHER,currentCityId);
        return update?Result.success("更新分类排序成功","更新分类排序成功",null):Result.error("更新分类排序失败") ;
    }
}


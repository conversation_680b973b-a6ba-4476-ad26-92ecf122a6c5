package com.jiuji.pick.web.common.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.DeliveryFeeArea;
import com.jiuji.pick.service.common.service.DeliveryFeeAreaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 *  物流费区域 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
@RestController
@RequestMapping("/api/common/deliveryFeeArea")
public class DeliveryFeeAreaController {

    @Resource
    private DeliveryFeeAreaService deliveryFeeAreaService;

    @PostMapping("/edit/v1")
    public Result<Boolean> edit(@RequestBody DeliveryFeeArea feeArea) {
        return deliveryFeeAreaService.edit(feeArea);
    }

    @GetMapping("/del/v1/{id}")
    public Result<Boolean> del(@PathVariable Integer id) {
        return deliveryFeeAreaService.delete(id);
    }


    @GetMapping("/page/v1")
    public Result<IPage<DeliveryFeeArea>> page(@RequestParam(value = "province", required = false) String province, @RequestParam(value = "provinceCode", required = false) String provinceCode,
                                               @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage, @RequestParam(value = "size", defaultValue = "20") Integer size) {
        return deliveryFeeAreaService.getPage(province,provinceCode,currentPage,size);
    }

    @GetMapping("/get/v1")
    public Result<DeliveryFeeArea> getFeeArea(@RequestParam String cityId) {
        if (StringUtils.isEmpty(cityId) || cityId.length() < MagicalValueConstant.INT_2) {
            return Result.errorInfo("入参有误");
        }
        return Result.success(deliveryFeeAreaService.getFeeArea(cityId));
    }

}


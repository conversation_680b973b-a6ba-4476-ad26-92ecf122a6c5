package com.jiuji.pick.web.common.controller;

import com.google.common.base.Splitter;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.service.ProductSearchService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @function:
 * @description: RefreshCacheController.java
 * @date: 2021/06/01
 * @author: sunfayun
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/common")
public class RefreshCacheController {

    @Resource
    private ProductSearchService productSearchService;

    @PostMapping("/updateRefreshCache/v1")
    public Result<String> updateRefreshCache(String ppid) {
        boolean result;
        if(StringUtils.isNotBlank(ppid)) {
            List<Long> ppidList = Splitter.on(",").splitToList(ppid).stream().map(p -> Long.valueOf(p)).collect(Collectors.toList());
            result = productSearchService.addProductBatch(ppidList, Boolean.FALSE);
        } else {
            result = productSearchService.addProductBatch(null, Boolean.TRUE);
        }

        if(result) {
            return Result.successInfo("修改刷新成功");
        }
        return Result.errorInfo("修改刷新失败");
    }

    @PostMapping("/deleteRefreshCache/v1")
    public Result<String> deleteRefreshCache(String bindIds) {
        if(StringUtils.isBlank(bindIds)) {
            return Result.errorInfo("请求参数错误");
        }
        List<Long> bindIdList = Splitter.on(",").splitToList(bindIds).stream().map(p -> Long.valueOf(p)).collect(Collectors.toList());
        boolean result = productSearchService.deleteProductBatch(bindIdList);
        if(result) {
            return Result.successInfo("删除刷新成功");
        }
        return Result.errorInfo("删除刷新失败");
    }

    @GetMapping("/rebuildEsData/v1")
    public Result<String> rebuildEsData() {
        boolean result = productSearchService.rebuildProductToEs();
        if(result) {
            return Result.successInfo("重建成功");
        }
        return Result.errorInfo("重建失败");
    }


    /**
     * 联想词重建
     * @return
     */
    @GetMapping("/rebuildEsDataAssociation/v1")
    public Result<String> rebuildEsDataAssociation() {
        boolean result = productSearchService.rebuildProductAssociationEs();
        if(result) {
            return Result.successInfo("重建成功");
        }
        return Result.errorInfo("重建失败");
    }

}

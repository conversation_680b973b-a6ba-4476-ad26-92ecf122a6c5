package com.jiuji.pick.web.order.controller;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.annotation.RepeatSubmitCheck;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.enums.EnumV0;
import com.jiuji.pick.common.enums.KeyTypeEnum;
import com.jiuji.pick.common.enums.LogisticsCompanyEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.param.OrderDeliveryParam;
import com.jiuji.pick.service.order.param.OrderOperatingParam;
import com.jiuji.pick.service.order.param.OrderSearchParam;
import com.jiuji.pick.service.order.service.OrderGetInfoService;
import com.jiuji.pick.service.order.service.OrderService;
import com.jiuji.pick.service.order.service.SaleOrderService;
import com.jiuji.pick.service.order.vo.CreateSaleOrderVo;
import com.jiuji.pick.service.order.vo.OrderDetailVO;
import com.jiuji.pick.service.order.vo.OrderListExportVO;
import com.jiuji.pick.service.order.vo.OrderListVO;
import com.jiuji.pick.service.rpc.vo.CommonQueryRoutReq;
import com.jiuji.pick.service.rpc.vo.QueryRoutTrackRes;
import com.jiuji.pick.service.user.service.SupplierUserService;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <p>
 * 供应商订单操作 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@Slf4j
@RestController
@RequestMapping("/api/order/supplier")
public class SupplierOrderController {

    /**
     * 每个sheet最大行数65536  超过就报错
     */
    private final static int SHEET_SIZE = 50000;
    @Resource
    private OrderService orderService;
    @Resource
    private OrderGetInfoService orderGetInfoService;
    @Resource
    private SaleOrderService saleOrderService;
    @Resource
    private SupplierUserService supplierUserService;


    @PostMapping("/queryRoute/v1")
    public R<QueryRoutTrackRes> queryRoute(@RequestBody @Valid CommonQueryRoutReq commonQueryRoutReq) {
        return saleOrderService.queryRoute(commonQueryRoutReq);
    }

    @GetMapping("/getEnum")
    public Result<Map<String, List<EnumV0>>> getEnum(){
        HashMap<String, List<EnumV0>> map = new HashMap<>();
        map.put("LogisticsCompanyEnum",LogisticsCompanyEnum.getAllEnum());
        return Result.success(map);
    }

    /**
     * 按产品发货
     *
     * @param param
     * @return
     */
    @PostMapping("/delivery/v2")
    public Result<String> delivery(@RequestBody @Valid OrderDeliveryParam param) {
        return orderService.deliveryForProduct(param);
    }




    /**
     * 按产品发货
     * @param param
     * @return
     */
    @PostMapping("/deliveryFromOa")
    public Result<String> deliveryFromOa(@RequestBody @Valid OrderDeliveryParam param) {
        String key = UUID.randomUUID().toString();
        log.warn("OA 发货同步严选传入参数：{}，唯一标识：{}",JSONUtil.toJsonStr(param),key);
        Result<String> result = orderService.deliveryFromOa(param);
        log.warn("OA 发货同步严选返回结果：{}，唯一标识：{}",JSONUtil.toJsonStr(result),key);
        return result;
    }



    /**
     * 按产品发货
     * @return
     */
    @GetMapping("/getSupplierUserToken")
    public Result<String> getSupplierUserToken() {
        //进行模拟供应商登录
        return Result.success(JSONUtil.toJsonStr(supplierUserService.simulateLogin()));
    }

    /**
     * 生成销售单
     *
     * @param orderIds
     * @return
     */
    @RepeatSubmitCheck
    @GetMapping("/createSaleOrder/v1")
    public Result<String> createSaleOrder(String orderIds, String areaCode) {
        return saleOrderService.createSaleOrder(orderIds, areaCode);
    }


    /**
     * 生成销售单第二接口
     *
     * @param createSaleOrderVo
     * @return
     */
    @RepeatSubmitCheck
    @PostMapping("/createSaleOrder/v2")
    public Result<String> createSaleOrder2(@RequestBody @Valid CreateSaleOrderVo createSaleOrderVo) {
        return saleOrderService.createSaleOrder(createSaleOrderVo);
    }

    /**
     * 订单发货
     *
     * @param param param
     * @return
     */
    @PostMapping("/deliveryChange/v1")
    public Result deliveryChange(@RequestBody OrderOperatingParam param) {
        if (param.getOrderId() == null) {
            return Result.error("请选择要变更发货的订单");
        }
        if (StringUtils.isBlank(param.getDeliveryTimeStr())) {
            return Result.error("请选择变更的发货时间");
        }
        return orderService.deliveryChange(param.getOrderId(), DateUtil.stringParseLocalDateTime(param.getDeliveryTimeStr()));
    }

    /**
     * 供应商订单列表
     *
     * @param param param
     * @return
     */
    @PostMapping("/list/v1")
    public Result<Page<OrderListVO>> list(@RequestBody OrderSearchParam param) {
        if (param.getCurrent() == null || param.getCurrent() < 1) {
            param.setCurrent(1);
        }
        if (param.getSize() == null || param.getSize() > MagicalValueConstant.INT_50) {
            param.setSize(MagicalValueConstant.INT_50);
        }
        if (StringUtils.isNotBlank(param.getStartTimeStr())) {
            LocalDateTime startTime = DateUtil.stringParseLocalDateTime(param.getStartTimeStr());
            param.setStartTime(startTime);
        }
        if (StringUtils.isNotBlank(param.getEndTimeStr())) {
            LocalDateTime endTime = DateUtil.stringParseLocalDateTime(param.getEndTimeStr());
            param.setEndTime(endTime);
        }
        Integer keyType = param.getKeyType();
        String code = KeyTypeEnum.ELEVEN.getCode();
        if(code.equals(keyType+"")){
            String value = param.getKeyValue();
            if(StringUtils.isNotEmpty(value)){
                boolean numeric = StringUtils.isNumeric(value);
                if(!numeric){
                    throw new BizException(KeyTypeEnum.ELEVEN.getMessage()+"必须位数字");
                }
            }
        }
        return orderGetInfoService.getSupplierOrderList(param);
    }
    /**
     * 供应商订单列表导出
     *
     * @param param param
     * @return
     */
    @PostMapping("/listExport/v1")
    public Result listExport(@RequestBody OrderSearchParam param,HttpServletResponse response) {
        if (param.getCurrent() == null || param.getCurrent() < 1) {
            param.setCurrent(1);
        }
        param.setSize(SHEET_SIZE);

        if (StringUtils.isNotBlank(param.getStartTimeStr())) {
            LocalDateTime startTime = DateUtil.stringParseLocalDateTime(param.getStartTimeStr());
            param.setStartTime(startTime);
        }
        if (StringUtils.isNotBlank(param.getEndTimeStr())) {
            LocalDateTime endTime = DateUtil.stringParseLocalDateTime(param.getEndTimeStr());
            param.setEndTime(endTime);
        }
        Result<Page<OrderListExportVO>> supplierOrderList = orderGetInfoService.getSupplierOrderListV2(param);
        if(supplierOrderList==null){
            return Result.error("查询订单为空，无法导出数据");
        }
        //获取到查询结果
        Page<OrderListExportVO> data = supplierOrderList.getData();
        if(data==null){
            return Result.error("查询订单为空，无法导出数据");
        }
        List<OrderListExportVO> records = data.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return Result.error("查询订单为空，无法导出数据");
        }
        return orderGetInfoService.listExport(records,response);

    }

    /**
     * 合作伙伴订单详情
     *
     * @param orderId 订单id
     * @return
     */
    @GetMapping("/detail/v1")
    public Result<OrderDetailVO> detail(Long orderId) {
        if (orderId == null) {
            return Result.error("请选择订单");
        }
        return orderGetInfoService.getSupplierOrderDetail(orderId);
    }

    /**
     * 待发货数量
     *
     * @return
     */
    @GetMapping("/toBeDelivered/v1")
    public Result<Integer> getToBeDeliveredOrderCount() {
        return orderService.getToBeDeliveredOrderCount();
    }

}

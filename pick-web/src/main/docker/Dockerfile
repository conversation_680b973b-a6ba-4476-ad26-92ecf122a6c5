FROM harbor.saas.ch999.cn:1088/common/jiuji-java-semeru-jdk17:20221205
COPY pick-web-0.0.1.jar /opt/pick-web.jar
# CMD不能换行  不然build时会出错！
CMD java --add-opens=java.base/java.lang=ALL-UNNAMED ${JAVA_OPTS} ${JAVA_MEM_OPTS} -javaagent:/pinpoint-agent/pinpoint-bootstrap.jar -Dpinpoint.agentId=${HOSTNAME}_${SERVER_PORT} -Dpinpoint.applicationName=pick-web.jar -Djava.awt.headless=true -Djava.net.preferIPv4Stack=true -Duser.timezone=Asia/Shanghai -Dspring.cloud.inetutils.preferred-networks=${HOSTNAME} -server -XX:+DisableExplicitGC -Xtune:virtualized -Xshareclasses -Xquickstart -Djava.security.egd=file:/dev/./urandom -jar /opt/pick-web.jar --spring.cloud.consul.discovery.instance-group=${GROUP} --spring.profiles.active=${PROFILE} --spring.cloud.bootstrap.name=bootstrap-${PROFILE} --server.port=${SERVER_PORT} --spring.cloud.client.hostname=${HOSTNAME}

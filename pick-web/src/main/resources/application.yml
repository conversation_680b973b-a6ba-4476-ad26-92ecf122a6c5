server:
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 1000
      min-spare: 30
    connection-timeout: 5000ms
  port: 8899
  servlet:
    context-path: /pick

spring:
  # 环境 dev|test|prod
  profiles:
    active: test
  # jackson时间格式化
  application:
    name: pick-web
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  mvc:
    throw-exception-if-no-handler-found: true
    static-path-pattern: /static/**
  resources:
    add-mappings: false
  cache:
    type: redis
  main:
    allow-bean-definition-overriding: true

#mybatis
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.jiuji.pick.**.entity
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 2
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 2
    #驼峰下划线转换
    db-column-underline: true
    #刷新mapper 调试神器
    refresh-mapper: true
    #数据库大写下划线转换
    #capital-mode: true
    #序列接口实现类配置
    #key-generator: com.baomidou.springboot.xxx
    #逻辑删除配置
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
    #自定义填充策略接口实现
    #meta-object-handler: com.baomidou.springboot.xxx
    #自定义SQL注入器
    #sql-injector: com.baomidou.mybatisplus.mapper.LogicSqlInjector
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true

management:
  endpoints:
    web:
      exposure:
        include: "info,health,prometheus"
  metrics:
    tags:
      application: ${spring.application.name}

#9机短信接口地址
sms:
  url: http://sms.ch999.com.cn/?test=1
  sendEmailUrl: http://sms.ch999.com.cn/email/email.aspx

#图片服务器地址
image:
  uploadImgUrl: http://192.168.254.7:9333
  delImgUrl: http://192.168.254.7:5083
  selectImgUrl: https://img2.ch999img.com/

# 供应商默认密码
supplier:
  default:
    password: 123456

# 文件上传
fileService:
  appId: 220000
  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALxuBOCezg8HbfiHshweE5BSdqMBsmr/s9VPBCok7zS+QvuFR7ZdwuflMCGsX3KzU+q3BxPP5AfBjZNMjbd2bo8CAwEAAQ==

# 九讯云
jiuxunadmin:
  rsa:
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCMHF06r5dc4MgWfBcpreock/Jit5QBudZqlRl25Ap3k+e/6y4qktIIGA/Dc0o8Kza5PRkNQgPEiEj8ux8jZCOlOPCYQXfTjlAgHr4WRz93LxXAM/bjcMhJXrr8YEJT/JHmfDM0bda/1LRsDMm1rohVhaiVlRSNEzpNpCbQCB+x/wIDAQAB


lmstfy:
  host: ${pick.lmstfyhost}
  namespace:
  token:
  # 重试次数
  retryTimes: 2
  # 重试时间间隔（单位：毫秒）
  retryIntervalMilliseconds: 1000
  # 读 超时时间（单位：秒）
  readTimeoutSecond: 5
  # 写 超时时间（单位：秒）
  writeTimeoutSecond: 5
  # 连接 超时时间（单位：秒）
  connectTimeoutSecond: 5
  mult:
    first-lmstfy-client:
      namespace: pickweb
      token: ${pick.lmstfytoken}
      #(延迟)处理方式为预约到店且订单类型为小件预约单
      oaSaleQueue: oaSaleQueue
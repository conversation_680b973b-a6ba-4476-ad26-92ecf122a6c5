spring:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************
    username: pick_goods_platform
    password: pfNBk49uMCW@
    max-wait: 10000
    min-idle: 5
    max-active: 50
    initial-size: 10
    maximum-pool-size: 50
    hikari:
      connection-test-query: SELECT 1 FROM DUAL
      connection-timeout: 30000
      maximum-pool-size: 50
      max-lifetime: 1800000
      minimum-idle: 5
  redis:
    host: master.main.redis.service.ch999.cn
    port: 6379
    password: JiujiOa2020
    jedis:
      pool:
        max-active: 128
        max-idle: 16
        min-idle: 0
    timeout: 10s
  rabbitmq:
    username: admin
    password: ch999
    host: master.main.rabbitmq.service.ch999.cn
    port: 5672
    virtual-host: pick-web
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 5
          max-interval: 12000
    template:
      mandatory: true

  cloud:
    consul:
      discovery:
        prefer-ip-address: true
        instance-group: blue
        service-name: ${spring.application.name}
        instance-id: ${spring.application.name}-${spring.cloud.consul.discovery.instance-group}-${spring.cloud.client.hostname}-${server.port}
        ip-address: ${spring.cloud.client.hostname}
        #健康检查失败多久强制取消服务注册
        health-check-critical-timeout: 1200s
        #heartbeat:
        #  enabled: true
        tags: traefik.frontend.rule=Host:pick-web
        instance-zone: 9ji
        default-query-tag: zone=${spring.cloud.consul.discovery.instance-zone}
        health-check-url: http://${spring.cloud.client.ipaddress}:${server.port}/${server.servlet.context-path}/actuator/info
      host: *************
      port: 8500

logging:
  config: classpath:log/log4j2-test.xml

jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson
  remote:
    default:
      type: redis
      keyConvertor: fastjson
      valueEncoder: kryo
      valueDecoder: kryo
      keyPrefix: pick-web
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 128
      host: master.main.redis.service.ch999.cn
      port: 6379
      password: JiujiOa2020

b2bconfig:
  minifile:
    uploadurl: http://weedfs.dev.ch999.cn:9333
    collection: pick-web
    contextpath: /newstatic/
    dcurl: img.dev.9ji.com
    httpProtocol: https://
    oldproductimagecontextpath: pic/product/
    pathbase: https://img2.ch999img.com/
  id:
    workId: 1
    centerId: 1

syshost:
  wcf: http://inwcf.ch999.cn

app:
  id: pick-web
apollo:
  meta: http://*************:8010,http://**************:8010,http://**************:8010
  bootstrap:
    enabled: true
    namespaces: application-prod.yml

elastic:
  cluster:
    nodes: **************:9205,**************:9205,**************:9205

# 合作伙伴跳转采货王跳转地址
skip:
  pick:
    url: https://chw.9xun.com/mall/login?uid=

# OA 同步供应商数据接口地址
oa:
  sync:
    supplier:
      url: https://moa.9ji.com/cloudapi_nc/oa-stock/api/chw/getById/v1?xservicename=oa-stock&id=
  stock:
    url: https://moa.9ji.com/cloudapi_nc/oa-stock/api/chw/get_small_sales/v1?xservicename=oa-stock


pick:
  lmstfyhost: ************
  lmstfytoken: 01H4HTDGG298HPQND2MSBQFSH1
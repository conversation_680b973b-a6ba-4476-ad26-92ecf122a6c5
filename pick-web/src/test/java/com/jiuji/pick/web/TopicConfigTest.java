package com.jiuji.pick.web;

import com.alibaba.fastjson.JSONObject;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.TopicConfig;
import com.jiuji.pick.service.common.service.TopicConfigService;
import com.jiuji.pick.service.common.vo.TopicConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.webAppContextSetup;

/**
 *
 * <AUTHOR>
 * @Date 2021/7/27
 */
@SpringBootTest(classes = PickWebApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
@ActiveProfiles("test")
public class TopicConfigTest {

    @Resource
    private TopicConfigService topicConfigService;

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setup() {
        this.mockMvc = webAppContextSetup(this.wac).build();
    }

    @Test
    public void testGetList() {
        Result<List<TopicConfigVO>> result = topicConfigService.getList(true, "专题页3");
        log.info("专题页配置列表测试：{}", result.getData());
    }

    @Test
    public void testEditNoLogin() {
        TopicConfig topicConfig = new TopicConfig();
        topicConfig.setEnabled(true).setName("专题页4").setSort(1).setLink("111.html").setBannerPic("xxx.jpg");

        Result<Boolean> result = topicConfigService.edit(topicConfig);

        log.info("专题页配置编辑测试,：{}", result);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testEditWithController() throws Exception {

        TopicConfig topicConfig = new TopicConfig();
        topicConfig.setEnabled(true).setName("专题页6").setSort(1).setLink("111.html").setBannerPic("xxx.jpg");

        String json = JSONObject.toJSONString(topicConfig);

        mockMvc.perform(MockMvcRequestBuilders
                .post("/adm/common/topicConfig/edit/v1")
                // 传json参数
                .content(json.getBytes())
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .header("Authorization","e7bb70d4f4a8440b9f44b9b0cb519121")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(print());
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testDelWithController() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders
                .post("/adm/common/topicConfig/del/v1")
                .param("id", "10")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .header("Authorization","e7bb70d4f4a8440b9f44b9b0cb519121")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(print());
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testEnabledWithController() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders
                .post("/adm/common/topicConfig/enabled/v1")
                .param("id", "8")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .header("Authorization","e7bb70d4f4a8440b9f44b9b0cb519121")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(print());
    }

}

package com.jiuji.pick.web;

import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.config.apollo.SwitchConfig;
import com.jiuji.pick.common.utils.HostManageUtil;
import com.jiuji.pick.common.utils.RedLockUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.UUID;

@SpringBootTest(classes = PickWebApplication.class)
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class ConfigTest {

    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private SwitchConfig switchConfig;

    @Test
    public void getRedisUserInfoTest() {
        System.err.println(currentRequestComponent.getStaffName());
    }

    @Test
    public void redLockTest() {
        String lockKey = UUID.randomUUID().toString();
        for (int i=0;i<10;i++) {
            new Thread(() -> {
                boolean result = RedLockUtil.tryLock(lockKey, 0, 60);
                try {
                    if(!result) {
                        System.err.println("获取锁失败");
                        return;
                    }
                    System.err.println("获取锁成功");
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    RedLockUtil.unlock(lockKey);
                }
            }).start();
        }
    }

    @Test
    public void apolloConfigTest() {
        System.err.println(switchConfig.isSystem());
    }

    @Test
    public void getHostTest() {
        System.err.println(HostManageUtil.getHost("50010"));
    }

}

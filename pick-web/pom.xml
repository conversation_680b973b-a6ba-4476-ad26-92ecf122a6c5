<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.jiuji.pick</groupId>
        <artifactId>pick-goods-platform</artifactId>
        <version>0.0.1</version>
    </parent>

    <groupId>com.jiuji.pick</groupId>
    <artifactId>pick-web</artifactId>
    <version>0.0.1</version>
    <name>pick-web</name>
    <packaging>jar</packaging>

    <description>pick-web</description>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.jiuji.pick</groupId>
            <artifactId>pick-common</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.pick</groupId>
            <artifactId>pick-service</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <artifactId>common-utils</artifactId>
            <groupId>com.jiuji.tc</groupId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.jiuji.pick.web.PickWebApplication</mainClass>
                </configuration>
            </plugin>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <dockerDirectory>src/main/docker</dockerDirectory>
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                    </resources>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>

    </build>

</project>

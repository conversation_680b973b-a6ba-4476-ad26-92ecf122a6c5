package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @function:
 * @description: MaterialEnum.java
 * @date: 2021/05/07
 * @author: sunfayun
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AccountTypeEnum {

    /***
     *
     */
    PUBLIC(1, "对公"),
    PRIVATE(2, "对私");

    private int code;
    private String desc;

    public static String getNameByValue(int value) {
        for (AccountTypeEnum materialEnum : AccountTypeEnum.values()) {
            if(materialEnum.getCode() == value) {
                return materialEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }


    /**
     * 枚举转换成map
     * @return
     */
    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        AccountTypeEnum[] values = AccountTypeEnum.values();
        for (AccountTypeEnum paramEnum:values) {
            map.put(paramEnum.getCode()+"",paramEnum.getDesc());
        }
        return map;
    }
}

package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description 质保政策类型和商品类型映射枚举
 * <AUTHOR>
 * @Date 2021/11/11
 */
@Getter
@AllArgsConstructor
public enum WarrantyPolicyEnum {

    BULKY(0, 1,"大件质保政策"),
    SMALL(1, 0,"小件质保政策"),
    FIX_ASSETS(2, 3,"资产质保政策"),
    COMMON_ASSETS(2, 4,"资产质保政策"),
    VIRTUAL(3, 2,"虚拟商品质保政策"),
    REPAIR_PART(4, 5,"维修配件质保政策");

    /**
     * 质保政策类型
     */
    private int warrantyCode;
    /**
    * 商品类型
    */
    private int productCode;
    /**
    * 质保名称
    */
    private String desc;

    public static int getWarrantyTypeByProductType(int code) {
        for (WarrantyPolicyEnum warrantyPolicyEnum : WarrantyPolicyEnum.values()) {
            if(warrantyPolicyEnum.getProductCode() == code) {
                return warrantyPolicyEnum.getWarrantyCode();
            }
        }
        return 0;
    }

    public static String getWarrantyTypeDesc(int code) {
        for (WarrantyPolicyEnum warrantyPolicyEnum : WarrantyPolicyEnum.values()) {
            if(warrantyPolicyEnum.getWarrantyCode() == code) {
                return warrantyPolicyEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }

}

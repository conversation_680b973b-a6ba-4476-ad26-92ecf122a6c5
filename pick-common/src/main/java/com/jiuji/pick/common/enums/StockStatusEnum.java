package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum StockStatusEnum {
    ZERO(0,"预定"),
    ONE(1,"现货"),
    TWO(2,"缺货");


    private int code;
    private String desc;


    /**
     * 枚举转换成map
     * @return
     */
    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        StockStatusEnum[] values = StockStatusEnum.values();
        for (StockStatusEnum paramEnum:values) {
            map.put(paramEnum.getCode()+"",paramEnum.getDesc());
        }
        return map;
    }
}

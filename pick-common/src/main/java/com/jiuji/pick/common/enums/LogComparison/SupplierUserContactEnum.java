package com.jiuji.pick.common.enums.LogComparison;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SupplierUserContactEnum {

    NAME("name","联系人姓名"),
    PHONE("phone","联系人电话"),
    REMARK("remark","备注");


    private String code;
    private String message;

    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        SupplierUserContactEnum[] values = SupplierUserContactEnum.values();
        for (SupplierUserContactEnum paramEnum:values) {
            map.put(paramEnum.getCode(),paramEnum.getMessage());
        }
        return map;
    }
}

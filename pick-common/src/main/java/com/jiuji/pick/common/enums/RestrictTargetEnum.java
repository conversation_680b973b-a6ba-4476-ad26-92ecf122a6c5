package com.jiuji.pick.common.enums;

import com.jiuji.pick.common.enums.LogComparison.SupplierUserAccountEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/11
 * @Time 14:15
 */
@Getter
@AllArgsConstructor
public enum RestrictTargetEnum {
    /***
     *
     */
    PARTNER(0,"合作伙伴"),
    SUPPLIER(1,"供应商");

    private int code;
    private String desc;


    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        RestrictTargetEnum[] values = RestrictTargetEnum.values();
        for (RestrictTargetEnum paramEnum:values) {
            map.put(paramEnum.getCode()+"",paramEnum.getDesc());
        }
        return map;
    }

}

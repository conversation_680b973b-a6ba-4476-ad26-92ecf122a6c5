package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @function:
 * @description: ProductStatusEnum.java
 * @date: 2021/04/29
 * @author: sunfayun
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum ProductStatusEnum {
    /***
     *
     */
    WAIT_UP(0, "待上架"),
    UP(1, "上架"),
    DOWN(2, "下架");

    private int code;
    private String desc;

    public static String getProductStatusName(int status) {
        for (ProductStatusEnum productStatusEnum : ProductStatusEnum.values()) {
            if(productStatusEnum.getCode() == status) {
                return productStatusEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }
}

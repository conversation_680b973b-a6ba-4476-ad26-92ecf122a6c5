package com.jiuji.pick.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @function: 功能开关注解
 * @description: FunctionSwitch.java
 * @date: 2021/04/25
 * @author: sunfayun
 * @version: 1.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FunctionSwitch {

    /**
     * 具体的功能名称，需要和SwitchConfig类中配置的字段名字一致
     * @return
     */
    String name();

}

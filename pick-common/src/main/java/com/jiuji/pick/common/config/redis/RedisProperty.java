package com.jiuji.pick.common.config.redis;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * <p>
 * redis属性类
 * </p>
 *
 * <AUTHOR>
 * @since 2020/8/14
 */
@Data
@Component
@ConfigurationProperties(prefix = "spring.redis",ignoreInvalidFields = true)
public class RedisProperty {

    private String host;

    private String port;

    private String password;

    private int database;

    private Cluster cluster;

    @Data
    public static class Cluster {
        private String nodes;
    }



}

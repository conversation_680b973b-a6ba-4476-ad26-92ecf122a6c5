package com.jiuji.pick.common.utils;

import com.alibaba.fastjson.JSON;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author:miaoshaoxuan
 * @Description: 商品相关数据回调函数
 * @Date 2020/12/24
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductSynchronizeUtils {

    private static final String PRODUCT_SYNCHRONIZE_URL = "https://m.9ji.com/web/api/synchronize/product/data/callback";

    public static void callBack() {
        try {
            Map<String, String> map = new HashMap<>(1);
            map.put("url", getCurrentMUrl());
            HttpClientUtils.postJson(PRODUCT_SYNCHRONIZE_URL, JSON.toJSONString(map));
        } catch (Exception e) {
            log.warn("callBack error", e);
        }
    }

    public static String getCurrentMUrl() {
        if (RequestContextHolder.getRequestAttributes() == null) {
            return null;
        }
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return "https://" + "ddd" + request
                .getRequestURI() + "?" + request.getQueryString();
    }
}

package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/19
 * @Time 17:01
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum OrderPriceTypeEnum {

    //0:未税,1:含税',
    UNTAXED(0, "未税"),
    TAX_INCLUDED(1, "含税");


    private Integer code;
    private String desc;

    public static String getDescByCode(Integer code) {
        for (OrderPriceTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

    /**
     * 枚举转换成list
     * @return
     */
    public static List<EnumV0> getList(){
        ArrayList<EnumV0> enumV0s = new ArrayList<>();
        OrderPriceTypeEnum[] values = OrderPriceTypeEnum.values();
        for (OrderPriceTypeEnum paramEnum:values) {
            EnumV0 enumV0 = new EnumV0();
            enumV0.setCode(paramEnum.getCode());
            enumV0.setMessage(paramEnum.getDesc());
            enumV0s.add(enumV0);
        }
        return enumV0s;
    }
}

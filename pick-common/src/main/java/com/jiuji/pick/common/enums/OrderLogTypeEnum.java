package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 订单操作日志类型枚举
 *
 * <AUTHOR>
 * @since 2021-5-20
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum OrderLogTypeEnum {

    /**
     * 生成订单
     */
    CREATE(0, "生成订单"),
    /**
     * 生成采购单
     */
    GENERATE_NO(1, "生成采购单"),
    /**
     * 取消
     */
    CANCEL(2, "取消"),
    /**
     * 发货
     */
    DELIVERY(3, "发货"),
    /**
     * 完成
     */
    COMPLETED(4, "完成"),
    /**
     * 其他
     */
    OTHER(5,"其他");

    private Integer code;
    private String desc;

}

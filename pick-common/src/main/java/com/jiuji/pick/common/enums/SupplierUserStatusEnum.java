package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @function: 供应商状态枚举
 * @date: 2021/04/25
 * @author: Lbj
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SupplierUserStatusEnum {

    /***
     * 待审核
     */
    REGISTERED(0, "已注册"),
    UNCHECKED(1, "待审核"),
    PASS(2, "审核通过"),
    REJECT(3, "审核拒绝");

    private int code;
    private String desc;

    /***
     * @description: 转换枚举类
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:54
     */
    public static SupplierUserStatusEnum pare(int status){
        for(SupplierUserStatusEnum item : SupplierUserStatusEnum.values()){
            if(status == item.getCode()){
                return item;
            }
        }
        return null;
    }
    /***
     * @description: 转换描述
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:53
     */
    public static String pareDesc(int status){
        for(SupplierUserStatusEnum item : SupplierUserStatusEnum.values()){
            if(status == item.getCode()){
                return item.getDesc();
            }
        }
        return "";
    }
}

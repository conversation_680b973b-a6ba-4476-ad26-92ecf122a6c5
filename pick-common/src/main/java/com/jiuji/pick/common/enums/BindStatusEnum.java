package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

/**
 * @function:
 * @description: BindStatusEnum.java
 * @date: 2021/05/06
 * @author: sunfayun
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum BindStatusEnum {

    /***
     *
     */
    WAIT(0, "待审核"),
    BIND(1, "已通过"),
    REJECT(2, "已拒绝"),
    UNBOUND(3, "解绑");

    private int code;
    private String desc;

    public static String getStatusName(int status) {
        for (BindStatusEnum bindStatusEnum : BindStatusEnum.values()) {
            if(bindStatusEnum.getCode() == status) {
                return bindStatusEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }

}

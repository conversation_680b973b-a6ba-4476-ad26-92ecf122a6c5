package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/11
 * @Time 14:15
 */
@Getter
@AllArgsConstructor
public enum RestrictTypeEnum {
    /***
     *
     */
    WHITELIST(0,"白名单"),
    BLACKLIST(1,"黑名单");

    private int code;
    private String desc;

    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        RestrictTypeEnum[] values = RestrictTypeEnum.values();
        for (RestrictTypeEnum paramEnum:values) {
            map.put(paramEnum.getCode()+"",paramEnum.getDesc());
        }
        return map;
    }

}

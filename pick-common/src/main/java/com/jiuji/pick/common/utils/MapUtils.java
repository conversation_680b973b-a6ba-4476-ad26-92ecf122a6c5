/**
 * Copyright 2018 人人开源 http://www.renren.io
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package com.jiuji.pick.common.utils;


import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;


/**
 * Map工具类
 *
 * @<NAME_EMAIL>
 * @since 2.0.0
 */
@Slf4j
public class MapUtils extends HashMap<String, Object> {

    @Override
    public MapUtils put(String key, Object value) {
        super.put(key, value);
        return this;
    }

    /**
     * 对象转map
     * @param object
     * @return
     */
    public static Map<String,Object> entityToMap(Object object){
        Map<String,Object> map = new HashMap<>(16);
        Field[] fields = object.getClass().getDeclaredFields();
        if (fields.length <= 0){
            return map;
        }
        for (Field field : fields) {
            try {
                boolean flag = field.isAccessible();
                field.setAccessible(true);
                Object o = field.get(object);
                map.put(field.getName(),o);
                field.setAccessible(flag);
            } catch (IllegalAccessException e) {
                log.error("对象转map异常：{}",e.getMessage());
            }
        }
        return map;
    }

}

package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * token过期配置枚举
 * 逻辑：hours是真正的过期时间，用来检查token用缓存的过期时间;但是为了减少Redis交互，设置checkHours属性，每隔对应的时间就刷新一下Redis;
 * redisTTLHours是token的ttl时间，应该比hours+checkHours大，避免token更新不及时造成用户token失效的问题
 * </p>
 *
 * <AUTHOR>
 * @since 2020/9/8
 */
@AllArgsConstructor
public enum PlatformSourceEnum {
    /***
     *
     */
    PC("PC", "web", 3, 1, 5),
    M("M", "m端", 72, 2, 75),
    ANDROID("Android", "Android", 72, 2, 75),
    ios("iOS", "iOS", 72, 2, 75);
    private String code;
    private String desc;
    private Integer hours;  // token失效时间
    private Integer checkHours; // token更新间隔时间
    private Integer redisTTLHours; // token设置TTL过期时间

    private static final Map<String, PlatformSourceEnum> MAP = new HashMap<>();

    static {
        for (PlatformSourceEnum e : PlatformSourceEnum.values()) {
            MAP.put(e.getCode(), e);
        }
    }

    public static PlatformSourceEnum getByCode(String code) {
        PlatformSourceEnum e = MAP.get(code);
        return e == null ? PC : e;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getHours() {
        return hours;
    }

    public Integer getCheckHours() {
        return checkHours;
    }

    public Integer getRedisTTLHours() {
        return redisTTLHours;
    }
}

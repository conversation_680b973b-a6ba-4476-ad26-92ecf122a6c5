package com.jiuji.pick.common.utils;

import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson.JSON;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 门店接待Metabase token生成
 * <AUTHOR>
 * @Date 2022/3/11 19:04
 */
public class JwtTokenUtils {

    /**
     * 秘钥
     */
    private static final String METABASE_SECRET_KEY = "64f780b94acc9d09c2bb8ba51a6838f58ed8e1236fc68a92e435d9e3a6e15c59";

    /**
     * 生成token
     * @param resource 对应Metabase的resource
     * @param params   请求参数
     * @return
     */
    public static String createToken(Map<String, Object> resource, Map<String, Object> params){

        Map<String, Object> payload = new LinkedHashMap(3);
        payload.put("resource", resource);
        payload.put("params", params);
        // 十分钟过期
        payload.put("exp", System.currentTimeMillis() +  60000*10);
        // 固定头
        Map<String, Object> headerClaims = new HashMap<>(2);
        headerClaims.put("alg", "HS256");
        headerClaims.put("typ", "JWT");

         return Jwts.builder()
                .setPayload(JSON.toJSONString(payload))
                .setHeader(headerClaims)
                .signWith(SignatureAlgorithm.HS256, Base64Encoder.encode(METABASE_SECRET_KEY))
                .compact();
    }
}

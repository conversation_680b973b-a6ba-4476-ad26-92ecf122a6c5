package com.jiuji.pick.common.bo;


import lombok.Data;

import java.util.Date;


/**
 * @function:
 * @description: 合作伙伴token信息
 * @date: 2021/04/29
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class PartnerTokenInfo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long xtenant;

    /**
     * 租户来源，oa/小型SAAS
     */
    private Integer source;

    /**
     * 名称
     */
    private String name;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 登陆时间
     */
    private Date loginTime;

    /**
     * 超时时间
     */
    private Date expireDate;

    /***
     *  oa 登陆用户id
     */
    private Long loginOAUserId;
    /***
     *  oa 登陆用户
     */
    private String loginOAUserName;

    /**
     * neo请求头参数，token
     */
    private String token;

    /**
     * neo请求头参数，域名
     */
    private String host;

    /**
     * OA域名
     */
    private String oaHost;

}

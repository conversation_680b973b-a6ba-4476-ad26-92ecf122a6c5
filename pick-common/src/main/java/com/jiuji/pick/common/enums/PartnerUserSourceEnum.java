package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @function: 合作伙伴来源枚举
 * @date: 2021/04/25
 * @author: Lbj
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PartnerUserSourceEnum {

    /***
     *
     */
    OA(1, "OA"),
    SMALL_SASS(2, "小型sass");

    private int code;
    private String desc;

    /***
     * @description: 转换枚举类
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:54
     */
    public static PartnerUserSourceEnum pare(int status){
        for(PartnerUserSourceEnum item : PartnerUserSourceEnum.values()){
            if(status == item.getCode()){
                return item;
            }
        }
        return null;
    }
    /***
     * @description: 转换描述
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:53
     */
    public static String pareDesc(int status){
        for(PartnerUserSourceEnum item : PartnerUserSourceEnum.values()){
            if(status == item.getCode()){
                return item.getDesc();
            }
        }
        return "";
    }
}

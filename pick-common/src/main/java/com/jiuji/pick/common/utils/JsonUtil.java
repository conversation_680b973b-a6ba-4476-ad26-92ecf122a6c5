package com.jiuji.pick.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.jiuji.pick.common.exception.BizException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.cglib.beans.BeanMap;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class JsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    public static final String SERVICE_EXCEPTION = "服务器异常";

    static {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    public static <T> T json2Object(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("JsonUtil json2Object失败，json串：" + json + " 原因：" + e.getMessage(), e);
            throw new BizException(SERVICE_EXCEPTION + e.getMessage());
        }
    }


    /**
     * 序列化java对象
     *
     * @param entity
     * @param <T>
     * @return
     */
    public static <T> String object2Json(T entity) {
        try {
            return objectMapper.writeValueAsString(entity);
        } catch (IOException e) {
            log.error("JsonUtil object2Json，原因：" + e.getMessage(), e);
            throw new BizException(SERVICE_EXCEPTION + e.getMessage());
        }
    }

    public static <T> T json2Object(String json, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("JsonUtil toCollection，原因：" + e.getMessage(), e);
            throw new BizException(SERVICE_EXCEPTION + e.getMessage());

        }
    }

    /**
     * json string 转换为 map 对象
     */
    public static Map<String, Object> jsonToMap(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return new HashMap<>();
        }
        Map<String, Object> map;
        try {
            map = objectMapper.readValue(jsonString, new TypeReference<HashMap<String, Object>>() {
            });
        } catch (IOException e) {
            log.error("JsonUtil toCollection，原因：" + e.getMessage(), e);
            throw new BizException(SERVICE_EXCEPTION + e.getMessage());
        }
        return map;
    }

    /**
     * json string 转换为 map 对象
     */
    public static Map<Integer, Object> jsonToMap2(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return new HashMap<>();
        }
        Map<Integer, Object> map;
        try {
            map = objectMapper.readValue(jsonString, new TypeReference<HashMap<Integer, Object>>() {
            });
        } catch (IOException e) {
            log.error("JsonUtil toCollection，原因：" + e.getMessage(), e);
            throw new BizException(SERVICE_EXCEPTION + e.getMessage());
        }
        return map;
    }

    public static <T> String object2JsonNoException(T entity) {
        if (entity == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(entity);
        } catch (IOException e) {
            log.error("JsonUtil object2Json，原因：" + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将对象转换为map
     *
     * @param bean
     * @return
     */
    public static <T> Map<String, Object> beanToMap(T bean) {
        Map<String, Object> map = Maps.newHashMap();
        if (bean != null) {
            BeanMap beanMap = BeanMap.create(bean);
            for (Object key : beanMap.keySet()) {
                map.put(key.toString(), beanMap.get(key));
            }
        }
        return map;
    }
}

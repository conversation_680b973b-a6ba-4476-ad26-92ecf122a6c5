package com.jiuji.pick.common.component.lock;

import com.jiuji.pick.common.utils.RedLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Lock接口实现类
 */
@Slf4j
@Component
public class RedissonDistributedLocker implements DistributedLocker {

    @Resource
    private RedissonClient redissonClient;

    public RedissonDistributedLocker() {
        RedLockUtil.setLocker(this);
    }

    @Override
    public boolean tryLock(String lockKey, TimeUnit unit, int waitTime, int leaseTime) {
        RLock lock = redissonClient.getFairLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (Exception e) {
            if(lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.info("tryLock has InterruptedException:", e);
            return false;
        }
    }

    @Override
    public void unlock(String lockKey) {
        RLock lock = redissonClient.getFairLock(lockKey);
        if(lock.isHeldByCurrentThread()) {
            log.info("当前线程解锁, lockKey:{}", lockKey);
            lock.unlock();
        }
    }

    @Override
    public boolean isLocked(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        return lock.isLocked();
    }

}
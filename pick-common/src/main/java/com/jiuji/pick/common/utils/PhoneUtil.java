package com.jiuji.pick.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PhoneUtil {

    private final static String PHONE_REGEX = "(^(0[0-9]{2,3}\\-)?([2-9][0-9]{6,7})+(\\-[0-9]{1,4})?$)|(^((\\(\\d{3}\\))|(\\d{3}\\-))?(1[35789]\\d{9})$)|(^(400)-(\\d{3})-(\\d{4})(.)(\\d{1,4})$)|(^(400)-(\\d{3})-(\\d{4}$))";

    /**
     * 验证手机号
     * @param phone
     * @return
     */
    public static boolean isMobile(String phone) {
        if(StringUtils.isBlank(phone)) {
            return false;
        }
        String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(166)|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[8|9]))\\d{8}$";
        if (phone.length() != 11) {
            return false;
        } else {
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(phone);
            boolean isMatch = m.matches();
            return isMatch;
        }
    }

    /**
     * 电话号手机号码验证
     * <AUTHOR>
     * 2016年12月5日下午4:34:21
     * @param  str
     * @return 验证通过返回true
     */
    public static boolean isPhone(String str) {
        if(StringUtils.isBlank(str)) {
            return false;
        }
        Pattern phoneNo = Pattern.compile(PHONE_REGEX);
        Matcher m = phoneNo.matcher(str);
        return m.matches();
    }
}

package com.jiuji.pick.common.utils;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * @function:
 * @description: GenerateNoUtil.java
 * @date: 2021/10/12
 * @author: sunfayun
 * @version: 1.0
 */
public class GenerateNoUtil {

    private final static AtomicInteger floatValue = new AtomicInteger();

    public static long generateOrderNo() {
        return System.currentTimeMillis() + floatValue.incrementAndGet();
    }

}

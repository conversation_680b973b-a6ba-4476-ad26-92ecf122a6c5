package com.jiuji.pick.common.component.interceptor;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import com.jiuji.pick.common.annotation.FunctionSwitch;
import com.jiuji.pick.common.config.apollo.SwitchConfig;
import com.jiuji.pick.common.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * @function: 降级开关拦截器
 * @description: SwitchInterceptor.java
 * @date: 2021/04/25
 * @author: sunfayun
 * @version: 1.0
 */
@Slf4j
@Component
public class SwitchInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private SwitchConfig switchConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            // 判断系统开关是否打开
            if(!switchConfig.isSystem()) {
                this.printWriterData(response, "系统暂不可用，请稍后再试");
                log.warn("系统开关关闭，系统暂不可用");
                return false;
            }
            // 根据URL前缀判断模块开关
            String requestPath = request.getRequestURI();
            if(StringUtils.isBlank(requestPath)) {
                this.printWriterData(response, "请求地址错误");
                log.warn("开关校验拦截器获取请求URI结果为空");
                return false;
            }
            if(requestPath.startsWith("/pick/api/order")) {
                if(!switchConfig.isOrderModule()) {
                    this.printWriterData(response, "订单业务暂不可用，请稍后再试");
                    log.warn("订单模块开关关闭，业务暂不可用");
                    return false;
                }
            }
            // 功能开关判断
            FunctionSwitch functionSwitch;
            if(handler instanceof HandlerMethod) {
                functionSwitch = ((HandlerMethod) handler).getMethodAnnotation(FunctionSwitch.class);
            } else {
                return super.preHandle(request, response, handler);
            }
            if(functionSwitch == null) {
                return super.preHandle(request, response, handler);
            }
            String functionName = functionSwitch.name();
            if(StringUtils.isBlank(functionName)) {
                return super.preHandle(request, response, handler);
            }
            Object functionSwitchValue = ReflectUtil.getFieldValue(switchConfig, functionName);
            if(functionSwitchValue != null && functionSwitchValue instanceof Boolean) {
                if(!(boolean)functionSwitchValue) {
                    this.printWriterData(response, "该功能暂不可用，请稍后再试");
                    log.warn("功能开关关闭，业务暂不可用");
                    return false;
                }
            }
            return super.preHandle(request, response, handler);
        } catch (Exception e) {
            log.error("开关判断拦截器发生异常，exception:", e);
            return super.preHandle(request, response, handler);
        }
    }

    private void printWriterData(HttpServletResponse response, String message) throws IOException {
        //设置编码格式
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=UTF-8");
        PrintWriter pw = response.getWriter();
        pw.println(JSONUtil.toJsonStr(Result.errorInfo(message)));
    }


}

package com.jiuji.pick.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum NewOperateLogInfoTypeEnum {





    SUPPLY_MANAGEMENT(1, "供应商供应信息"),
    SUPPLIER_INFORMATION(2, "供应商信息"),
    ORDER_MANAGEMENT(3, "订单管理"),
    GOODS_ON_AND_OFF_SHELVES(4, "商品库管理上下架"),
    COMMODITY_WAREHOUSE_MANAGEMENT(5, "商品库新增"),
    SUPPLIER_BOUND_GOODS_QUERY(6, "供应商绑定商品信息"),
    BLACK_AND_WHITE_LIST_MANAGEMENT(7, "商品黑白名单添加"),
    SUPPLIER_LIST(8, "供应商详情"),
    PARTNER_LIST_ORDER(9, "合作伙伴列表下单"),
    PARTNER_LIST(10, "合作伙伴编辑"),
    PURCHASE_ORDER_DATA_QUERY(11, "采购单数据查询");


    private Integer code;
    private String desc;


    /**
     * 枚举转换成list
     * @return
     */
    public static List<EnumV0> getList(){
        ArrayList<EnumV0> enumV0s = new ArrayList<>();
        NewOperateLogInfoTypeEnum[] values = NewOperateLogInfoTypeEnum.values();
        for (NewOperateLogInfoTypeEnum paramEnum:values) {
            EnumV0 enumV0 = new EnumV0();
            enumV0.setCode(paramEnum.getCode());
            enumV0.setMessage(paramEnum.getDesc());
            enumV0s.add(enumV0);
        }
        return enumV0s;
    }
}

package com.jiuji.pick.common.constant;

import java.math.BigDecimal;

/**
 * 魔法值
 *
 * <AUTHOR>
 * @since 2021-6-2
 */
public final class MagicalValueConstant {

    public static final int INT_0 = 0;
    public static final int INT_1 = 1;
    public static final int INT_2 = 2;
    public static final int INT_3 = 3;
    public static final int INT_4 = 4;
    public static final int INT_5 = 5;
    public static final int INT_6 = 6;
    public static final int INT_7 = 7;
    public static final int INT_8 = 8;
    public static final int INT_9 = 9;
    public static final int INT_10 = 10;
    public static final int INT_50 = 50;
    public static final int INT_200 = 200;
    public static final int INT_5000 = 5000;

    public static final long LONG_23 = 23L;

    public static final String STRING_CODE = "code";

    public static final BigDecimal BD_9999 = BigDecimal.valueOf(9999);

    private MagicalValueConstant() {
        throw new IllegalStateException("Utility class");
    }

}

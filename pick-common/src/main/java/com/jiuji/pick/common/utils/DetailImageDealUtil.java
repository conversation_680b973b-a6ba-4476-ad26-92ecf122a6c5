package com.jiuji.pick.common.utils;

import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

/**
 * @function:
 * @description: DetailImageDealUtil.java
 * @date: 2021/05/25
 * @author: sunfayun
 * @version: 1.0
 */
public class DetailImageDealUtil {

    public static String getPicDetail(String detail) {
        if (StringUtils.isBlank(detail)) {
            return null;
        }
        Document document = Jsoup.parse(detail);
        if(document == null) {
            return detail;
        }
        Elements videos = document.getElementsByTag("video");
        videos.forEach(video -> {
            String src = video.attr("src");
            String poster = video.attr("poster");
            if (StringUtils.isNotBlank(poster) && !"undefined".equals(poster) && !poster.contains("https://img") && !poster.contains("http://img")) {
                String newPoster = RegExUtils.replaceAll(poster, "^((https?://www.9ji.com)|(https?://img2.ch999img.com)|(https?://www.yaya.cn))?", "https://img2.ch999img.com/");
                video.attr("poster", newPoster);
            }
            if(StringUtils.isNotBlank(src) && !src.contains("https://img") && !src.contains("http://img")){
                String newSrc = RegExUtils.replaceAll(src, "^((https?://www.9ji.com)|(https?://img2.ch999img.com)|(https?://www.yaya.cn))?", "https://img2.ch999img.com/");
                video.attr("src", newSrc);
            }
        });

        document.getElementsByTag("img").forEach(img -> {
            String src = img.attr("src");
            String newSrc = RegExUtils.replaceAll(src, "^/pic", "https://img2.ch999img.com/pic");
            img.attr("src", newSrc);
        });

        return document.outerHtml();
    }

}

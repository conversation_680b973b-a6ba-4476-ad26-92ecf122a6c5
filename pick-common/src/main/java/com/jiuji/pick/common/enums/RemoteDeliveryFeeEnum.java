package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RemoteDeliveryFeeEnum {



    NO_FREIGHT(0,"不收取运费"),
    FREIGHT(1,"收取运费");


    private int code;
    private String desc;


    /**
     * 枚举转换成map
     * @return
     */
    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        RemoteDeliveryFeeEnum[] values = RemoteDeliveryFeeEnum.values();
        for (RemoteDeliveryFeeEnum paramEnum:values) {
            map.put(paramEnum.getCode()+"",paramEnum.getDesc());
        }
        return map;
    }
}

package com.jiuji.pick.common.enums.LogComparison;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class LogDifferences {
    /**
     * 老实体
     */
    private Object oldEntity;
    /**
     * 新实体
     */
    private Object newEntity;
    /**
     * 参数转换成中文
     */
    private Map paramMap;
    /**
     * 枚举类型的转换
     */
    private  Map<String, Map<String,String>> transformationMap;

    /**
     * 排除字段
     */
    private List<String> excludeParams;
}

package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @function:
 * @description: MessageReadStatusEnum.java
 * @date: 2021/05/14
 * @author: sun<PERSON>yun
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum MessageReadStatusEnum {

    /***
     *
     */
    HAVE_READ(0, "已读"),
    UNREAD(1, "未读");

    public Integer code;
    public String desc;

}

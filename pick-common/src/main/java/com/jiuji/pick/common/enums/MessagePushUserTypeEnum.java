package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @function:
 * @description: MessagePushUserTypeEnum.java
 * @date: 2021/05/14
 * @author: sun<PERSON>yun
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum MessagePushUserTypeEnum {

    /***
     *
     */
    SUPPLIER(0, "供应商"),
    PARTNER(1, "合作伙伴");

    public Integer code;
    public String desc;

    /***
     * @description: 转换描述
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:53
     */
    public static String pareDesc(Integer code){
        for(MessagePushUserTypeEnum item : MessagePushUserTypeEnum.values()){
            if(code.equals(item.getCode())){
                return item.getDesc();
            }
        }
        return "";
    }
}

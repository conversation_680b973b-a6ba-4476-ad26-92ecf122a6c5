package com.jiuji.pick.common.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * @function:
 * @description: ApplicationContextUtil.java
 * @date: 2021/01/08
 * @author: sunfayun
 * @version: 1.0
 */
@Component
public class ApplicationContextUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationContextUtil.applicationContext = applicationContext;
    }

    public static <T> T popBean(Class<T> clazz) {
        if(applicationContext == null) {return null;}
        return applicationContext.getBean(clazz);
    }

    public static <T> T popBean(String name, Class<T> clazz) {
        if(applicationContext == null) {return null;}
        return applicationContext.getBean(name, clazz);
    }

}

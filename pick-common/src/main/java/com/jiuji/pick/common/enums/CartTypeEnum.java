package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 购物车类型枚举
 *
 * <AUTHOR>
 * @since 2021-5-20
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum  CartTypeEnum {

    /**
     * 普通购买
     */
    NORMAL(1, "普通购买"),
    /**
     * 直接购买
     */
    DIRECT_BUY(2, "直接购买");

    private Integer code;
    private String desc;

}

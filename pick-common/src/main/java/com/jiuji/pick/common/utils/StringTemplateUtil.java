package com.jiuji.pick.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;

import java.util.Map;

import static com.jiuji.pick.common.constant.CommonConstant.DIGITAL_ROAD_KING;
import static com.jiuji.pick.common.constant.CommonConstant.NO_DIGITAL_ROAD_KING;

/**
 * @function: 字符串模板替换
 * @description: StringTemplateUtils.java
 * @date: 2021/04/23
 * @author: sunfayun
 * @version: 1.0
 */
@Slf4j
public class StringTemplateUtil {

    public static String renderStringTemplate(String source, Map<String, Object> templateValue) {
        if(StringUtils.isBlank(source) || MapUtils.isEmpty(templateValue)) {
            log.warn("renderStringTemplate param is error");
            return StringUtils.EMPTY;
        }
        try {
            return new StringSubstitutor(templateValue).replace(source);
        } catch (Exception e) {
            log.error("renderStringTemplate has exception:",e);
            return StringUtils.EMPTY;
        }
    }

    public static String roadKing(String link){
        if (StringUtils.isNoneBlank(link)){
            link = link.replaceAll("(http|https)://.*\\.9xun\\.com/", "");
            if (StringUtils.isNumeric(link)){
                if(!link.startsWith(NO_DIGITAL_ROAD_KING) && !link.startsWith(DIGITAL_ROAD_KING)) {
                    link = NO_DIGITAL_ROAD_KING + link;
                }
            }else {
                if(!link.startsWith(NO_DIGITAL_ROAD_KING) && !link.startsWith(DIGITAL_ROAD_KING)) {
                    link = DIGITAL_ROAD_KING + link;
                }
            }
        }
        return link;
    }

}

package com.jiuji.pick.common.config.apollo;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * 产品类型分类
 * @function:
 * @description: CategoryConfig.java
 * @date: 2021/10/14
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class CategoryConfig {

    /**
     * 虚拟商品分类
     */
    @Value("${productType.virtual}")
    private String virtualProductCategory;

    /**
     * 固定资产分类
     */
    @Value("${productType.fixAssets}")
    private String fixAssetsProductCategory;

    /**
     * 常用资产分类
     */
    @Value("${productType.commonAssets}")
    private String commonAssetsProductCategory;
    /**
     * 维修配件分类
     */
    @Value("${productType.repairPart}")
    private String repairPartCategory;

}

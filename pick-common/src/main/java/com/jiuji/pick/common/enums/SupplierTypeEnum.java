package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @function: 供应商类型枚举
 * @date: 2021/04/25
 * @author: Lbj
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SupplierTypeEnum {

    /***
     * 待审核
     */
    SMALL("0", "小型"),
    MIDDLE("1", "中型"),
    LARGE("2", "大型");

    private String code;
    private String desc;

    /***
     * @description: 转换枚举类
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:54
     */
    public static SupplierTypeEnum pare(String code){
        for(SupplierTypeEnum item : SupplierTypeEnum.values()){
            if(code.equals(item.getCode())){
                return item;
            }
        }
        return null;
    }
    /***
     * @description: 转换描述
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:53
     */
    public static String pareDesc(String code){
        for(SupplierTypeEnum item : SupplierTypeEnum.values()){
            if(code.equals(item.getCode())){
                return item.getDesc();
            }
        }
        return "";
    }
}

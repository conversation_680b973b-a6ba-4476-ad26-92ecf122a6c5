/*
 * Copyright (c) 2006 - 2014, EPIC Team
 * 
 * All rights reserved.
 */
package com.jiuji.pick.common.utils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 线程相关工具类.
 *
 * <AUTHOR> href="mailto:<EMAIL>">Yuanjun.Li</a>
 */
public final class ThreadUtil {

    /**
     * 私有构造器
     */
    private ThreadUtil() {
    }

    /**
     * sleep等待,单位毫秒,忽略InterruptedException.
     *
     * @param millis 等待多少毫秒
     */
    public static void sleep(final long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            // Ignore.
            Thread.currentThread().interrupt();
        }
    }

    /**
     * sleep等待,忽略InterruptedException.
     *
     * @param duration 等待多少单位时间
     * @param unit 时间单位
     */
    public static void sleep(final long duration, final TimeUnit unit) {
        try {
            Thread.sleep(unit.toMillis(duration));
        } catch (InterruptedException e) {
            // Ignore.
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 按照ExecutorService JavaDoc示例代码编写的Graceful Shutdown方法.
     * 先使用shutdown, 停止接收新任务并尝试完成所有已存在任务.
     * 如果超时, 则调用shutdownNow, 取消在workQueue中Pending的任务,并中断所有阻塞函数.
     * 另对在shutdown时线程本身被调用中断做了处理.
     *
     * @param pool ExecutorService 池对象
     * @param shutdownTimeout 关闭超时时间
     * @param shutdownNowTimeout 立即关闭超时时间
     * @param timeUnit 时间单位
     *
     */
    public static void gracefulShutdown(final ExecutorService pool, final int shutdownTimeout,
            final int shutdownNowTimeout, final TimeUnit timeUnit) {
        pool.shutdown(); // Disable new tasks from being submitted
        try {
            // Wait a while for existing tasks to terminate
            if (!pool.awaitTermination(shutdownTimeout, timeUnit)) {
                pool.shutdownNow(); // Cancel currently executing tasks
                // Wait a while for tasks to respond to being cancelled
                if (!pool.awaitTermination(shutdownNowTimeout, timeUnit)) {
                    System.err.println("Pool did not terminate");
                }
            }
        } catch (InterruptedException ie) {
            // (Re-)Cancel if current thread also interrupted
            pool.shutdownNow();
            // Preserve interrupt status
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 直接调用shutdownNow的方法, 取消在workQueue中Pending的任务,并中断所有阻塞函数.
     *
     * @param pool ExecutorService 池对象
     * @param timeout 超时时间
     * @param timeUnit 时间单位
     */
    public static void normalShutdown(final ExecutorService pool, final int timeout, final TimeUnit timeUnit) {
        try {
            pool.shutdownNow();
            if (!pool.awaitTermination(timeout, timeUnit)) {
                System.err.println("Pool did not terminate");
            }
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
    }
}

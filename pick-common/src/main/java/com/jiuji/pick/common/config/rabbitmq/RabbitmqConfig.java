package com.jiuji.pick.common.config.rabbitmq;


import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;

@Configuration
public class RabbitmqConfig {

    @Resource
    private RabbitProperties rabbitProperties;

    @Bean("connectionFactory")
    public ConnectionFactory connectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(rabbitProperties.getHost());
        connectionFactory.setPort(rabbitProperties.getPort());
        connectionFactory.setUsername(rabbitProperties.getUsername());
        connectionFactory.setPassword(rabbitProperties.getPassword());
        connectionFactory.setVirtualHost(rabbitProperties.getVirtualHost());
        connectionFactory.setPublisherReturns(rabbitProperties.isPublisherReturns());
        connectionFactory.setPublisherConfirms(rabbitProperties.isPublisherConfirms());
        return connectionFactory;
    }

    @Primary
    @Bean("rabbitTemplate")
    public RabbitTemplate rabbitTemplate(@Autowired ConnectionFactory connectionFactory) {
        return new RabbitTemplate(connectionFactory);
    }

    @Bean(name = "msgCenterConnectionFactory")
    public ConnectionFactory msgCenterConnectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(rabbitProperties.getHost());
        connectionFactory.setPort(rabbitProperties.getPort());
        connectionFactory.setUsername(rabbitProperties.getUsername());
        connectionFactory.setPassword(rabbitProperties.getPassword());
        connectionFactory.setVirtualHost(rabbitProperties.getVirtualHost());
        connectionFactory.setPublisherReturns(rabbitProperties.isPublisherReturns());
        connectionFactory.setPublisherConfirms(rabbitProperties.isPublisherConfirms());
        return connectionFactory;
    }

    @Bean(name = "msgCenterRabbitAdmin")
    public RabbitAdmin msgCenterRabbitAdmin(@Qualifier("msgCenterConnectionFactory") ConnectionFactory connectionFactory){
        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
        rabbitAdmin.setAutoStartup(false);
        return rabbitAdmin;
    }

    //配置监听链接
    @Bean(name = "msgCenterRabbitListenerContainerFactory")
    public SimpleRabbitListenerContainerFactory brabbitListenerContainerFactory3(
            @Qualifier("msgCenterConnectionFactory") ConnectionFactory connectionFactory,
            RabbitProperties config
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        RabbitProperties.Listener listenerConfig = config.getListener();
        factory.setAutoStartup(listenerConfig.getSimple().isAutoStartup());
        //手动act开启
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        if (listenerConfig.getSimple().getConcurrency() != null) {
            factory.setConcurrentConsumers(listenerConfig.getSimple().getConcurrency());
        }
        if (listenerConfig.getSimple().getMaxConcurrency() != null) {
            factory.setMaxConcurrentConsumers(listenerConfig.getSimple().getMaxConcurrency());
        }
        factory.setPrefetchCount(1);
        if (listenerConfig.getSimple().getTransactionSize() != null) {
            factory.setTxSize(listenerConfig.getSimple().getTransactionSize());
        }
        return factory;
    }

}

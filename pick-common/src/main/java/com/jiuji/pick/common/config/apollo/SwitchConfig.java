package com.jiuji.pick.common.config.apollo;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * @function:
 * @description: SwitchConfig.java
 * @date: 2021/04/21
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class SwitchConfig {

    /**
     * 系统开关
     */
    @Value("${switch.system:true}")
    private boolean system;

    /**
     * 用户模块开关
     */
    @Value("${switch.userModule:true}")
    private boolean userModule;

    /**
     * 产品模块开关
     */
    @Value("${switch.productModule:true}")
    private boolean productModule;

    /**
     * 订单模块开关
     */
    @Value("${switch.orderModule:true}")
    private boolean orderModule;

    /**
     * 提交订单功能开关
     */
    @Value("${switch.addOrderFunction:true}")
    private boolean addOrderFunction;

}

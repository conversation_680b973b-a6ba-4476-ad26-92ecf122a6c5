package com.jiuji.pick.common.enums.LogComparison;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SupplierUserEnum {

    NAME("name","公司名称"),
    SHORTNAME("shortName","公司简称"),
    PROVINCENAME("provinceName","省名称"),
    CITYNAME("cityName","城市名称"),
    DISTRICTNAME("districtName","区名称"),
    ADDRESS("address","详细地址");


    private String code;
    private String message;

    /**
     * 枚举转换成map
     * @return
     */
    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        SupplierUserEnum[] values = SupplierUserEnum.values();
        for (SupplierUserEnum paramEnum:values) {
            map.put(paramEnum.getCode(),paramEnum.getMessage());
        }
        return map;
    }
}

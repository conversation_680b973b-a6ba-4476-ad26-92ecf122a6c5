package com.jiuji.pick.common.utils;

import com.jiuji.pick.common.component.minifile.MiniFileConfig;
import com.jiuji.pick.common.constant.WebConstant;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2020/8/28 10:07
 * @Description
 * @Version 1.0.0
 */
public class MiniFileUtils {
    /**
     * 新版相对
     */
    public final static String NEW_CONTEXPATH = "newstatic/";
    private final static  String NEW_IMAGE_PATTERN = "^\\d{1,9}/.*";
    private static MiniFileConfig fileConfig = ApplicationContextUtil.popBean("miniFileConfig", MiniFileConfig.class);;

    public static String getFileUrl(String fid, String fileName) {
      if(StringUtils.isBlank(fid)||StringUtils.isBlank(fileName)){
        return "";
      }
      return fileConfig.getHttpProtocol() + fileConfig.getDcurl() + fileConfig.getContextpath() + fid
          + fileName.substring(fileName.lastIndexOf("."));
    }

    public static String getFileUrl(Object fid, Object fileName) {
      if(fid == null || fileName == null){
        return "";
      }
      return getFileUrl((String)fid, (String)fileName);
    }

    /**
     * 新版fid模式，路径newstatic
     * 旧版自定义模式，路径/pic/product/
     * 获取商品图片路径   商品的图片公用方法（视频、分类、品牌....不要用，存储路径和商品存在一些差别）
     * @param prefix 图片域名
     * @param picUrl 图片存储相对路径
     * @param size   图片存储地址
     * @return
     */
    public static String getProductImageUrl(String prefix,String picUrl,String size) {
        if (StringUtil.isBlank(picUrl)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(prefix);

        picUrl = picUrl.trim();
        //fid的模式,新版本的fid中包含一个/或者,   这里我们采取了/
        if (Pattern.matches(NEW_IMAGE_PATTERN, picUrl)) {
            sb.append(NEW_CONTEXPATH);
            sb.append(picUrl);
        } else {//旧版/pic的模式  默认440*440
            sb.append("pic/product/");
            sb.append(size);
            sb.append("/");
            sb.append(picUrl);
        }
        return sb.toString();
    }


//    /**
//     * 获取商品图片路径
//     */
//    public static String getProductImageUrl(String picUrl) {
//        return getProductImageUrl(picUrl, WebConstant.PICTURE_SIZE.PIC_440x440);
//    }
}

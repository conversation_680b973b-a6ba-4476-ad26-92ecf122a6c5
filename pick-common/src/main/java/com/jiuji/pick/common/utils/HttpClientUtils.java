/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.common.utils;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.lang.reflect.Field;
import java.net.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HttpClientUtils
 *
 * <AUTHOR>
 */
public class HttpClientUtils {

    private static final String CHARSET_UTF_8 = "UTF-8";

    private static final String CONTENT_TYPE_JSON = "application/json";

    private static Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);

    private static void doHttpClientClose(CloseableHttpClient httpClient) {
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (IOException e) {
                logger.debug("IOException", e);
            }
        }
    }

    /**
     * 发送get请求
     *
     * @param url
     * @return
     */
    public static String get(String url) {
        String res = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            URL url1 = null;
            try {
                url1 = new URL(url);
                URI uri = new URI(url1.getProtocol(), url1.getHost(), url1.getPath(), url1.getQuery(), null);
                HttpGet httpGet = new HttpGet(uri);
                httpGet.setHeader("Content-Type", "application/x-www-form-urlencoded");
                res = execute(httpClient, httpGet);
            } catch (MalformedURLException e) {
                logger.debug("MalformedURLException", e);
            } catch (URISyntaxException e) {
                logger.debug("URISyntaxException", e);
            }
        } finally {
            doHttpClientClose(httpClient);
        }
        return res;
    }

    public static String getJson(String requestUrl) {
        String msg = "";
        try {
            // 1. 得到访问地址的URL
            URL url = new URL(requestUrl);
            // 2. 得到网络访问对象java.net.HttpURLConnection
            HttpURLConnection connection = (HttpURLConnection) url
                    .openConnection();
            /* 3. 设置请求参数（过期时间，输入、输出流、访问方式），以流的形式进行连接 */
            // 设置是否向HttpURLConnection输出
            connection.setDoOutput(false);
            connection.setRequestProperty("Content-type", "application/json");
            // 设置是否从httpUrlConnection读入
            connection.setDoInput(true);
            // 设置请求方式
            connection.setRequestMethod("GET");
            // 设置是否使用缓存
            connection.setUseCaches(false);
            // 设置此 HttpURLConnection 实例是否应该自动执行 HTTP 重定向
            connection.setInstanceFollowRedirects(false);
            // 设置超时时间
            connection.setConnectTimeout(3000);
            // 连接
            connection.connect();
            // 4. 得到响应状态码的返回值 responseCode
            int code = connection.getResponseCode();
            // 5. 如果返回值正常，数据在网络中是以流的形式得到服务端返回的数据
            if (code == 200) { // 正常响应
                // 从流中读取响应信息
                BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream()));
                String line = null;

                while ((line = reader.readLine()) != null) { // 循环从流中读取
                    msg += line + "\n";
                }
                reader.close(); // 关闭流
            }
            // 6. 断开连接，释放资源
            connection.disconnect();
        } catch (Exception e) {
            logger.error("request error,url is :{}", requestUrl, e);
        }
        // 显示响应结果
        return msg;

    }

    public static String get(String url, String contentType) {
        String res = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();

        try {
            URL url1 = null;
            try {
                url1 = new URL(url);
                URI uri = new URI(url1.getProtocol(), url1.getHost(), url1.getPath(), url1.getQuery(), null);
                HttpGet httpGet = new HttpGet(uri);
                httpGet.setHeader("Content-Type", contentType);
                res = execute(httpClient, httpGet);
            } catch (MalformedURLException e) {
                logger.debug("MalformedURLException", e);
            } catch (URISyntaxException e) {
                logger.debug("URISyntaxException", e);
            }
        } finally {
            doHttpClientClose(httpClient);
        }
        return res;
    }

    /**
     * 发送post请求
     *
     * @param url    post url
     * @param params post参数
     * @return
     */
    public static String post(String url, Map<String, String> params) {
        String res = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpPost httpPost = httpPostHandler(url, params);
            res = execute(httpClient, httpPost);
        } finally {
            doHttpClientClose(httpClient);
        }
        return res;
    }

    /**
     * 发送post请求
     *
     * @param url post url
     *            post参数
     * @return
     */
    public static String post(String url, Map<String, String> headerMap, Map<String, String> bodyMap) {
        String res = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpPost httpPost = httpPostHandler(url, bodyMap);
            if (headerMap != null) {
                headerMap.forEach((key, value) -> {
                    httpPost.addHeader(key, value);
                });
            }
            res = execute(httpClient, httpPost);
        } finally {
            doHttpClientClose(httpClient);
        }
        return res;
    }

    /**
     * 发送get请求
     *
     * @param url get url
     *            get
     * @return
     */
    public static String get(String url, Map<String, String> headerMap) {
        String res = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpGet httpGet = new HttpGet(url);
            if (headerMap != null) {
                headerMap.forEach((key, value) -> {
                    httpGet.addHeader(key, value);
                });
            }
            res = execute(httpClient, httpGet);
        } finally {
            doHttpClientClose(httpClient);
        }
        return res;
    }

    /**
     * 发送post请求
     *
     * @param url    post url
     * @param object post参数
     * @return
     */
    public static String postObject(String url, Object object) {
        String res = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            Map<String, String> params = objectToMap(object);
            HttpPost httpPost = httpPostHandler(url, params);
            res = execute(httpClient, httpPost);
        } catch (Exception e) {
            logger.error("Exception", e);
        } finally {
            doHttpClientClose(httpClient);
        }
        return res;
    }

    /**
     * 发送delete请求
     *
     * @param url 请求地址
     * @return String
     */
    public static String sndDelete(String url) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpDelete del = new HttpDelete(url);
        try {
            // 提交请求并以指定编码获取返回数据
            HttpResponse httpResponse = httpClient.execute(del);
            logger.info("请求地址：" + url + "；响应状态：" + httpResponse.getStatusLine());
            HttpEntity entity = httpResponse.getEntity();
            return EntityUtils.toString(entity, CHARSET_UTF_8);
        } catch (ClientProtocolException e) {
            logger.error("协议异常,堆栈信息如下", e);
        } catch (IOException e) {
            logger.error("网络异常,堆栈信息如下", e);
        } finally {
            // 关闭连接，释放资源
            try {
                httpClient.close();
            } catch (Exception e) {
                logger.debug("Exception", e);
                httpClient = null;
            }
        }
        return null;
    }

    /**
     * post json数据
     *
     * @param url
     * @param jsonStr
     * @return
     */
    public static String postJson(String url, String jsonStr) {
        String res = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpPost httpPost = new HttpPost(url);
            StringEntity stringEntity;
            try {
                stringEntity = new StringEntity(jsonStr, Charset.forName(CHARSET_UTF_8));
                stringEntity.setContentEncoding(CHARSET_UTF_8);
            } catch (Exception e) {
                return null;
            }
            httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("Accept", CONTENT_TYPE_JSON);
            httpPost.setEntity(stringEntity);
            res = execute(httpClient, httpPost);
        } finally {
            doHttpClientClose(httpClient);
        }
        return res;
    }

    public static String postJson(String url, String jsonStr, Map<String, String> header) {
        String res = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpPost httpPost = new HttpPost(url);
            StringEntity stringEntity;
            try {
                stringEntity = new StringEntity(jsonStr, Charset.forName(CHARSET_UTF_8));
                stringEntity.setContentEncoding(CHARSET_UTF_8);
            } catch (Exception e) {
                return null;
            }
            if (header != null) {
                for (Map.Entry<String, String> headerMap : header.entrySet()) {
                    httpPost.addHeader(headerMap.getKey(), headerMap.getValue());
                }
            }
            httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("Accept", CONTENT_TYPE_JSON);
            httpPost.setEntity(stringEntity);
            res = execute(httpClient, httpPost);
        } finally {
            doHttpClientClose(httpClient);
        }
        return res;
    }

    /**
     * post json数据
     *
     * @param url     url
     * @param jsonStr RequestBody
     * @param headMap 追加head参数
     * @return 调用结果
     */
    public static String postJsonWithHead(String url, String jsonStr, Map<String, String> headMap) {
        String res;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpPost httpPost = new HttpPost(url);
            StringEntity stringEntity;
            try {
                stringEntity = new StringEntity(jsonStr, StandardCharsets.UTF_8);
                stringEntity.setContentEncoding(CHARSET_UTF_8);
            } catch (Exception e) {
                return null;
            }
            httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("Accept", CONTENT_TYPE_JSON);
            for (Map.Entry<String, String> head : headMap.entrySet()) {
                httpPost.addHeader(head.getKey(), head.getValue());
            }
            httpPost.setEntity(stringEntity);
            res = execute(httpClient, httpPost);
        } finally {
            doHttpClientClose(httpClient);
        }
        return res;
    }

    /**
     * post json数据
     *
     * @param url
     * @param jsonStr
     * @return
     */
    public static String postJsonForm(String url, String jsonStr) {
        String res = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpPost httpPost = new HttpPost(url);
            StringEntity stringEntity;
            try {
                stringEntity = new StringEntity(jsonStr, Charset.forName(CHARSET_UTF_8));
                stringEntity.setContentEncoding(CHARSET_UTF_8);
            } catch (Exception e) {
                return null;
            }
            httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
            httpPost.setHeader("Accept", CONTENT_TYPE_JSON);
            httpPost.setEntity(stringEntity);
            res = execute(httpClient, httpPost);
        } finally {
            doHttpClientClose(httpClient);
        }
        return res;
    }

    /**
     * 文件读取（读取为二进制数组）
     *
     * @param url 文件地址
     * @return 文件的二进制数组
     */
    public static byte[] download(String url) {
        byte[] data = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        ByteArrayOutputStream bos = null;
        try {
            URL url1 = null;
            try {
                url1 = new URL(url);
                URI uri = new URI(url1.getProtocol(), url1.getHost(), url1.getPath(), url1.getQuery(), null);
                HttpGet httpGet = new HttpGet(uri);
                httpGet.setHeader("Content-Type", "application/x-www-form-urlencoded");
                CloseableHttpResponse response = httpClient.execute(httpGet);
                HttpEntity entity = response.getEntity();
                bos = new ByteArrayOutputStream();
                entity.writeTo(bos);
                data = bos.toByteArray();
            } catch (MalformedURLException e) {
                logger.debug("文件下载失败！", e);
            } catch (URISyntaxException e) {
                logger.debug("文件下载失败！", e);
            } catch (ClientProtocolException e) {
                logger.debug("文件下载失败！", e);
            } catch (IOException e) {
                logger.debug("文件下载失败！", e);
            }
        } finally {
            doHttpClientClose(httpClient);
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    logger.debug("流关闭失败", e);
                }
            }
        }
        return data;
    }


    /**
     * 文件上传
     *
     * @param rootURL 根路径
     * @param reqURL  请求地址
     * @return
     */
    public static String uploadFile(String rootURL, String reqURL, MultipartFile multipartFile) {

        String info = null;
        InputStream inputStream = null;
        OutputStream outputStream = null;
        HttpURLConnection httpUrlConn = null;
        BufferedReader bufferedReader = null;
        InputStream inputStreamRequest = null;
        InputStreamReader inputStreamReader = null;

        try {

            // 连接
            URL url = new URL(rootURL + reqURL);

            httpUrlConn = (HttpURLConnection) url.openConnection();
            httpUrlConn.setDoOutput(true);
            httpUrlConn.setDoInput(true);
            httpUrlConn.setUseCaches(false);

            // 设置请求方式（GET/POST）
            httpUrlConn.setRequestMethod("POST");
            // 设置请求头信息
            httpUrlConn.setRequestProperty("Connection", "Keep-Alive");
            httpUrlConn.setRequestProperty("Charset", "UTF-8");

            String boundary = "-----------------------------" + System.currentTimeMillis();
            httpUrlConn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            outputStream = httpUrlConn.getOutputStream();
            outputStream.write(("--" + boundary + "\r\n").getBytes());
            outputStream.write(String.format(
                    "Content-Disposition: form-data; name=\"attachment\"; filename=\"%s\"\r\n",
                    multipartFile.getOriginalFilename()).getBytes("UTF-8"));

            outputStream.write(String.format("Content-Type: %s \r\n\r\n", multipartFile.getContentType()).getBytes(
                    "UTF-8"));

            byte[] data = new byte[1024];
            int len = 0;
            inputStream = multipartFile.getInputStream();
            while ((len = inputStream.read(data)) > -1) {
                outputStream.write(data, 0, len);
            }

            outputStream.write(("\r\n--" + boundary + "\r\n").getBytes());
            outputStream.write("Content-Disposition: form-data; name=\"parameters\";\r\n\r\n".getBytes("UTF-8"));
            outputStream.write(("\r\n--" + boundary + "--\r\n\r\n").getBytes());
            // 将返回的输入流转换成字符串
            inputStreamRequest = httpUrlConn.getInputStream();
            inputStreamReader = new InputStreamReader(inputStreamRequest, "utf-8");
            bufferedReader = new BufferedReader(inputStreamReader);
            String str = null;
            StringBuffer buffer = new StringBuffer();
            while ((str = bufferedReader.readLine()) != null) {
                buffer.append(str);
            }
            info = buffer.toString();

        } catch (UnsupportedEncodingException e) {
            logger.error("上传文件失败!", e);
        } catch (MalformedURLException e) {
            logger.error("上传文件失败!", e);
        } catch (ProtocolException e) {
            logger.error("上传文件失败!", e);
        } catch (IOException e) {
            logger.error("上传文件失败!", e);
        } finally {
            // 释放资源
            if (httpUrlConn != null) {
                httpUrlConn.disconnect();
            }
            IOUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(outputStream);
            IOUtils.closeQuietly(bufferedReader);
            IOUtils.closeQuietly(inputStreamReader);
            IOUtils.closeQuietly(inputStreamRequest);
        }
        return info;
    }

    private static HttpPost httpPostHandler(String url, Map<String, String> params) {
        HttpPost httpPost = new HttpPost(url);
        List<NameValuePair> nvps = new ArrayList<>();
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
        }
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(nvps, CHARSET_UTF_8));
        } catch (UnsupportedEncodingException e) {
            logger.debug("UnsupportedEncodingException", e);
        }
        return httpPost;
    }

    private static String execute(CloseableHttpClient httpClient, HttpUriRequest httpGetOrPost) {
        String res = null;
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpGetOrPost);
            HttpEntity entity = response.getEntity();
            res = EntityUtils.toString(entity, CHARSET_UTF_8);
        } catch (IOException e) {
            logger.debug("IOException", e);
        } finally {
            doResponseClose(response);
        }
        return res;
    }

    private static void doResponseClose(CloseableHttpResponse response) {
        if (response != null) {
            try {
                response.close();
            } catch (IOException e) {
                logger.debug("IOException", e);
            }
        }
    }

    /***
     * 获取ip地址
     * @param request
     * @return
     */
    public static String getIpAddr(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        String ip = null;
        String localIP = "127.0.0.1";
        if ((ip == null) || (ip.length() == 0) || (ip.equalsIgnoreCase(localIP)) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
            if ((ip == null) || (ip.length() == 0) || (ip.equalsIgnoreCase(localIP)) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-Real-IP");
                if ((ip == null) || (ip.length() == 0) || (ip.equalsIgnoreCase(localIP))
                        || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("Proxy-Client-IP");
                    if ((ip == null) || (ip.length() == 0) || (ip.equalsIgnoreCase(localIP))
                            || "unknown".equalsIgnoreCase(ip)) {
                        ip = request.getHeader("WL-Proxy-Client-IP");
                        if ((ip == null) || (ip.length() == 0) || (ip.equalsIgnoreCase(localIP))
                                || "unknown".equalsIgnoreCase(ip)) {
                            ip = request.getRemoteAddr();
                        }
                    }
                }
            }
        }
        // 多个IP地址时获取第一个IP
        if (ip != null && (!"".equals(ip)) && ip.contains(",")) {
            ip = ip.split(",")[0];
        }
        return ip;
    }

    public static Map<String, String> objectToMap(Object obj) throws IllegalAccessException {
        Map<String, String> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        System.out.println(clazz);
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            String value = field.get(obj) + "";
            map.put(fieldName, value);
        }
        return map;
    }
}

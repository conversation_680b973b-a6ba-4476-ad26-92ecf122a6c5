package com.jiuji.pick.common.enums.LogComparison;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AddOrUpdateProductParamEnum {
    advicePrice("advicePrice","建议售价"),
    productFuture("productFuture","商品卖点"),
    productConfig("productConfig","默认配置"),
    defaultArea("defaultArea","默认专区"),
    hotArea("hotArea","九机爆品"),
    happyArea("happyArea","乐物专区"),
    recommendArea("recommendArea","新品推荐");


    private String code;
    private String message;


    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        AddOrUpdateProductParamEnum[] values = AddOrUpdateProductParamEnum.values();
        for (AddOrUpdateProductParamEnum paramEnum:values) {
            map.put(paramEnum.getCode(),paramEnum.getMessage());
        }
        return map;
    }
}

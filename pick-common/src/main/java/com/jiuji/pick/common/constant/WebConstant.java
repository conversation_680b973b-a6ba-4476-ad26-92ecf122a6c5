package com.jiuji.pick.common.constant;

/**
 * <AUTHOR>
 */
public interface WebConstant {

    String OA_KEY_PREFIX_LOGIN_TOKEN = "urn:tokeninfo:";

    /***
     * 合作伙伴 token 存储
     */
    String PARTNER_KEY_PREFIX_LOGIN_TOKEN = "partner:tokeninfo:";

    /***
     * 供应商 token 存储
     */
    String SUPPLIER_KEY_PREFIX_LOGIN_TOKEN = "supplier:tokeninfo:";

    interface CATEGORYDIY_SORT_OR_REMOVE_TYPE {
        /**
         * pc一级分类 此处删除和排序和其他不一样
         */
        Integer PC_TYPE = 0;
        /**
         * 其他
         */
        Integer OTHER = 1;
    }

    interface WEBURL {

        String IMAGE_SERVER_NEWSTATIC = "https://img2.ch999img.com/newstatic/";
    }

    interface PICTURE_SIZE {
        String PIC_60x60 = "60x60";
        String PIC_70x70 = "70x70";
        String PIC_160x160 = "160x160";
        String PIC_216x216 = "216x216";
        String PIC_440x440 = "440x440";
        String PIC_800x800 = "800x800";
    }

    /**
     * 分类diylevel
     */
    interface CATEGORYDIY_LEVEL {
        Integer Level_1 = 1;
        Integer Level_2 = 2;
        Integer Level_3 = 3;
        Integer Level_4 = 4;
    }


    /**
     * 公用请求头
     */
    interface REQUEST_HEADER {

        String AUTHORIZATION = "Authorization";// 用户加密字符串，所有的request必须携带，值为登录接口返回的token

        String PLATFORM = "Platform";// 平台标识，所有的request必须携带，值为平台/版本

        String CITY = "City";// 针对主站业务的多城市，所有request必须携带，值为用户选择或者定位的城市的id（第三级）

    }

    /**
     * 通用正则表达式
     */
    interface REG_EXP {

        String BRACKET_SURROUND = "(\\（|\\().*(\\）|\\))";//（中英文）括号包围的    "\(.*?\)|（.*?）|（.*?\)|\(.*?）|\[.*?\]"
    }

    /**
     * 商品状态
     */
    interface PRODUCT_STATE {

        /**
         * 正常
         */
        int NORMAL = 0;

        /**
         * 缺货
         */
        int STOCKOUT = 1;

        /**
         * 下市
         */
        int DELISTING = 2;

        /**
         * 预约
         */
        int SUBSCRIBE = 3;

        /**
         * 有货
         */
        int IN_STOCK = 4;
    }



    interface PRODUCTIMAGETYPE {

        Integer PRODUCTIMAGE = 1;// 商品图
        Integer PPIDIMAGE = 2;// 商品规格副图
        Integer PPIDIMAGE360 = 4;// 商品规格立体图（3D图）
        Integer UNPACKIAMGE = 8;// 商品开箱照

        int MAIN = 16;//商品主图,与地区关联
        int SELL_POINT = 32; // 商品卖点
        int HOST_DIFF = 64; // 热销对比
        int DETAILS = 128; // 细节图 局部特写图
        int AD_IMAGE = 256; // 物料广告图
    }


    //搜索排序类别 0 综合排序 1 价格 2最新上架 3 销量
    interface SEARCH_ORDER_TYPE {
        int DEFAULT_SORT = 0;
        int PRICE = 1;
        int NEW_PRIORITY = 2;
        int SALES = 3;
    }

    //搜索排序类型
    interface SEARCH_ORDER {
        int DESC = 0;
        int ASC = 1;
    }

    interface CATEGORYDIY_PLATFORM {
        Integer PC = 0;

    }


}



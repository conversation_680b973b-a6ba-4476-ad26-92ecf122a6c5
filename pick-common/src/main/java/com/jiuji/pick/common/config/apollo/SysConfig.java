package com.jiuji.pick.common.config.apollo;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 */
@Data
public class SysConfig {


    /**
     * 生成销售单人员名称
     */
    @Value("${sysConfig.salesOrderPersonnel:付庭伟}")
    private String salesOrderPersonnel ;

    /**
     * 生成销售单人员工号
     */
    @Value("${sysConfig.jobNumber:11605}")
    private Integer jobNumber;

    /**
     * 生成销售单门店代码
     */
    @Value("${sysConfig.salesOrderAreaCode:9X_scc3}")
    private String salesOrderAreaCode;


    /**
     * 生成销售单门店id
     */
    @Value("${sysConfig.salesOrderAreaId:1146}")
    private Long salesOrderAreaId;

    /**
     * 延迟队列配置时间
     */
    @Value("${sysConfig.delayPushTime:3600}")
    private Integer delayPushTime;
}

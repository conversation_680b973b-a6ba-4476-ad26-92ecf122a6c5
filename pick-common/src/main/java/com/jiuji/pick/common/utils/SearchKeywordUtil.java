package com.jiuji.pick.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SearchKeywordUtil {

  private static final Pattern NUMBER_PATTERN = Pattern.compile("[0-9]*");

  public static Boolean isBarCode(String value) {
    Matcher numberMatcher = NUMBER_PATTERN.matcher(value);
    return numberMatcher.matches() && value.length() > 8;
  }

  public static Boolean isPPIdOrProductId(String value) {
    Matcher numberMatcher = NUMBER_PATTERN.matcher(value);
    return numberMatcher.matches() && value.length() < 8;
  }

}

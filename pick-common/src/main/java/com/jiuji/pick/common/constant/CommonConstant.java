package com.jiuji.pick.common.constant;

import com.jiuji.pick.common.bo.SaleAreaBo;

import java.util.HashMap;
import java.util.Map;

public interface CommonConstant {

  /**
   * 商品上架
   */
  int PRODUCT_UP = 1;

  /**
   * 商品下架
   */
  int PRODUCT_DOWN = 2;

  /**
   * 管理员类型
   */
  int ADMIN = 1;

  /**
   * 供应商类型
   */
  int SUPPLIER = 2;

  /**
   * 同意绑定
   */
  int AGREE_BIND = 1;

  /**
   * 拒绝绑定
   */
  int REJECT_BIND = 2;

  /**
   * 解绑
   */
  int UNBOUND = 3;

  /**
   * 更新商品
   */
  int UPDATE_PRODUCT = 1;

  /**
   * 下架/删除商品
   */
  int DELETE_PRODUCT = 2;

  /**
   * 列表页前缀链接
   */
  String DIGITAL_ROAD_KING="mall/home/<USER>/";

  /**
   * 详情页前缀链接
   */
  String NO_DIGITAL_ROAD_KING="mall/home/<USER>/";

  /**
   * 输出测试租户xtenant
   */
  String OUT_TEST_XTENANT = "50000";
  String OUT_TEST_XTENANT_02 = "50002";

  /**
   * 九机测试xtenant
   */
  String JIUJI_TEST_XTENANT = "0";

  /**
   * 主站接口 获取区域名称
   */
  String WEB_GET_AREA_NAME_URL="https://m.9ji.com/web/api/open/getAreaName?code=";

  /**
   * 全部发货操作类型
   */
  Integer DELIVERY_ALL_OPT_TYPE = 1;

  /**
   * 深圳九讯云供应商id
   */
  Long SHENZHEN_JIUXUN_SUPPLIER_ID = 300016L;

  /**
   * 虚拟、资产商品默认渠道id
   */
  String DEFAULT_CHANNEL_ID = "-1";

  /**
   * 用于url拼接
   */
  String HTTPS = "https://";

  /**
   * map key  域名 用于调用NEO接口的请求头参数
   */
  String XTENANT = "xtenant";

  /**
   * map key  token
   */
  String AUTHORIZATION = "Authorization";

  /**
   * 生成销售单的门店信息
   */
  Map<String, SaleAreaBo> SALE_AREA_MAP = new HashMap<String, SaleAreaBo>(){{
    SaleAreaBo jxyArea = new SaleAreaBo();
    jxyArea.setAreaCode("jxy");
    jxyArea.setAreaId(830L);
    jxyArea.setCityId(530102);
    put("jxy", jxyArea);

    SaleAreaBo dcMsArea = new SaleAreaBo();
    dcMsArea.setAreaCode("9X_scc");
    dcMsArea.setAreaId(826L);
    dcMsArea.setCityId(530102);
    put("9X_scc", dcMsArea);

    SaleAreaBo CHD2 = new SaleAreaBo();
    CHD2.setAreaCode("9X_scc2");
    CHD2.setAreaId(993L);
    CHD2.setCityId(520103);
    put("9X_scc2", CHD2);


    SaleAreaBo CH = new SaleAreaBo();
    CH.setAreaCode("CH");
    CH.setAreaId(960L);
    CH.setCityId(530102);
    put("CH", CH);
  }};

  /**
   * 收货超时时间240H
   */
  Integer RECEIVE_END_TIME = 240;

}

package com.jiuji.pick.common.utils;

import com.ecwid.consul.v1.ConsulClient;
import com.ecwid.consul.v1.Response;
import com.ecwid.consul.v1.kv.model.GetValue;
import com.jiuji.pick.common.component.minifile.MiniFileConfig;
import jodd.util.StringUtil;

import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/06
 * @Time 14:18
 */
public class ImageUtil {

    private static MiniFileConfig miniFileConfig = ApplicationContextUtil.popBean(MiniFileConfig.class);

    private ImageUtil(){}
    
    private final static  String NEW_IMAGE_PATTERN = "^\\d{1,9}/.*";

    public static final String CONF_ROOT = "9xunyun.small.saas";

    public static final String CONF_NAMESPACE_ROOT = CONF_ROOT + "/namespace";

    public static final String STR="/";

//    private static ConsulClient consulClient = new ConsulClient(
//            Envs.getConsulServer(),
//            Envs.getConsulServerPort());
    
    /**
     * 新版fid模式，路径newstatic
     * 旧版自定义模式，路径/pic/product/
     * 获取商品图片路径   商品的图片公用方法（视频、分类、品牌....不要用，存储路径和商品存在一些差别）
     * @param prefix 图片域名
     * @param picUrl 图片存储相对路径
     * @param size   图片存储地址
     * @return
     */
    public static String getProductImageUrl(String prefix,String picUrl,String size) {

        if (StringUtil.isBlank(picUrl)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(prefix);

        picUrl = picUrl.trim();
        //fid的模式,新版本的fid中包含一个/或者,   这里我们采取了/
        if (Pattern.matches(NEW_IMAGE_PATTERN, picUrl)) {
            sb.append(miniFileConfig.getContextpath());
            sb.append(picUrl);
        } else {//旧版/pic的模式  默认440*440
            sb.append(miniFileConfig.getOldproductimagecontextpath());
            sb.append(size);
            sb.append("/");
            sb.append(picUrl);
        }
        return sb.toString();
    }

    private static ConcurrentHashMap<String, String> imgDomainNamespceMap = new ConcurrentHashMap<>();

//    public static String getImgDomainByNamespace(String namespace, boolean canGetFromCache) {
//        //启用缓存后, 从缓存获取
//        if (canGetFromCache && imgDomainNamespceMap.containsKey(namespace)) {
//            return imgDomainNamespceMap.get(namespace);
//        }
//        String path = CONF_NAMESPACE_ROOT + STR + namespace + "/imgDomain";
//        Response<GetValue> b = consulClient.getKVValue(path);
//        GetValue gv = b.getValue();
//        if (null == gv) {
//            return "img.dev.9ji.com";
//        }
//        String n = gv.getDecodedValue();
//
//        //启用缓存后,更新缓存
//        if (canGetFromCache && n != null) {
//            imgDomainNamespceMap.put(namespace, n);
//        }
//
//        return n;
//    }
}

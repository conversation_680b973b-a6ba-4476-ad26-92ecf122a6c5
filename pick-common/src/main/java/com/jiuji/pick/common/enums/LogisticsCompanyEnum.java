package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/10
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum LogisticsCompanyEnum {

    /**
     * 物流公司
     */
    SHUNFENG("shunfeng", "顺丰快递"),
    SHUNFENG_JIUJI("shunfeng_jiuji","顺丰快递(九机特惠)"),
    SFTC("sftc","顺丰跑腿"),
    ZHONGTONG("zhongtong", "中通快递"),
    ZHONGTONG_NEW("zhongtong-new", "中通快递新"),
    MEITUAN("meituan", "美团跑腿"),
    EMS("ems", "邮政快递"),
    QUANYI("quanyi", "全一快递"),
    ZHONGTIE("zhongtie", "中铁快运"),
    DEBANG("debang", "德邦快递"),
    DEBANG_9JI("debang-jiuji","德邦快递(九机特惠)"),
    KUAYUE("kuayue","跨越快递"),
    SZUEM("szuem","联运通"),
    YUANTONG("yuantong", "圆通快递"),
    SHENGTONG("shentong", "申通快递"),
    ZENGYI("zengyi", "增益速递"),
    YUNDA("yunda", "韵达快递"),
    BAISHI("baishi", "百世快递"),
    SHANGSONG("shansong", "闪送"),
    JINGDONG_JIUJI("jingdong-jiuji", "京东物流（九机特惠）"),
    MEITUAN_JIUJI("meituan_jiuji", "美团中台"),
    DADA("dada", "达达跑腿"),
    DADA_9JI("dada-jiuji","达达(九机特惠)"),
    UU("uupt","UU跑腿"),
    OTHER("other", "其他");

    private String spell;
    private String chinese;

    public static String getChinese(String spell) {
        for (LogisticsCompanyEnum logisticsCompanyEnum : LogisticsCompanyEnum.values()) {
            if (logisticsCompanyEnum.getSpell().equals(spell)) {
                return logisticsCompanyEnum.getChinese();
            }
        }
        return StringUtils.EMPTY;
    }

    public static List<EnumV0> getAllEnum() {
        ArrayList<EnumV0> enumV0s = new ArrayList<>();
        for (LogisticsCompanyEnum logisticsCompanyEnum : LogisticsCompanyEnum.values()) {
            enumV0s.add(getEnumV0(logisticsCompanyEnum));
        }
        return enumV0s;
    }

    public static EnumV0 getEnumV0(LogisticsCompanyEnum logisticsCompanyEnum) {
        EnumV0 enumV0 = new EnumV0();
        enumV0.setCode(logisticsCompanyEnum.getSpell());
        enumV0.setMessage(logisticsCompanyEnum.getChinese());
        return enumV0;
    }
}

package com.jiuji.pick.common.enums.LogComparison;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SupplierUserAccountEnum {

    TYPE("type","类型"),
    ACCOUNTNAME("accountName","户名"),
    ACCOUNTNUM("accountNum","对公账号"),
    BANKDEPOSIT("bankDeposit","开户行"),
    PROVINCENAME("provinceName","省名称"),
    CITYNAME("cityName","城市名称"),
    DISTRICTNAME("districtName","区名称"),
    REMARK("remark","备注");


    private String code;
    private String message;

    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        SupplierUserAccountEnum[] values = SupplierUserAccountEnum.values();
        for (SupplierUserAccountEnum paramEnum:values) {
            map.put(paramEnum.getCode(),paramEnum.getMessage());
        }
        return map;
    }
}

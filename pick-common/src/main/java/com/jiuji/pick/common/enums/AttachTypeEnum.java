package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @function: 附件类型枚举
 * @date: 2021/04/25
 * @author: Lbj
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AttachTypeEnum {

    /***
     *
     */
    SUPPLIER_CONTRACT_TEMPLATE(0, "供应商合同模板"),
    COMMON(1, "共公资源"),
    PRODUCT(2, "商品"),
    SUPPLIER(3, "供应商"),
    PRODUCT_IMAGE(5, "商品图片");

    private int code;
    private String desc;

    /***
     * @description: 转换枚举类
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:54
     */
    public static AttachTypeEnum pare(int code) {
        for (AttachTypeEnum item : AttachTypeEnum.values()) {
            if (code == item.getCode()) {
                return item;
            }
        }
        return null;
    }

    /***
     * @description: 转换描述
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:53
     */
    public static String pareDesc(int code) {
        for (AttachTypeEnum item : AttachTypeEnum.values()) {
            if (code == item.getCode()) {
                return item.getDesc();
            }
        }
        return "";
    }
}

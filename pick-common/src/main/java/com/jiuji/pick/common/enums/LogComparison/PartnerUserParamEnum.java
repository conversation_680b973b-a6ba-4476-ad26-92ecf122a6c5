package com.jiuji.pick.common.enums.LogComparison;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PartnerUserParamEnum {

    NAME("name","公司名称"),
    BUSINESSNAME("businessName","业务联系人"),
    REBUSINESSPHONEMARK("businessPhone","业务联系人电话号码"),
    INVOICETITLE("invoiceTitle","开票信息"),
    INVOICENUM("invoiceNum","开票税号");


    private String code;
    private String message;

    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        PartnerUserParamEnum[] values = PartnerUserParamEnum.values();
        for (PartnerUserParamEnum paramEnum:values) {
            map.put(paramEnum.getCode(),paramEnum.getMessage());
        }
        return map;
    }
}

package com.jiuji.pick.common.annotation;

import java.lang.annotation.*;

/**
 * 重复点击的切面
 * <AUTHOR>
 * @Date 16:42 2021/07/19
**/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RepeatSubmitCheck {
    /**
     * 方法执行成功后锁过期的时间,默认为0
     * */
    int seconds() default 0;
    /**
     * 要扫描的参数位置 不指定使用全部参数进行防止重复提交
     * 接口为get类型请求时必传，值为要作为key的参数的下标值,下标从0开始
     * 如 getXList(@Param("p1") Integer p1,@Param("p2") Integer p2)接口，若要将p2的值作为接口防重复提交的key值，则argIndex=1
     * 接口为post请求类型时不用传，或默认传0
     * */
    int[] argIndexs() default {};
}

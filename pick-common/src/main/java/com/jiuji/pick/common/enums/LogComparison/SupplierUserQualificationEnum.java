package com.jiuji.pick.common.enums.LogComparison;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SupplierUserQualificationEnum {

    registeredFund("registeredFund","注册资金(万元)"),
    nature("nature","企业性质"),
    legalPerson("legalPerson","法人代表"),
    categoryId("categoryId","商品分类"),
    afterSaleName("afterSaleName","售后联系人"),
    afterSalePhone("afterSalePhone","售后联系人电话"),
    afterSaleProvinceName("afterSaleProvinceName","售后省份"),
    afterSaleCityName("afterSaleCityName","售后城市"),
    afterSaleDistrictName("afterSaleDistrictName","售后区"),
    afterSaleAddress("afterSaleAddress","售后联系地址"),
    leaderPhone("leaderPhone","大小件商品负责人联系手机（默认）"),
    virtualPhone("virtualPhone","虚拟商品负责人手机号"),
    assetsPhone("assetsPhone","资产商品负责人手机号"),
    majorBusinesses("majorBusinesses","主营业务");


    private String code;
    private String message;

    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        SupplierUserQualificationEnum[] values = SupplierUserQualificationEnum.values();
        for (SupplierUserQualificationEnum paramEnum:values) {
            map.put(paramEnum.getCode(),paramEnum.getMessage());
        }
        return map;
    }
}

package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @function:
 * @description: TopicTypeEnum.java
 * @date: 2021/10/26
 * @author: sun<PERSON>yun
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum TopicTypeEnum {

    ROTATION(1, "轮播图"),
    HOT(2, "爆品专题"),
    HAPPY(3, "乐物专题"),
    BULKY(4, "大件专题"),
    VIRTUAL(5, "虚拟商品专题")
    ;

    private int code;
    private String desc;

    public static String getTopicTypeDesc(int code) {
        for (TopicTypeEnum topicTypeEnum : TopicTypeEnum.values()) {
            if(topicTypeEnum.getCode() == code) {
                return topicTypeEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }

}

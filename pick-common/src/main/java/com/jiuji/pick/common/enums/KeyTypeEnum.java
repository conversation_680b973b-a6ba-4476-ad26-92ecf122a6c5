package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum KeyTypeEnum {


    ONE("供应商ID", "1"),
    TWO("供应商名称", "2"),
    THREE("合作伙伴id", "3"),
    FOUR("合作伙伴名称", "4"),
    FIVE("采购单号", "5"),
    SIX("采购单名称", "6"),
    SEVEN("销售单号", "7"),
    EIGHT("sku_id", "8"),
    NINE("商品名称", "9"),
    TEM("销售人员名称", "10"),
    ELEVEN("销售人员工号", "11");
    private final static String PARTNER="searchTermPartner";
    private final static List COOPERATIVE_PARTNER_ORDER_TYPE_LIST=Arrays.asList("5");
    private final static String SUPPLIER="searchTermSupplier";
    private final static List SUPPLIER_ORDER_TYPE_LIST=Arrays.asList("4","5","6","7","8","9","10","11");
    private final static String PLATFORM="searchTermPlatform";
    private final static List PLATFORM_ORDER_TYPE_LIST=Arrays.asList("1","2","4","6","7");
    private final static List JIU_XUN=Arrays.asList("11","10","7");
    private String message;
    private String code;


    /**
     * 不同的平台获取的搜索词不一样
     * @param id
     * @return
     */
    public static Map<String,List<EnumV0>> getMapEnum(Long id) {
        HashMap<String, List<EnumV0>> map = new HashMap<>();
        ArrayList<EnumV0> enumV0sPartner = new ArrayList<>();
        ArrayList<EnumV0> enumV0sSupplier = new ArrayList<>();
        ArrayList<EnumV0> enumV0sPlatform = new ArrayList<>();
        for (KeyTypeEnum keyTypeEnum : KeyTypeEnum.values()) {
            EnumV0 enumV0 = new EnumV0();
            String userId = Optional.ofNullable(id).orElse(Long.MAX_VALUE).toString();
            if(JIU_XUN.contains(keyTypeEnum.code) && !"300016".equals(userId)){
                continue;
            } else {
                String code = keyTypeEnum.getCode();
                enumV0.setCode(code);
                enumV0.setMessage(keyTypeEnum.getMessage());
                if(COOPERATIVE_PARTNER_ORDER_TYPE_LIST.contains(code)){
                    enumV0sPartner.add(enumV0);
                }
                if(SUPPLIER_ORDER_TYPE_LIST.contains(code)){
                    enumV0sSupplier.add(enumV0);
                }
                if(PLATFORM_ORDER_TYPE_LIST.contains(code)){
                    enumV0sPlatform.add(enumV0);
                }
            }
        }
        map.put(PARTNER,enumV0sPartner);
        map.put(SUPPLIER,enumV0sSupplier);
        map.put(PLATFORM,enumV0sPlatform);
        return map;
    }


}

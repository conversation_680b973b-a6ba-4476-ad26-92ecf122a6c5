package com.jiuji.pick.common.config.cache;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springframework.context.annotation.Configuration;

/**
 * @function:
 * @description: JetCacheConfig.java
 * @date: 2021/04/28
 * @author: sunfayun
 * @version: 1.0
 */
@Configuration
@EnableMethodCache(basePackages = "com.jiuji.pick")
@EnableCreateCacheAnnotation
public class JetCacheConfig {
}

package com.jiuji.pick.common.enums.LogComparison;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum EditProductWhiteConfigParamEnum {

    restrictTarget("restrictTarget","限制对象"),
    userIds("userIds","供应商ID"),
    restrictType("restrictType","限制类型");


    private String code;
    private String message;


    /**
     * 枚举转换成map
     * @return
     */
    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        EditProductWhiteConfigParamEnum[] values = EditProductWhiteConfigParamEnum.values();
        for (EditProductWhiteConfigParamEnum paramEnum:values) {
            map.put(paramEnum.getCode(),paramEnum.getMessage());
        }
        return map;
    }
}

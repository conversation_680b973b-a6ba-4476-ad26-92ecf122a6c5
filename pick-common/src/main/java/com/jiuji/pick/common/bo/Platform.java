package com.jiuji.pick.common.bo;

import lombok.Data;

/**
 * @function:
 * @description: PlatformBo.java
 * @date: 2021/04/15
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class Platform {

    private String client = "M";

    private String version = "0.0.0";

    private String mClient = "Browser";  //m版客户端类型

    private String other = "browser"; // 当时app里的m版页时候这里是app

    public Platform() {
    }

    public Platform(String client, String version) {
        this(client, version, null, null);
    }

    public Platform(String client, String version, String mClient) {
        this(client, version, mClient, null);
    }

    public Platform(String client, String version, String mClient, String other) {
        this.client = client;
        this.version = version;
        this.mClient = mClient;
        this.other = other;
    }

}

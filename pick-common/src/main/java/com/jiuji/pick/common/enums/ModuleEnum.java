package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @function: 模块枚举
 * @description: ModuleEnum.java
 * @date: 2021/04/25
 * @author: sun<PERSON>yun
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ModuleEnum {

    /***
     *
     */
    USER(1, "用户模块"),
    PRODUCT(2, "产品模块"),
    ORDER(3, "订单模块"),
    COMMON(4, "公共模块");

    private int code;
    private String desc;

}

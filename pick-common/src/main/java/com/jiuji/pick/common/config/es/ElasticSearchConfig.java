package com.jiuji.pick.common.config.es;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> create by https://www.jianshu.com/p/491d34d8349a
 */
@Slf4j
@Configuration
public class ElasticSearchConfig {


    @Value("${elastic.cluster.nodes}")
    private String nodes;

    @Bean
    public RestHighLevelClient createInstance() {
        try {
            String[] hostList = nodes.split(",");
            HttpHost[] hosts = new HttpHost[hostList.length];
            for (int i = 0; i < hostList.length; i++) {
                String[] ipPortPair = hostList[i].split(":");
                if (ipPortPair.length > 1) {
                    hosts[i] = new HttpHost(ipPortPair[0], Integer.parseInt(ipPortPair[1]), "http");
                }
            }
            // 如果有多个节点，构建多个HttpHost
            return new RestHighLevelClient(RestClient.builder(hosts));
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }


}

package com.jiuji.pick.common.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Date;

/**
 * @function:
 * @description: OATokenInfoBo.java
 * @date: 2021/04/29
 * @author: sun<PERSON>yun
 * @version: 1.0
 */
@Data
public class OATokenInfo {

    //用户Id
    private Integer userId;

    //签发Token
    private String id;

    //登陆时间
    private Date loginTime;

    //超时时间
    private Date expireDate;

    //密码签名
    private String pwdSign;

    private Integer clientType;

    private Boolean isJiuJiStaff;

    private String name;

}

package com.jiuji.pick.common.utils;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.pick.common.enums.LogComparison.LogDifferences;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <AUTHOR>
 */
public class ReflexUtils {


    /**
     * 比较两个对象是否一样 并且拼接成字符串
     * @param logDifferences
     * @return
     * @throws Exception
     */
    public static String entityComparison(LogDifferences logDifferences) throws Exception {
        Object oldEntity = logDifferences.getOldEntity();
        Object newEntity = logDifferences.getNewEntity();
        Class<?> oldEntityClass = oldEntity.getClass();
        Class<?> newEntityClass = newEntity.getClass();
        Field[] oldFields = oldEntityClass.getDeclaredFields();
        Field[] newFields = newEntityClass.getDeclaredFields();
        if(oldFields.length!=newFields.length){
            return "";
        }
        Map paramMap = logDifferences.getParamMap();
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < oldFields.length; i++) {
            Field oldField = oldFields[i];
            Field newField = newFields[i];
            oldField.setAccessible(Boolean.TRUE);
            newField.setAccessible(Boolean.TRUE);
            String oldType = oldField.getGenericType().toString();
            String newType = newField.getGenericType().toString();
            if (!oldType.equals(newType)) {
                return "";
            }
            String originalName = oldField.getName();
            //排除实体序列化的属性
            if("serialVersionUID".equals(originalName)){
                continue;
            }
            //排除list类型的属性
            if("java.util.List".equals(newType)){
                continue;
            }
            List<String> excludeParams = logDifferences.getExcludeParams();
            if(CollectionUtils.isNotEmpty(excludeParams)){
                if(excludeParams.contains(originalName)){
                    continue;
                }
            }
            String transformationName = originalName.substring(0, 1).toUpperCase() + originalName.substring(1);
            String oldValue = Optional.ofNullable(oldEntityClass.getMethod("get" + transformationName).invoke(oldEntity)).orElse("").toString();
            String newValue = Optional.ofNullable(newEntityClass.getMethod("get" + transformationName).invoke(newEntity)).orElse("").toString();
            //对比出不一样的字段然后进行日志的记录
            if("class java.math.BigDecimal".equals(newType)){
                BigDecimal bigDecimalOld;
                BigDecimal bigDecimalNew;
                if(StringUtils.isEmpty(oldValue)){
                    bigDecimalOld=BigDecimal.ZERO;
                }else {
                    bigDecimalOld = new BigDecimal(oldValue);
                }
                if(StringUtils.isEmpty(newValue)){
                    bigDecimalNew=BigDecimal.ZERO;
                }else {
                    bigDecimalNew= new BigDecimal(newValue);
                }
                if(bigDecimalOld.compareTo(bigDecimalNew)!=0){
                   spellComment(logDifferences,originalName,oldValue,newValue,paramMap,stringBuilder);
                }
                continue;
            }
            if (!oldValue.equals(newValue)) {
                spellComment(logDifferences,originalName,oldValue,newValue,paramMap,stringBuilder);
            }
        }
        if(stringBuilder.length()>1){
            stringBuilder.replace(stringBuilder.length()-1,stringBuilder.length(),"。");
        }
        return stringBuilder.toString();
    }


    private static void spellComment(LogDifferences logDifferences,String originalName,String oldValue,String newValue,Map paramMap,StringBuilder stringBuilder){
        //判断该字段是否需要枚举转换
        Map<String, Map<String, String>> transformationMap = logDifferences.getTransformationMap();
        if(CollectionUtils.isNotEmpty(transformationMap)){
            Map<String, String> map = transformationMap.get(originalName);
            if(CollectionUtils.isNotEmpty(map)){
                oldValue=Optional.ofNullable(map.get(oldValue)).orElse("");
                newValue=Optional.ofNullable(map.get(newValue)).orElse("");
            }
        }
        //判断有差异的字段是否可以转化成中文，如果不能转化成中文则不进行记录
        String value = Optional.ofNullable(paramMap.get(originalName)).orElse("").toString();
        if(StringUtils.isNotEmpty(value)){
            stringBuilder.append(value).append("由【").append(oldValue).append("】修改为【").append(newValue).append("】，");
        }
    }
}

package com.jiuji.pick.common.component.minifile;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2020/8/28 10:24
 * @Description
 * @Version 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "b2bconfig.minifile")
public class MiniFileConfig {
  @Value("${b2bconfig.minifile.uploadurl}")
  private String uploadurl;
  @Value("${b2bconfig.minifile.collection}")
  private String collection;
  @Value("${b2bconfig.minifile.contextpath}")
  private String contextpath;
  @Value("${b2bconfig.minifile.dcurl}")
  private String dcurl;
  @Value("${b2bconfig.minifile.httpProtocol}")
  private String httpProtocol;
  @Value("${b2bconfig.minifile.oldproductimagecontextpath}")
  private String oldproductimagecontextpath;
  @Value("${b2bconfig.minifile.pathbase}")
  private String pathbase;

}

package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description：
 * @date ：2021/5/12 16:52
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SupplierNatureEnum {

    /***
     *
     */
    individual("1", "个体经营"),
    COMPANY("2", "公司"),
    LISTED_COMPANY("3", "上市公司");

    private String code;
    private String desc;

    /***
     * @description: 转换枚举类
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:54
     */
    public static SupplierNatureEnum pare(String code){
        for(SupplierNatureEnum item : SupplierNatureEnum.values()){
            if(code.equals(item.getCode())){
                return item;
            }
        }
        return null;
    }

    /***
     * @description: 转换描述
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:53
     */
    public static String pareDesc(String code){
        for(SupplierNatureEnum item : SupplierNatureEnum.values()){
            if(code.equals(item.getCode())){
                return item.getDesc();
            }
        }
        return "";
    }
}

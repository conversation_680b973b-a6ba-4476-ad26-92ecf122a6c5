package com.jiuji.pick.common.component.interceptor;


import com.alibaba.fastjson.JSON;
import com.jiuji.pick.common.annotation.AuthorizationAdmin;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * @function: 管理员登录拦截器
 * @description: AuthenticationAdminInterceptor.java
 * @date: 2021/04/25
 * @author: sunfayun
 * @version: 1.0
 */
@Slf4j
@Component
public class AuthenticationAdminInterceptor extends HandlerInterceptorAdapter {

    private final static String NULL_STR = "null";
    private final static String UNDEFINED_STR = "undefined";

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        AuthorizationAdmin authorizationAdmin;
        if (handler instanceof HandlerMethod) {
            authorizationAdmin = ((HandlerMethod) handler).getMethodAnnotation(AuthorizationAdmin.class);
        } else {
            return true;
        }
        if (authorizationAdmin == null) {
            return true;
        }
        //从header中获取token
        String token = request.getHeader(WebConstant.REQUEST_HEADER.AUTHORIZATION);
        if (StringUtils.isBlank(token) || NULL_STR.equals(token) || UNDEFINED_STR.equals(token)) {
            //重置response
            response.reset();
            //设置编码格式
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json;charset=UTF-8");
            PrintWriter pw = response.getWriter();
            pw.println(JSON.toJSON(Result.notLoginError()));
            return false;
        }
        String tokenInfoStr = stringRedisTemplate.opsForValue().get(WebConstant.OA_KEY_PREFIX_LOGIN_TOKEN + token);
        if (StringUtils.isBlank(tokenInfoStr)) {
            //重置response
            response.reset();
            //设置编码格式
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json;charset=UTF-8");
            PrintWriter pw = response.getWriter();
            pw.println(JSON.toJSON(Result.notLoginError()));
            return false;
        }
        return true;
    }
}

package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @function:
 * @description: ProductTypeEnum.java
 * @date: 2021/10/09
 * @author: sunfayun
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum ProductTypeEnum {

    /***
     * 目前 小件 = 销售配件 （销售配件 包含 维修配件）
     */
    SMALL(0, "小件"),
    BULKY(1, "大件"),
    VIRTUAL(2, "虚拟商品"),
    FIX_ASSETS(3, "固定资产"),
    COMMON_ASSETS(4, "常用资产"),
    /***
     * 维修配件 也属于小件 流程走小件渠道
     */
    REPAIR_PART(5, "维修配件");

    private int code;
    private String desc;

    public static String getProductTypeDesc(int code) {
        for (ProductTypeEnum productTypeEnum : ProductTypeEnum.values()) {
            if(productTypeEnum.getCode() == code) {
                return productTypeEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 检验枚举值是否合法
     * @param code
     * @return
     */
    public static boolean checkLegal(int code){
        boolean legal = false;
        for (ProductTypeEnum e: ProductTypeEnum.values()){
            if(e.getCode() == code){
                legal = true;
                break;
            }
        }
        return legal;
    }
}

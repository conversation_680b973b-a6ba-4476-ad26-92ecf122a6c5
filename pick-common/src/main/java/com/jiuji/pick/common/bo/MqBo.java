package com.jiuji.pick.common.bo;

import com.jiuji.pick.common.constant.RabbitMqConstant;
import lombok.Data;

import java.io.Serializable;

@Data
public class MqBo<T extends Serializable> implements Serializable {
    private static final int serialVersionUID = 1;
    private String exchange= RabbitMqConstant.EXCHANGE_COMMON;
    private String routingKey;
    private String xtenant;
    private T data;
}

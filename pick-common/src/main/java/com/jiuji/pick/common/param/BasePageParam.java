package com.jiuji.pick.common.param;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description：
 * @date ：2021/5/12 11:07
 */
@Getter
@Setter
public class BasePageParam {

    private static final Integer DEFAULT_CURRENT = 1;
    private static final Integer DEFAULT_SIZE = 10;

    @NotNull(message = "当前页不能为空")
    private Integer current;

    @NotNull(message = "请输入每页显示记录条数")
    private Integer size;

    public Integer getCurrentPage() {
        return current == null ? DEFAULT_CURRENT : current;
    }

    public Integer getSize() {
        return size == null ? DEFAULT_SIZE : size;
    }

}

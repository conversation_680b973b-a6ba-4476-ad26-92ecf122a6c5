package com.jiuji.pick.common.config.redis;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.lang.Nullable;

import java.nio.charset.StandardCharsets;

/**
 * 从org.springframework.data.cache.serializer.StringRedisSerializer拷贝的，增加多租户支持
 */
@Slf4j
public class MyStringSerializer extends StringRedisSerializer {

    private final String appName="pick-web";

    @Override
    public byte[] serialize(@Nullable String string) {
        if (string == null) {
            return null;
        }
        if(checkTokenKey(string)) {
            return string.getBytes(StandardCharsets.UTF_8);
        }
        string = appName + ":" + string;
        return string.getBytes(StandardCharsets.UTF_8);
    }

    private boolean checkTokenKey(String str) {
        return StringUtils.isNotBlank(str) && (str.contains("urn:tokeninfo:") || str.contains("pcLoginToken_") || str.contains("APPTOKEN_"));
    }
}


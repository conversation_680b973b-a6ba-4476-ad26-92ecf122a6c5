package com.jiuji.pick.common.enums.LogComparison;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SupplierAddOrUpdateProductParamEnum implements LogEnum {

    PRODUCTID("productId","商品id"),
    PPID("ppid","ppid"),
    PAYMENTDAY("paymentDay","账期"),
    MATERIAL("material","物料"),
    boxRule("boxRule","箱规"),
    qualityDate("qualityDate","质保日期"),
    BUYNOTAXPRICE("buyNoTaxPrice","采购未税单价"),
    BUYTAXPRICE("buyTaxPrice","采购含税单价"),
    DELIVERYDAY("deliveryDay","发货时间"),
    NOREASONRETURN("noReasonReturn","无理由退货"),
    CHANGEDAY("changeDay","换货时间"),
    BADPAY("badPay","破损包赔"),
    LACKPAY("lackPay","少货包赔"),
    AFTERSALEPOLICY("afterSalePolicy","售后政策"),
    OTHERPOLICY("otherPolicy","其他政策"),
    REMOTEDELIVERYFEE("remoteDeliveryFee","偏远地区收费");


    private String code;
    private String message;


    /**
     * 枚举转换成map
     * @return
     */
    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        SupplierAddOrUpdateProductParamEnum[] values = SupplierAddOrUpdateProductParamEnum.values();
        for (SupplierAddOrUpdateProductParamEnum paramEnum:values) {
            map.put(paramEnum.getCode(),paramEnum.getMessage());
        }
        return map;
    }

}

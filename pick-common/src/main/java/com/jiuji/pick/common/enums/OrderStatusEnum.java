package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @since 2021-5-20
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum  OrderStatusEnum {

    // 1已审核，2取消，3已发货，4完成
    DEFAULT(0, "默认状态"),
    AUDITED(1, "已审核"),
    CANCEL(2, "取消"),
    DELIVERY(3, "已发货"),
    COMPLETED(4, "完成");

    private Integer code;
    private String desc;

    public static String getDescByCode(Integer code) {
        for (OrderStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

}

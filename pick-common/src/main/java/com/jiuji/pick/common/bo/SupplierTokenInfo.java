package com.jiuji.pick.common.bo;


import lombok.Data;

import java.util.Date;


/**
 * @function:
 * @description: SupplierTokenInfo.java 供应商token信息
 * @date: 2021/04/29
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class SupplierTokenInfo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;
    /**
     * 状态
     */
    private Integer status;


    /**
     * 登录名称
     */
    private String loginName;

    /**
     * 类型 大型、小型、中型
     */
    private String type;


    /**
     * 登陆时间
     */
    private Date loginTime;

    /**
     * 超时时间
     */
    private Date expireDate;

}

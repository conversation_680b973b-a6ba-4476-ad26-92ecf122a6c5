package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @function:
 * @description: OrderTypeEnum.java
 * @date: 2021/10/09
 * @author: sunfayun
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum OrderTypeEnum {

    /***
     *
     */
    SMALL(0, "小件"),
    BULKY(1, "大件"),
    VIRTUAL(2, "虚拟商品"),
    FIX_ASSETS(3, "固定资产"),
    COMMON_ASSETS(4, "常用资产"),
    /***
     * 维修配件 也属于小件 流程走小件渠道
     */
    REPAIR_PART(5, "维修配件");

    private int code;
    private String desc;

    private final static Map<Integer, Integer> orderTypeMap = new HashMap<>();
    static {
        orderTypeMap.put(ProductTypeEnum.SMALL.getCode(), SMALL.getCode());
        orderTypeMap.put(ProductTypeEnum.BULKY.getCode(), BULKY.getCode());
        orderTypeMap.put(ProductTypeEnum.VIRTUAL.getCode(), VIRTUAL.getCode());
        orderTypeMap.put(ProductTypeEnum.FIX_ASSETS.getCode(), FIX_ASSETS.getCode());
        orderTypeMap.put(ProductTypeEnum.COMMON_ASSETS.getCode(), COMMON_ASSETS.getCode());
        orderTypeMap.put(ProductTypeEnum.COMMON_ASSETS.getCode(), COMMON_ASSETS.getCode());
        // 维修配件走小件流程
        orderTypeMap.put(ProductTypeEnum.REPAIR_PART.getCode(), REPAIR_PART.getCode());
    }

    public static int getOrderTypeByProductType(int productType) {
        if(orderTypeMap.get(productType) == null) {
            return SMALL.getCode();
        }
        return orderTypeMap.get(productType);
    }

    public static String getDescByCode(Integer code) {
        for (OrderTypeEnum value : values()) {
            if (value.getCode()==code) {
                return value.getDesc();
            }
        }
        return "";
    }

}

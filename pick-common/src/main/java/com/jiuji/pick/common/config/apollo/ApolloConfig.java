package com.jiuji.pick.common.config.apollo;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @function:
 * @description: ApolloConfig.java
 * @date: 2020/08/13
 * @author: sunfayun
 * @version: 1.0
 */
@Configuration
@EnableApolloConfig(value = {"application-dev.yml", "application-prod.yml"})
public class ApolloConfig {

    @Bean
    public SwitchConfig switchConfig() {
        return new SwitchConfig();
    }

    @Bean
    public CategoryConfig categoryConfig() {
        return new CategoryConfig();
    }


    @Bean
    public SysConfig sysConfig() {
        return new SysConfig();
    }

}

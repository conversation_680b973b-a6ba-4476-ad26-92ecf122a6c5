package com.jiuji.pick.common.component;

import com.alibaba.fastjson.JSON;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.Platform;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.constant.WebConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Optional;


/**
 * 这个类只能出现在controller上。 在service中从request获取参数虽然简化了开发，但是维护困难，单元测试也困难，而且不能使用并行流
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CurrentRequestComponent {

  @Resource
  private final StringRedisTemplate stringRedisTemplate;

  /**
   * 获取IP地址
   *
   * @return
   */
  public String getCurrentIp() {
    HttpServletRequest request = getRequestAttributes().getRequest();
    String ip = request.getHeader("X-Forwarded-For");
    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("Proxy-Client-IP");
    }
    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("WL-Proxy-Client-IP");
    }
    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("HTTP_CLIENT_IP");
    }
    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("HTTP_X_FORWARDED_FOR");
    }
    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getRemoteAddr();
    }
    String[] ips = StringUtils.split(ip, ",");
    if (ips != null && ips.length > 1) {
      ip = ips[0];
    }
    return ip;
  }

  /**
   * 获取登录员工ID
   * @return
   */
  public Integer getStaffUserId() {
    final OATokenInfo userTokenInfo = getOATokenInfoBoWithoutCheck();
    if (userTokenInfo == null) {
      return null;
    }
    return userTokenInfo.getUserId();
  }

  /**
   * 判断是否是员工
   * @return
   */
  public Boolean isStaff() {
    final OATokenInfo userTokenInfo = getOATokenInfoBoWithoutCheck();
    if (userTokenInfo == null) {
      return null;
    }
    return userTokenInfo.getIsJiuJiStaff();
  }

  /**
   * 获取登录员工姓名
   * @return
   */
  public String getStaffName() {
    final OATokenInfo userTokenInfo = getOATokenInfoBoWithoutCheck();
    if (userTokenInfo == null) {
      return null;
    }
    return userTokenInfo.getName();
  }

  public OATokenInfo getOATokenInfoBoWithoutCheck() {
    String token = getAuthorization();
    if (StringUtils.isBlank(token) || "null".equals(token) || "undefined".equals(token)) {
      return null;
    }
    String tokenInfoStr = stringRedisTemplate.opsForValue().get(WebConstant.OA_KEY_PREFIX_LOGIN_TOKEN + token);
    if(StringUtils.isBlank(tokenInfoStr)) {
      return null;
    }
    OATokenInfo tokenInfo = JSON.parseObject(tokenInfoStr, OATokenInfo.class);
    if (tokenInfo == null) {
      log.info("redis里tokenInfo为空");
      return null;
    }
    tokenInfo.setIsJiuJiStaff(true);
    log.info("tokenInfo:{}",tokenInfo);
    return tokenInfo;
  }

  /**
   * 获取登录供应商名称
   * @return
   */
  public String getSupplierName() {
    final SupplierTokenInfo supplierTokenInfo = getSupplierTokenInfoWithoutCheck();
    if (supplierTokenInfo == null) {
      return null;
    }
    return supplierTokenInfo.getName();
  }

  public Long getSupplierId() {
    final SupplierTokenInfo supplierTokenInfo = getSupplierTokenInfoWithoutCheck();
    if (supplierTokenInfo == null) {
      return null;
    }
    return supplierTokenInfo.getId();
  }

  /***
   * @description: 获取供应商信息
   * @author: Lbj
   * @date: 2021/5/7 15:24
   */
  public SupplierTokenInfo getSupplierTokenInfoWithoutCheck() {
    String token = getAuthorization();
    if (StringUtils.isBlank(token) || "null".equals(token) || "undefined".equals(token)) {
      return null;
    }
    String tokenInfoStr = stringRedisTemplate.opsForValue().get(WebConstant.SUPPLIER_KEY_PREFIX_LOGIN_TOKEN + token);
    if(StringUtils.isBlank(tokenInfoStr)) {
      return null;
    }
    SupplierTokenInfo tokenInfo = JSON.parseObject(tokenInfoStr, SupplierTokenInfo.class);
    if (tokenInfo == null) {
      log.info("redis里tokenInfo为空");
      return null;
    }
    log.info("tokenInfo:{}",tokenInfo);
    return tokenInfo;
  }

  /**
   * 获取登录合作伙伴名称
   * @return
   */
  public String getPartnerName() {
    final PartnerTokenInfo partnerTokenInfo = getPartnerTokenInfoWithoutCheck();
    if (partnerTokenInfo == null) {
      return null;
    }
    return partnerTokenInfo.getName();
  }

  /**
   * 获取登录合作伙伴ID
   * @return
   */
  public Long getPartnerId() {
    PartnerTokenInfo partnerTokenInfo = getPartnerTokenInfoWithoutCheck();
    if (partnerTokenInfo == null) {
      return null;
    }
    return partnerTokenInfo.getId();
  }

  /***
   * @description: 获取合作伙伴信息
   * @author: Lbj
   * @date: 2021/5/7 15:24
   */
  public PartnerTokenInfo getPartnerTokenInfoWithoutCheck() {
    String token = getAuthorization();
    if (StringUtils.isBlank(token) || "null".equals(token) || "undefined".equals(token)) {
      return null;
    }
    String tokenInfoStr = stringRedisTemplate.opsForValue().get(WebConstant.PARTNER_KEY_PREFIX_LOGIN_TOKEN + token);
    if(StringUtils.isBlank(tokenInfoStr)) {
      return null;
    }
    PartnerTokenInfo tokenInfo = JSON.parseObject(tokenInfoStr, PartnerTokenInfo.class);
    if (tokenInfo == null) {
      log.info("redis里tokenInfo为空");
      return null;
    }
    log.info("tokenInfo:{}",tokenInfo);
    return tokenInfo;
  }


  public Platform getCurrentPlatform() {
    if (RequestContextHolder.getRequestAttributes() == null) {
      return new Platform();
    }
    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    String platform = request.getHeader(WebConstant.REQUEST_HEADER.PLATFORM);
    return getPlatform(platform);
  }

  public Platform getPlatform(String platform) {
    if (StringUtils.isBlank(platform)) {
      return new Platform();
    }
    String[] args = StringUtils.split(platform, "\\/");
    if (args.length == 2) {
      return new Platform(args[0], args[1]);
    } else if (args.length == 3) {
      return new Platform(args[0], args[1], args[2]);
    } else if(args.length == 4){
      return new Platform(args[0], args[1], args[2], args[3]);
    }
    return new Platform();
  }

  public Integer getCurrentCityId() {
    if (RequestContextHolder.getRequestAttributes() == null) {
      return null;
    }
    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    String header = request.getHeader(WebConstant.REQUEST_HEADER.CITY);
    if (StringUtils.isBlank(header) || StringUtils.equals(header, "0") || !StringUtils.isNumeric(header) || StringUtils.length(header) != 6) {
      header = "530102";
    }
    return Integer.valueOf(header);
  }

  /**
   * 获取Authorization
   */
  public String getAuthorization() {
    try {
      return getRequestAttributes().getRequest().getHeader(WebConstant.REQUEST_HEADER.AUTHORIZATION);
    } catch (Exception e) {
      log.error("getAuthorization exception:", e);
      return StringUtils.EMPTY;
    }
  }

  private ServletRequestAttributes getRequestAttributes() {
    return Optional
        .ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
        .orElseThrow(() -> new RuntimeException("未获取到用户请求信息"));
  }

}

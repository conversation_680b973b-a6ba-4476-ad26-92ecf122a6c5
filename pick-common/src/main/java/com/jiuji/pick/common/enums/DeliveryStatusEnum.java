package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

/**
 * @function:
 * @description: DeliveryStatusEnum.java
 * @date: 2021/06/30
 * @author: sun<PERSON>yun
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DeliveryStatusEnum {

    WAIT(0, "待发货"),
    SEND(1, "已发货");

    private int code;
    private String desc;

    public static String getStatusName(int status) {
        for (DeliveryStatusEnum deliveryStatusEnum : DeliveryStatusEnum.values()) {
            if(deliveryStatusEnum.getCode() == status) {
                return deliveryStatusEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }

}

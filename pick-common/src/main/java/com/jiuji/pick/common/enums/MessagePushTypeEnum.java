package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @function:
 * @description: MessagePushType.java
 * @date: 2021/05/14
 * @author: sunfayun
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum MessagePushTypeEnum {

    /***
     *
     */
    ALL_USER(0, "全部用户"),
    SPECIFY_USER(1, "指定用户");

    public Integer code;
    public String desc;

    /***
     * @description: 转换描述
     * @Param: [status]
     * @author: Lbj
     * @date: 2021/5/6 14:53
     */
    public static String pareDesc(Integer code){
        for(MessagePushTypeEnum item : MessagePushTypeEnum.values()){
            if(code.equals(item.getCode())){
                return item.getDesc();
            }
        }
        return "";
    }
}

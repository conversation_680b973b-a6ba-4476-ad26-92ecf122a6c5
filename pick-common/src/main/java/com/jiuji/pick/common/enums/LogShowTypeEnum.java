package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * @function:
 * @description: LogShowTypeEnum.java
 * @date: 2021/04/29
 * @author: sunfayun
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum LogShowTypeEnum {

    /***
     *
     */
    NOT_SHOW(0, "不1展示"),
    ALL_USER(1, "所有用户"),
    ADMIN(2, "管理员"),
    SUPPLIER(3, "供应商"),
    PARTNER(4, "合作伙伴");

    private int code;
    private String desc;

    /**
     * 枚举转换成list
     *
     * @return
     */
    public static List<EnumV0> getList() {
        ArrayList<EnumV0> enumV0s = new ArrayList<>();
        LogShowTypeEnum[] values = LogShowTypeEnum.values();
        for (LogShowTypeEnum paramEnum : values) {
            EnumV0 enumV0 = new EnumV0();
            enumV0.setCode(paramEnum.getCode());
            enumV0.setMessage(paramEnum.getDesc());
            enumV0s.add(enumV0);
        }
        return enumV0s;
    }

}

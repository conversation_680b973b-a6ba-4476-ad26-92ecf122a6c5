package com.jiuji.pick.common.utils;

import com.jiuji.pick.common.component.lock.DistributedLocker;

import java.util.concurrent.TimeUnit;

public class RedLockUtil {

    private static DistributedLocker redisLock;

    // 获取锁等待时间
    public final static int ACQUIRE_LOCK_WAIT_TIME = 0;

    public static void setLocker(DistributedLocker locker) {
        redisLock = locker;
    }

    /**
     * 释放锁
     * @param lockKey
     */
    public static void unlock(String lockKey) {
        redisLock.unlock(lockKey);
    }

    /**
     * 尝试获取锁
     * @param lockKey
     * @param waitTime 最多等待时间
     * @param leaseTime 上锁后自动释放锁时间
     * @return
     */
    public static boolean tryLock(String lockKey, int waitTime, int leaseTime) {
        return redisLock.tryLock(lockKey, TimeUnit.SECONDS, waitTime, leaseTime);
    }

    /**
     * 尝试获取锁
     * @param lockKey
     * @param unit 时间单位
     * @param waitTime 最多等待时间
     * @param leaseTime 上锁后自动释放锁时间
     * @return
     */
    public static boolean tryLock(String lockKey, TimeUnit unit, int waitTime, int leaseTime) {
        return redisLock.tryLock(lockKey, unit, waitTime, leaseTime);
    }

    /**
     * 判断是否已加锁
     * @param lockKey
     * @return
     */
    public static boolean isLocked(String lockKey) {
        return redisLock.isLocked(lockKey);
    }

}
package com.jiuji.pick.common.utils;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @description：
 * @date ：2021/5/8 11:41
 */
@Slf4j
public class EnumUtil {

    public static  final String BASEPACKAGE = "com.jiuji.pick.common.enums.";

    /***
     * @description: 根据名字 获取枚举值信息- （类名）
     * @Param: [className]
     * @author: Lbj
     * @date: 2021/5/8 14:18
     */
    public static List<Map<String, Object>> getEnumByClassName(String className) throws Exception {
        // 1.得到枚举类对象
        Class<Enum> clz = (Class<Enum>) Class.forName(BASEPACKAGE + className);
        // 2.得到所有枚举常量
        Object[] objects = clz.getEnumConstants();
        Method getCode = clz.getMethod("getCode");
        Method getDesc = clz.getMethod("getDesc");
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        Map<String, Object> map = null;
        for (Object obj : objects) {
            map = new HashMap<String, Object>(2);
            map.put("code", getCode.invoke(obj));
            map.put("desc", getDesc.invoke(obj));
            list.add(map);
        }
        return list;
    }

    /***
     * @description: 根据名字 获取枚举值信息- （类名）
     * @Param: [className]
     * @author: Lbj
     * @date: 2021/5/8 14:18
     */
    public static Map<String, Object> getEnumByClassNameList(List<String> classNameList) throws Exception {
        if(CollectionUtils.isEmpty(classNameList)){
            return Maps.newHashMap();
        }
        Map<String, Object>  map  = Maps.newHashMap();
        classNameList.forEach(name -> {
            try {
                map.put(name, getEnumByClassName(name));
            } catch (Exception e) {
                log.error("获取枚举信息出错:", e);
            }
        });
        return map;
    }

}

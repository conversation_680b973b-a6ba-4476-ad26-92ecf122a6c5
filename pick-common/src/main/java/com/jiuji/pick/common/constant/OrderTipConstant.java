package com.jiuji.pick.common.constant;

/**
 * 订单模块提示变量
 *
 * <AUTHOR>
 * @since 2021-5-31
 */
public class OrderTipConstant {

    public static final String SAVE_ERROR = "保存失败!";

    public static final String ADD_ERROR = "添加失败";

    public static final String UPDATE_ERROR = "修改失败";

    public static final String SUPPLIER_NO_PRODUCT = " 供应商无法供应该商品，请选择其他供应商采购!";

    public static final String NO_CAN_RE_BUY_PRODUCT = "没有可以重新购买的商品";

    public static final String NOT_FIND_ORDER = "未查询到当前订单";

    public static final String QUERY_ORDER_ERROR = "订单查询失败";

    public static final String PRODUCT_NUMBER_ERROR = "订单查询失败";

    public static final String SUBMIT_ORDER_LIMIT_ERROR = "您已被平台禁止下单，如有疑问请咨询平台人员";

    public static final String NEED_SELECT_PRODUCT = "请选择商品";

    public static final String NEED_SELECT_SUPPLIER = "请选择是那个供应商";

    public static final String SELECT_PRODUCT_ERROR = "商品信息查询失败";

    public static final String PRODUCT_UP_ERROR = "商品未上架";

    public static final String SUPPLIER_NO_PRODUCT_ERROR = "供应商没有当前商品";

    public static final String SELECT_PRODUCT_NUMBER_ERROR = "商品数量不能小于1";

    public static final String ERORR_PRICE_TYPE = "含税类型不能空";

}

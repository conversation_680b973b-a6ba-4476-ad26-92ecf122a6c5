package com.jiuji.pick.common.annotation;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.pick.common.vo.Result;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * api接口防重复提交
 * <AUTHOR> 分布式下防止重复提交
 * @Date 17:06 2021/9/29
**/
@Component
@Aspect
@Slf4j
public class NoRepeatSubmitCheckAspect {

    private static final String NO_REPEAT_LOCK_PREFIX = "api_repeat_check_lock";

    @Resource
    private RedissonClient redissonClient;

    private final ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor(ThreadUtil.newNamedThreadFactory("repeatSubmit",false));

    /**
     * 横切点
     */
    @Pointcut("@annotation(repeatSubmitCheck)")
    public void repeatPoint(RepeatSubmitCheck repeatSubmitCheck) {
        //空方法,aop切点定义
    }

    /**
     *  接收请求，并记录数据
     */
    @SneakyThrows
    @Around(value = "repeatPoint(repeatSubmitCheck)")
    public Object before(ProceedingJoinPoint joinPoint, RepeatSubmitCheck repeatSubmitCheck){

        Object[] args = joinPoint.getArgs();
        int[] argIndexs = repeatSubmitCheck.argIndexs();
        String suffix = "default";
        if (args != null && args.length > 0) {
            suffix = (ArrayUtil.isEmpty(argIndexs)? IntStream.range(0, args.length): Arrays.stream(argIndexs))
                    .mapToObj(argIndex -> {
                        Object valueObj = args[argIndex];
                        if (valueObj == null || ClassUtil.isBasicType(valueObj.getClass())) {
                            return String.valueOf(valueObj);
                        } else {
                            return DigestUtil.md5Hex(JSON.toJSONBytes(valueObj));
                        }
                    }).collect(Collectors.joining("/"));
        }

        Signature signature = joinPoint.getSignature();
        String key = String.format("%s:%s.%s:%s",NO_REPEAT_LOCK_PREFIX, signature.getDeclaringType().getPackage().getName(), signature.toShortString(),suffix);
        RLock lock = redissonClient.getLock(key);
        if (!lock.tryLock()) {
            Result r = new Result();
            r.setCode(Result.FAIL);
            r.setMsg("操作过于频繁，请稍后重试");
            r.setUserMsg("操作过于频繁，请稍后重试");
            log.info("lock key : {}", key);
            return r;
        }
        boolean delayUnlock = false;
        try {
            Object result = joinPoint.proceed();
            boolean isRResult = result instanceof Result;
            if (repeatSubmitCheck.seconds()>0 && ((isRResult && ((Result<?>) result).getCode()==Result.SUCCESS) || !isRResult)) {
                //成功或未知的结果类型,延时解锁
                delayUnlock = true;
            }
            return result;
        } finally {
            if(delayUnlock){
                scheduledExecutorService.schedule(lock::forceUnlock,repeatSubmitCheck.seconds(), TimeUnit.SECONDS);
            }else{
                lock.unlock();
            }

        }
    }

}
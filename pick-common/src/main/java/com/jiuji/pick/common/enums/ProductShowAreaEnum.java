package com.jiuji.pick.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @function:
 * @description: ProductShowAreaEnum.java
 * @date: 2021/05/21
 * @author: sunfayun
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ProductShowAreaEnum {

    /***
     *
     */
    DEFAULT_AREA(1, "默认专区"),
    HOT_AREA(2, "爆品专区"),
    HAPPY_AREA(3, "乐物专区"),
    RECOMMEND_AREA(4, "推荐专区"),
    BULKY_AREA(5, "大件商品专区"),
    VIRTUAL_AREA(6, "虚拟商品专区"),
    ASSETS_AREA(7, "资产专区")
    ;



    private int code;
    private String desc;

}

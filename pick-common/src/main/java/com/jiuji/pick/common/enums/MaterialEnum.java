package com.jiuji.pick.common.enums;

import com.jiuji.pick.common.enums.LogComparison.SupplierAddOrUpdateProductParamEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @function:
 * @description: MaterialEnum.java
 * @date: 2021/05/07
 * @author: sunfayun
 * @version: 1.0
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum MaterialEnum {

    /***
     *
     */
    ZHAN_JIA(1, "展架"),
    ZHAN_GUI(2, "展柜"),
    YANG_JI(3, "样机"),
    MO_XING_JI(4, "模型机");

    private int code;
    private String desc;

    public static String getNameByValue(int value) {
        for (MaterialEnum materialEnum : MaterialEnum.values()) {
            if(materialEnum.getCode() == value) {
                return materialEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }


    /**
     * 枚举转换成map
     * @return
     */
    public static Map<String,String> getMap(){
        HashMap<String, String> map = new HashMap<>();
        MaterialEnum[] values = MaterialEnum.values();
        for (MaterialEnum paramEnum:values) {
            map.put(paramEnum.getCode()+"",paramEnum.getDesc());
        }
        return map;
    }
}

package com.jiuji.pick.common.utils;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.jiuji.pick.common.config.yml.SystemConfig;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * @function:
 * @description: HostManageUtil.java
 * @date: 2021/05/12
 * @author: sunfayun
 * @version: 1.0
 */
@Slf4j
public class HostManageUtil {

    private final static String URL = "https://manager.saas.ch999.cn/saasManager/api/thirdParty/getMoaDomain/v1";

    private final static SystemConfig SYSTEM_CONFIG = ApplicationContextUtil.popBean(SystemConfig.class);

    private static final ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(1, 1,
            0L, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<Runnable>(1));

    private static LoadingCache<String, String> cache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .refreshAfterWrite(10, TimeUnit.HOURS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) {
                    log.info("调用load方法加载数据。。。。");
                    return getOaHost(key);
                }

                @Override
                public ListenableFuture<String> reload(String key, String oldValue) {
                    log.info("调用reload方法刷新缓存数据。。。");
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> getOaHost(key));
                    EXECUTOR_SERVICE.execute(task);
                    return task;
                }
            });

    public static String getHost(String xtenant) {
        try {
            if(StringUtils.isBlank(xtenant)) {
                log.warn("获取租户域名，请求参数xtenant为空");
                return StringUtils.EMPTY;
            }
            if(!Objects.equals(SYSTEM_CONFIG.getActive(), "prod") && !Objects.equals(SYSTEM_CONFIG.getActive(), "sim")) {
                if(Objects.equals(xtenant, CommonConstant.OUT_TEST_XTENANT) || Objects.equals(xtenant, CommonConstant.OUT_TEST_XTENANT_02)) {
                    return "https://test01.oa.saas.ch999.cn";
                } else if (Objects.equals(xtenant, CommonConstant.JIUJI_TEST_XTENANT)) {
                    return "https://oa.dev.9ji.com";
                } else {
                    return StringUtils.EMPTY;
                }
            }
            String host = cache.get(xtenant);
            if(StringUtils.isBlank(host)) {
                log.error("获取租户域名结果为空，xtenant:{}", xtenant);
                return StringUtils.EMPTY;
            }
            return host;
        } catch (Exception e) {
            log.error("获取租户域名发生异常，xtenant:{},exception:", xtenant, e);
            return getOaHost(xtenant);
        }
    }

    private static String getOaHost(String xtenant) {
        if(StringUtils.isBlank(xtenant)) {
            return StringUtils.EMPTY;
        }
        try {
            String hostResult = HttpRequest.get(URL)
                    .header("token", SecureUtil.md5(DateUtil.stringParseLocalDate(LocalDateTime.now())))
                    .form("xtenant", xtenant)
                    .execute().body();
            Result<String> result = JSONUtil.toBean(hostResult, Result.class);
            if(result != null && result.getCode() == 0 && StringUtils.isNotBlank(result.getData())) {
                if(result.getData().startsWith("https://")) {
                    return result.getData();
                }
                return "https://" + result.getData();
            }
            return StringUtils.EMPTY;
        } catch (HttpException e) {
            log.error("调用接口获取租户域名异常，xtenant:{}, exception:", xtenant, e);
            return StringUtils.EMPTY;
        }
    }

}

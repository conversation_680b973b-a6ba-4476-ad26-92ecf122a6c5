#!/usr/bin/env python
import pandas as pd
import json
import sys
import os

def excel_to_json(excel_file, output_file):
    """
    将Excel文件转换为JSON格式，并生成SQL更新语句
    
    参数:
        excel_file (str): Excel文件的路径
        output_file (str): 输出SQL语句的文件路径
    
    返回:
        None
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        # 将DataFrame转换为JSON
        json_data = df.to_dict(orient='records')
        
        # 根据JSON数据生成SQL更新语句
        sql_statements = []
        for record in json_data:
            product_sn_id = record.get('product_sn_id')  # 假设Excel中有这个字段
            product_sn = record.get('product_sn')        # 假设Excel中有这个字段
            
            if product_sn_id and product_sn:  # 确保字段存在
                sql_statement = f"update product_sn_log set product_sn_id = {product_sn_id} where product_sn = '{product_sn}' and is_deleted = 0;"
                sql_statements.append(sql_statement)
        
        # 将SQL语句写入文件
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"文件: {excel_file}\n")
            f.write("\n".join(sql_statements) + "\n\n")
        
    except Exception as e:
        print(f"错误: {str(e)}")

def main():
    # 直接指定要读取的Excel文件路径
    excel_files = [
        r"C:\Users\<USER>\Desktop\新建 XLSX 工作表 (2).xlsx",
        r"C:\Users\<USER>\Desktop\新建 XLSX 工作表 (3).xlsx"
    ]
    
    # 输出SQL语句的文件路径
    output_file = r"C:\Users\<USER>\Desktop\output_sql_statements.txt"
    
    # 遍历所有指定的Excel文件
    for excel_file in excel_files:
        if not os.path.exists(excel_file):
            print(f"错误: 找不到Excel文件 '{excel_file}'")
            continue  # 跳过不存在的文件
        
        excel_to_json(excel_file, output_file)

if __name__ == "__main__":
    main() 
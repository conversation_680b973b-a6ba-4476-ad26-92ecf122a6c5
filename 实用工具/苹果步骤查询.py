#!/usr/bin/env python
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import json
import requests
import threading

class StepInfoQueryApp:
    def __init__(self, root):
        self.root = root
        self.root.title("步骤信息查询工具")
        self.root.geometry("800x600")
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("Arial", 10))
        self.style.configure("TLabel", font=("Arial", 10))
        
        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建输入框架
        self.input_frame = ttk.LabelFrame(self.main_frame, text="查询参数", padding="10")
        self.input_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # saleAreaId 输入
        ttk.Label(self.input_frame, text="销售门店id:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.sale_area_id_var = tk.StringVar()
        ttk.Entry(self.input_frame, textvariable=self.sale_area_id_var, width=15).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # mkcId 输入
        ttk.Label(self.input_frame, text="mkcId:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.mkc_id_var = tk.StringVar()
        ttk.Entry(self.input_frame, textvariable=self.mkc_id_var, width=15).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # 查询按钮
        self.query_button = ttk.Button(self.input_frame, text="查询", command=self.start_query)
        self.query_button.grid(row=0, column=4, padx=10, pady=5)
        
        # 结果显示区域
        self.result_frame = ttk.LabelFrame(self.main_frame, text="查询结果", padding="10")
        self.result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.result_text = scrolledtext.ScrolledText(self.result_frame, wrap=tk.WORD, width=80, height=20)
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        self.status_bar = ttk.Label(self.main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM, padx=5, pady=2)
        
        # 按钮框架
        self.button_frame = ttk.Frame(self.main_frame)
        self.button_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=5, pady=5)
        
        # 清空按钮
        self.clear_button = ttk.Button(self.button_frame, text="清空结果", command=self.clear_results)
        self.clear_button.pack(side=tk.RIGHT, padx=5)
        
        # 复制按钮
        self.copy_button = ttk.Button(self.button_frame, text="复制结果", command=self.copy_results)
        self.copy_button.pack(side=tk.RIGHT, padx=5)
    
    def start_query(self):
        """在单独的线程中启动查询"""
        # 禁用查询按钮，避免重复点击
        self.query_button.config(state="disabled")
        self.status_var.set("查询中...")
        
        # 在新线程中执行查询，避免界面冻结
        threading.Thread(target=self.query_step_info, daemon=True).start()
    
    def query_step_info(self):
        """执行API查询"""
        try:
            # 获取输入值
            sale_area_id = self.sale_area_id_var.get().strip()
            mkc_id = self.mkc_id_var.get().strip()
            
            # 验证输入
            if not sale_area_id or not mkc_id:
                self.root.after(0, lambda: messagebox.showerror("错误", "saleAreaId和mkcId不能为空"))
                self.root.after(0, lambda: self.status_var.set("查询失败: 参数不能为空"))
                self.root.after(0, lambda: self.query_button.config(state="normal"))
                return
            
            # 设置请求参数
            url = "https://moa.9ji.com/cloudapi_nc/oa-stock/api/accountingRecords/selectStepInfoRes"
            headers = {
                "xservicename": "oa-stock",
                "Authorization": "65DA1E5182274768BE32C4A83D19718F",
                "Content-Type": "application/json"
            }
            data = {
                "saleAreaId": int(sale_area_id),
                "mkcId": int(mkc_id)
            }
            
            # 发送请求
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()  # 抛出HTTP错误
            
            # 解析响应
            result = response.json()
            
            # 格式化显示JSON
            formatted_json = json.dumps(result, indent=2, ensure_ascii=False)
            
            # 在UI线程中更新结果
            self.root.after(0, lambda: self.update_results(formatted_json))
            self.root.after(0, lambda: self.status_var.set("查询完成"))
            
        except requests.exceptions.RequestException as e:
            self.root.after(0, lambda: messagebox.showerror("请求错误", f"API请求失败: {str(e)}"))
            self.root.after(0, lambda: self.status_var.set(f"查询失败: {str(e)}"))
        except ValueError as e:
            self.root.after(0, lambda: messagebox.showerror("输入错误", f"请输入有效的数字: {str(e)}"))
            self.root.after(0, lambda: self.status_var.set(f"查询失败: {str(e)}"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"发生未知错误: {str(e)}"))
            self.root.after(0, lambda: self.status_var.set(f"查询失败: {str(e)}"))
        finally:
            # 恢复查询按钮
            self.root.after(0, lambda: self.query_button.config(state="normal"))
    
    def update_results(self, text):
        """更新结果显示区域"""
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, text)
    
    def clear_results(self):
        """清空结果区域"""
        self.result_text.delete(1.0, tk.END)
        self.status_var.set("已清空结果")
    
    def copy_results(self):
        """复制结果到剪贴板"""
        text = self.result_text.get(1.0, tk.END)
        if text.strip():
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            self.status_var.set("已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的内容")

def main():
    root = tk.Tk()
    app = StepInfoQueryApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()

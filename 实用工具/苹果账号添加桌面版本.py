#!/usr/bin/env python
import pandas as pd
import sys
import os
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
from tkinter import ttk

def excel_to_batch_insert_sql(excel_file, start_id=20047):
    """
    将Excel文件内容转换为批量INSERT SQL语句
    
    参数:
        excel_file (str): Excel文件的路径
        start_id (int): 起始ID值
    
    返回:
        str: 生成的SQL语句
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        # 获取下一个ID值
        next_id = start_id
        
        # 创建批量插入的SQL头部
        batch_sql = "INSERT INTO ch999oanew.dbo.area_guestbook_account (id, show_area_code, guestbook_account, guestbook_password, search_area_code, del_flag, show_area_id, affiliated_area_id) VALUES "
        
        # 存储所有值部分
        values_list = []
        
        # 生成所有值部分
        for index, row in df.iterrows():
            # 假设Excel中有 show_area_code, guestbook_account, guestbook_password, 
            # search_area_code, show_area_id 这些列
            # 根据实际Excel列名调整
            show_area_code = row.get('show_area_code', '')
            guestbook_account = row.get('guestbook_account', '')  # 邮箱账号
            guestbook_password = row.get('guestbook_password', '')  # 密码
            search_area_code = row.get('search_area_code', show_area_code)  # 如果没有，使用show_area_code
            show_area_id = row.get('show_area_id', 0)
            
            # 创建VALUES部分
            value_part = f"({next_id}, N'{show_area_code}', N'{guestbook_account}', N'{guestbook_password}', N'{search_area_code}', 0, {show_area_id}, null)"
            values_list.append(value_part)
            
            # 增加ID
            next_id += 1
        
        # 组合完整的批量插入SQL
        batch_sql += ",\n".join(values_list) + ";"
        
        return batch_sql
        
    except Exception as e:
        return f"错误: {str(e)}"

class ExcelToSqlApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel转SQL工具")
        self.root.geometry("800x600")
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("Arial", 10))
        self.style.configure("TLabel", font=("Arial", 10))
        
        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建顶部框架
        self.top_frame = ttk.Frame(self.main_frame)
        self.top_frame.pack(fill=tk.X, pady=5)
        
        # 文件路径输入
        self.path_label = ttk.Label(self.top_frame, text="Excel文件路径:")
        self.path_label.pack(side=tk.LEFT, padx=5)
        
        self.path_var = tk.StringVar()
        self.path_entry = ttk.Entry(self.top_frame, textvariable=self.path_var, width=50)
        self.path_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        self.browse_button = ttk.Button(self.top_frame, text="浏览...", command=self.browse_file)
        self.browse_button.pack(side=tk.LEFT, padx=5)
        
        # 创建设置框架
        self.settings_frame = ttk.Frame(self.main_frame)
        self.settings_frame.pack(fill=tk.X, pady=5)
        
        # 起始ID输入
        self.id_label = ttk.Label(self.settings_frame, text="起始ID:")
        self.id_label.pack(side=tk.LEFT, padx=5)
        
        self.id_var = tk.StringVar(value="20047")  # 默认值
        self.id_entry = ttk.Entry(self.settings_frame, textvariable=self.id_var, width=10)
        self.id_entry.pack(side=tk.LEFT, padx=5)
        
        self.convert_button = ttk.Button(self.settings_frame, text="转换", command=self.convert)
        self.convert_button.pack(side=tk.LEFT, padx=5)
        
        # SQL输出区域
        self.result_frame = ttk.LabelFrame(self.main_frame, text="SQL输出", padding="5")
        self.result_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.result_text = scrolledtext.ScrolledText(self.result_frame, wrap=tk.WORD, width=80, height=20)
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 底部按钮
        self.bottom_frame = ttk.Frame(self.main_frame)
        self.bottom_frame.pack(fill=tk.X, pady=5)
        
        self.copy_button = ttk.Button(self.bottom_frame, text="复制到剪贴板", command=self.copy_to_clipboard)
        self.copy_button.pack(side=tk.RIGHT, padx=5)
        
        self.save_button = ttk.Button(self.bottom_frame, text="保存到文件", command=self.save_to_file)
        self.save_button.pack(side=tk.RIGHT, padx=5)
    
    def browse_file(self):
        """打开文件选择对话框"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        if file_path:
            self.path_var.set(file_path)
    
    def convert(self):
        """转换Excel文件为SQL语句"""
        excel_file = self.path_var.get()
        if not excel_file:
            messagebox.showerror("错误", "请选择一个Excel文件")
            return
        
        if not os.path.exists(excel_file):
            messagebox.showerror("错误", f"文件不存在: {excel_file}")
            return
        
        # 获取起始ID
        try:
            start_id = int(self.id_var.get())
            if start_id <= 0:
                messagebox.showerror("错误", "起始ID必须是正整数")
                return
        except ValueError:
            messagebox.showerror("错误", "起始ID必须是整数")
            return
        
        # 转换Excel为SQL
        sql_result = excel_to_batch_insert_sql(excel_file, start_id)
        
        # 显示结果
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, sql_result)
        
        messagebox.showinfo("成功", "转换完成！")
    
    def copy_to_clipboard(self):
        """复制SQL到剪贴板"""
        sql_text = self.result_text.get(1.0, tk.END)
        if sql_text.strip():
            self.root.clipboard_clear()
            self.root.clipboard_append(sql_text)
            messagebox.showinfo("复制", "SQL已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的SQL内容")
    
    def save_to_file(self):
        """保存SQL到文件"""
        sql_text = self.result_text.get(1.0, tk.END)
        if not sql_text.strip():
            messagebox.showwarning("警告", "没有可保存的SQL内容")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存SQL文件",
            defaultextension=".sql",
            filetypes=[("SQL文件", "*.sql"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(sql_text)
                messagebox.showinfo("保存", f"SQL已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存文件时出错: {str(e)}")

def main():
    root = tk.Tk()
    app = ExcelToSqlApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()

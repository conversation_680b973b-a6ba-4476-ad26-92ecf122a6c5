<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.jiuji.pick</groupId>
        <artifactId>pick-goods-platform</artifactId>
        <version>0.0.1</version>
    </parent>

    <groupId>com.jiuji.pick</groupId>
    <artifactId>pick-service</artifactId>
    <version>0.0.1</version>
    <name>pick-service</name>
    <packaging>jar</packaging>

    <description>pick-service</description>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jiuji.pick</groupId>
            <artifactId>pick-common</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <artifactId>common-utils</artifactId>
            <groupId>com.jiuji.tc</groupId>
        </dependency>
        <dependency>
            <groupId>com.jiuji.stock</groupId>
            <artifactId>oa-stock-cloud</artifactId>
            <version>1.2.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.wcf</groupId>
            <artifactId>wcfclient</artifactId>
            <version>1.11.166</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jiuji.cloud</groupId>
            <artifactId>oa-office-cloud</artifactId>
            <version>1.0.9-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.jiuji.oa.office</groupId>
            <artifactId>oa-office-api</artifactId>
            <version>1.0.1</version>
        </dependency>
    </dependencies>

</project>

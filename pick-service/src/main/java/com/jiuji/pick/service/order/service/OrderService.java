package com.jiuji.pick.service.order.service;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.param.CartOrderParam;
import com.jiuji.pick.service.order.param.OrderDeliveryParam;
import com.jiuji.pick.service.order.param.OrderOperatingParam;
import com.jiuji.pick.service.order.param.OrderSearchParam;
import com.jiuji.pick.service.order.vo.OrderAreaInfoVO;
import com.jiuji.pick.service.order.vo.SalesReportFormSearchReq;
import com.jiuji.pick.service.order.vo.UpdateOrderPriceVo;
import com.jiuji.pick.service.order.vo.VendorCancelReq;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
public interface OrderService {

    /**
     * 已全部发货（不含部分发货订单）的订单，从发货时间开始计算，超过20天订单未完成的，每天定时10:30系统自动变为已完成，订单记录日志：订单发货后超过20天未完成，订单自动完成交易
     * 系统自动收件
     * @return
     */
    String automaticPickup();

    /**
     * 订单改价
     * @param updateOrderPriceVoList
     * @return
     */
    Result updateOrderPrice( UpdateOrderPriceVo updateOrderPriceVoList);

    /**
     * 根据订单获取跳转路径
     * @param orderNumber
     * @return
     */
    Result<String> getAllDomainsByOrder(Long orderNumber);

    /**
     * 提交订单
     *
     * @param cartOrderParam cartOrderParam
     * @return
     */
    Result submitOrderByCart(CartOrderParam cartOrderParam);

    /**
     * 取消订单
     *
     * @param orderId orderId
     * @return
     */
    Result cancelOrder(Long orderId);


    /**
     * 供应商取消订单
     * @param param
     * @return
     */
    Result vendorCancel(VendorCancelReq param);

    /**
     * 订单变更
     *
     * @param orderId      orderId
     * @param deliveryTime 发货时间
     * @return
     */
    Result deliveryChange(Long orderId, LocalDateTime deliveryTime);

    /**
     * oa取消订单
     *
     * @param orderNo   采购单号
     * @param xtenant   xtenant
     * @return
     */
    Result<Boolean> oaCancelOrder(Long orderNo, Long xtenant);

    /**
     * neo取消订单
     * @param orderNo   采购单号
     * @param xtenant   xtenant
     * @return boolean
     */
    Result<Boolean> neoCancelOrder(Long orderNo, Long xtenant);

    /**
     * 订单入库(完成)
     *
     * @param orderNo   采购单号
     * @param xtenant   xtenant
     * @param count 入库数量，大件上使用
     * @param orderType 订单类型 OrderTypeEnum
     * @return
     */
    Result<Boolean> finish(Long orderNo, Long xtenant, Integer count, Integer orderType);

    /**
     * NEO订单入库
     * @param orderNo 订单号
     * @param xtenant xtenant
     * @param inStockCount 已入库数量
     * @return boolean
     */
    Result<Boolean> neoOrderFinish(Long orderNo, Long xtenant, Integer inStockCount);

    /**
     *  导出平台订单列表
     *
     * @param param
     * @param response
     * @return
     */
    Result exportPlatformOrderList(OrderSearchParam param, HttpServletResponse response);

    /**
     * 再次购买
     *
     * @param orderId
     * @return
     */
    Result<String> reBuy(Long orderId);

    /**
     * 获取所有门店信息
     *
     * @return
     */
    Result<List<OrderAreaInfoVO>> getAreaInfo();

    /**
     * 获取待发货商品数量
     *
     * @return
     */
    Result<Integer> getToBeDeliveredOrderCount();

    /**
     * 按产品发货
     * @param param
     * @return
     */
    Result<String> deliveryForProduct(OrderDeliveryParam param);


    /**
     * OA发货功能同步
     * @param param
     * @return
     */
    Result<String> deliveryFromOa(OrderDeliveryParam param);

    /**
     * 用户收货
     * @param orderIdList 收货订单号
     * @return
     */
    Result<String> orderReceive4User(List<Long> orderIdList);

    /**
     * 系统自动收货
     * @return
     */
    void orderReceive4System();

    /**
     * 通过销售单号获取单号
     * @param saleNo
     * @return
     */
    Result<Long> getOrderIdBySaleNo(Integer saleNo);


    String getSalesReportFormPageList(SalesReportFormSearchReq req) ;

}

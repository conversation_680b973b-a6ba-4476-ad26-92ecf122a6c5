package com.jiuji.pick.service.product.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @function:
 * @description: ProductUnboundJudgeParam.java
 * @date: 2021/05/11
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class ProductUnboundJudgeParam {

    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;
    @NotNull(message = "ppid不能为空")
    private Long ppid;

}

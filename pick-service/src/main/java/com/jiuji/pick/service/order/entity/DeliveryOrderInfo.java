package com.jiuji.pick.service.order.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 订单发货记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_delivery_order_info")
public class DeliveryOrderInfo extends Model<DeliveryOrderInfo> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 订单号
     */
    private Long orderNo;

    /**
     * 订单表主键
     */
    private Long orderId;

    /**
     * 订单表主键
     */
    private Long orderDetailId;

    /**
     * 发货的商品ID
     */
    private Long productId;

    /**
     * 商品详情ID
     */
    private Long productDetailId;

    /**
     * 发货数量
     */
    private Integer productCount;

    /**
     * 物流单状态，0待发货，1已发货
     */
    private Integer deliveryStatus;

    /**
     * 快递公司
     */
    private String deliveryName;

    /**
     * 快递单号
     */
    private String deliveryNo;

    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

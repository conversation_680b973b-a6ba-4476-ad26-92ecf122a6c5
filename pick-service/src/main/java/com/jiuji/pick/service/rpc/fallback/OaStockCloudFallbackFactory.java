package com.jiuji.pick.service.rpc.fallback;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.vo.ProductInfoReq;
import com.jiuji.pick.service.rpc.cloud.OaStockCloud;
import com.jiuji.pick.service.rpc.vo.DiyCostFlowLeftRes;
import com.jiuji.pick.service.rpc.vo.DiyCostFlowSearchReq;
import com.jiuji.pick.service.rpc.vo.DiyCostFlowSearchRes;
import com.jiuji.pick.service.rpc.vo.OaStockVo;
import com.jiuji.tc.common.vo.R;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @function:
 * @description: OaStockCloudFallbackFactory.java
 * @date: 2021/08/09
 * @author: sunfayun
 * @version: 1.0
 */
@Component
@Slf4j
public class OaStockCloudFallbackFactory implements FallbackFactory<OaStockCloud> {

    private final static String ERROR_LOG = "调用oaCloud获取库存销量出错，url:{},params:{}";

    @Resource
    private OaStockCloud stockCloud;

    @Override
    public OaStockCloud create(Throwable throwable) {
        return new OaStockCloud() {
            @Override
            public Result<OaStockVo> getSmallPartsSales(List<Long> ppidList, String token) {
                log.error(ERROR_LOG, "/api/chw/get_small_sales/v1", ppidList);
                return Result.error();
            }

            @Override
            public Result<Integer> queryIsMobile(ProductInfoReq req) {
                log.error(ERROR_LOG, "/api/product-info/isMobile", JSONUtil.toJsonStr(req));
                return Result.error();
            }

            @Override
            public Result<Page<DiyCostFlowSearchRes>> getDiyCostFlowPageList(DiyCostFlowSearchReq req) {
                log.error(ERROR_LOG, "/api/diy/cost-flow/list/v1", JSONUtil.toJsonStr(req));
                return Result.error();
            }

            @Override
            public Result<Page<DiyCostFlowSearchRes>> getDiyCostFlowPageListV2(DiyCostFlowSearchReq query) {
                log.error("DIY订单列表查询异常，查询接口：{}，传入参数：{}", "/api/diy/cost-flow/list/v2", JSONUtil.toJsonStr(query));
                //服务降价查询之前的接口
                return stockCloud.getDiyCostFlowPageList(query);
            }

            @Override
            public Result<List<DiyCostFlowLeftRes>> getLeftDiyCostList(DiyCostFlowSearchReq req) {
                log.error(ERROR_LOG, "/api/diy/cost-flow/left/v1", JSONUtil.toJsonStr(req));
                return Result.error();
            }
        };
    }
}

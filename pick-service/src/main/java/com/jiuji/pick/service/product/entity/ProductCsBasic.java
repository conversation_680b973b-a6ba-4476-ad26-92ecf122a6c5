/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
@Data
@Accessors(chain = true)
@TableName("t_product_cs_basic")
public class ProductCsBasic extends Model<ProductCsBasic> {

    public static final String ID = "id";
    public static final String NAME = "name";
    public static final String GROUPS = "`groups`";
    /**
     * 0单行输入1多行输入2单选3多选    4图片5日期6数字
     */
    public static final String INPUTTYPE = "inputtype";
    public static final String RANK = "`rank`";
    public static final String ISDEL = "is_del";
    public static final String ISSELECT = "is_select";
    public static final String ISDETAILSHOW = "is_detail_show";
    private static final long serialVersionUID = 1L;

    public static final List<Integer> INCLUDE_ITEM_INPUT_TYPE = IntStream.of(2, 3).boxed()
            .collect(Collectors.toList());
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * ParamGroup对应ID
     */
    @TableField("`groups`")
    private Integer groups;

    private Integer inputtype;
    /**
     * 排序
     */
    @TableField("`rank`")
    private Integer rank;

    @TableLogic
    @TableField("is_delete")
    private Boolean isDel;
    /**
     * 是否纳入前台筛选
     */
    private Boolean isSelect;

    private Boolean isDetailShow;

    private String imgPath;

    private Boolean commonRec;

    /**
     * 是否作为卖点显示
     */
    private Boolean isSellPoint;

    /**
     * 排除对比
     */
    private Boolean excludePk;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

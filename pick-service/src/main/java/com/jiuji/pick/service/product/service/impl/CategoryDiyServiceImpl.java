package com.jiuji.pick.service.product.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.utils.StringTemplateUtil;
import com.jiuji.pick.service.product.entity.CategoryDiy;
import com.jiuji.pick.service.product.entity.CategoryDiyPcType;
import com.jiuji.pick.service.product.mapper.CategoryDiyMapper;
import com.jiuji.pick.service.product.param.CategoryDiy4PcItemSaveParam;
import com.jiuji.pick.service.product.param.CategoryDiy4PcTypeSaveParam;
import com.jiuji.pick.service.product.param.CategoryDiySortSaveParam;
import com.jiuji.pick.service.product.service.CategoryDiyPcTypeService;
import com.jiuji.pick.service.product.service.CategoryDiyService;
import com.jiuji.pick.service.product.vo.MenuNevVo;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author:zhouhanlin
 * @Description
 * @Date 2021-04-28
 */
@Slf4j
@Service
public class CategoryDiyServiceImpl extends ServiceImpl<CategoryDiyMapper, CategoryDiy> implements CategoryDiyService {

    @Resource
    private CategoryDiyPcTypeService categoryDiyPcTypeService;

    @Override
    public boolean saveByPcType(CategoryDiy4PcTypeSaveParam diy4PcTypeSave, Integer staffUserId) {
        List<CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeKeywords> keywords = diy4PcTypeSave.getKeywords();
        List<CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeBo> types = diy4PcTypeSave.getTypes();
        Integer id = diy4PcTypeSave.getId();
        if (id != null && id!=0) {
            List<CategoryDiyPcType> updatePcTypes = new ArrayList<>();//有修改
            List<CategoryDiyPcType> insertPcTypes = new ArrayList<>();//有新增
            List<Integer> delPcTypes = new ArrayList<>();//有删除
            if(CollectionUtils.isNotEmpty(types)){
                //保存分类
                for (int i = 0; i < types.size(); i++) {
                    CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeBo type = types.get(i);
                    if(type != null && type.getId()!=null && type.getId()>0){
                        if(type.getIsdel() != null && type.getIsdel()==1){
                            delPcTypes.add(type.getId());
                        } else {
                            updatePcTypes.add(CategoryDiy4PcTypeSaveParam.map2CategoryDiyPcTypeByType(type,i,id,staffUserId));
                        }
                    } else {
                        CategoryDiyPcType categoryDiyPcType  = CategoryDiy4PcTypeSaveParam.map2CategoryDiyPcTypeByType(type,i,id,staffUserId);
                        categoryDiyPcType.setDeleted(false);
                        categoryDiyPcType.setUpdateTime(LocalDateTime.now());
                        insertPcTypes.add(categoryDiyPcType);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(keywords)){
                //保存关键字
                for (int i = 0; i < keywords.size(); i++) {
                    CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeKeywords keyword = keywords.get(i);
                    if(keyword != null && keyword.getId()!=null && keyword.getId()>0){
                        if(keyword.getIsdel() != null && keyword.getIsdel()==1){
                            delPcTypes.add(keyword.getId());
                        } else {
                            updatePcTypes.add(CategoryDiy4PcTypeSaveParam.map2CategoryDiyPcTypeByKeyWord(keyword,i,id,staffUserId));
                        }
                    } else {
                        CategoryDiyPcType categoryDiyPcType = CategoryDiy4PcTypeSaveParam.map2CategoryDiyPcTypeByKeyWord(keyword,i,id,staffUserId);
                        categoryDiyPcType.setDeleted(false);
                        categoryDiyPcType.setUpdateTime(LocalDateTime.now());
                        insertPcTypes.add(categoryDiyPcType);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(updatePcTypes)){
                categoryDiyPcTypeService.batchUpdate(updatePcTypes);
            }
            if(CollectionUtils.isNotEmpty(insertPcTypes)){
                categoryDiyPcTypeService.batchInsert(insertPcTypes);
            }
            if(CollectionUtils.isNotEmpty(delPcTypes)){
                categoryDiyPcTypeService.batchDelete(delPcTypes);
            }
            return true;
        } else {
            //保存父级
            CategoryDiy categoryDiy = new CategoryDiy();
            categoryDiy.setName("pc分类");
            categoryDiy.setUpdateUserId(staffUserId);
            categoryDiy.setPlatform(WebConstant.CATEGORYDIY_PLATFORM.PC);
            categoryDiy.setLevel(WebConstant.CATEGORYDIY_LEVEL.Level_1);
            int sort = this.getLastSortByPlatFormAndLevel(WebConstant.CATEGORYDIY_PLATFORM.PC, WebConstant.CATEGORYDIY_LEVEL.Level_1, 0);
            categoryDiy.setSort(sort);
            boolean parentSuccess = this.save(categoryDiy);
            List<CategoryDiyPcType> pcTypes = new ArrayList<>(types.size() + keywords.size());
            //保存分类
            for (int i = 0; i < types.size(); i++) {
                CategoryDiyPcType categoryDiyPcType = CategoryDiy4PcTypeSaveParam.map2CategoryDiyPcTypeByType(types.get(i), i, categoryDiy.getId(), staffUserId);
                categoryDiyPcType.setCreateTime(LocalDateTime.now());
                pcTypes.add(categoryDiyPcType);
            }
            //保存关键字
            for (int i = 0; i < keywords.size(); i++) {
                CategoryDiyPcType categoryDiyPcType = CategoryDiy4PcTypeSaveParam.map2CategoryDiyPcTypeByKeyWord(keywords.get(i), i, categoryDiy.getId(), staffUserId);
                categoryDiyPcType.setCreateTime(LocalDateTime.now());
                pcTypes.add(categoryDiyPcType);
            }
            int pcTypesSuccessSize = categoryDiyPcTypeService.batchInsert(pcTypes);
            if (!parentSuccess||pcTypesSuccessSize<=0) {
                log.warn("保存diy分类失败,失败消息为：{}", "未全部保存成功，手动回滚");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return false;
            }
            return true;
        }
    }

    /**
     * 获取排在最后面的排序值
     * @param platform
     * @param level
     * @param parentId
     * @return
     */
    private int getLastSortByPlatFormAndLevel(int platform,int level,Integer parentId){
        CategoryDiy one = this.getOne(new LambdaQueryWrapper<CategoryDiy>().eq(CategoryDiy::getPlatform, platform)
                .eq(CategoryDiy::getLevel, level).eq(CategoryDiy::getParentId, parentId).
                        eq(CategoryDiy::getDeleted,0).orderByDesc(CategoryDiy::getSort).last("limit 1"));
        return one == null ? 0 : one.getSort() + 1;
    }


    @Override
    public List<MenuNevVo> getAllClass(Integer currentCityId) {
        final List<CategoryDiy> all = this.getAll();
        if(CollectionUtils.isEmpty(all)) {
            return new ArrayList<>();
        }
        List<CategoryDiy> pcCategoryDiys = filterByPlatForm(all, Arrays.asList(WebConstant.CATEGORYDIY_PLATFORM.PC));
        pcCategoryDiys=sort(pcCategoryDiys);
        Map<Integer, List<CategoryDiy>> group = pcCategoryDiys.stream().collect(Collectors.groupingBy(CategoryDiy::getLevel));
        List<CategoryDiy> categoryDiysLevel1 = group.get(1);//一级分类
        List<CategoryDiy> level2s = group.get(2);//二级分类
        List<CategoryDiy> level3s = group.get(3);//三级分类
        if(CollectionUtils.isEmpty(categoryDiysLevel1)) {
            return new ArrayList<>();
        }
        List<Integer> categoryDiysLevel1Ids = categoryDiysLevel1.stream().map(CategoryDiy::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(categoryDiysLevel1Ids)){
            return new ArrayList<>();
        }
        List<CategoryDiyPcType> typeAndKeyWords = categoryDiyPcTypeService.list(new LambdaQueryWrapper<CategoryDiyPcType>().eq(CategoryDiyPcType::getDeleted,0).in(CategoryDiyPcType::getCategoryDiyId,categoryDiysLevel1Ids));
        if (CollectionUtils.isEmpty(typeAndKeyWords)){
            return new ArrayList<>();
        }
        Map<Integer, List<CategoryDiyPcType>> typeAndKeyWordsGroup = typeAndKeyWords.stream().collect(Collectors.groupingBy(CategoryDiyPcType::getCategoryDiyId));
        if (MapUtils.isEmpty(typeAndKeyWordsGroup)){
            return new ArrayList<>();
        }
        return  categoryDiysLevel1.stream().map(e -> {
            MenuNevVo nevVo = new MenuNevVo();
            final String[] image = {""};
            List<CategoryDiyPcType> pcTypes = typeAndKeyWordsGroup.get(e.getId());
            if (CollectionUtils.isNotEmpty(pcTypes)) {
                List<CategoryDiyPcType> types = pcTypes.stream().filter(pcType -> pcType.getKinds() == 1).collect(Collectors.toList());
                List<CategoryDiyPcType> keyWords = pcTypes.stream().filter(pcType -> pcType.getKinds() == 2).collect(Collectors.toList());
                List<MenuNevVo.Item> items = types.stream().map(type -> {
                    MenuNevVo.Item typeBo = new MenuNevVo.Item();
                    typeBo.setTitle(type.getName());
                    typeBo.setLink(type.getLink());
                    //循环过程中，一组内，有一个有图标，就把图标赋值给父级
                    if (StringUtils.isEmpty(image[0]) && StringUtils.isNotEmpty(type.getFid())) {
                        if(type.getFid().contains(".png") || type.getFid().contains(".jpg")) {
                            image[0] = WebConstant.WEBURL.IMAGE_SERVER_NEWSTATIC + type.getFid();
                        } else {
                            image[0] = WebConstant.WEBURL.IMAGE_SERVER_NEWSTATIC + type.getFid() + ".jpg";
                        }
                    }
                    return typeBo;
                }).collect(Collectors.toList());
                nevVo.setImage(image[0]);
                nevVo.setItem(items);
                List<MenuNevVo.Item> keywordList = keyWords.stream().map(type -> {
                    MenuNevVo.Item typeBo = new MenuNevVo.Item();
                    typeBo.setTitle(type.getName());
                    typeBo.setLink(type.getLink());
                    return typeBo;
                }).collect(Collectors.toList());
                nevVo.setLevel2(keywordList);
            }

            if(CollectionUtils.isNotEmpty(level2s)) {
                List<MenuNevVo.Level3> level2and3 = level2s.stream().filter(e2 -> e2.getParentId().equals(e.getId())).map(leve2Item -> {
                    MenuNevVo.Level3 level2 = new MenuNevVo.Level3();
                    level2.setTitle(leve2Item.getName());
                    level2.setLink(leve2Item.getLink());
                    if(CollectionUtils.isNotEmpty(level3s)) {
                        List<CategoryDiy> level3List = level3s.stream().filter(level3Item -> level3Item.getParentId().equals(leve2Item.getId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(level3List)) {
                            List<MenuNevVo.Item> level3Items = level3List.stream().map(level3Item -> {
                                MenuNevVo.Item item = new MenuNevVo.Item();
                                item.setTitle(level3Item.getName());
                                item.setLink(level3Item.getLink());
                                return item;
                            }).collect(Collectors.toList());
                            level2.setChildren(level3Items);
                        }
                    }
                    return level2;
                }).collect(Collectors.toList());
                nevVo.setLevel3(level2and3);
            }
            return nevVo;
        }).collect(Collectors.toList());

    }


    @Override
    public List<CategoryDiy>  getAll(){
        return baseMapper.selectList(new LambdaQueryWrapper<CategoryDiy>().eq(CategoryDiy::getDeleted,0).eq(CategoryDiy::getPlatform, WebConstant.CATEGORYDIY_PLATFORM.PC));
    }

    private List<CategoryDiy> filterByPlatForm( List<CategoryDiy> all,List<Integer> platFormList){
        return all.stream().filter(e->platFormList.contains(e.getPlatform())).collect(Collectors.toList());
    }

    private List<CategoryDiy> sort(List<CategoryDiy> all) {
        return all.stream().sorted(Comparator.comparing(CategoryDiy::getSort)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveByPcItem(CategoryDiy4PcItemSaveParam diy4PcItemSave, Integer staffUserId) {
        CategoryDiy categoryDiy = new CategoryDiy();
        categoryDiy.setId(diy4PcItemSave.getId());
        categoryDiy.setName(diy4PcItemSave.getName());
        categoryDiy.setLink(StringTemplateUtil.roadKing(diy4PcItemSave.getLink()));
        categoryDiy.setUpdateUserId(staffUserId);
        categoryDiy.setParentId(diy4PcItemSave.getParentId());
        categoryDiy.setLevel(diy4PcItemSave.getLevel());
        categoryDiy.setPlatform(WebConstant.CATEGORYDIY_PLATFORM.PC);
        categoryDiy.setParentIds(String.valueOf(diy4PcItemSave.getParentId()));
        if (categoryDiy.getId() == null) {
            categoryDiy.setSort(this.getLastSortByPlatFormAndLevel(categoryDiy.getPlatform(), categoryDiy.getLevel(), categoryDiy.getParentId()));
        }
        return ((CategoryDiyServiceImpl) AopContext.currentProxy()).saveOrUpdate(categoryDiy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCascade(Integer id, Integer type) {
        if(WebConstant.CATEGORYDIY_SORT_OR_REMOVE_TYPE.OTHER.equals(type)){
            boolean removeParent = ((CategoryDiyServiceImpl) AopContext.currentProxy()).remove(
                    new LambdaQueryWrapper<CategoryDiy>().eq(CategoryDiy::getId, id)
            );
            ((CategoryDiyServiceImpl) AopContext.currentProxy()).remove(
                    new LambdaQueryWrapper<CategoryDiy>().like(CategoryDiy::getParentIds, "%" + id + ",")
            );
            return removeParent;
        }else {
            ((CategoryDiyServiceImpl) AopContext.currentProxy()).remove(
                    new LambdaQueryWrapper<CategoryDiy>().eq(CategoryDiy::getId, id)
            );
            ((CategoryDiyServiceImpl) AopContext.currentProxy()).remove(
                    new LambdaQueryWrapper<CategoryDiy>().like(CategoryDiy::getParentIds, "%" + id + ",")
            );
            categoryDiyPcTypeService.remove(new LambdaQueryWrapper<CategoryDiyPcType>().eq(CategoryDiyPcType::getCategoryDiyId, id));
            return true;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSort(List<CategoryDiySortSaveParam> categoryDiySortSaveParam, Integer type, Integer staffUserId) {
        if (WebConstant.CATEGORYDIY_SORT_OR_REMOVE_TYPE.OTHER.equals(type)) {
            List<CategoryDiy> updateList = categoryDiySortSaveParam.stream().map(e -> {
                CategoryDiy categoryDiy = new CategoryDiy();
                categoryDiy.setSort(e.getSort());
                categoryDiy.setId(e.getId());
                return categoryDiy;
            }).collect(Collectors.toList());
            updateList.forEach(e -> {
                e.setUpdateUserId(staffUserId);
                e.setUpdateTime(LocalDateTime.now());
            });
            log.info("更新分类排序，更新人:{},data:{}", staffUserId, categoryDiySortSaveParam.stream().map(CategoryDiySortSaveParam::toString).map(String::valueOf).collect(Collectors.joining(",")));
            return ((CategoryDiyServiceImpl) AopContext.currentProxy()).updateBatchById(updateList);
        } else {
            List<CategoryDiyPcType> updateList = categoryDiySortSaveParam.stream().map(e -> {
                CategoryDiyPcType categoryDiyPcType = new CategoryDiyPcType();
                categoryDiyPcType.setSort(e.getSort());
                categoryDiyPcType.setId(e.getId());
                return categoryDiyPcType;
            }).collect(Collectors.toList());
            updateList.forEach(e -> {
                e.setUpdateUserId(staffUserId);
                e.setUpdateTime(LocalDateTime.now());
            });
            log.info("更新分类排序，更新人:{},data:{}", staffUserId, categoryDiySortSaveParam.stream().map(CategoryDiySortSaveParam::toString).map(String::valueOf).collect(Collectors.joining(",")));
            return categoryDiyPcTypeService.updateBatchById(updateList);
        }
    }
}


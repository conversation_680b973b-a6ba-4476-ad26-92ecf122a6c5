package com.jiuji.pick.service.product.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * @function:
 * @description: ProductSpecVo.java
 * @date: 2021/04/30
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class ProductSpecVo {

    /**
     * 规格名字
     */
    private String specName;

    /**
     * 规格id
     */
    private Integer specId;

    /**
     * 排序
     */
    @JsonIgnore
    private Integer rank;

    /**
     * 规格选项
     */
    private List<Select> list;

    @Data
    public static class Select{

        /**
         * 规格选项id
         */
        private Integer id;

        /**
         * 规格选项值
         */
        private String name;

        /**
         * 是否选中
         */
        private Boolean selected;

        /**
         * 排序
         */
        @JsonIgnore
        private Integer rank;
    }

}

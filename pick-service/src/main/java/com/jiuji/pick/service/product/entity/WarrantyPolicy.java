package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Description 质保政策
 * <AUTHOR>
 * @Date 2021/11/10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_warranty_policy")
public class WarrantyPolicy extends Model<WarrantyPolicy> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 质保政策标题
     */
    private String title;

    /**
     * 质保政策内容
     */
    private String content;

    /**
     * 质保政策类型  WarrantyPolicyEnum
     */
    private Integer type;

    /**
     * 操作人id
     */
    private Long optUserId;

    /**
     * 操作人姓名
     */
    private String optUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;

}

package com.jiuji.pick.service.order.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车VO
 *
 * <AUTHOR>
 * @since 2021-05-08
 */
@Data
public class CartInfoVO {

    /**
     * 购物车列表
     */
    private List<CartInfoData> cartInfoData;

    /**
     * 失效商品集合
     */
    private List<CartProduct> invalidProductList;

    @Data
    public static class CartInfoData {
        // 供应商名称
        private String supplierName;

        // 供应商id
        private Long supplierId;
        /**
         * 特殊逻辑标识  null或0是原来逻辑 1 不用创建oa订单
         */
        private Integer orderFlag;

        // 订单类型
        private Integer productType;

        // 商品类型名称
        private String productTypeStr;

        // 购物车商品列表
        private List<CartProduct> productList;
    }

    @Data
    public static class CartProduct {
        // 购物车id
        private Long id;

        // 商品详情id
        private Long productDetailId;

        // 商品id
        private Long productId;

        // ppid
        private Long ppid;

        // 商品数量
        private Integer productCount;

        // 未税
        private BigDecimal buyNoTaxPrice;

        // 含税
        private BigDecimal buyTaxPrice;

        // 商品名称
        private String productName;

        // 商品规格
        private String productColor;

        // 商品图片
        private String productImage;

        // 商品分类
        private Long productCid;

        // 商品分类名称
        private String productCidName;

        // 商品条码
        private String productBarCode;

        // 是否大小件
        private Boolean isMobile;

        /**
         * 是否失效
         */
        private Boolean invalid;

        /**
         * 金额类型，0:未税，1:含税
         */
        private Integer priceType;

        /**
         * 是否收取偏远地区物流费
         */
        private Integer remoteDeliveryFee;

        /**
         * 起订量
         */
        private Integer minimumOrderQuantity;

        /**
         * 箱规
         */
        private String boxRule;
    }

}

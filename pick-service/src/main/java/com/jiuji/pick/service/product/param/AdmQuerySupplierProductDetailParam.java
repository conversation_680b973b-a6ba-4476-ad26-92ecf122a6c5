package com.jiuji.pick.service.product.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @function:
 * @description: AdmQuerySupplierProductDetailParam.java
 * @date: 2021/05/19
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class AdmQuerySupplierProductDetailParam {

    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;
    @NotNull(message = "ppid不能为空")
    private Long ppid;

}

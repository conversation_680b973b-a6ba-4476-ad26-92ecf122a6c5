package com.jiuji.pick.service.product.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.entity.CategorySearchWord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-17
 */
public interface CategorySearchWordMapper extends BaseMapper<CategorySearchWord> {
    /**
     * 根绝关键字获取分类id
     * @param keywords
     * @return
     */
    List<Integer> getCategoryIdsByKeyword(@Param("keywords") List<String> keywords);
}

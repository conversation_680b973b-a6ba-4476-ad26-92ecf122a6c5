package com.jiuji.pick.service.order.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jiuji.pick.common.config.apollo.SysConfig;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.constant.RedisKey;
import com.jiuji.pick.common.enums.OrderTypeEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.utils.HostManageUtil;
import com.jiuji.pick.common.utils.HttpClientUtils;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.NotifyLogInfo;
import com.jiuji.pick.service.common.service.NotifyLogInfoService;
import com.jiuji.pick.service.order.bo.OrderDeliveryBO;
import com.jiuji.pick.service.order.dto.OaAreaInfoDTO;
import com.jiuji.pick.service.order.dto.OaChannelCheckDTO;
import com.jiuji.pick.service.order.dto.OaOrderDTO;
import com.jiuji.pick.service.order.dto.OaOrderResultDTO;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.param.OaChannelCheckParam;
import com.jiuji.pick.service.order.service.OaService;
import com.jiuji.pick.service.order.vo.OaSalePush;
import com.jiuji.pick.service.order.vo.OrderAreaInfoVO;
import com.jiuji.pick.service.order.vo.ProductInfoReq;
import com.jiuji.pick.service.user.dto.OaSupplierDTO;
import com.jiuji.pick.service.user.dto.OaSupplierResultDTO;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * oa服务调用 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
@Slf4j
@Service
public class OaServiceImpl implements OaService {

    @Value("${lmstfy.mult.first-lmstfy-client.oaSaleQueue}")
    private String OASALE_QUEUE;

    private static final String QUERY_ISMOBILE_URL = "https://moa.9ji.com/cloudapi_nc/logistics/api/product-info/isMobile?xservicename=logistics-service";
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private NotifyLogInfoService notifyLogInfoService;
    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;


    @Resource
    private SysConfig sysConfig;

    @Override
    public List<OrderAreaInfoVO> getAreaInfo(Long xTenant) {
        if (xTenant == null) {
            return Collections.emptyList();
        }
        String cacheKey = RedisKey.AREA_INFO_REDIS_KEY + xTenant;
        String redisString = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(redisString)) {
            return JSON.parseArray(redisString, OrderAreaInfoVO.class);
        }
        // 获取地址
        String host = HostManageUtil.getHost(xTenant + "");
        String url = host + "/ajaxapi/GetAreaTreeDetail?xtenant=" + xTenant;
        // 获取门店信息
        String res = HttpUtil.get(url);
        log.info("调用OA获取门店列表信息， url:{}, xTenant:{}, res:{}", url, xTenant, res);
        notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(xTenant).notifyName("获取门店").notifyUrl(url).notifyParam(url).notifyResult(res).build());
        List<OrderAreaInfoVO> orderAreaInfoVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(res)) {
            Result<OaAreaInfoDTO> oaAreaInfoResult = JSON.parseObject(res, new TypeReference<Result<OaAreaInfoDTO>>() {
            });
            if (oaAreaInfoResult != null && oaAreaInfoResult.getData() != null
                    && CollectionUtils.isNotEmpty(oaAreaInfoResult.getData().getAreaOptions())) {
                for (OaAreaInfoDTO.OaAreaOptions areaOption : oaAreaInfoResult.getData().getAreaOptions()) {
                    if(StringUtils.isBlank(areaOption.getLabel())) {
                        continue;
                    }
                    OrderAreaInfoVO orderAreaInfoVO = getOrderAreaInfoVO(areaOption);
                    orderAreaInfoVOList.add(orderAreaInfoVO);
                }
                if(CollectionUtils.isNotEmpty(orderAreaInfoVOList)) {
                    // 放入缓存23h
                    stringRedisTemplate.opsForValue().set(cacheKey,JSON.toJSONString(orderAreaInfoVOList), MagicalValueConstant.LONG_23, TimeUnit.HOURS);
                }
            }
        }
        return orderAreaInfoVOList;
    }

    /**
     * 获取门店vo
     *
     * @param areaOptions
     * @return
     */
    private static OrderAreaInfoVO getOrderAreaInfoVO(OaAreaInfoDTO.OaAreaOptions areaOptions) {
        OrderAreaInfoVO orderAreaInfoVO = new OrderAreaInfoVO();
        orderAreaInfoVO.setId(areaOptions.getId());
        orderAreaInfoVO.setLabel(areaOptions.getLabel());
        orderAreaInfoVO.setArea(areaOptions.getArea());
        orderAreaInfoVO.setMobile(areaOptions.getMobile());
        orderAreaInfoVO.setContacts(areaOptions.getContacts());
        orderAreaInfoVO.setCityId(String.valueOf(areaOptions.getCityid()));
        if(StringUtils.isNotBlank(areaOptions.getAuthorizeid())) {
            orderAreaInfoVO.setAuthId(Integer.valueOf(areaOptions.getAuthorizeid()));
        }
        String address = areaOptions.getAddress();
        String prov = areaOptions.getProv();
        String city = areaOptions.getCity();
        String dist = areaOptions.getDist();
        StringBuilder resAddress = new StringBuilder();
        if (StringUtils.isBlank(address)) {
            resAddress.append(prov).append(city).append(dist);
        } else {
            if (StringUtils.isNotBlank(prov) && !address.contains(prov)) {
                resAddress.append(prov);
            }
            if (StringUtils.isNotBlank(city) && !address.contains(city)) {
                resAddress.append(city);
            }
            if (StringUtils.isNotBlank(dist) && !address.contains(dist)) {
                resAddress.append(dist);
            }
            resAddress.append(address);
        }
        orderAreaInfoVO.setAddress(resAddress.toString());
        return orderAreaInfoVO;
    }

    @Override
    public OaOrderResultDTO<OaOrderResultDTO.OaOrderData> createOaOrder(OaOrderDTO oaOrderDTO, Long xTenant, Integer orderType) {
        // 获取地址
        String host = HostManageUtil.getHost(xTenant + "");

        // 调用oa接口生成采购单号
        String url = "";
        // 小件
        if(OrderTypeEnum.SMALL.getCode() == orderType){
            url = host + "/cloudapi_nc/oa-stock/api/chw/purchaseOrderSave/v1?xservicename=oa-stock";
        }
        // 大件
        if(OrderTypeEnum.BULKY.getCode() == orderType){
            url = host + "/cloudapi_nc/oa-stock/api/ChwMkc/createMkcPurchase/v1?xservicename=oa-stock";
        }

        if(StringUtils.isBlank(url)){
            log.warn("调用oa接口生成采购单失败，url为空，orderType={}", orderType);
            return null;
        }

        log.info("入参：{}", JSON.toJSONString(oaOrderDTO));
        String res = HttpClientUtils.postJson(url, JSON.toJSONString(oaOrderDTO));
        log.info("调用OA生产采购单， url:{}, param:{}, res:{}", url, JSON.toJSONString(oaOrderDTO), res);
        notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(xTenant).notifyName("创建订单").notifyUrl(url).notifyParam(JSON.toJSONString(oaOrderDTO)).notifyResult(res).build());
        if (StringUtils.isBlank(res) || !res.contains(MagicalValueConstant.STRING_CODE)) {
            return null;
        }
        OaOrderResultDTO<OaOrderResultDTO.OaOrderData> oaOrderDataOaOrderResultDTO = JSON.parseObject(res, new TypeReference<OaOrderResultDTO<OaOrderResultDTO.OaOrderData>>() {
        });
        // todo 中型使用订单号 发送延迟队列 并且是小件
        if(OrderTypeEnum.SMALL.getCode() == orderType){
            OaSalePush oaSalePush = new OaSalePush();
            oaSalePush.setSubId(oaOrderDTO.getPurchaseOrderId())
                    .setSupplierId(oaOrderDTO.getSupplierId());
            try {
                //计算延迟队列时间和统计推送次数
                int calculationDelaySecond = sysConfig.getDelayPushTime();
                String publish = firstLmstfyClient.publish(OASALE_QUEUE, JSONUtil.toJsonStr(oaSalePush).getBytes(), 0, (short) 1, calculationDelaySecond);
                log.warn("中型使用订单号 发送延迟队列 并且是小件队列推送成功，队列名称{}，推送参数{}，返回结果{}",OASALE_QUEUE,JSONUtil.toJsonStr(oaSalePush),publish);
            } catch (LmstfyException e){
                log.error("中型使用订单号 发送延迟队列 并且是小件队列推送异常，队列名称{}，推送参数{}",OASALE_QUEUE,JSONUtil.toJsonStr(oaSalePush),e);
            }
        }

        return oaOrderDataOaOrderResultDTO;
    }

    @Override
    public Integer queryIsMobile(Long ppid) {
        ProductInfoReq req = new ProductInfoReq();
        req.setPpid(ppid);
        String result = HttpClientUtils.postJson(QUERY_ISMOBILE_URL, JSON.toJSONString(req));
        String notExistNum = "-1";
        if (notExistNum.equals(result)) {
            throw new BizException("大小件配置查询错误");
        }
        return Convert.toInt(result);
    }


    @Override
    public OaSupplierResultDTO createOaChannelId(OaSupplierDTO perfectSupplierDTO, Long xTenant) {
        perfectSupplierDTO.setXTenant(Math.toIntExact(xTenant));

        // 没有渠道id，去oa获取渠道id
        String host = HostManageUtil.getHost(xTenant + "");

        // 调用oa接口生成渠道id
        String url = host + "/cloudapi_nc/oa-stock/api/chw/save/v1?xservicename=oa-stock";
        String res = HttpClientUtils.postJson(url, JSON.toJSONString(perfectSupplierDTO));
        log.info("调用OA获取渠道ID，url:{}, param:{}, res:{}", url, JSON.toJSONString(perfectSupplierDTO), res);
        notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(xTenant).notifyName("创建渠道").notifyUrl(url).notifyParam(JSON.toJSONString(perfectSupplierDTO)).notifyResult(res).build());
        if (StringUtils.isBlank(res) || !res.contains(MagicalValueConstant.STRING_CODE)) {
            return null;
        }
        OaSupplierResultDTO oaSupplierResultDTO = JSON.parseObject(res, OaSupplierResultDTO.class);
        if(oaSupplierResultDTO!=null && oaSupplierResultDTO.getCode()!=0){
             throw new BizException("中型系统异常："+ Optional.ofNullable(oaSupplierResultDTO.getMsg()).orElse(oaSupplierResultDTO.getUserMsg()));
        }
        return oaSupplierResultDTO;
    }

    @Override
    public OaOrderResultDTO cancelOrder(Long orderNo, Long xTenant, Integer orderType) {
        if (orderNo == null) {
            return null;
        }
        // 没有渠道id，去oa获取渠道id
        String host = HostManageUtil.getHost(xTenant + "");

        // 调用oa接口取消采购单号
        String url = "";
        // 小件
        if(OrderTypeEnum.SMALL.getCode() == orderType){
            url = host + "/cloudapi_nc/oa-stock/api/chw/purchase/cancel/platform/v1?xservicename=oa-stock&id=" + orderNo;
        }
        // 大件
        if(OrderTypeEnum.BULKY.getCode() == orderType){
            url = host + "/cloudapi_nc/oa-stock/api/ChwMkc/cancelMkcPurchase/v1?xservicename=oa-stock&subIds=" + orderNo;
        }

        if(StringUtils.isBlank(url)){
            log.warn("调用oa接口取消订单失败，url为空，orderType={}", orderType);
            return null;
        }
        String res = HttpClientUtils.get(url);
        log.info("调用OA取消采购单， url:{}, orderNo:{}, res:{}", url, orderNo, res);
        notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(xTenant).orderNo(orderNo).notifyName("取消订单").notifyUrl(url).notifyParam(url).notifyResult(res).build());
        if (StringUtils.isBlank(res) || !res.contains(MagicalValueConstant.STRING_CODE)) {
            log.error("调用OA取消采购单失败， url:{}, orderNo:{}, res:{}", url, orderNo, res);
            return null;
        }

        return JSON.parseObject(res, OaOrderResultDTO.class);
    }

    @Override
    public OaOrderResultDTO orderDeliveryForProduct(OrderInfo orderInfo, OrderDeliveryBO orderDeliveryBO, Integer orderType) {
        if(orderInfo == null || orderDeliveryBO == null || null == orderType) {
            return null;
        }
        // 没有渠道id，去oa获取渠道id
        String host = HostManageUtil.getHost(orderInfo.getXtenantId() + "");
        // 调用oa接口发货
        String url = "";
        // 小件
        if(OrderTypeEnum.SMALL.getCode() == orderType){
            url = host + "/cloudapi_nc/oa-stock/api/chw/updateCaigouStats/v1?xservicename=oa-stock";
        }
        // 大件
        if(OrderTypeEnum.BULKY.getCode() == orderType){
            url = host + "/cloudapi_nc/oa-stock/api/ChwMkc/saveMkcComment/v1?xservicename=oa-stock";
        }

        if(StringUtils.isBlank(url)){
            log.warn("调用oa接口发货失败，url为空，orderType={}", orderType);
            return null;
        }

        String res = HttpClientUtils.postJson(url, JSON.toJSONString(orderDeliveryBO));
        log.info("调用OA分批发货， url:{}, param:{}, res:{}", url, JSON.toJSONString(orderDeliveryBO), res);
        notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(orderInfo.getXtenantId()).orderNo(orderInfo.getOrderNo()).notifyName("订单发货").notifyUrl(url).notifyParam(JSON.toJSONString(orderDeliveryBO)).notifyResult(res).build());
        if (StringUtils.isBlank(res) || !res.contains(MagicalValueConstant.STRING_CODE)) {
            return null;
        }
        return JSON.parseObject(res, OaOrderResultDTO.class);
    }

    @Override
    public OaChannelCheckDTO channelCheck(OaChannelCheckParam param) {
        if(param == null || param.getXtenant() == null || StringUtils.isBlank(param.getAuthId()) || StringUtils.isBlank(param.getChannelId())) {
            return null;
        }
        String host = HostManageUtil.getHost(String.valueOf(param.getXtenant()));
        if(StringUtils.isBlank(host)) {
            return null;
        }
        String url = host + "/cloudapi_nc/oa-stock/api/chw/check/v1?xservicename=oa-stock&channelId=%s&authorizeid=%s&kind=%s";
        url = String.format(url, param.getChannelId(), param.getAuthId(), param.getKind());
        String ciphertext = DigestUtils.md5Hex(DateUtil.stringParseLocalDate(LocalDateTime.now()));
        String res = HttpRequest.get(url).header("token", ciphertext).execute().body();
        log.info("调用OA校验渠道， url:{}, res:{}", url, res);
        notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(param.getXtenant()).notifyName("渠道校验").notifyUrl(url).notifyResult(res).build());
        if (StringUtils.isBlank(res) || !res.contains(MagicalValueConstant.STRING_CODE)) {
            return null;
        }
        return JSON.parseObject(res, OaChannelCheckDTO.class);
    }

    @Override
    public OaChannelCheckDTO channelCheckV2(OaChannelCheckParam param) {
        if(param == null || param.getXtenant() == null || StringUtils.isBlank(param.getAuthId()) || StringUtils.isBlank(param.getChannelId())) {
            return null;
        }
        String host = HostManageUtil.getHost(String.valueOf(param.getXtenant()));
        if(StringUtils.isBlank(host)) {
            return null;
        }
        String url = host + "/cloudapi_nc/oa-stock/api/chw/check/v1?xservicename=oa-stock&channelId=%s&authorizeid=%s";
        url = String.format(url, param.getChannelId(), param.getAuthId());
        String ciphertext = DigestUtils.md5Hex(DateUtil.stringParseLocalDate(LocalDateTime.now()));
        String res = HttpRequest.get(url).header("token", ciphertext).execute().body();
        log.info("调用OA校验渠道， url:{}, res:{}", url, res);
        notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(param.getXtenant()).notifyName("渠道校验").notifyUrl(url).notifyResult(res).build());
        if (StringUtils.isBlank(res) || !res.contains(MagicalValueConstant.STRING_CODE)) {
            return null;
        }
        return JSON.parseObject(res, OaChannelCheckDTO.class);
    }
}

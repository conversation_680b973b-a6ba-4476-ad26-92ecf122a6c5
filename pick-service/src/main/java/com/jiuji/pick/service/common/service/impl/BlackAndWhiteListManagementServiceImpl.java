package com.jiuji.pick.service.common.service.impl;

import cn.hutool.json.JSONUtil;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.LogComparison.EditProductWhiteConfigParamEnum;
import com.jiuji.pick.common.enums.LogComparison.LogDifferences;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.common.enums.RestrictTargetEnum;
import com.jiuji.pick.common.utils.ReflexUtils;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.service.LogService;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.product.param.EditProductWhiteConfigParam;
import com.jiuji.pick.service.product.param.EditProductWhiteConfigParamOld;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("BlackAndWhiteListManagementServiceImpl")
public class BlackAndWhiteListManagementServiceImpl implements LogService {

    @Resource
    private NewOperateLogInfoService logInfoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Override
    public void systemSaveLog(Object param) {
        OATokenInfo oaTokenInfo = Optional.ofNullable(currentRequestComponent.getOATokenInfoBoWithoutCheck())
                .orElse(new OATokenInfo());
        CompletableFuture.runAsync(() -> {
            EditProductWhiteConfigParam editProductWhiteConfigParam = JSONUtil.toBean(JSONUtil.toJsonStr(param), EditProductWhiteConfigParam.class);
            EditProductWhiteConfigParamOld paramOld = editProductWhiteConfigParam.getParamOld();
            EditProductWhiteConfigParamOld paramNew = new EditProductWhiteConfigParamOld();
            BeanUtils.copyProperties(param, paramNew);
            Map<String, Map<String, String>> map = new HashMap<>(2);
            map.put("restrictTarget", RestrictTargetEnum.getMap());
            map.put("restrictType", RestrictTargetEnum.getMap());
            LogDifferences logDifferences = new LogDifferences();
            logDifferences.setOldEntity(paramOld)
                    .setNewEntity(paramNew)
                    .setParamMap(EditProductWhiteConfigParamEnum.getMap())
                    .setTransformationMap(map);
            String connent = null;
            try {
                connent = ReflexUtils.entityComparison(logDifferences);
            } catch (Exception e) {
                log.error("黑白名单管理日志记录异常{}", e.getMessage(), e);
            }
            NewOperateLogInfo newOperateLogInfo = new NewOperateLogInfo();
            newOperateLogInfo.setContent(connent).setRelateId(paramOld.getPpid())
                    .setShowType(LogShowTypeEnum.ADMIN.getCode())
                    .setOptUserName(oaTokenInfo.getName())
                    .setOptUserId(oaTokenInfo.getUserId().longValue())
                    .setCreateTime(LocalDateTime.now())
                    .setType(NewOperateLogInfoTypeEnum.BLACK_AND_WHITE_LIST_MANAGEMENT.getCode());
            logInfoService.saveSystemLog(newOperateLogInfo);
        });

    }
}

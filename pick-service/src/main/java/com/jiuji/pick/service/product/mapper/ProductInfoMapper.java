package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.bo.ProductInRedis;
import com.jiuji.pick.service.product.bo.ProductSearchInEsV2;
import com.jiuji.pick.service.product.entity.EsIndexAssociation;
import com.jiuji.pick.service.product.entity.ProductInfo;
import com.jiuji.pick.service.product.vo.ProductColorInfoVo;
import com.jiuji.pick.service.product.vo.QueryProduct4AddVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 产品信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductInfoMapper extends BaseMapper<ProductInfo> {

    QueryProduct4AddVo queryProductInfo4Add(Long ppid);

    /**
     * 包含删除 不要随意改动
     * @param pid
     * @return
     */
    List<ProductInfo> getAllProductInfoByPid(@Param("pid") Long pid);

    List<ProductColorInfoVo> listProductColor(@Param("ppids")Collection<Long> ppids);
    /**
     * 通过ppid获取商品详细缓存对象
     */
    ProductInfo getProductInfoByPpid(@Param("ppid") Long ppid);

    /**
     * 新增productinfo
     */
    Integer insertProductInfo(ProductInfo productInfo);

    /**
     * 更新ProductInfo 通过pid
     */
    int updateByPid(ProductInfo productInfo);

    /**
     * 通过ppid获取商品详细缓存对象
     */
    ProductInRedis getProductInRedisFromDB(@Param("ppid") Long ppid);

    /**
     * 获取商品的es存储对象
     * @param ppids  指定ppid
     * @param needAllPpid  需要所有有效的ppid
     * @return
     */
    List<ProductSearchInEsV2> getProductSearchInES(@Param("ppids") List<Long> ppids, Integer needAllPpid);


    @Select("SELECT\n" +
            "p.ppriceid ppid,\n" +
            "p.productid as productId,\n" +
            "p.product_name as productName,\n" +
            "ifnull(pro.search_key, p.product_name) searchKey,\n" +
            "pick.product_status as state\n" +
            "FROM t_pick_product pick\n" +
            "inner join  t_product_info p  on pick.ppid =p.ppriceid \n" +
            "inner join t_product pro on p.productid = pro.id")
    List<EsIndexAssociation> getProductSearchInAssociation();




}

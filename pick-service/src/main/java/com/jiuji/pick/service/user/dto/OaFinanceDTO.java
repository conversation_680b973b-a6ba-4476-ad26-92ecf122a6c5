package com.jiuji.pick.service.user.dto;


import lombok.Data;

import java.util.List;

/**
 * 财务信息
 *
 * <AUTHOR>
 * @since 2021-5-21
 */
@Data
public class OaFinanceDTO {

    /**
     * 财务信息
     */
    private KindLinkData kindLink;

    /**
     * 对公或对私账户列表
     */
    private List<OaAccountsDTO> accountsList;

    /**
     * {
     *    "kind": 0,
     *    "invoicingFlag": 1
     * }
     */
    @Data
    public static class KindLinkData {

        private Integer kind;

        /**
         * 能否开增票
         */
        private Integer invoicingFlag;
    }
}

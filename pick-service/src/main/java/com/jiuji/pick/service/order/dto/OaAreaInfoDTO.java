package com.jiuji.pick.service.order.dto;

import lombok.Data;

import java.util.List;

/**
 * od返回门店信息
 *
 * <AUTHOR>
 * @since 2021/6/10
 */
@Data
public class OaAreaInfoDTO {

    private List<OaAreaOptions> areaOptions;

    @Data
    public static class OaAreaOptions {
        /**
         * area : YN_km
         * address : 昆明市五华区圆通北路120号云大晟苑A座1楼(佰腾数码广场)
         * city : 昆明市
         * areaName : 佰腾店
         * mobile : 0871-65141852
         * dist : 五华区
         * id : 1
         * label : YN_km(佰腾店)
         * value : 1
         * prov : 云南省
         * contacts : 蒋先生
         */
        private String area;
        private String address;
        private String city;
        private String areaName;
        private String mobile;
        private String dist;
        private String id;
        private String label;
        private String value;
        private String prov;
        private String contacts;
        private Integer cityid;
        private String authorizeid;
    }

}

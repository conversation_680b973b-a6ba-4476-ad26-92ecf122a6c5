package com.jiuji.pick.service.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.utils.BeanCopyUtil;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.MessageInfo;
import com.jiuji.pick.service.common.entity.MessageInfoUser;
import com.jiuji.pick.service.common.mapper.MessageInfoMapper;
import com.jiuji.pick.service.common.mapper.MessageInfoUserMapper;
import com.jiuji.pick.service.common.param.MessageInfoQuery;
import com.jiuji.pick.service.common.param.MessageInfoSaveParam;
import com.jiuji.pick.service.common.param.QueryUserMessageParam;
import com.jiuji.pick.service.common.param.UserMessageDealParam;
import com.jiuji.pick.service.common.service.MessageInfoService;
import com.jiuji.pick.service.common.service.MessageInfoUserService;
import com.jiuji.pick.service.common.vo.*;
import com.jiuji.pick.service.order.entity.CartInfo;
import com.jiuji.pick.service.order.service.CartInfoService;
import com.jiuji.pick.service.product.entity.PickProduct;
import com.jiuji.pick.service.product.entity.ProductBind;
import com.jiuji.pick.service.product.mapper.PickProductMapper;
import com.jiuji.pick.service.product.mapper.ProductBindMapper;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.mapper.PartnerUserMapper;
import com.jiuji.pick.service.user.mapper.SupplierUserMapper;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-08
 */
@Slf4j
@Service
public class MessageInfoServiceImpl extends ServiceImpl<MessageInfoMapper, MessageInfo> implements MessageInfoService {

    @Resource
    private MessageInfoMapper messageInfoMapper;
    @Resource
    private PickProductMapper pickProductMapper;
    @Resource
    private SupplierUserMapper supplierUserMapper;
    @Resource
    private ProductBindMapper productBindMapper;
    @Resource
    private PartnerUserMapper partnerUserMapper;
    @Resource
    private MessageInfoUserMapper messageInfoUserMapper;
    @Resource
    private MessageInfoUserService messageInfoUserService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private CartInfoService cartInfoService;

    @Override
    public QueryMessageStatusVo noticeSign() {
        QueryMessageStatusVo infoSign = new QueryMessageStatusVo();
        infoSign.setBindStatus(this.queryBindStatus());
        infoSign.setSupplierStatus(this.querySupplierStatus());
        infoSign.setPickProductStatus(this.queryPickProductStatus());
        return infoSign;
    }

    @Override
    public int queryPickProductStatus() {
        return pickProductMapper.selectCount(
                new LambdaQueryWrapper<PickProduct>().
                        eq(PickProduct::getProductStatus, ProductStatusEnum.WAIT_UP.getCode()));
    }

    @Override
    public int querySupplierStatus() {
        return supplierUserMapper.selectCount(
                new LambdaQueryWrapper<SupplierUser>().
                        eq(SupplierUser::getStatus, SupplierUserStatusEnum.UNCHECKED.getCode()));

    }

    @Override
    public int queryBindStatus() {
        return productBindMapper.selectCount(
                new LambdaQueryWrapper<ProductBind>().
                        eq(ProductBind::getBindStatus, BindStatusEnum.WAIT.getCode()));

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result saveMessageInfo(MessageInfoSaveParam param) {
        // 字段校验
        if(Objects.isNull(param)){
            return Result.error("参数为空！");
        }

        if (null == param.getPushType()){
            return Result.error("请选择推送标识");
        }
        if (MessagePushTypeEnum.SPECIFY_USER.getCode().equals(param.getPushType()) && StringUtils.isEmpty(param.getUserIdStr())){
            return Result.error("请填写对象ID");
        }

        if (null == param.getUserType()){
            return Result.error("请选择通知对象类型");
        }
        //保存MessageInfo
        MessageInfo messageInfo = new MessageInfo();
        messageInfo.setTitle(param.getTitle());
        messageInfo.setContent(param.getContent());
        messageInfo.setPushType(param.getPushType());
        messageInfo.setUserType(param.getUserType());
        messageInfoMapper.insert(messageInfo);

        // 查询推送用户信息表
        // 合作伙伴推送
        if(MessagePushUserTypeEnum.PARTNER.getCode().equals(param.getUserType())){
            List<PartnerUser> partnerList = Lists.newArrayList();
            if(MessagePushTypeEnum.SPECIFY_USER.getCode().equals(param.getPushType())){
                List<Long> partnerIdList = CommonUtil.covertIdStr2Long(param.getUserIdStr());
                partnerList = partnerUserMapper.selectBatchIds(partnerIdList);
            }else if(MessagePushTypeEnum.ALL_USER.getCode().equals(param.getPushType())){
                // 全部推送
                partnerList = partnerUserMapper.selectList(null);
            }else {
                return Result.error("不合法的推送类型{}", String.valueOf(param.getPushType()));
            }
            if(CollectionUtils.isEmpty(partnerList)){
                return Result.error("未查询到合作伙伴信息！");
            }
            List<MessageInfoUser> messageUserList = Lists.newArrayList();
            partnerList.forEach(partnerUser -> {
                MessageInfoUser messageInfoUser = new MessageInfoUser();
                messageInfoUser.setUserId(partnerUser.getId());
                messageInfoUser.setMessageId(messageInfo.getId());
                messageInfoUser.setHasRead(MessageReadStatusEnum.UNREAD.getCode());
                messageInfoUser.setUserName(partnerUser.getName());
                messageUserList.add(messageInfoUser);
            });
            messageInfoUserService.saveBatch(messageUserList);
        }

        // 供应商推送
        if(MessagePushUserTypeEnum.SUPPLIER.getCode().equals(param.getUserType())){
            List<SupplierUser> supplierList = Lists.newArrayList();
            if(MessagePushTypeEnum.SPECIFY_USER.getCode().equals(param.getPushType())){
                List<Long> supplierIdList = CommonUtil.covertIdStr2Long(param.getUserIdStr());
                supplierList = supplierUserMapper.selectBatchIds(supplierIdList);
            }else if(MessagePushTypeEnum.ALL_USER.getCode().equals(param.getPushType())){
                // 全部推送
                supplierList = supplierUserMapper.selectList(null);
            }else {
                return Result.error("不合法的推送类型{}", String.valueOf(param.getPushType()));
            }
            if(CollectionUtils.isEmpty(supplierList)){
                return Result.error("未查询到供应商信息！");
            }
            List<MessageInfoUser> messageUserList = Lists.newArrayList();
            supplierList.forEach(supplierUser -> {
                MessageInfoUser messageInfoUser = new MessageInfoUser();
                messageInfoUser.setUserId(supplierUser.getId());
                messageInfoUser.setMessageId(messageInfo.getId());
                messageInfoUser.setHasRead(MessageReadStatusEnum.UNREAD.getCode());
                messageInfoUser.setUserName(supplierUser.getName());
                messageUserList.add(messageInfoUser);
            });
            messageInfoUserService.saveBatch(messageUserList);
        }

        return Result.success();
    }

    @Override
    public Result<QueryMessageInfoDetailsVo> queryMessageDetails(Long id) {
        if (null == id){
            return Result.error("无法获取 MessageInfo id");
        }
        QueryMessageInfoDetailsVo queryMessage= new QueryMessageInfoDetailsVo();
        MessageInfo messageInfo = messageInfoMapper.selectById(id);
        if(null == messageInfo){
            return Result.error("无法根据id 获取到对应的值");
        }
        Integer pushType = messageInfo.getPushType();
        if (MessagePushTypeEnum.SPECIFY_USER.getCode().equals(pushType)){
            List<MessageInfoUser> supplierUsersList = messageInfoUserMapper.selectList(
                    new LambdaQueryWrapper<MessageInfoUser>().
                            eq(MessageInfoUser::getMessageId,id));
            if (CollectionUtils.isEmpty(supplierUsersList)){
                return Result.error("无法根据用户ID到用户信息的内容");
            }
                String str = supplierUsersList.stream().map(MessageInfoUser::getUserId).map(String::valueOf).collect(Collectors.joining(","));
                queryMessage.setUserIdStr(str);
        }
        queryMessage.setContent(messageInfo.getContent());
        queryMessage.setTitle(messageInfo.getTitle());
        queryMessage.setPushType(messageInfo.getPushType());
        queryMessage.setUserType(messageInfo.getUserType());
        queryMessage.setId(messageInfo.getId());
        queryMessage.setCreateTime(messageInfo.getCreateTime());
        queryMessage.setCreateTime(messageInfo.getCreateTime());
        return Result.success(queryMessage);
    }

    @Override
    public Result<Page<UserMessageInfoVo>> queryUserMessageInfo(QueryUserMessageParam param) {
        if(param == null) {
            return Result.errorInfo("请求参数错误");
        }
        Long userId = null;
        if(MessagePushUserTypeEnum.SUPPLIER.getCode().equals(param.getType())) {
            userId = currentRequestComponent.getSupplierId();
        } else if (MessagePushUserTypeEnum.PARTNER.getCode().equals(param.getType())) {
            userId = currentRequestComponent.getPartnerId();
        }
        if(userId == null) {
            return Result.notLoginError();
        }
        Page<UserMessageInfoVo> page = new Page<>(param.getCurrentPage(),param.getSize());
        Page<UserMessageInfoVo> userMessageInfoVoPage = baseMapper.queryUserMessageInfo(page, userId);
        if(userMessageInfoVoPage == null || CollectionUtils.isEmpty(userMessageInfoVoPage.getRecords())) {
            return Result.noData(page);
        }
        return Result.success(userMessageInfoVoPage);
    }

    @Override
    public Page pageList(MessageInfoQuery messageInfoQuery) {
        IPage page = new Page(messageInfoQuery.getCurrent(), messageInfoQuery.getSize());
        LambdaQueryWrapper<MessageInfo> query = new LambdaQueryWrapper<>();
        if(null != messageInfoQuery.getUserType()){
            query.eq(MessageInfo::getUserType, messageInfoQuery.getUserType());
        }
        if(StringUtils.isNotEmpty(messageInfoQuery.getTitle())){
            query.like(MessageInfo::getTitle, messageInfoQuery.getTitle());
        }
        query.orderByDesc(MessageInfo::getCreateTime);
        IPage<MessageInfo> iPage = baseMapper.selectPage(page, query);
        if (CollectionUtils.isEmpty(iPage.getRecords())) {
            return new Page();
        }
        List<MessageInfoVO> messageInfoVOList = Lists.newArrayList();
        iPage.getRecords().forEach(messageInfo -> {
            MessageInfoVO messageVO = BeanCopyUtil.copy(messageInfo, MessageInfoVO.class);
            messageVO.setPushTypeDesc(MessagePushTypeEnum.pareDesc(messageVO.getPushType()));
            messageVO.setUserTypeDesc(MessagePushUserTypeEnum.pareDesc(messageVO.getUserType()));
            messageInfoVOList.add(messageVO);
        });
        Page pageInfo = new Page(iPage.getCurrent(), iPage.getSize(), iPage.getTotal());
        pageInfo.setRecords(messageInfoVOList);
        return pageInfo;
    }

    @Override
    public Result<String> userMessageDeal(UserMessageDealParam param) {
        if(param == null) {
            return Result.errorInfo("请求参数错误");
        }
        Long userId = null;
        if(param.getType().equals(MessagePushUserTypeEnum.SUPPLIER.getCode())) {
            userId = currentRequestComponent.getSupplierId();
        } else if (param.getType().equals(MessagePushUserTypeEnum.PARTNER.getCode())) {
            userId = currentRequestComponent.getPartnerId();
        }
        if(userId == null) {
            return Result.notLoginError();
        }
        if(param.getOperateType() == 0) {
            // 单条已读
            if(param.getUserMessageId() == null) {
                return Result.errorInfo("消息ID不能为空");
            }
            MessageInfoUser messageInfoUser = messageInfoUserService.getById(param.getUserMessageId());
            if(messageInfoUser == null) {
                return Result.errorInfo("消息记录不存在");
            }
            messageInfoUser.setHasRead(MessageReadStatusEnum.HAVE_READ.getCode());
            messageInfoUser.setUpdateTime(LocalDateTime.now());
            messageInfoUserService.saveOrUpdate(messageInfoUser);
        } else if (param.getOperateType() == 1) {
            // 全部已读
            LambdaUpdateWrapper<MessageInfoUser> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(MessageInfoUser::getHasRead, MessageReadStatusEnum.HAVE_READ.getCode());
            wrapper.set(MessageInfoUser::getUpdateTime, LocalDateTime.now());
            wrapper.eq(MessageInfoUser::getUserId, userId);
            messageInfoUserService.update(wrapper);
        }
        return Result.success("处理成功");
    }

    @Override
    public Result<UserCountInfoReq> getUnreadMessageCount(Integer type) {
        Long userId = null;
        if(MessagePushUserTypeEnum.SUPPLIER.getCode().equals(type)) {
            userId = currentRequestComponent.getSupplierId();
        } else if (MessagePushUserTypeEnum.PARTNER.getCode().equals(type)) {
            userId = currentRequestComponent.getPartnerId();
        }
        if(userId == null) {
            return Result.notLoginError();
        }
        int unreadCount = messageInfoUserService.count(new LambdaQueryWrapper<MessageInfoUser>().eq(MessageInfoUser::getUserId, userId).eq(MessageInfoUser::getHasRead,MessageReadStatusEnum.UNREAD.getCode()));
        // 购物车数量查询
        Integer cartCount = cartInfoService.lambdaQuery().eq(CartInfo::getPartnerId, userId)
                .list().stream().map(cartInfo -> Optional.ofNullable(cartInfo.getProductCount()).orElse(NumberConstant.ZERO))
                .reduce(Integer::sum)
                .orElse(NumberConstant.ZERO);
        UserCountInfoReq userCountInfoReq = new UserCountInfoReq();
        userCountInfoReq.setCartCount(cartCount);
        userCountInfoReq.setUnreadCount(unreadCount);
        return Result.success(userCountInfoReq);
    }
}

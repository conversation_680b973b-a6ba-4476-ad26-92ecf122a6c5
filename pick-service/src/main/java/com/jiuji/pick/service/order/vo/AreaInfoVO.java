package com.jiuji.pick.service.order.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 门店信息
 *
 * <AUTHOR>
 * @since 2021-5-12
 */
@Data
public class AreaInfoVO {

    private List<AreaOptions> areaOptions;

    @Data
    public static class AreaOptions {

        @JsonInclude(value = JsonInclude.Include.NON_NULL)
        private List<AreaOptions> children;

        private String id;
        private String label;
    }
}

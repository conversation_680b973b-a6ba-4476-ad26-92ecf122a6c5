/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_category_search_word")
public class CategorySearchWord extends Model<CategorySearchWord> implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("search_word")
    private String searchWord;

    @TableField("category_id")
    private Integer categoryId;


    public static final String CATEGORYID = "category_id";

    public static final String ID = "id";

    public static final String SEARCHWORD = "search_word";

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

package com.jiuji.pick.service.order.service;

import com.jiuji.pick.common.enums.OrderLogTypeEnum;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.entity.OrderInfoLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 订单操作日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-20
 */
public interface OrderInfoLogService extends IService<OrderInfoLog> {

    void errorLog(boolean orderUpdate, boolean productVersionUpdate, boolean orderDetailUpdate, boolean remove,
                  OrderInfo orderInfo, Long orderNo, List<Long> cartIdList);

    void saveOrderLog(String content, OrderLogTypeEnum type, Long orderId, Long userId, String userName);
}

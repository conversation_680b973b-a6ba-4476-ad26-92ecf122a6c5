/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.service;




import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.entity.BrandCategory;
import com.jiuji.pick.service.product.vo.BindBrandToCategoryVO;
import com.jiuji.pick.service.product.vo.BrandCategoryVO;

import java.util.List;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
public interface BrandCategoryService extends IService<BrandCategory> {

    /**
     * 获取所有数据，包括对应的分类名和品牌名
     */
    List<BrandCategoryVO> listAllWithName();

    /**
     * 通过品牌id获取分类id集合
     */
    List<Integer> selectCateIdbyBrandId(Integer brandId);

    /**
     * 保存品牌和分类的关联
     *
     * @param associatedCategoryIdList 品牌分类List
     * @param brandId 品牌id
     * @return 是否保存成功
     */
    boolean bindCategoryToBrand(List<Integer> associatedCategoryIdList, Integer brandId);

    /**
     * 绑定品牌到分类
     *
     * @param bindBrandToCategoryVO VO
     * @return 是否成功
     */
    boolean bindBrandToCategory(BindBrandToCategoryVO bindBrandToCategoryVO);

    /**
     * 通过分类id查询品牌
     */
    List<Integer> getBrandIdsByCid(List<Integer> categoryId);
}

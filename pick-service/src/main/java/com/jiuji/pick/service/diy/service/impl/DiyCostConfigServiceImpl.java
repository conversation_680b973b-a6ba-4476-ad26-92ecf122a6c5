package com.jiuji.pick.service.diy.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.CommonUtils;
import com.jiuji.cloud.office.service.PartnerFinanceCloud;
import com.jiuji.cloud.office.vo.PartnerBalanceReq;
import com.jiuji.cloud.office.vo.PartnerBalanceRes;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.diy.entity.DiyCostConfig;
import com.jiuji.pick.service.diy.enums.DiyCostKindEnum;
import com.jiuji.pick.service.diy.enums.DiySearchTypeEnum;
import com.jiuji.pick.service.diy.enums.MoneyKindEnum;
import com.jiuji.pick.service.diy.enums.ProductInfoQueEnum;
import com.jiuji.pick.service.diy.mapper.DiyCostConfigMapper;
import com.jiuji.pick.service.diy.service.IDiyCostConfigService;
import com.jiuji.pick.service.diy.vo.req.DiyCostConfigBatchReq;
import com.jiuji.pick.service.diy.vo.req.DiyCostConfigSearchReq;
import com.jiuji.pick.service.diy.vo.req.TenantBalanceReq;
import com.jiuji.pick.service.diy.vo.res.DiyCostConfigRes;
import com.jiuji.pick.service.diy.vo.res.DiyCostConfigSearchRes;
import com.jiuji.pick.service.diy.vo.res.ExportDetailVO;
import com.jiuji.pick.service.diy.vo.res.TenantBalanceExportRes;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.service.OrderInfoService;
import com.jiuji.pick.service.rpc.cloud.OaOfficeCloud;
import com.jiuji.pick.service.rpc.cloud.OaStockCloud;
import com.jiuji.pick.service.rpc.vo.*;
import com.jiuji.tc.common.vo.R;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-06-16
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DiyCostConfigServiceImpl extends ServiceImpl<DiyCostConfigMapper, DiyCostConfig> implements IDiyCostConfigService {


    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Resource
    private OaStockCloud oaStockCloud;

    @Resource
    private OaOfficeCloud oaOfficeCloud;
    @Resource
    private OrderInfoService orderInfoService;


    private static final Integer EXPORT_EXCEL_MAX=50000;

    @Resource
    private PartnerFinanceCloud partnerFinanceCloud;

    //过滤退款和购买类型
    private final static List<String> kindFilterList = Arrays.asList(MoneyKindEnum.PURCHASE.getCode().toString(), MoneyKindEnum.REFUND.getCode().toString());


    @Override
    public void tenantBalanceExport(HttpServletResponse response, TenantBalanceReq tenantBalanceReq) {
        tenantBalanceReq.setCurrent(NumberFormat.FRACTION_FIELD);
        tenantBalanceReq.setSize(EXPORT_EXCEL_MAX);
        Page<PartnerBalanceRes> page = selectTenantBalance(tenantBalanceReq);
        List<PartnerBalanceRes> records = page.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        List<TenantBalanceExportRes> collect = records.stream().map(item -> {
            TenantBalanceExportRes tenantBalanceExportRes = new TenantBalanceExportRes();
            BeanUtils.copyProperties(item, tenantBalanceExportRes);
            return tenantBalanceExportRes;
        }).collect(Collectors.toList());
        ExcelWriter excel = ExcelUtil.getWriter();
        excel.addHeaderAlias("realName", "采购方");
        excel.addHeaderAlias("balance", "余额");
        excel.write(collect, true);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        ServletOutputStream out = null;
        try {
            String fileName = URLEncoder.encode("采购方余额管理", "UTF-8") + StringPool.UNDERSCORE + LocalDateTime.now().toLocalDate() + ".xls";
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
        } catch (Exception e) {
            throw new BizException("导出excel出错");
        } finally {
            excel.flush(out, true);
            excel.close();
            IoUtil.close(out);
        }
    }


    @Override
    public Page<PartnerBalanceRes> selectTenantBalance(TenantBalanceReq tenantBalanceReq) {
        PartnerBalanceReq req = new PartnerBalanceReq();
        req.setCurrent(tenantBalanceReq.getCurrent());
        req.setSize(tenantBalanceReq.getSize());
        req.setPartnerName(tenantBalanceReq.getPartnerName());
        req.setMaxBalance(tenantBalanceReq.getMaxBalance());
        R<Page<PartnerBalanceRes>> pageR = partnerFinanceCloud.pagePartnerBalance(req);
        log.warn("严选查询合作伙伴余额，传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(req),JSONUtil.toJsonStr(pageR));
        if(pageR.getCode()!=0){
            throw new BizException(Optional.ofNullable(pageR.getMsg()).orElse(pageR.getUserMsg()));
        }
        return pageR.getData();
    }



    @Override
    public Page<DiyCostConfigSearchRes> getCostConfigList(DiyCostConfigSearchReq req) {
        int current = req.getCurrent() == 0 ? 1 : req.getCurrent();
        int size = req.getSize();

        Page<DiyCostConfigSearchRes> page = new Page<>(current, size);
        // 查询数据
        page = this.baseMapper.pageList(page,req);

        for (DiyCostConfigSearchRes x : page.getRecords()) {
            if (Objects.nonNull(x.getQue())) {
                x.setQueName(ProductInfoQueEnum.getDescByCode(x.getQue()));
            }
        }
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateDiyCostConfig(DiyCostConfigBatchReq vo) {
        List<DiyCostConfigBatchReq.DiyCostConfigIdReq> updateList = vo.getUpdateList();
        for (DiyCostConfigBatchReq.DiyCostConfigIdReq req : updateList) {
            Integer ppriceid = req.getPpriceid();
            if (Objects.isNull(ppriceid)) {
                throw new BizException("ppid不能为空");
            }
            DiyCostConfig one = this.lambdaQuery()
                    .eq(DiyCostConfig::getPpriceid, ppriceid).one();
            if (Objects.isNull(one)) {
                DiyCostConfig entity = new DiyCostConfig();
                BeanUtils.copyProperties(req, entity);
                this.save(entity);
            } else {
                LambdaUpdateChainWrapper<DiyCostConfig> eq = this.lambdaUpdate().eq(DiyCostConfig::getPpriceid, ppriceid);
                if (Objects.nonNull(req.getSaleCost())) {
                    eq.set(DiyCostConfig::getSaleCost, req.getSaleCost());
                }
                if (Objects.nonNull(req.getPickwebCost())) {
                    eq.set(DiyCostConfig::getPickwebCost, req.getPickwebCost());
                }
                eq.set(DiyCostConfig::getUpdateTime, LocalDateTime.now());
                eq.update();
            }
        }
        return true;
    }

    @Override
    public Page<DiyCostFlowSearchRes> getDiyCostFlowPageList(DiyCostFlowSearchReq query) {
        String searchValue = query.getSearchValue();
        if(!org.springframework.util.StringUtils.isEmpty(searchValue)){
            query.setSearchValue(searchValue.replaceAll("\t",""));
        }
        if(org.springframework.util.StringUtils.isEmpty(query.getSearchType()) && !org.springframework.util.StringUtils.isEmpty(searchValue)){
            query.setSearchType(DiySearchTypeEnum.ORDER.getCode());
        }
        Page<DiyCostFlowSearchRes> data = new Page<>();

        Boolean isMall = query.getIsMall();

        Result<List<PickInfoVo>> listResult = oaOfficeCloud.listDiyShell(new ArrayList<>());
        List<PickInfoVo> pickInfoVoList = listResult.getData();
        if (CollectionUtils.isEmpty(pickInfoVoList)){
            throw new BizException("获取不到合作伙伴档案,请稍后再试");
        }
        pickInfoVoList = pickInfoVoList.stream()
                .filter(x-> Objects.nonNull(x.getCustomerId())).collect(Collectors.toList());
        Map<String, PickInfoVo> userIdToPickInfoVoMap = pickInfoVoList.stream()
                .collect(Collectors.toMap(PickInfoVo::getCustomerId, Function.identity(), (v1, v2) -> v1));

        Map<String, PickInfoVo> userNameToPickInfoVoMap = pickInfoVoList.stream()
                .collect(Collectors.toMap(PickInfoVo::getCustomerRealName, Function.identity(), (v1, v2) -> v1));

        Set<String> allUserIdList = userIdToPickInfoVoMap.keySet();

        Map<Long, String> xtenantIdToCustomerIdMap =
                pickInfoVoList.stream().collect(Collectors.toMap(PickInfoVo::getXtenant, PickInfoVo::getCustomerId, (v1, v2) -> v1));
        query.setUserIdList( new ArrayList<>(allUserIdList));

        Long xtenant = null;
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (Objects.nonNull(partnerTokenInfo) && Objects.nonNull(partnerTokenInfo.getXtenant())) {
            xtenant = partnerTokenInfo.getXtenant();
//            if (xtenant == 50000) {
//                xtenant = 10050L;
//            }
            String userId = xtenantIdToCustomerIdMap.get(xtenant);
            if (StringUtils.isNotEmpty(userId)){
                query.setUserId(Convert.toStr(userId));
            }
        }

        if (isMall && StringUtils.isEmpty(query.getUserId())) {
            return data;
        }

        List<String> kindList = query.getKind();
        if (CollectionUtils.isNotEmpty(kindList)){
            List<String> newKind = new ArrayList<>();
            for (String kind : kindList) {
                if (kind.equals(DiyCostKindEnum.ONE.getCode())){
                    newKind.add(MoneyKindEnum.DIYCoverPay.getCode().toString());
                }
                if (kind.equals(DiyCostKindEnum.TWO.getCode())){
                    newKind.add(MoneyKindEnum.DIYCoverPay.getCode().toString());
                }

                if (kind.equals(DiyCostKindEnum.THREE.getCode())){
                    newKind.add(MoneyKindEnum.SYSTEM.getCode().toString());
                    newKind.add(MoneyKindEnum.MANUAL_OPERATION_LOG.getCode().toString());
                    newKind.add(MoneyKindEnum.MANUAL_OPERATION_NO_LOG.getCode().toString());
                    newKind.add(MoneyKindEnum.MANUAL_IMPORT_LOG.getCode().toString());
                }
                //购买
                if (kind.equals(DiyCostKindEnum.PURCHASE.getCode())){
                    newKind.add(MoneyKindEnum.PURCHASE.getCode().toString());
                }
                //退款
                if (kind.equals(DiyCostKindEnum.REFUND.getCode())){
                    newKind.add(MoneyKindEnum.REFUND.getCode().toString());
                }
            }

            query.setKind(newKind);
        }
        if (StringUtils.isNotEmpty(query.getUserName())){
            PickInfoVo pickInfoVo = Optional.ofNullable(userNameToPickInfoVoMap.get(query.getUserName())).orElse(userIdToPickInfoVoMap.get(query.getUserName()));
            if (Objects.nonNull(pickInfoVo)){
                query.setUserId(pickInfoVo.getCustomerId());
            }
        }
        if(DiySearchTypeEnum.ORDER.getCode().equals(query.getSearchType())){
            // 对订单号进行转化 输入的单号和转化的OA单号都进行查询
            Optional.ofNullable(query.getSearchValue()).ifPresent(subId->{
                query.setSubId(subId.trim());
                Optional.ofNullable(orderInfoService.getById(subId.trim())).ifPresent(orderInfo -> {
                    StringJoiner joiner = new StringJoiner(",");
                    String saleOrderNo = orderInfo.getSaleOrderNo();
                    query.setSubId(joiner.add(subId).add(saleOrderNo).toString());
                });
            });
        }

        Result<Page<DiyCostFlowSearchRes>> result = oaStockCloud.getDiyCostFlowPageListV2(query);
        data = result.getData();
        List<DiyCostFlowSearchRes> records = data.getRecords();
        Map<String, Long> orderMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(records)){
            List<String> subIdList = records.stream().filter(item-> kindFilterList.contains(item.getKind()))
                    .map(DiyCostFlowSearchRes::getSubId)
                    .filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(subIdList)){
                List<OrderInfo> list = orderInfoService.lambdaQuery().in(OrderInfo::getSaleOrderNo, subIdList).list();
                if(CollectionUtils.isNotEmpty(list)){
                    orderMap = list.stream().collect(Collectors.toMap(OrderInfo::getSaleOrderNo, OrderInfo::getId, (n1, n2) -> n2));
                }
            }
        }


        for (DiyCostFlowSearchRes record : records) {
            String userid = record.getUserid();
            PickInfoVo pickInfoVo = userIdToPickInfoVoMap.get(userid);
            if (Objects.nonNull(pickInfoVo)){
                record.setUserName(pickInfoVo.getCustomerRealName());
            }
            String subId = record.getSubId();
            if(kindFilterList.contains(record.getKind())){
                record.setStrictSelectionOrderId(orderMap.get(subId));
            } else {
                //判断是否位数字
                if(StringUtils.isNumeric(subId)){
                    record.setStrictSelectionOrderId(Long.parseLong(subId));
                }
            }
            MoneyKindEnum moneyKindEnum = MoneyKindEnum.parseCode(Convert.toInt(record.getKind()));
            if (Objects.nonNull(moneyKindEnum)){
                record.setKind(moneyKindEnum.getName());
            }
            if (StringUtils.isNotEmpty(record.getMoney())){
                BigDecimal money = new BigDecimal(record.getMoney());
                record.setMoney(Convert.toStr(money.setScale(2, BigDecimal.ROUND_HALF_UP)));
            }
            if (StringUtils.isNotEmpty(record.getSmoney())){
                BigDecimal money = new BigDecimal(record.getSmoney());
                record.setSmoney(Convert.toStr(money.setScale(2, BigDecimal.ROUND_HALF_UP)));
            }

            if (StringUtils.isNotEmpty(record.getDtime())){
                try {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = inputFormat.parse(record.getDtime());
                String formattedDate = outputFormat.format(date);
                record.setDtime(formattedDate);
                } catch (Exception e) {
                    throw new BizException("时间转换错误："+record.getDtime());
                }
            }
        }


        return data;
    }

    @Override
    public Result<String> getDiyCostFlowPageListExportDetail(HttpServletResponse response, DiyCostFlowSearchReq req){
        req.setCurrent(1);
        req.setSize(EXPORT_EXCEL_MAX);
        List<String> kindList = Optional.ofNullable(req.getKind()).orElse(new ArrayList<>());
        List<String> strings = Arrays.asList(DiyCostKindEnum.PURCHASE.getCode(), DiyCostKindEnum.REFUND.getCode());
        List<String> intersection = new ArrayList<>(CollectionUtils.intersection(kindList, strings));
        if(CollectionUtils.isEmpty(intersection)){
            return Result.error("只有类型为"+DiyCostKindEnum.PURCHASE.getDesc()+","+DiyCostKindEnum.REFUND.getDesc()+"可以导出明细");
        }
        req.setKind(intersection);
        Page<DiyCostFlowSearchRes> res = this.getDiyCostFlowPageList(req);
        List<DiyCostFlowSearchRes> dataSource = res.getRecords();
        if(CollectionUtils.isEmpty(dataSource)){
            return Result.error("导出数据为空");
        }
        //组装导出明细的数据
        List<ExportDetailVO> exportDetailVOList = dataSource.stream()
                .filter(item -> CollectionUtils.isNotEmpty(item.getDetailInfoList()))
                .flatMap(item -> item.getDetailInfoList().stream()
                        .map(detail -> {
                            ExportDetailVO exportDetailVO = new ExportDetailVO();
                            BeanUtils.copyProperties(detail, exportDetailVO);
                            exportDetailVO.setUserid(item.getUserid())
                                    .setUserName(item.getUserName())
                                    .setSubId(item.getSubId())
                                    .setKind(item.getKind());
                            return exportDetailVO;
                        })
                )
                .limit(EXPORT_EXCEL_MAX)
                .collect(Collectors.toList());
        ExcelWriter excel = ExcelUtil.getWriter();
        excel.addHeaderAlias("userid", "采购方id");
        excel.addHeaderAlias("userName", "采购方");
        excel.addHeaderAlias("kind", "变动类型");
        excel.addHeaderAlias("subId", "单号");
        excel.addHeaderAlias("skuId", "sku_id");
        excel.addHeaderAlias("productName", "商品名称");
        excel.addHeaderAlias("productColor", "商品规格");
        excel.addHeaderAlias("count", "数量");
        excel.addHeaderAlias("totalAmount", "总金额");
        excel.addHeaderAlias("time", "时间");
        excel.addHeaderAlias("remark", "备注");
        excel.write(exportDetailVOList, true);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        ServletOutputStream out = null;
        try {
            String fileName = URLEncoder.encode("对账单明细", "UTF-8") + StringPool.UNDERSCORE + LocalDateTime.now().toLocalDate() + ".xls";
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
        } catch (Exception e) {
            throw new BizException("导出excel出错");
        } finally {
            excel.flush(out, true);
            excel.close();
            IoUtil.close(out);
        }
        return Result.success("导出成功");
    }

    @Override
    public void getDiyCostFlowPageListExport(DiyCostFlowSearchReq req, HttpServletResponse response) {
        req.setCurrent(1);
        req.setSize(50000);
        Page<DiyCostFlowSearchRes> res = this.getDiyCostFlowPageList(req);
        List<DiyCostFlowSearchRes> dataSource = res.getRecords();
        List<DiyCostFlowSearchExcelRes> excelRes = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dataSource)){
            dataSource.forEach(item->{
                DiyCostFlowSearchExcelRes diyCostFlowSearchExcelRes = new DiyCostFlowSearchExcelRes();
                BeanUtils.copyProperties(item,diyCostFlowSearchExcelRes);
                excelRes.add(diyCostFlowSearchExcelRes);
            });
        }

        ExcelWriter excel = ExcelUtil.getWriter();

        excel.addHeaderAlias("userid", "采购方id");
        excel.addHeaderAlias("userName", "采购方");
        excel.addHeaderAlias("kind", "变动类型");
        excel.addHeaderAlias("subId", "单号");
        excel.addHeaderAlias("money", "发生额");
        excel.addHeaderAlias("smoney", "余额");
        excel.addHeaderAlias("dtime", "时间");
        excel.addHeaderAlias("comment", "备注");
        excel.write(excelRes, true);

        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        ServletOutputStream out = null;
        try {
            String fileName = URLEncoder.encode("对账单", "UTF-8") + StringPool.UNDERSCORE + LocalDateTime.now().toLocalDate() + ".xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
        } catch (Exception e) {
            throw new BizException("导出excel出错");
        } finally {
            excel.flush(out, true);
            excel.close();
            IoUtil.close(out);
        }
    }

    @Override
    public List<DiyCostConfigRes> getDiyCostConfigListByPpriceId(DiyCostConfigSearchReq query) {
        List<Long> ppriceIdList = query.getPpriceIdList();
        if (CollectionUtils.isEmpty(ppriceIdList)){
            throw new BizException("ppid列表不能为空");
        }
        List<DiyCostConfigRes> result = new ArrayList<>();

        List<DiyCostConfig> list = this.lambdaQuery().in(DiyCostConfig::getPpriceid, ppriceIdList)
                .eq(DiyCostConfig::getDelFlag,0).list();
        for (DiyCostConfig diyCostConfig : list) {
            DiyCostConfigRes temp = new DiyCostConfigRes();
            BeanUtils.copyProperties(diyCostConfig,temp);
            result.add(temp);
        }
        return result;
    }

    @Override
    public DiyCostFlowLeftRes getLeftDiyCostList(DiyCostFlowLeftReq query) {
        DiyCostFlowLeftRes result = new DiyCostFlowLeftRes();
        Long xtenant = query.getXtenant();
        if (Objects.isNull(xtenant)) {
            PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
            if (Objects.nonNull(partnerTokenInfo) && Objects.nonNull(partnerTokenInfo.getXtenant())) {
                xtenant = partnerTokenInfo.getXtenant();
            }
        }
//        if (xtenant == 50000) {
//            xtenant = 10050L;
//        }
        Result<List<PickInfoVo>> listResult = oaOfficeCloud.listDiyShell(new ArrayList<>());
        List<PickInfoVo> pickInfoVoList = listResult.getData();
        if (CollectionUtils.isEmpty(pickInfoVoList)){
            throw new BizException("获取不到合作伙伴档案,请稍后再试");
        }
        Map<Long, PickInfoVo> pickInfoVoMap = pickInfoVoList.stream().collect(Collectors.toMap(PickInfoVo::getXtenant, Function.identity(), (v1, v2) -> v1));


        PickInfoVo pickInfoVo = pickInfoVoMap.get(xtenant);
        if (Objects.nonNull(pickInfoVo)) {
            DiyCostFlowSearchReq temp = new DiyCostFlowSearchReq();
            temp.setUserIdList(Arrays.asList(pickInfoVo.getCustomerId()));
            Result<List<DiyCostFlowLeftRes>> leftDiyCostList = oaStockCloud.getLeftDiyCostList(temp);
            List<DiyCostFlowLeftRes> data = leftDiyCostList.getData();
            DiyCostFlowLeftRes diyCostFlowLeftRes1 = data.stream().findFirst().orElse(null);
            if(Objects.nonNull(diyCostFlowLeftRes1)){
                result.setXtenant(xtenant);
                result.setUserid(diyCostFlowLeftRes1.getUserid());
                result.setSaveMoney(diyCostFlowLeftRes1.getSaveMoney());
                result.setErdu(diyCostFlowLeftRes1.getErdu());
            }
        }
        return result;
    }

    @Override
    public DiyCostFlowUserRes getNameList(DiyCostFlowUserReq req) {
        DiyCostFlowUserRes res = new DiyCostFlowUserRes();
        Result<List<PickInfoVo>> listResult = oaOfficeCloud.listDiyShell(new ArrayList<>());
        List<PickInfoVo> pickInfoVoList = listResult.getData();
        if (CollectionUtils.isEmpty(pickInfoVoList)){
            throw new BizException("获取不到合作伙伴档案,请稍后再试");
        }
        List<String> userNameList = pickInfoVoList.stream()
                .map(PickInfoVo::getCustomerRealName)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (StringUtils.isNotEmpty(req.getUserName())){
            userNameList = userNameList.stream()
                    .filter(x -> x.contains(req.getUserName()))
                    .collect(Collectors.toList());
        }
        res.setUserNameList(userNameList);
        return res;
    }

}

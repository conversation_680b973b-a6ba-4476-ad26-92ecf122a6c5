package com.jiuji.pick.service.order.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OaOrderDTO {

    @JSONField(name = "sub_id")
    private String subId;

    /**
     * 当前登录用户名称
     */
    private String userName;

    /**
     * 采货王订单id
     */
    private Long purchaseOrderId;


    /**
     * 供应商ID
     */
    private Long supplierId;



    /**
     * 备注
     */
    @JSONField(name = "beizhu")
    private String remark;

    /**
     * 采购单类别 0  采购  1 库存调价
     */
    private Integer subKind;

    /**
     * 标题
     */
    @JSONField(name = "title_")
    private String title;

    /**
     * 渠道
     */
    @JSONField(name = "insourceid")
    private String inSourceId;

    /**
     * 大小件 true 大件  false 小件
     */
    @JSONField(name = "ismodiy")
    private Boolean isBulky;

    /**
     * 门店id
     */
    private Integer companyAreaId;
    /**
     * 小件 = pj 维修 = wx
     * 此参数如果为空 OA 默认 pj
     */
    private String kinds;


    /**
     * 授权隔离ID
     */
    @JSONField(name = "authorizeid")
    private Integer authId;

    /**
     * 商品详情
     */
    private List<PurchaseGoods> list;

    /*******大件用 start*********/

    /**
     * 渠道 大件专用 注意json  key 值与小件不一致
     */
    @JSONField(name = "insourceId")
    private String inSourceId4Bulky;

    /**
     * 操作人 （大件用）
     */
    private String inuser;

    /**
     * 门店id （大件用）
     */
    private Integer areaid;

    /**
     * 商品详情 （大件）
     */
    private List<BulkyPurchaseGoods> mkcIdList;

    /*******大件用 end*********/



    /**
     * 采购商品（小件）
     */
    @Data
    public static class PurchaseGoods {

        @JSONField(name = "inprice")
        private BigDecimal price;

        private Integer count;

        @JSONField(name = "ppriceid")
        private Integer ppid;

        @JSONField(name = "product_name")
        private String productName;

        /**
         * 备注
         */
        private String comment;
    }

    /**
     * 采购商品 大件
     */
    @Data
    public static class BulkyPurchaseGoods {

        @JSONField(name = "inprice")
        private BigDecimal price;


        @JSONField(name = "ppriceid")
        private Integer ppid;

        /**
         * 采购真实价格 = 商品价格+运费
         */
        private BigDecimal inbeihuoprice;
    }

}

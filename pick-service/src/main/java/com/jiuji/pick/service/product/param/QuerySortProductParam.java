package com.jiuji.pick.service.product.param;

import com.jiuji.pick.common.param.BasePageParam;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/07
 * @Time 10:31
 */
@Data
public class QuerySortProductParam extends BasePageParam {
    private String type;
    private BigDecimal maxPrice;
    private BigDecimal minPrice;
    private Integer sortPrice;
    private Integer sortSaleCount;
    private Integer partnerType;
    private List<Long> filterPpidList;
}

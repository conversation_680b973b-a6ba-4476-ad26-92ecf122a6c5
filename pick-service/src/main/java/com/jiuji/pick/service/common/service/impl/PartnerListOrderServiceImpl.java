package com.jiuji.pick.service.common.service.impl;

import cn.hutool.json.JSONUtil;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.service.LogService;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.user.entity.PartnerUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("PartnerListOrderServiceImpl")
public class PartnerListOrderServiceImpl implements LogService {


    @Resource
    private NewOperateLogInfoService logInfoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Override
    public void systemSaveLog(Object param) {
        OATokenInfo oaTokenInfo = Optional.ofNullable(currentRequestComponent.getOATokenInfoBoWithoutCheck())
                .orElse(new OATokenInfo());
        CompletableFuture.runAsync(() -> {
            PartnerUser partnerUser = JSONUtil.toBean(JSONUtil.toJsonStr(param), PartnerUser.class);
            Boolean status = partnerUser.getStatus();
            String oldStatus = status ? "是" : "否";
            String newStatus = status ? "否" : "是";
            String comment = String.format("是否封禁止下单由【%s】改为【%s】", newStatus, oldStatus);
            NewOperateLogInfo newOperateLogInfo = new NewOperateLogInfo();
            newOperateLogInfo.setContent(comment).setCreateTime(LocalDateTime.now())
                    .setOptUserId(oaTokenInfo.getUserId().longValue()).setOptUserName(oaTokenInfo.getName())
                    .setType(NewOperateLogInfoTypeEnum.PARTNER_LIST_ORDER.getCode())
                    .setRelateId(partnerUser.getId() + "")
                    .setShowType(LogShowTypeEnum.ADMIN.getCode());
            logInfoService.saveSystemLog(newOperateLogInfo);
        });

    }
}

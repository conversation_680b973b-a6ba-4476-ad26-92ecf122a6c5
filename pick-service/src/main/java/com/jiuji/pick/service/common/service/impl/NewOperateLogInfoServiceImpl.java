package com.jiuji.pick.service.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.mapper.NewOperateLogInfoMapper;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.common.vo.SaveLogVo;
import com.jiuji.pick.service.common.vo.SelectLogVo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
@Slf4j
@Service
public class NewOperateLogInfoServiceImpl extends ServiceImpl<NewOperateLogInfoMapper, NewOperateLogInfo> implements NewOperateLogInfoService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;

    /**
     * 日志保存
     *
     * @param saveLogVo
     */
    @Override
    public void saveLog(SaveLogVo saveLogVo) {
        Long userId = null;
        String userName = null;
        if (saveLogVo.getShowType().equals(LogShowTypeEnum.SUPPLIER.getCode())) {
            SupplierTokenInfo tokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck())
                    .orElseThrow(() -> new BizException("登录信息过期，请重新登录"));
            userId = tokenInfo.getId();
            userName = tokenInfo.getLoginName();
        }
        if (saveLogVo.getShowType().equals(LogShowTypeEnum.PARTNER.getCode())) {
            PartnerTokenInfo tokenInfo = Optional.ofNullable(currentRequestComponent.getPartnerTokenInfoWithoutCheck())
                    .orElseThrow(() -> new BizException("登录信息过期，请重新登录"));
            userId = tokenInfo.getLoginOAUserId();
            userName = tokenInfo.getLoginOAUserName();
        }
        if (saveLogVo.getShowType().equals(LogShowTypeEnum.ADMIN.getCode())) {
            OATokenInfo tokenInfo = Optional.ofNullable(currentRequestComponent.getOATokenInfoBoWithoutCheck())
                    .orElseThrow(() -> new BizException("登录信息过期，请重新登录"));
            userId = tokenInfo.getUserId().longValue();
            userName = tokenInfo.getName();
        }
        NewOperateLogInfo newOperateLogInfo = new NewOperateLogInfo();
        newOperateLogInfo.setType(saveLogVo.getType())
                .setShowType(saveLogVo.getShowType())
                .setContent(saveLogVo.getContent())
                .setRelateId(saveLogVo.getRelateId())
                .setOptUserId(userId)
                .setOptUserName(userName)
                .setCreateTime(LocalDateTime.now());
        this.save(newOperateLogInfo);


    }

    /**
     * 系统操作日志保存
     *
     * @param newOperateLogInfo
     */
    @Override
    public void saveSystemLog(NewOperateLogInfo newOperateLogInfo) {
        String content = newOperateLogInfo.getContent();
        if (StringUtil.isNotBlank(content)) {
            this.save(newOperateLogInfo);
        }
    }

    @Override
    public Result<List<NewOperateLogInfo>> selectLog(SelectLogVo saveLogVo) {
        QueryWrapper<NewOperateLogInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("relate_id", saveLogVo.getRelateId())
                .eq(saveLogVo.getShowType() != null, "show_type", saveLogVo.getShowType());
        Integer type = saveLogVo.getType();
        // 因为日志不想进行系统区分，如果过之后要进行系统区分把这个代码删除   SUPPLIER_LIST(8, "供应商详情"),     SUPPLIER_INFORMATION(2, "供应商信息"),  都使用2来查询
        List<Integer> supplierTypeList = Arrays.asList(NewOperateLogInfoTypeEnum.SUPPLIER_LIST.getCode(), NewOperateLogInfoTypeEnum.SUPPLIER_INFORMATION.getCode());
        List<Integer> supplierGoodsList = Arrays.asList(NewOperateLogInfoTypeEnum.SUPPLY_MANAGEMENT.getCode(), NewOperateLogInfoTypeEnum.SUPPLIER_BOUND_GOODS_QUERY.getCode());
        List<Integer> commodityList = Arrays.asList(NewOperateLogInfoTypeEnum.COMMODITY_WAREHOUSE_MANAGEMENT.getCode(), NewOperateLogInfoTypeEnum.GOODS_ON_AND_OFF_SHELVES.getCode());
        List<Integer> partnerList = Arrays.asList(NewOperateLogInfoTypeEnum.PARTNER_LIST_ORDER.getCode(), NewOperateLogInfoTypeEnum.PARTNER_LIST.getCode());
        if (supplierTypeList.contains(type)) {
            queryWrapper.in("type", supplierTypeList);
        } else if (commodityList.contains(type)) {
            queryWrapper.in("type", commodityList);
        } else if (supplierGoodsList.contains(type)) {
            queryWrapper.in("type", supplierGoodsList);
        } else if(partnerList.contains(type)){
            queryWrapper.in("type", partnerList);
        } else {
            queryWrapper.eq("type", type);
        }
        queryWrapper.eq(saveLogVo.getShowType() != null, "show_type", saveLogVo.getShowType());
        queryWrapper.orderByAsc("create_time");
        List<NewOperateLogInfo> list = this.list(queryWrapper);
        return Result.success(list);
    }
}

package com.jiuji.pick.service.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.service.user.entity.SupplierUserAccount;
import com.jiuji.pick.service.user.mapper.SupplierUserAccountMapper;
import com.jiuji.pick.service.user.service.SupplierUserAccountService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
@Service
public class SupplierUserAccountServiceImpl extends ServiceImpl<SupplierUserAccountMapper, SupplierUserAccount> implements SupplierUserAccountService {


    @Override
    public List<SupplierUserAccount> listBySupplierUserId(Long supplierUserId) {
        LambdaQueryWrapper<SupplierUserAccount> query = new LambdaQueryWrapper<>();
        query.eq(SupplierUserAccount::getSupplierUserId, supplierUserId);
        return baseMapper.selectList(query);
    }
}

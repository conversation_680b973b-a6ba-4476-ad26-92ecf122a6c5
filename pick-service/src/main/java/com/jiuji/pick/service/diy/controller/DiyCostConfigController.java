package com.jiuji.pick.service.diy.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.office.service.PartnerFinanceCloud;
import com.jiuji.cloud.office.vo.PartnerBalanceRes;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.diy.enums.DiyCostSearchEnum;
import com.jiuji.pick.service.diy.enums.DiySearchTypeEnum;
import com.jiuji.pick.service.diy.service.IDiyCostConfigService;
import com.jiuji.pick.service.diy.vo.req.DiyCostConfigBatchReq;
import com.jiuji.pick.service.diy.vo.req.DiyCostConfigSearchReq;
import com.jiuji.pick.service.diy.vo.req.TenantBalanceReq;
import com.jiuji.pick.service.diy.vo.res.DiyCostConfigRes;
import com.jiuji.pick.service.diy.vo.res.DiyCostConfigSearchRes;
import com.jiuji.pick.service.rpc.vo.*;
import com.jiuji.tc.common.vo.R;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-06-16
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/diy/cost-configuration")
public class DiyCostConfigController {

    @Resource
    private IDiyCostConfigService diyCostConfigurationService;



    /**
     * 查询租户余额
     * @param tenantBalanceReq
     * @return
     */
    @PostMapping("/selectTenantBalance/v1")
    public Result<Page<PartnerBalanceRes>> selectTenantBalance(@RequestBody TenantBalanceReq tenantBalanceReq){
        Page<PartnerBalanceRes> page = diyCostConfigurationService.selectTenantBalance(tenantBalanceReq);
        return Result.success(page);
    }


    /**
     * 导出余额
     * @param response
     * @param tenantBalanceReq
     */
    @PostMapping("/tenantBalance/export/v1")
    public void tenantBalanceExport( HttpServletResponse response, @RequestBody TenantBalanceReq tenantBalanceReq) {
        diyCostConfigurationService.tenantBalanceExport(response,tenantBalanceReq);
    }


    /***
     * @description: 后台站内信列表
     * @Param: []
     * @date: 2021/5/28 11:47
     */
    @PostMapping("/list/v1")
    public Result<Page<DiyCostConfigSearchRes>> getCostConfigList(@RequestBody DiyCostConfigSearchReq query){
        DiyCostSearchEnum.onlyNumber(query.getSearchType(),query.getSearchValue());
        return Result.success(diyCostConfigurationService.getCostConfigList(query));
    }
    @PostMapping("/list-by-ppriceId/v1")
    public R<List<DiyCostConfigRes>> getDiyCostConfigListByPpriceId(@RequestBody DiyCostConfigSearchReq query){
        List<DiyCostConfigRes> diyCostFlowSearchResPage = diyCostConfigurationService.getDiyCostConfigListByPpriceId(query);
        return R.success(diyCostFlowSearchResPage);
    }

    /***
     * @description: 后台站内信列表
     * @Param: []
     * @date: 2021/5/28 11:47
     */
    @PostMapping("/cost-flow-list/v1")
    public R<IPage<DiyCostFlowSearchRes>> getDiyCostFlowPageList(@RequestBody DiyCostFlowSearchReq query){
        Page<DiyCostFlowSearchRes> diyCostFlowSearchResPage = diyCostConfigurationService.getDiyCostFlowPageList(query);
        return R.success(diyCostFlowSearchResPage);
    }


    @PostMapping("/left/v1")
    public R<DiyCostFlowLeftRes> getLeftDiyCostList(@RequestBody DiyCostFlowLeftReq query){
        DiyCostFlowLeftRes diyCostFlowSearchResPage = diyCostConfigurationService.getLeftDiyCostList(query);
        return R.success(diyCostFlowSearchResPage);
    }

    @PostMapping("/user/v1")
    public R<DiyCostFlowUserRes> getNameList(@RequestBody DiyCostFlowUserReq req){
        DiyCostFlowUserRes diyCostFlowSearchResPage = diyCostConfigurationService.getNameList(req);
        return R.success(diyCostFlowSearchResPage);
    }

    @PostMapping("/cost-flow-list/export/v1")
    public void getDiyCostFlowPageListExport(HttpServletRequest request, HttpServletResponse response, @RequestBody DiyCostFlowSearchReq req) {
        diyCostConfigurationService.getDiyCostFlowPageListExport(req,response);
    }

    @PostMapping("/cost-flow-list/exportDetail/v1")
    public Result<String> getDiyCostFlowPageListExportDetail( HttpServletResponse response, @RequestBody DiyCostFlowSearchReq req) {
       return diyCostConfigurationService.getDiyCostFlowPageListExportDetail(response,req);
    }


    /**
     * 保存
     *
     * @return
     */
    @PostMapping("/saveOrUpdate/v1")
    public Result<Boolean> saveOrUpdateDiyCostConfig(@RequestBody @Valid DiyCostConfigBatchReq req) {
        Boolean result = diyCostConfigurationService.saveOrUpdateDiyCostConfig(req);
        return Result.success(result);
    }
}


package com.jiuji.pick.service.product.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:zuofei
 * @Description
 * @Date 2020-08-11
 */
@NoArgsConstructor
@Data
public class ProductChangeSpecVo {


    /**
     * 可选择规格的sku
     */
    private List<Sku> sku;
    /**
     * 版本
     */
    private List<Sku.ListX> relation;

    /**
     * 默认已经选择的规格拼接串
     */
    private String selectedSpec;

    @NoArgsConstructor
    @Data
    public static class Sku implements Serializable {

        /**
         * title : 颜　　色 list : [{"value":"银色","ppid":53112,"diy":"47383-**************-**************-2023-2460","selected":true,"disable":true}]
         */
        private String title;//规格
        private List<ListX> list;//规格明细
        private transient int rank;//规格排序值

        @NoArgsConstructor
        @AllArgsConstructor
        @Data
        public static class ListX implements Serializable {

            /**
             * 规格明细值，例如黑色
             */
            private String value;
            /**
             * skuid
             */
            private long ppid;
            /**
             * 是否被选中
             */
            private boolean selected;
            /**
             * 是否可以选择
             */
            private boolean enable;
            /**
             * 规格明细id
             */
            @JsonIgnore
            private Integer standDetailId;

            @JsonIgnore
            private Integer count = 0;
            /**
             * 规格明细排序值
             */
            private Integer rank;

            private String imagePath;

            public ListX(String value, long ppid, boolean selected, boolean enable) {
                this.value = value;
                this.ppid = ppid;
                this.selected = selected;
                this.enable = enable;
            }

            public ListX(Integer standDetailId, Integer count) {
                this.standDetailId = standDetailId;
                this.count = count;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) {
                    return true;
                }

                if (o == null || getClass() != o.getClass()) {
                    return false;
                }

                ListX listX = (ListX) o;

                return new EqualsBuilder()
                        .append(ppid, listX.ppid)
                        .isEquals();
            }

            @Override
            public int hashCode() {
                return new HashCodeBuilder(17, 37)
                        .append(ppid)
                        .toHashCode();
            }

            @JsonIgnore
            private Boolean byGuess = false;


        }
    }

}

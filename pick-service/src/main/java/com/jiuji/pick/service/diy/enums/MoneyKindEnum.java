package com.jiuji.pick.service.diy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum MoneyKindEnum {

    /**
     * 对应c#的枚举信息： eSaveMoneyKind
     */
    充值(1,"充值"),
    PURCHASE(2,"购买"),
    维修费(3,"维修费"),
    加盟配件(5,"加盟配件"),
    加盟手机(6,"加盟手机"),
    加盟管理费用(7,"加盟管理费用"),
    REFUND(8,"退款"),
    换其它型号订金(9,"换其它型号订金"),
    手机转库存(10,"手机转库存"),
    手机转售后(11,"手机转售后"),
    自提点余额(12,"自提点余额"),
    回购(13,"回购"),
    加盟手机回收(14,"加盟手机回收"),
    加盟店调拨固定资产(15,"加盟店调拨固定资产"),
    加盟店九机服务(16,"加盟店九机服务"),
    加盟店代收(17,"加盟店代收"),
    代金券扣款(18,"代金券扣款"),
    加盟店微信红包订单(19,"加盟店微信红包订单"),
    OVG返现(20,"OVG返现"),
    竞拍定金(21,"竞拍定金"),
    二手良品调拨(22,"二手良品调拨"),
    良品加盟店提成(23,"良品加盟店提成"),
    竞价商提现冻结(24,"竞价商提现冻结"),
    竞价商提现(25,"竞价商提现"),
    客户价保扣款(26,"客户价保扣款"),
    回收转租机(27,"回收转租机"),
    竞价(28,"竞价"),
    竞价充值(29,"竞价充值"),
    竞价支付(30,"竞价支付"),
    竞价提现(31,"竞价提现"),
    竞价退款(32,"竞价退款"),
    竞价服务费(33,"竞价服务费"),
    竞价解冻(34,"竞价解冻"),
    竞价收款(35,"竞价收款"),
    收款(36,"收款"),
    扣款(37,"扣款"),
    提现(38,"提现"),
    物流中台美团(39,"物流中台美团"),
    物流中台顺丰(40,"物流中台顺丰"),
    物流中台京东(41,"物流中台京东"),
    物流中台达达(42,"物流中台达达"),
    客户回收价保扣款(43,"客户回收价保扣款"),
    /**
     * 兴业银行充值
     */
    SmalloBankCharge(44,"SmalloBankCharge"),
    加盟手机转售(45,"加盟手机转售"),
    加盟常用资产(46,"加盟常用资产"),
    加盟大件维修配件成本(47,"加盟大件维修配件成本"),
    加盟小件维修配件成本(48,"加盟小件维修配件成本"),
    租机支付(49,"租机支付"),
    /**
     * 小店手动充值类型
     */
    SmallShopCharge(50,"SmallShopCharge"),
    /**
     * 合作伙伴兴业充值
     */
    CooperateCharge(51,"CooperateCharge"),

    余额手动充值(52,"余额手动充值"),

    发票查验(53,"发票查验"),
    RECOVERY_PRICE_PROTECTION_RECHARGE(54,"回收价保充值"),
    DIYCoverPay(55,"DIY保护壳"),



    /**
     * 余额充值类型
     * 100-103 如果扩展新的注意修改
     *【余额充值相关的sql会限制s.kind in (100, 101,102,103)】
     */
    SYSTEM(100,"系统-银企直连流水"),
    MANUAL_OPERATION_LOG(101,"手动充值-流水"),
    MANUAL_OPERATION_NO_LOG(102,"手动充值-非流水"),
    MANUAL_IMPORT_LOG(103,"人工导入充值")
    ;

    private final Integer code;

    private final String name;

    public static MoneyKindEnum parseCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(MoneyKindEnum.values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }
}

package com.jiuji.pick.service.rpc.cloud;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.rpc.fallback.OaUserCloudFallbackFactory;
import com.jiuji.pick.service.rpc.vo.OaUserVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @function:
 * @description: OaUserCloud.java
 * @date: 2021/07/01
 * @author: sunfayun
 * @version: 1.0
 */
@FeignClient(value = "ch999Service", fallbackFactory = OaUserCloudFallbackFactory.class)
public interface OaUserCloud {

    /**
     * 通过手机号获取用户信息
     * @return
     */
    @GetMapping("/api/user/getUserByPhone")
    Result<OaUserVo> getUserByPhone(@RequestParam("phone") String phone,
                                    @RequestHeader("xtenant") long xtenant);

}

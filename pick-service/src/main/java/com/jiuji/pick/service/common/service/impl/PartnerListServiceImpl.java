package com.jiuji.pick.service.common.service.impl;

import cn.hutool.json.JSONUtil;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.LogComparison.LogDifferences;
import com.jiuji.pick.common.enums.LogComparison.PartnerUserParamEnum;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.common.utils.ReflexUtils;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.service.LogService;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.common.vo.PartnerUserParam;
import com.jiuji.pick.service.common.vo.PartnerUserParamOld;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("PartnerListServiceImpl")
public class PartnerListServiceImpl implements LogService {


    @Resource
    private NewOperateLogInfoService logInfoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Override
    public void systemSaveLog(Object param) {
        OATokenInfo oaTokenInfo = Optional.ofNullable(currentRequestComponent.getOATokenInfoBoWithoutCheck())
                .orElse(new OATokenInfo());
        CompletableFuture.runAsync(()->{
        PartnerUserParam productDetailParam = JSONUtil.toBean(JSONUtil.toJsonStr(param), PartnerUserParam.class);
        PartnerUserParamOld paramOld = productDetailParam.getParamOld();
        PartnerUserParamOld paramNew = new PartnerUserParamOld();

        BeanUtils.copyProperties(productDetailParam, paramNew);
        LogDifferences logDifferences = new LogDifferences();
        logDifferences.setOldEntity(paramOld).setNewEntity(paramNew)
                .setParamMap(PartnerUserParamEnum.getMap());


        String comment = null;
        try {
            comment = ReflexUtils.entityComparison(logDifferences);
        } catch (Exception e) {
            log.error("合作伙伴查询日志记录异常{}", e.getMessage(), e);
        }

        NewOperateLogInfo newOperateLogInfo = new NewOperateLogInfo();
        newOperateLogInfo.setContent(comment).setCreateTime(LocalDateTime.now())
                .setOptUserId(oaTokenInfo.getUserId().longValue()).setOptUserName(oaTokenInfo.getName())
                .setType(NewOperateLogInfoTypeEnum.PARTNER_LIST.getCode())
                .setRelateId(paramOld.getId() + "")
                .setShowType(LogShowTypeEnum.ADMIN.getCode());
        logInfoService.saveSystemLog(newOperateLogInfo);
        });

    }
}

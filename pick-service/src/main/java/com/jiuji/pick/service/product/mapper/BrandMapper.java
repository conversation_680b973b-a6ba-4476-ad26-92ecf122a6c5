package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.entity.Brand;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 品牌 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface BrandMapper extends BaseMapper<Brand> {



    List<Brand> listWithCategorySort(@Param("brandIds") List<Integer> brandIds,
                                     @Param("categoryId") int categoryId);


    int insertBrand(Brand brand);
}

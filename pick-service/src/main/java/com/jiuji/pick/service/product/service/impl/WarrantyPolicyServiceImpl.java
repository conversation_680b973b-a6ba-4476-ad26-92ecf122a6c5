package com.jiuji.pick.service.product.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.ModuleEnum;
import com.jiuji.pick.common.enums.WarrantyPolicyEnum;
import com.jiuji.pick.service.common.entity.OperateLogInfo;
import com.jiuji.pick.service.common.service.OperateLogInfoService;
import com.jiuji.pick.service.product.entity.WarrantyPolicy;
import com.jiuji.pick.service.product.mapper.WarrantyPolicyMapper;
import com.jiuji.pick.service.product.param.WarrantPolicySetParam;
import com.jiuji.pick.service.product.service.WarrantPolicyService;
import com.jiuji.pick.service.product.vo.WarrantyPolicyVO;
import com.jiuji.pick.service.product.vo.WarrantyTypeVO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 质保政策实现类
 * <AUTHOR>
 * @Date 2021/11/10
 */
@Service
public class WarrantyPolicyServiceImpl extends ServiceImpl<WarrantyPolicyMapper, WarrantyPolicy> implements WarrantPolicyService {

    @Resource
    private OperateLogInfoService operateLogInfoService;
    @Resource
    private WarrantyPolicyMapper warrantyPolicyMapper;

    @Override
    public List<WarrantyTypeVO> getAllWarrantyType() {
        try {
            Map<Integer, String> enumMap = Arrays.stream(WarrantyPolicyEnum.values()).collect(Collectors
                    .toMap(WarrantyPolicyEnum::getWarrantyCode, WarrantyPolicyEnum::getDesc, (v1, v2) -> v2));
            List<WarrantyTypeVO> warrantyTypeList = Lists.newArrayList();
            for (Map.Entry<Integer, String> entry : enumMap.entrySet()) {
                WarrantyTypeVO warrantyTypeVO = new WarrantyTypeVO();
                warrantyTypeVO.setType(entry.getKey());
                warrantyTypeVO.setName(entry.getValue());
                warrantyTypeList.add(warrantyTypeVO);
            }
            return warrantyTypeList;
        } catch (Exception e) {
            log.error("获取质保政策类型异常,exception:{}", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Boolean setWarrantyPolicy(WarrantPolicySetParam param, OATokenInfo oaTokenInfo) {

        WarrantyPolicy warrantyPolicy = new WarrantyPolicy();
        warrantyPolicy.setTitle(param.getTitle());
        warrantyPolicy.setContent(param.getContent());
        warrantyPolicy.setType(param.getType());
        warrantyPolicy.setOptUserId(Long.valueOf(oaTokenInfo.getUserId()));
        warrantyPolicy.setOptUserName(oaTokenInfo.getName());

        // 操作日志记录
        OperateLogInfo operateLogInfo = new OperateLogInfo();
        operateLogInfo.setRelateId((oaTokenInfo.getUserId() + "" + param.getType()));
        operateLogInfo.setType(ModuleEnum.PRODUCT.getCode());
        operateLogInfo.setOptUserId(Long.valueOf(oaTokenInfo.getUserId()));
        operateLogInfo.setOptUserName(oaTokenInfo.getName());
        operateLogInfo.setContent("修改" + WarrantyPolicyEnum.getWarrantyTypeDesc(param.getType()));
        operateLogInfo.setShowType(LogShowTypeEnum.ADMIN.getCode());
        operateLogInfoService.saveOrUpdate(operateLogInfo);

        return warrantyPolicy.insert();
    }

    @Override
    public WarrantyPolicyVO getWarrantyPolicy(Integer type, boolean isMapping) {
        if (type == null) {
            return null;
        }
        try {
            // 质保政策类型与商品类型不是一一对应，映射处理
            if (isMapping) {
                type = WarrantyPolicyEnum.getWarrantyTypeByProductType(type);
            }
            LambdaQueryWrapper<WarrantyPolicy> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(WarrantyPolicy::getType, type).orderByDesc(WarrantyPolicy::getCreateTime).last("limit 1");
            WarrantyPolicy warrantyPolicy = warrantyPolicyMapper.selectOne(queryWrapper);
            if (ObjectUtil.isNull(warrantyPolicy)) {
                return null;
            }
            WarrantyPolicyVO warrantyPolicyVO = new WarrantyPolicyVO();
            warrantyPolicyVO.setId(warrantyPolicy.getId());
            warrantyPolicyVO.setTitle(warrantyPolicy.getTitle());
            warrantyPolicyVO.setContent(warrantyPolicy.getContent());
            warrantyPolicyVO.setType(warrantyPolicy.getType());
            return warrantyPolicyVO;
        } catch (Exception e) {
            log.error("根据商品类型查询质保政策处理异常,exception:{}", e);
            return null;
        }
    }
}

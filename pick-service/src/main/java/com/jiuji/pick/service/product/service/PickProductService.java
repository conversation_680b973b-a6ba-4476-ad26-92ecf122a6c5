package com.jiuji.pick.service.product.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.bo.AddOrUpdateProductParam;
import com.jiuji.pick.service.product.entity.PickProduct;
import com.jiuji.pick.service.product.entity.SupplierProductDetail;
import com.jiuji.pick.service.product.param.*;
import com.jiuji.pick.service.product.vo.QueryProductExamineListVo;
import com.jiuji.pick.service.product.vo.QueryProductListVo;
import com.jiuji.pick.service.product.vo.SynOtherProductVo;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface PickProductService extends IService<PickProduct> {

    /**
     * 添加商品
     * @param param
     * @return
     */
    Result<Boolean> addOrUpdateProduct(AddOrUpdateProductParam param);


    Result<String> synOtherProduct(SynOtherProductVo param);

    /**
     * 商品列表查询
     * @param param
     * @return
     */
    Result<Page<QueryProductListVo>> queryProductListInfo(QueryProductListParam param);


    /**
     * 商品上下架
     * @param param
     * @return
     */
    Result<String> productUpOrDown(ProductUpOrDownParam param);

    /**
     * 获取商品供应审核列表
     * @param param
     * @return
     */
    Result<Page<QueryProductExamineListVo>> getProductExamineList(QueryProductExamineListParam param);

    /**
     * 商品绑定、解绑
     * @param param
     * @return
     */
    Result<String> bindOrUnboundProduct(ProductApplyDealParam param);

    /**
     * 判断解绑产品是否会下架
     * @param ppid
     * @return
     */
    Result<Boolean> judgeProductDown4Unbound(Long ppid);

    /**
     * 查询供应商商品详情
     * @param param
     * @return
     */
    Result<SupplierProductDetail> getSupplierProductDetailInfo(AdmQuerySupplierProductDetailParam param);

    /**
     * 管理员修改供应商商品详情
     * @param param
     * @return
     */
    Result<String> updateSupplierProductDetailInfo(AdmUpdateSupplierProductDetailParam param);

    /**
     * 删除供应商后，删除供应商绑定的相关商品
     * @param supplierId
     * @return
     */
    Boolean deleteProduct4DelSupplier(Long supplierId);

    /**
     * 根据ppid  和 商品类型 查询
     * @param ppid
     * @param productType
     * @return
     */
    PickProduct getByPpidAndProductType(Long ppid, Integer productType);

}

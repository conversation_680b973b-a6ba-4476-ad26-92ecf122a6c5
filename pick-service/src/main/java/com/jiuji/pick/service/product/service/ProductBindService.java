package com.jiuji.pick.service.product.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.entity.ProductBind;
import com.jiuji.pick.service.product.vo.SupplierProductCountVO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductBindService extends IService<ProductBind> {

    /***
     * @description: 统计供应商绑定的商品数量（不区分商品状态）
     * @Param: [supplierIdList]
     * @author: Lbj
     * @date: 2021/5/7 14:22
     */
    List<SupplierProductCountVO> supplierProductCount(List<Long> supplierIdList);

    /**
     * 获取商品绑定列表
     * @param supplierProductIdList 供应商品id
     * @return list
     */
    List<ProductBind> listProductBind(Collection<Long> supplierProductIdList);

}

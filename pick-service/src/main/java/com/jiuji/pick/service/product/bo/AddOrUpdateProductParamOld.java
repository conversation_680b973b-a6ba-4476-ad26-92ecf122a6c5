package com.jiuji.pick.service.product.bo;

import com.jiuji.pick.service.common.param.FileUploadParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @function: 添加商品请求参数
 * @description: AddOrUpdateProductParam.java
 * @date: 2021/04/29
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class AddOrUpdateProductParamOld {


    private Long jiuJiProductId;
    private Long ppid;
    private BigDecimal advicePrice;
    private String productFuture;
    private String productConfig;
    private Integer productType;
    private Integer defaultArea;
    private Integer hotArea;
    private Integer happyArea;
    private Integer recommendArea;
    private List<FileUploadParam> fileList;
    private List<FileUploadParam> imageList;


}

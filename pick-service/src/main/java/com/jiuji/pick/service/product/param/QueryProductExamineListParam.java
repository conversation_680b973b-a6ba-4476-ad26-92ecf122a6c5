package com.jiuji.pick.service.product.param;

import com.jiuji.pick.common.param.BasePageParam;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @function:
 * @description: QueryProductExamineListParam.java
 * @date: 2021/05/08
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class QueryProductExamineListParam extends BasePageParam {

    // 搜索类型，1ppid,2产品名称,3供应商名称,4供应商ID
    private String searchType;
    private String keyWord;
    @NotEmpty(message = "绑定状态不能为空")
    private List<Integer> bindStatusList;
    private LocalDateTime startTime;
    private LocalDateTime endTime;

}

/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.entity.BrandCategory;
import com.jiuji.pick.service.product.vo.BrandCategoryVO;
import com.jiuji.pick.service.product.vo.RecommendSearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2017-07-24
 */
public interface BrandCategoryMapper extends BaseMapper<BrandCategory> {
    /**
     * 查询相关推荐搜索.
     */
    List<RecommendSearchVo> getRecommendSearch(@Param("searchName") String searchName,
                                               @Param("brandId") Integer brandId);


    List<BrandCategoryVO> listAllWithName();


    /**
     * 通过品牌id获取分类id集合
     */
    List<Integer> selectCateIdbyBrandId(@Param("brandId") Integer brandId);


    /**
     * 通过分类id查询品牌
     */
    List<Integer> getBrandIdsByCid(@Param("ids") List<Integer> categoryId);

    /**
     * 查询采货王上架商品的cid
     */
    List<String> getProductName(@Param("searchName")String searchName);

}

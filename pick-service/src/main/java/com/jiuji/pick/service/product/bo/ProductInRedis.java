package com.jiuji.pick.service.product.bo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author:zuofei
 * @Description
 * @Date 2020-06-29 这个对象包含productinfo的部分字段，这个类根据业务的需要灵活加字段进行缓存
 */
@Setter
@Getter
@Data
public class ProductInRedis implements Serializable {

    private static final long serialVersionUID = -2460379803698735637L;

    private Long pPriceId;

    private Long productId;

    private String productName;

    private String shortName;

    private String productColor;

    private BigDecimal costPrice;

    private BigDecimal vipPrice;

    private Integer cid;

    private Boolean isMobile;

    private String bPic;

    private BigDecimal memberPrice;

    private Integer que;

    private Boolean display;

    private Boolean isDel;

    private Long pPriceId1;
    /**
     * 配置
     */
    private String config;
    /**
     * 品牌
     */
    private Integer brandID;
    /**
     * 分类树
     */
    private String cidFamily;
    /**
     * sku排序
     */
    private Integer rank;
    /**
     * 特价   1-特价；0-非特价
     */
    private Boolean noPromotion;
    /**
     * 69码
     */
    private String barCode;
    /**
     * 购买限制
     */
    private Integer buyLimit;
    /**
     * 商品卖点
     */
    private String sellingpoint;
    /**
     * 商品简介
     */
    private String decription;
    /**
     * OA启用状态
     */
    private Boolean oaEnable;

    private BigDecimal areaPrice;

    /**
     * 是否获取区域价异常.
     */
    @JSONField(serialize = false, deserialize = false)
    private Boolean areaPriceException = false;

}

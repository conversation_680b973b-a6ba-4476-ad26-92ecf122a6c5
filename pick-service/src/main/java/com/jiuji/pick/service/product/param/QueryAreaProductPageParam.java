package com.jiuji.pick.service.product.param;

import com.jiuji.pick.common.param.BasePageParam;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 分页查询分区商品入参
 *
 * <AUTHOR>
 * @since 2021年11月16日
 */
@Data
public class QueryAreaProductPageParam extends BasePageParam {

    /**
     * 专区代码
     */
    @NotNull(message = "专区代码不能为空")
    private Integer areaCode;

    private String ppid;

    private String productName;

    private String companyName;

    /**
     * 上架开始时间
     */
    private String startTime;

    /**
     * 上架结束时间
     */
    private String endTime;

}

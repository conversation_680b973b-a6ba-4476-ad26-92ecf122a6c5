package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.entity.SupplierProductDetail;
import com.jiuji.pick.service.product.param.QuerySupplierApplyProductParam;
import com.jiuji.pick.service.product.param.QuerySupplierProductDetailParam;
import com.jiuji.pick.service.product.param.QuerySupplierProductListParam;
import com.jiuji.pick.service.product.param.SupplierAddOrUpdateProductParam;
import com.jiuji.pick.service.product.vo.QuerySupplierApplyProductListVo;
import com.jiuji.pick.service.product.vo.QuerySupplierProductListVo;
import com.jiuji.pick.service.rpc.vo.OaStockVo;

import java.util.List;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface SupplierProductDetailService extends IService<SupplierProductDetail> {


    /**
     * 保存或者更新供应商产品信息
     * @param param
     * @return
     */
    Result<String> addOrUpdateSupplierProduct(SupplierAddOrUpdateProductParam param);

    /**
     * 供应商平台商品库查询
     * @param param
     * @return
     */
    Result<Page<QuerySupplierProductListVo>> querySupplierProductList(QuerySupplierProductListParam param);

    /**
     * 供应商申请商品列表查询
     * @param param
     * @return
     */
    Result<Page<QuerySupplierApplyProductListVo>> querySupplierApplyProductList(QuerySupplierApplyProductParam param);

    /**
     * 查询供应商商品详情
     * @param param
     * @return
     */
    Result<SupplierProductDetail> querySupplierProductDetail(QuerySupplierProductDetailParam param);

    /**
     * 获取包含逻辑删除在内的供应商商品
     * @param supplierProductIds supplierProductIds
     * @return list
     */
    List<SupplierProductDetail> noLogicList(Collection<Long> supplierProductIds);

    /**
     * 同步库存和销量
     */
    void syncStockAndSaleCount();

}

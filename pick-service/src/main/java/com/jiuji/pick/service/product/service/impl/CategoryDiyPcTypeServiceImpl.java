package com.jiuji.pick.service.product.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.service.product.entity.CategoryDiyPcType;
import com.jiuji.pick.service.product.mapper.CategoryDiyPcTypeMapper;
import com.jiuji.pick.service.product.service.CategoryDiyPcTypeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
@Service
public class CategoryDiyPcTypeServiceImpl extends ServiceImpl<CategoryDiyPcTypeMapper, CategoryDiyPcType> implements CategoryDiyPcTypeService {

    @Override
    public List<CategoryDiyPcType> list(Wrapper<CategoryDiyPcType> queryWrapper) {
        return super.list(queryWrapper);
    }

    @Override
    public int batchInsert(List<CategoryDiyPcType> list) {
        return baseMapper.batchInsert(list);
    }

    @Override
    public int batchUpdate(List<CategoryDiyPcType> list) {
        return baseMapper.batchUpdate(list);
    }

    @Override
    public int batchDelete(List<Integer> ids) {
        return baseMapper.batchDelete(ids);
    }

    @Override
    public boolean save(CategoryDiyPcType entity) {
        return super.save(entity);
    }
}

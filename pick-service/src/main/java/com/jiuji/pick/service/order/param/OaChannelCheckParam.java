package com.jiuji.pick.service.order.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @function:
 * @description: OaChannelCheckParam.java
 * @date: 2021/09/26
 * @author: sunfayun
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OaChannelCheckParam {

    /**
     * 合作伙伴id
     */
    private Long xtenant;

    /**
     * 授权隔离ID
     */
    private String authId;

    /**
     * 渠道ID
     */
    private String channelId;

    /**
     * 类型，0：小件，3：大件
     */
    private Integer kind;

}

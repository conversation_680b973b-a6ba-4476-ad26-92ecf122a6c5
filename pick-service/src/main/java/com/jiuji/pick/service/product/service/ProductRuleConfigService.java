package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.param.BasePageParam;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.entity.ProductRuleConfig;
import com.jiuji.pick.service.product.param.EditProductWhiteConfigParam;
import com.jiuji.pick.service.product.param.RemoveUserFromProductRuleParam;
import com.jiuji.pick.service.product.param.SaveProductWhiteConfigParam;
import com.jiuji.pick.service.product.param.SearchProductWhiteConfigParam;
import com.jiuji.pick.service.product.vo.QueryIndexWhiteConfigVo;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
public interface ProductRuleConfigService extends IService<ProductRuleConfig> {

    /**
     * 添加黑名单
     * @param param
     * @return
     */
    Result saveProductWhiteConfig(SaveProductWhiteConfigParam param);

    /**
     * 根据id 删除
     * @param id
     * @return
     */
    Result removeWhiteConfig(Long id);

    /**
     * 编辑后保存黑名单的详情
     * @param param
     * @return
     */
    Result editSaveWhiteConfig(EditProductWhiteConfigParam param);

    /**
     * 查询详情
     * @param  id
     * @return
     */
    Result<ProductRuleConfig> getProductRuleConfigDetail(Long id);

    /**
     * 条件搜索
     * @return
     */
    Result<Page<QueryIndexWhiteConfigVo>> conditionSearch(SearchProductWhiteConfigParam param);

    /**
     * 从黑白名单中移除指定用户
     * @param param
     * @return
     */
    Boolean removeUserFromProductRule(RemoveUserFromProductRuleParam param);
}

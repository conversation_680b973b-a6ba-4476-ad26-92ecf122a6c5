package com.jiuji.pick.service.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.param.FileUploadParam;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
public interface AttachmentInfoService extends IService<AttachmentInfo> {

    /***
     * @description: 获取供应商合同模板
     * @Param: []
     * @author: Lbj
     * @date: 2021/5/17 10:36
     */
    AttachmentInfo getSupplierContractTemplate();

    /***
     * @description: 处理并存储附件信息[根据fid 新增 更新 删除数据]
     * @Param: attachmentInfoList 数据库查询出的附件信息
     * @Param: fileList  需要修改的附件列表
     * @Param: relateId  相关id
     * @Param: fileType 文件类型
     * @return: void
     * @date: 2021/6/3 10:15
     */
    void handleAndSaveAttachment(List<AttachmentInfo> attachmentInfoList, List<FileUploadParam> fileList, Long relateId, Integer fileType);
}

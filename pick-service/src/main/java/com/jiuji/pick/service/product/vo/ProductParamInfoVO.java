package com.jiuji.pick.service.product.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProductParamInfoVO implements Serializable {

    private static final long serialVersionUID = -3253406578633301800L;
    private Integer parentParamId;

    private String name;

    private Integer sort;

    List<ChildrenParam> childrenParamList;

    @Data
    public static class ChildrenParam implements Serializable{

        private static final long serialVersionUID = 2723711465873236650L;

        private Integer paramId;

        private String name;

        private Integer inputType;

        /**
         * 是否展示字段
         */
        private Boolean displayFlag;

        @JsonIgnore
        private Integer sort;

        /**
         * 不同参数类型此值不同（单选多选时候为Select，其余为String）
         */
        private Object info;
    }

    @Data
    public static class Select {

        private Integer id;

        private String name;

        @JsonIgnore
        private Integer sort;

        private boolean selectedFlag;
    }
}

package com.jiuji.pick.service.diy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DiySearchTypeEnum {

    /***
     *
     */
    ORDER(1, "DIY保护壳"),

    ;

    private Integer code;
    private String desc;


    public static String getDescByCode(Integer code) {
        for (DiySearchTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}

package com.jiuji.pick.service.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Splitter;
import com.jiuji.pick.common.config.apollo.CategoryConfig;
import com.jiuji.pick.common.constant.RedisKey;
import com.jiuji.pick.common.enums.AttachTypeEnum;
import com.jiuji.pick.common.enums.ProductTypeEnum;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.service.AttachmentInfoService;
import com.jiuji.pick.service.product.bo.ProductInRedis;
import com.jiuji.pick.service.product.entity.*;
import com.jiuji.pick.service.product.mapper.ProductInfoMapper;
import com.jiuji.pick.service.product.service.*;
import com.jiuji.pick.service.product.vo.*;
import com.jiuji.tc.utils.common.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Service
@Slf4j
public class ProductInfoServiceImpl extends ServiceImpl<ProductInfoMapper, ProductInfo> implements ProductInfoService {

    @Resource
    private AttachmentInfoService attachmentInfoService;
    @Resource
    private ProductStInfoService productStInfoService;
    @Resource
    private ProductStandardService productStandardService;
    @Resource
    private ProductStDetailService productStDetailService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private CategoryConfig categoryConfig;

    @Override
    public Result<QueryProduct4AddVo> queryProductInfo4Add(Long ppid) {
        if(ppid == null) {
            return Result.errorInfo("请求参数错误");
        }
        QueryProduct4AddVo queryProduct4AddVo = baseMapper.queryProductInfo4Add(ppid);
        if(queryProduct4AddVo == null) {
            return Result.noData("查询商品不存在");
        }
        // 设置产品类型
        ProductTypeEnum productTypeEnum = this.getProductType(queryProduct4AddVo.getCid(), queryProduct4AddVo.getIsMobile());
        if(productTypeEnum != null) {
            queryProduct4AddVo.setProductType(productTypeEnum.getCode());
            queryProduct4AddVo.setProductTypeStr(productTypeEnum.getDesc());
        }
        // 查询附件信息
        if(StringUtils.isBlank(queryProduct4AddVo.getAttachmentIds())) {
            return Result.success(queryProduct4AddVo);
        }
        //imageList 展示图片查询
        List<AttachmentInfo> list = attachmentInfoService.lambdaQuery().eq(AttachmentInfo::getRelateId, ppid)
                .eq(AttachmentInfo::getType, AttachTypeEnum.PRODUCT_IMAGE.getCode()).list();
        if(!CollectionUtils.isEmpty(list)){
            List<IndexProductDetailVo.FileInfo> imageList = new ArrayList<>();
            list.forEach((AttachmentInfo item)->{
                IndexProductDetailVo.FileInfo fileInfo = new IndexProductDetailVo.FileInfo();
                fileInfo.setFileName(item.getFileName());
                fileInfo.setFilePath(item.getFilePath());
                fileInfo.setFid(item.getFid());
                imageList.add(fileInfo);
            });
            queryProduct4AddVo.setImageList(imageList);
        }
        List<Long> attachmentIdList = Splitter.on(",").splitToList(queryProduct4AddVo.getAttachmentIds()).stream().map(id -> Long.valueOf(id)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(attachmentIdList)) {
            return Result.success(queryProduct4AddVo);
        }
        List<AttachmentInfo> attachmentInfoList = attachmentInfoService.list(new LambdaQueryWrapper<AttachmentInfo>().in(AttachmentInfo::getId, attachmentIdList));
        if(CollectionUtils.isEmpty(attachmentInfoList)) {
            return Result.success(queryProduct4AddVo);
        }
        List<AttachmentVo> attachmentVoList = Lists.newArrayList();
        attachmentInfoList.forEach(attachmentInfo -> {
            AttachmentVo attachmentVo = new AttachmentVo();
            attachmentVo.setId(attachmentInfo.getId());
            attachmentVo.setFid(attachmentInfo.getFid());
            attachmentVo.setFileName(attachmentInfo.getFileName());
            attachmentVo.setPath(attachmentInfo.getFilePath());
            attachmentVoList.add(attachmentVo);
        });
        queryProduct4AddVo.setAttachmentList(attachmentVoList);
        return Result.success(queryProduct4AddVo);
    }

    @Override
    public Result<List<ProductSpecVo>> getProductSpecInfo(Long ppid) {
        ProductInfo productInfo = this.getOne(new LambdaQueryWrapper<ProductInfo>().eq(ProductInfo::getPpriceid, ppid).last("limit 1"));
        if(productInfo == null) {
            return Result.errorInfo("商品不存在或者已删除");
        }
        // 查询此商品规格配置参数
        List<Integer> infoList = productStInfoService.list(new LambdaQueryWrapper<ProductStInfo>().eq(ProductStInfo::getPpriceid, ppid))
                .stream().map(ProductStInfo::getStandardDetailId).collect(Collectors.toList());
        List<ProductSpecVo> productSpecVoList = getSpec(productInfo.getCid(),infoList);
        return Result.success(productSpecVoList);
    }

    public List<ProductSpecVo> getSpec(Long cid, List<Integer> infoList){
        List<Integer> specIds = new ArrayList<>();
        List<ProductSpecVo> collect = productStandardService.list(new LambdaQueryWrapper<ProductStandard>().eq(ProductStandard::getCid, cid))
                .stream()
                .filter(li -> StringUtils.isNotBlank(li.getName()))
                .map(li -> {
                    specIds.add(li.getId());
                    ProductSpecVo vo = new ProductSpecVo();
                    vo.setSpecId(li.getId());
                    String specName = li.getName();
                    if(StringUtils.isNotEmpty(specName)){
                        specName = specName.replaceAll("　","");//去除字符串中的空格
                    }
                    vo.setSpecName(specName);
                    vo.setRank(li.getOrderId());
                    return vo;
                }).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(specIds)){
            Map<Integer, List<ProductStDetail>> detailMap = productStDetailService.list(new LambdaQueryWrapper<ProductStDetail>().in(ProductStDetail::getStandId, specIds))
                    .stream()
                    .collect(Collectors.groupingBy(ProductStDetail::getStandId));
            collect.forEach(li->{
                List<ProductStDetail> details = detailMap.get(li.getSpecId());
                if(CollectionUtils.isNotEmpty(details)){
                    List<ProductSpecVo.Select> selectList = details.stream().map(it -> {
                        ProductSpecVo.Select select = new ProductSpecVo.Select();
                        select.setId(it.getId());
                        select.setRank(it.getOrderId());
                        select.setName(it.getValue());
                        select.setSelected(infoList.contains(it.getId()));
                        return select;
                    }).collect(Collectors.toList());
                    li.setList(selectList);
                }
            });
        }
        return collect;
    }

    @Override
    public boolean saveOrUpdateProductInfo(Long productId) {
        List<ProductInfo> newProductInfos = this.getAllProductInfoByPid(productId);
        if (CollectionUtils.isNotEmpty(newProductInfos)) {
            for (ProductInfo p : newProductInfos) {
                ProductInfo newProductInfo = baseMapper.getProductInfoByPpid(p.getPpriceid());
                if (newProductInfo == null) {
                    try {
                         baseMapper.insertProductInfo(p);
                    } catch (Exception e) {
                        log.info(String.format("exception: %s", e.getMessage()), e);
                    }
                } else {
                    baseMapper.updateByPid(p);
                }
            }
        }
        return true;
    }

    @Override
    public List<ProductInRedis> batchGetProductInRedisList(List<Long> ppids) {
        List<ProductInRedis> list = new ArrayList<>();
        for (Long ppid : ppids) {
            ProductInRedis productInRedis = getProductInRedisByPpid(ppid);
            if (productInRedis == null) {
                continue;
            }
            list.add(productInRedis);
        }
        return list;
    }

    @Override
    public ProductInRedis getProductInRedisByPpid(Long ppid) {
        String redisKey = RedisKey.PRODUCTINFO + ppid;
        String redisValue = stringRedisTemplate.opsForValue().get(redisKey);
        ProductInRedis productInRedis = JSON.parseObject(redisValue, ProductInRedis.class);
        if (productInRedis == null) {
            ProductInRedis productInRedisFromDb = baseMapper.getProductInRedisFromDB(ppid);
            stringRedisTemplate.opsForValue()
                    .set(redisKey, JSON.toJSONString(productInRedisFromDb),1, TimeUnit.DAYS);
            return productInRedisFromDb;
        }
        return productInRedis;
    }

    @Override
    public ProductInRedis getProductInRedisFromDB(Long ppid) {
        return baseMapper.getProductInRedisFromDB(ppid);
    }

    @Override
    public List<Long> getPpidsByPid(Long pid) {
        return list(new QueryWrapper<ProductInfo>()
                .select(ProductInfo.P_PRICE_ID)
                .eq(ProductInfo.PRODUCT_ID, pid)
                .eq(ProductInfo.DISPLAY, true)
                .eq(ProductInfo.IS_DEL, false))
                .stream().map(ProductInfo::getPpriceid)
                .collect(Collectors.toList());
    }

    @Override
    public ProductTypeEnum getProductType(Long cid, Boolean isMobile) {
        if(cid == null && isMobile == null) {
            return null;
        }
        if(cid == null) {
            if(isMobile) {
                return ProductTypeEnum.BULKY;
            } else {
                return ProductTypeEnum.SMALL;
            }
        }
        List<Long> virtualCategoryList = Lists.newArrayList();
        List<Long> fixAssetsCategoryList = Lists.newArrayList();
        List<Long> commonAssetsCategoryList = Lists.newArrayList();
        List<Long> repairPartCategoryList = Lists.newArrayList();
        if(StringUtils.isNotBlank(categoryConfig.getVirtualProductCategory())) {
            virtualCategoryList = Splitter.on(",").splitToList(categoryConfig.getVirtualProductCategory()).stream().map(Long::new).collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(categoryConfig.getFixAssetsProductCategory())) {
            fixAssetsCategoryList = Splitter.on(",").splitToList(categoryConfig.getFixAssetsProductCategory()).stream().map(Long::new).collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(categoryConfig.getCommonAssetsProductCategory())) {
            commonAssetsCategoryList = Splitter.on(",").splitToList(categoryConfig.getCommonAssetsProductCategory()).stream().map(Long::new).collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(categoryConfig.getRepairPartCategory())) {
            repairPartCategoryList = Splitter.on(",").splitToList(categoryConfig.getRepairPartCategory()).stream().map(Long::new).collect(Collectors.toList());
        }


        if(virtualCategoryList.contains(cid)) {
            return ProductTypeEnum.VIRTUAL;
        }
        if(fixAssetsCategoryList.contains(cid)) {
            return ProductTypeEnum.FIX_ASSETS;
        }
        if(commonAssetsCategoryList.contains(cid)) {
            return ProductTypeEnum.COMMON_ASSETS;
        }

        // 判断是否是维修配件
        if(repairPartCategoryList.contains(cid)) {
            return ProductTypeEnum.REPAIR_PART;
        }

        // 既不是虚拟商品也不是资产
        if(isMobile) {
            return ProductTypeEnum.BULKY;
        } else {
            return ProductTypeEnum.SMALL;
        }


    }

    @Override
    public List<ProductInfo> getAllProductInfoByPid(Long pid) {
        List<ProductInfo> newProductInfos = baseMapper.getAllProductInfoByPid(pid);
        //获取规格信息 newProductInfos的ppriceid并去重
        List<Long> ppriceids = newProductInfos.stream().map(ProductInfo::getPpriceid).distinct().collect(Collectors.toList());
        Map<Long, ProductColorInfoVo> productColorInfoMap = CommonUtils.bigDataInQuery(ppriceids, ids -> baseMapper.listProductColor(ids))
                .stream().collect(Collectors.toMap(ProductColorInfoVo::getPpriceid, Function.identity(), (v1, v2) -> v1));

            newProductInfos.forEach(productInfo -> {
                ProductColorInfoVo productColorInfoVo = productColorInfoMap.get(productInfo.getPpriceid());
                if(productColorInfoVo != null) {
                    productInfo.setProductColor(productColorInfoVo.getProductColor());
                    productInfo.setProductColorIds(productColorInfoVo.getProductColorIds());
                }
            });
        return newProductInfos;
    }
}

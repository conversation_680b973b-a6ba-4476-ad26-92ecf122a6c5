package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.entity.Product;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 后台管理同步商品（插入指定主键）
     */
    @SqlParser(filter = true)
    int insertProduct(@Param("product") Product product);

    /**
     * 根据商品ID更新 mybatis-plus默认不更新null值 （虽然可以在属性上加注解 但是有的地方可能真的不需要更新 还是写SQL吧）
     */
    @SqlParser(filter = true)
    int updateProductById(@Param("product") Product product);
}

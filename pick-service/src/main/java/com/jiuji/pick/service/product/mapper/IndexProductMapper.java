package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.service.product.param.QueryAreaProductPageParam;
import com.jiuji.pick.service.product.param.QuerySortProductParam;
import com.jiuji.pick.service.product.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface IndexProductMapper extends BaseMapper<IndexProductVo> {

    List<IndexProductVo> queryIndexProduct(@Param("type") Integer type, @Param("partnerType") Integer partnerType);

    List<SimilarProductVo>  querySimilarProduct(@Param("id") Long id, @Param("supplierProductId") Long supplierProductId);

    Page<IndexProductVo> queryAreaProduct (@Param("page") Page<Integer> page,@Param("param") QuerySortProductParam param);

    List<IndexProductVo> queryAreaProductAll ();

    IndexProductDetailVo getProductDetailInfo(Long supplierProductId);

    List<IndexMiniProductSpecVo> getProductDetailSpecInfo(@Param("jiuJiProductId") Long jiuJiProductId,
                                                          @Param("supplierId") Long supplierId,@Param("ppid") Long ppid);



    List<IndexMiniProductSpecVo> getProductDetailSpecInfoV2(@Param("jiuJiProductId") Long jiuJiProductId,
                                                          @Param("supplierId") Long supplierId,@Param("ppid") Long ppid);

    Page<AreaProductVO> pageAreaProductVO(@Param("page") Page<AreaProductVO> page, @Param("param") QueryAreaProductPageParam pageParam);
}

package com.jiuji.pick.service.product.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:zuofei
 * @Description
 * @Date 2020-08-19
 */
@Data
@NoArgsConstructor
public class ProductSearchRequestVo {

    /**
     * 只显示有货缺货
     */
    private boolean inStock;


    /**
     * 搜索条件模仿主站的 目前规则：
     * 第一个为分类
     * 第二个参数为品牌
     * 倒数第一位价格区间
     * 倒数第二位正序还是倒序 0--降序
     * 倒数第三位按什么排序(综合，价格，销量)
     */
    private String coll = "0-0-0-0-0_0";

    /**
     * 搜索关键字，搜索筛选接口整合在一起
     */
    private String keyword;

    /**
     * 当前页码
     */
    private int currentPage = 1;

    /**
     * 0--默认搜索，1---列表
     */
    private int from = 0;
}

package com.jiuji.pick.service.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.order.dto.PreviousOrderPriceReq;
import com.jiuji.pick.service.order.dto.PreviousProductDTO;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.vo.OrderProductVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 采购单详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
public interface OrderDetailInfoMapper extends BaseMapper<OrderDetailInfo> {

    List<OrderProductVO> getOrderProduct(Long orderId);



    List<PreviousProductDTO> selectPreviousOrderPrice(@Param(value = "req") PreviousOrderPriceReq req);



}

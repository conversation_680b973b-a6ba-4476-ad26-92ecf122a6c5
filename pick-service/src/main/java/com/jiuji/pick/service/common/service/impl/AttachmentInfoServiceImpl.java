package com.jiuji.pick.service.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.enums.AttachTypeEnum;
import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.mapper.AttachmentInfoMapper;
import com.jiuji.pick.service.common.param.FileUploadParam;
import com.jiuji.pick.service.common.service.AttachmentInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
@Service
@Slf4j
public class AttachmentInfoServiceImpl extends ServiceImpl<AttachmentInfoMapper, AttachmentInfo> implements AttachmentInfoService {

    @Override
    public AttachmentInfo getSupplierContractTemplate() {
        LambdaQueryWrapper<AttachmentInfo> query = new LambdaQueryWrapper<>();
        query.eq(AttachmentInfo::getType, AttachTypeEnum.SUPPLIER_CONTRACT_TEMPLATE.getCode());
        query.orderByDesc(AttachmentInfo::getId);
        return baseMapper.selectList(query).stream().findFirst().orElse(null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleAndSaveAttachment(List<AttachmentInfo> attachmentInfoList, List<FileUploadParam> fileList, Long relateId, Integer fileType) {
        if(null == relateId || null == fileType){
            return;
        }

        if(CollectionUtils.isEmpty(fileList) && CollectionUtils.isEmpty(attachmentInfoList)) {
            return;
        }

        if(CollectionUtils.isEmpty(fileList) && CollectionUtils.isNotEmpty(attachmentInfoList)) {
            // 删除所有的文件
            this.removeByIds(attachmentInfoList.stream().map(AttachmentInfo::getId).collect(Collectors.toList()));
            return;
        }

        if(CollectionUtils.isEmpty(attachmentInfoList)) {
            attachmentInfoList = Lists.newArrayList();
            for (FileUploadParam fileUploadParam : fileList) {
                AttachmentInfo attachmentInfo = new AttachmentInfo();
                attachmentInfo.setRelateId(relateId);
                attachmentInfo.setType(fileType);
                attachmentInfo.setFid(fileUploadParam.getFid());
                attachmentInfo.setFileName(fileUploadParam.getFileName());
                attachmentInfo.setFilePath(fileUploadParam.getFileUrl());
                attachmentInfoList.add(attachmentInfo);
            }
            // 添加新的
            this.saveOrUpdateBatch(attachmentInfoList);
        } else {
            List<String> newFidList = fileList.stream().map(FileUploadParam::getFid).collect(Collectors.toList());
            List<AttachmentInfo> deleteAttachmentList = attachmentInfoList.stream().filter(attachmentInfo -> !newFidList.contains(attachmentInfo.getFid())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(deleteAttachmentList)) {
                // 删除不存在的
                this.removeByIds(deleteAttachmentList.stream().map(AttachmentInfo::getId).collect(Collectors.toList()));
            }
            List<String> oldFidList = attachmentInfoList.stream().map(AttachmentInfo::getFid).collect(Collectors.toList());
            List<FileUploadParam> addFileList = fileList.stream().filter(fileUploadParam -> !oldFidList.contains(fileUploadParam.getFid())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(addFileList)) {
                List<AttachmentInfo> addAttachmentList = Lists.newArrayList();
                for (FileUploadParam fileUploadParam : addFileList) {
                    AttachmentInfo attachmentInfo = new AttachmentInfo();
                    attachmentInfo.setRelateId(relateId);
                    attachmentInfo.setType(fileType);
                    attachmentInfo.setFid(fileUploadParam.getFid());
                    attachmentInfo.setFileName(fileUploadParam.getFileName());
                    attachmentInfo.setFilePath(fileUploadParam.getFileUrl());
                    addAttachmentList.add(attachmentInfo);
                }
                // 添加新的
                this.saveOrUpdateBatch(addAttachmentList);
            }
        }
    }
}

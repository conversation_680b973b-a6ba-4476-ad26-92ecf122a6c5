package com.jiuji.pick.service.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.pick.common.annotation.LogFieldInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 物流费区域表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_delivery_fee_area")
public class DeliveryFeeArea extends Model<DeliveryFeeArea> {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 省
     */
    @LogFieldInfo(fieldName = "省份")
    private String province;

    /**
     * 省代码
     */
    @LogFieldInfo(fieldName = "省份代码")
    private String provinceCode;

    /**
     * 物流费
     */
    @LogFieldInfo(fieldName = "物流费")
    private BigDecimal deliveryFee;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 删除标识
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

package com.jiuji.pick.service.user.vo;

import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.param.FileUploadParam;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.entity.SupplierUserAccount;
import com.jiuji.pick.service.user.entity.SupplierUserContact;
import com.jiuji.pick.service.user.entity.SupplierUserQualification;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description： 供应商详情信息
 * @date ：2021/4/30 15:54
 */
@Data
public class SupplierUserDetailVO {
    /***
     *  供应商信息
     */
    private SupplierUser supplierUser;
    /***
     * 供应商资质信息
     */
    private SupplierUserQualification supplierUserQualification;
    /***
     * 供应商联系人信息
     */
    private List<SupplierUserContact> supplierUserContactList;
    /***
     * 供应商开户信息
     */
    private List<SupplierUserAccount> supplierUserAccountList;

    /***
     * 供应商附件信息
     */
    private List<AttachmentInfo> attachmentList;

    private SupplierUserDetailOldVO paramOld;

}

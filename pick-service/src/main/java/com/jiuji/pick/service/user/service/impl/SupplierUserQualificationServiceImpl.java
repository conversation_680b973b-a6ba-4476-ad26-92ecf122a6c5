package com.jiuji.pick.service.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.service.user.entity.SupplierUserQualification;
import com.jiuji.pick.service.user.mapper.SupplierUserQualificationMapper;
import com.jiuji.pick.service.user.service.SupplierUserQualificationService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
@Service
public class SupplierUserQualificationServiceImpl extends ServiceImpl<SupplierUserQualificationMapper, SupplierUserQualification> implements SupplierUserQualificationService {

    @Override
    public SupplierUserQualification getBySupplierUserId(Long supplierUserId) {
        LambdaQueryWrapper<SupplierUserQualification> query = new LambdaQueryWrapper<>();
        query.eq(SupplierUserQualification::getSupplierUserId, supplierUserId);
        return baseMapper.selectList(query).stream().findFirst().orElse(null);
    }
}

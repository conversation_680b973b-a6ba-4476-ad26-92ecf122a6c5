package com.jiuji.pick.service.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.service.common.entity.MessageInfoUser;
import com.jiuji.pick.service.common.mapper.MessageInfoUserMapper;
import com.jiuji.pick.service.common.service.MessageInfoUserService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Service
public class MessageInfoUserServiceImpl extends ServiceImpl<MessageInfoUserMapper, MessageInfoUser> implements MessageInfoUserService {

    @Override
    public List<MessageInfoUser> listByMessageId(Long messageId) {
        if(null == messageId){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<MessageInfoUser> query = new LambdaQueryWrapper<>();
        query.eq(MessageInfoUser::getMessageId, messageId);
        return baseMapper.selectList(query);
    }
}

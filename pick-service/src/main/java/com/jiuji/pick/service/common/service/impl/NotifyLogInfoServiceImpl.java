package com.jiuji.pick.service.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.service.common.entity.NotifyLogInfo;
import com.jiuji.pick.service.common.mapper.NotifyLogInfoMapper;
import com.jiuji.pick.service.common.service.NotifyLogInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * <p>
 * 和外部系统交互日志记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Slf4j
@Service
public class NotifyLogInfoServiceImpl extends ServiceImpl<NotifyLogInfoMapper, NotifyLogInfo> implements NotifyLogInfoService {

    @Override
    public void saveNotifyLogInfo(NotifyLogInfo notifyLogInfo) {
        if(notifyLogInfo == null) {
            return;
        }
        try {
            CompletableFuture.runAsync(() -> baseMapper.insert(notifyLogInfo));
        } catch (Exception e) {
            log.error("保存交互通知记录异常，exception:", e);
        }
    }


}

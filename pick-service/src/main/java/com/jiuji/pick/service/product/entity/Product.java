package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_product")
public class Product extends Model<Product> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品分类
     */
    private Integer cid;

    /**
     * 分类树
     */
    private String cidFamily;

    /**
     * 扩展分类
     */
    private Integer cidExtend;

    /**
     * 名称
     */
    private String name;

    /**
     * 关键字
     */
    private String proKey;

    /**
     * 商品简介
     */
    private String decription;

    /**
     * 描述
     */
    private String decription2;

    /**
     * 商品详细信息
     */
    private String detail;

    /**
     * 查看次数
     */
    private Integer views;

    /**
     * 购买次数
     */
    private Integer buys;

    /**
     * 创建时间
     */
    private LocalDateTime adddate;

    /**
     * 是否显示
     */
    private Boolean display;

    /**
     * 备注
     */
    private String comment;

    /**
     * 一周查看次数
     */
    private Integer viewsWeek;

    /**
     * 操作人
     */
    private String inuser;

    /**
     * 排序
     */
    private Integer rank1;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 最新商品
     */
    private Boolean isNew;

    /**
     * 推荐
     */
    private Boolean isRecommend;

    /**
     * 热门
     */
    private Boolean isPopular;

    /**
     * 特价
     */
    private Boolean isSpecial;

    private Integer commentCount;

    /**
     * 搜索关键字
     */
    private String searchKey;

    /**
     * 是否是大小件
     */
    private Boolean isMobile;

    private Boolean isServices;

    private String desLinkName;

    private String desLinkUrl;

    /**
     * 修改时间
     */
    private LocalDateTime lastModifyTime;

    /**
     * 商品简称
     */
    private String shotName;

    private Integer oldPid;

    private Integer psort;

    /**
     * 是否是旗舰商品
     */
    private Boolean ultimate;

    /**
     * PC版产品简介链接名称
     */
    private String pcDesLinkName;

    /**
     * PC版产品简介链接地址
     */
    private String pcDesLinkUrl;

    /**
     * 去看看链接名称
     */
    private String qkkDesLinkName;

    /**
     * 去看看链接地址
     */
    private String qkkDesLinkUrl;

    /**
     * 商品卖点
     */
    private String sellingPoint;

    /**
     * 是否在首页掌上专享推荐
     */
    private Boolean isRecommendByLikeProduct;

    private Integer sevenDayReturn;

    /**
     * 提供的服务 1--礼品盒;2--上门维修;4--列表隐藏;8--钢化膜年包服务;16--半价复购
     */
    private Integer supportService;

    /**
     * 排序生效时间
     */
    private LocalDateTime sortStartTime;

    /**
     * 排序生效时间
     */
    private LocalDateTime sortEndTime;

    /**
     * 服务介绍跳转链接
     */
    private String serviceIntroduceLink;

    /**
     * 商品的参数搜索关键词（参数中的多选项参数）
     */
    private String paramSearchKey;

    /**
     * 上架时间
     */
    private LocalDateTime displayTime;

    /**
     * 小件预留列表排除限制 1-是否尽心预留限制    OA需要的字段（可以找oa咨询）
     */
    private Integer otherLimit;

    /**
     * 是否数量盘点   OA需要的字段（可以找oa咨询） 1--是 0--否
     */
    private Boolean numberPanDian;

    /**
     * OA是否启用
     */
    private Integer oaEnable;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

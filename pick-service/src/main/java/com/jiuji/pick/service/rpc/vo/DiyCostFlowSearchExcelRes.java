package com.jiuji.pick.service.rpc.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DiyCostFlowSearchExcelRes implements Serializable {

    private String userid;

    private String userName;

    /**
     * kind
     */
    private String kind;

    /**
     * subId
     */
    private String subId;

    /**
     * 发生额
     */
    private String money;

    /**
     * 余额
     */
    private String smoney;

    /**
     * 时间
     *
     */
    private String dtime;

    /**
     * 备注
     *
     */
    private String comment;
}

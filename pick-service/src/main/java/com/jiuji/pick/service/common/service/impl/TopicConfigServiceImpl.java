package com.jiuji.pick.service.common.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.ModuleEnum;
import com.jiuji.pick.common.enums.TopicTypeEnum;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.OperateLogInfo;
import com.jiuji.pick.service.common.entity.TopicConfig;
import com.jiuji.pick.service.common.mapper.TopicConfigMapper;
import com.jiuji.pick.service.common.service.OperateLogInfoService;
import com.jiuji.pick.service.common.service.TopicConfigService;
import com.jiuji.pick.service.common.vo.TopicConfigVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 专题配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Service
public class TopicConfigServiceImpl extends ServiceImpl<TopicConfigMapper, TopicConfig> implements TopicConfigService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Resource
    private OperateLogInfoService operateLogInfoService;

    @Override
    public Result<List<TopicConfigVO>> getList(Boolean enabled, String name) {
        LambdaQueryWrapper<TopicConfig> wrapper = new LambdaQueryWrapper<>();
        if (enabled != null) {
            wrapper.eq(TopicConfig::getEnabled, enabled);
        }
        if (StringUtils.isNotEmpty(name)) {
            wrapper.like(TopicConfig::getName, name);
        }
        wrapper.orderByDesc(TopicConfig::getSort);
        List<TopicConfig> list = this.list(wrapper);
        List<TopicConfigVO> voList = new ArrayList<>();
        list.forEach(e->{
            TopicConfigVO vo = new TopicConfigVO();
            BeanUtil.copyProperties(e,vo);
            vo.setTypeStr(TopicTypeEnum.getTopicTypeDesc(e.getType()));
            vo.setCreateTime(DateUtil.stringParseLocalDateTime(e.getCreateTime()));
            voList.add(vo);
        });
        return Result.success(voList);
    }

    @Override
    public Result<Boolean> edit(TopicConfig config) {
        OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
        if (Objects.isNull(oaTokenInfo)) {
            return Result.errorInfo("用户未登录");
        }
        String content;
        if (Objects.isNull(config.getId())) {
            this.save(config);
            content = "新增专题页配置";
        } else {
            TopicConfig oldConfig = this.getById(config.getId());
            content = "专题页配置：" + CommonUtil.objCompare(oldConfig, config, false);
            this.updateById(config);
        }
        // 记录操作日志
        OperateLogInfo bindOperateLogInfo = OperateLogInfo.builder()
                .relateId(String.valueOf(config.getId()))
                .type(ModuleEnum.COMMON.getCode())
                .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                .optUserName(oaTokenInfo.getName())
                .content(content)
                .showType(LogShowTypeEnum.ADMIN.getCode())
                .build();
        operateLogInfoService.saveOrUpdate(bindOperateLogInfo);
        return Result.success();
    }

    @Override
    public Result<Boolean> del(Integer id) {
        OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
        if (Objects.isNull(oaTokenInfo)) {
            return Result.errorInfo("用户未登录");
        }
        this.removeById(id);
        // 记录操作日志
        OperateLogInfo bindOperateLogInfo = OperateLogInfo.builder()
                .relateId(String.valueOf(id))
                .type(ModuleEnum.COMMON.getCode())
                .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                .optUserName(oaTokenInfo.getName())
                .content("删除专题页配置")
                .showType(LogShowTypeEnum.ADMIN.getCode())
                .build();
        operateLogInfoService.saveOrUpdate(bindOperateLogInfo);
        return Result.success();
    }

    @Override
    public Result<Boolean> enabled(Integer id) {
        OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
        if (Objects.isNull(oaTokenInfo)) {
            return Result.errorInfo("用户未登录");
        }
        TopicConfig config = this.getById(id);
        String content;
        if (config.getEnabled()){
            config.setEnabled(false);
            content = "开切换到关";
        } else {
            int count = this.count(Wrappers.<TopicConfig>lambdaQuery().eq(TopicConfig::getType, config.getType()).eq(TopicConfig::getEnabled,true));
            if (count >= MagicalValueConstant.INT_8) {
                return Result.errorInfo("专题同类配置最多8张");
            }
            config.setEnabled(true);
            content = "关切换到开";
        }
        config.updateById();
        // 记录操作日志
        OperateLogInfo bindOperateLogInfo = OperateLogInfo.builder()
                .relateId(String.valueOf(id))
                .type(ModuleEnum.COMMON.getCode())
                .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                .optUserName(oaTokenInfo.getName())
                .content("专题页配置开关切换：" + content)
                .showType(LogShowTypeEnum.ADMIN.getCode())
                .build();
        operateLogInfoService.saveOrUpdate(bindOperateLogInfo);
        return Result.success();
    }
}

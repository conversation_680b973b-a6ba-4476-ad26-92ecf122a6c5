package com.jiuji.pick.service.product.vo;

import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.product.entity.PickProduct;
import com.jiuji.pick.service.product.entity.ProductInfo;
import com.jiuji.pick.service.rpc.vo.OaUserVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BuildSaleOrderParam {

    private OrderInfo orderInfo;
    private Map<Long, PickProduct> pickProductMap;
    private OaUserVo oaUserVo;
    private Map<Long, ProductInfo> productInfoMap;
    private Map<Long, List<OrderDetailInfo>> orderDetailInfoMap;
    private String areaCode;
    private String saleName;
}

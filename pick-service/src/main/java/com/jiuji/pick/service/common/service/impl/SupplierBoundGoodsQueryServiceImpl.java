package com.jiuji.pick.service.common.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.enums.LogComparison.LogDifferences;
import com.jiuji.pick.common.enums.LogComparison.SupplierAddOrUpdateProductParamEnum;
import com.jiuji.pick.common.utils.ReflexUtils;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.service.LogService;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.product.param.AdmUpdateSupplierProductDetailParam;
import com.jiuji.pick.service.product.param.SupplierAddOrUpdateProductParamOld;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("SupplierBoundGoodsQueryServiceImpl")
public class SupplierBoundGoodsQueryServiceImpl implements LogService {

    @Resource
    private NewOperateLogInfoService logInfoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Override
    public void systemSaveLog(Object param){
        OATokenInfo oaTokenInfo = Optional.ofNullable(currentRequestComponent.getOATokenInfoBoWithoutCheck())
                .orElse(new OATokenInfo());
        CompletableFuture.runAsync(()->{
        AdmUpdateSupplierProductDetailParam productDetailParam = JSONUtil.toBean(JSONUtil.toJsonStr(param), AdmUpdateSupplierProductDetailParam.class);
        SupplierAddOrUpdateProductParamOld paramOld1 = productDetailParam.getParamOld();
        SupplierAddOrUpdateProductParamOld paramNew = new SupplierAddOrUpdateProductParamOld();
        SupplierAddOrUpdateProductParamOld paramOld = new SupplierAddOrUpdateProductParamOld();
        BeanUtils.copyProperties(param,paramNew);
        BeanUtils.copyProperties(paramOld1,paramOld);
        LogDifferences logDifferences = getLogDifferences(paramOld, paramNew);

            String comment = null;
            try {
                comment = ReflexUtils.entityComparison(logDifferences);
            } catch (Exception e) {
                log.error("供应商绑定商品查询日志记录异常{}",e.getMessage(),e);
            }
            //特殊字段处理
            String connent = specialField(paramOld, paramNew, comment);

            NewOperateLogInfo newOperateLogInfo = new NewOperateLogInfo();
            newOperateLogInfo.setContent(connent).setCreateTime(LocalDateTime.now())
                    .setOptUserId(oaTokenInfo.getUserId().longValue()).setOptUserName(oaTokenInfo.getName())
                    .setType(NewOperateLogInfoTypeEnum.SUPPLIER_BOUND_GOODS_QUERY.getCode())
                    .setRelateId(productDetailParam.getPpid()+"")
                    .setShowType(LogShowTypeEnum.ADMIN.getCode());
            logInfoService.saveSystemLog(newOperateLogInfo);
        });

    }

    private String specialField(SupplierAddOrUpdateProductParamOld paramOld ,SupplierAddOrUpdateProductParamOld paramNew,String comment){
        //MATERIAL("material","物料")进行特殊处理
        String materialNew = Optional.ofNullable(paramNew.getMaterial()).orElse("");
        String materialOld = Optional.ofNullable(paramOld.getMaterial()).orElse("") ;
        if(materialNew.equals(materialOld)){
            return comment;
        }

        StringJoiner joinerNew = new StringJoiner(",");
        StringJoiner joinerOld = new StringJoiner(",");


        if(!"".equals(materialNew)){
            List<String> stringsNew= Arrays.asList(materialNew.split(","));
            if(CollectionUtils.isNotEmpty(stringsNew)){
                stringsNew.forEach((String item)->{
                    String nameByValue = MaterialEnum.getNameByValue(Integer.parseInt(item));
                    joinerNew.add(nameByValue);
                });
            }
        }

        if(!"".equals(materialOld)){
            List<String> stringsOld= Arrays.asList(materialOld.split(","));
            if(CollectionUtils.isNotEmpty(stringsOld)){
                stringsOld.forEach((String item)->{
                    String nameByValue = MaterialEnum.getNameByValue(Integer.parseInt(item));
                    joinerOld.add(nameByValue);
                });
            }
        }


        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(comment).append(SupplierAddOrUpdateProductParamEnum.MATERIAL.getMessage())
                .append("由[").append(joinerOld).append("]修改为[").append(joinerNew).append("]。");

        return stringBuilder.toString();
    }

    /**
     * 获取日志的参数
     *
     * @param oldParam
     * @param newParam
     * @return
     */
    private LogDifferences getLogDifferences(Object oldParam, Object newParam) {
        LogDifferences logDifferences = new LogDifferences();
        logDifferences.setOldEntity(oldParam).setNewEntity(newParam).setParamMap(SupplierAddOrUpdateProductParamEnum.getMap());
        HashMap<String, Map<String, String>> map = new HashMap<>(6);
        map.put("material", MaterialEnum.getMap());
        map.put("noReasonReturn", NoReasonReturnEnum.getMap());
        map.put("badPay", BadPayEnum.getMap());
        map.put("lackPay", LackPayEnum.getMap());
        map.put("remoteDeliveryFee", RemoteDeliveryFeeEnum.getMap());
        logDifferences.setExcludeParams(Arrays.asList(SupplierAddOrUpdateProductParamEnum.MATERIAL.getCode()));
        logDifferences.setTransformationMap(map);
        return logDifferences;
    }
}

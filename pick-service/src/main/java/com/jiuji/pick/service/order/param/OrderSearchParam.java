package com.jiuji.pick.service.order.param;

import com.jiuji.pick.common.enums.OrderStatusEnum;
import com.jiuji.pick.common.param.BasePageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 订单列表查询参数
 *
 * <AUTHOR>
 * @since 2021-6-2
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderSearchParam extends BasePageParam {

    /**
     * 订单状态
     *
     * @see OrderStatusEnum
     */
    private Integer orderStatus;

    /**
     * 门店id
     */
    private Long areaId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    private String startTimeStr;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    private String endTimeStr;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 合作伙伴id
     */
    private Long partnerId;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 关键字类型，1、供应商id，2、供应商名称，3、合作伙伴id，4、合作伙伴名称，5、采购单号、6、采购单名称，7、销售单号，8、ppid，9、商品名称，
     */
    private Integer keyType;

    /**
     * 部分发货
     * 0-未勾选
     * 1-勾选
     */
    private Integer partialDelivery;

    /**
     * 关键字内容
     */
    private String keyValue;

    /**
     *     UNTAXED(0, "未税"),
     *     TAX_INCLUDED(1, "含税");
     * @see com.jiuji.pick.common.enums.OrderPriceTypeEnum
     */
    private Integer priceType;

    /**
     * 0-不勾选
     * 1-勾选
     * 是否超过24 小时
     */
    private Integer isOverOneDay;
}

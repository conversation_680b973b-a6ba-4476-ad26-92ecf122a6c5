package com.jiuji.pick.service.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.service.product.bo.AdmSkuBO;
import com.jiuji.pick.service.product.bo.ProductBO;
import com.jiuji.pick.service.product.entity.BrandCategory;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.entity.Product;
import com.jiuji.pick.service.product.entity.ProductInfo;
import com.jiuji.pick.service.product.mapper.ProductMapper;
import com.jiuji.pick.service.product.service.BrandCategoryService;
import com.jiuji.pick.service.product.service.CategoryService;
import com.jiuji.pick.service.product.service.ProductInfoService;
import com.jiuji.pick.service.product.service.ProductPriceService;
import com.jiuji.pick.service.product.service.ProductSearchService;
import com.jiuji.pick.service.product.service.ProductService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private ProductPriceService productPriceService;

    @Autowired
    private BrandCategoryService brandCategoryService;

    @Autowired
    private ProductInfoService productInfoService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ProductSearchService productSearchService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addProduct(ProductBO productBO, Long staffId, boolean isSynchronize) {
        /****** 映射数据库实体对象---Product---商品 ******/
        Product product = new Product();
        transform2Product(productBO, product);

        /****** 保存数据库 ******/
        // 基本信息
        addBaseInfo(product, productBO, isSynchronize);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProduct(ProductBO productBO, Long staffId) {

        /****** 映射数据库实体对象---Product---商品 ******/
        Product product = new Product();
        transform2Product(productBO, product);

        /****** 保存数据库 ******/
        updateBaseInfo(product, productBO);
        Long productId = product.getId();
        //保存商品productInfo
        productInfoService.saveOrUpdateProductInfo(productId);

        /******************** 修改成功之后的操作 ******************/
        // ② 保存到MongoDB并构建日志入参（other库 此处构建透传到controller层记录）
        //add2MongoAndFillLog(product, productPrice, staffId, product.getInuser(), productBO, false);
        // ④ 放入缓存（因为有上下架操作 所以要查询该商品下所有的PPID）
        flushRedisCacheAndES(productId);
        return true;
    }



    /**
     * 将业务对象转换为商品实体
     */
    private void transform2Product(ProductBO productBO, Product product) {
        BeanUtils.copyProperties(productBO, product);
        if(productBO.getId() != null && productBO.getId() == 0){
            product.setId(null);
        }
        // 分类树
        String cidFamily = getCidFamily(productBO.getCid(), productBO.getCidExtend());
        productBO.setCidFamily(cidFamily);
        // 七天退货
        int seven;
        if (productBO.getSevenDays() != null && productBO.getSevenDays()) {
            seven = 1;
        } else {
            seven = productBO.getSevenDaysUnAct() != null && productBO.getSevenDaysUnAct() ? 2 : productBO.getNoSevenDays() != null && productBO.getNoSevenDays() ? 3 : 0;
        }
        // 上下架
        boolean isDisplay = productBO.getIsDisplay() == null ? false : productBO.getIsDisplay();
        productBO.setIsDisplay(isDisplay);
        // 支持的服务
        int supportService = CommonUtil.covertIdStr(productBO.getSupportService()).stream().mapToInt(e -> e).sum();

        product.setCidFamily(cidFamily);
        product.setComment(productBO.getRemark());
        product.setDecription(productBO.getProBrief());
        product.setDecription2(productBO.getDescription());
        product.setBrandId(productBO.getBrandId());
        product.setDisplay(productBO.getIsDisplay());
        product.setIsMobile(productBO.getIsMobile());
        product.setUltimate(productBO.getIsUltimate());
        product.setSevenDayReturn(seven);
        product.setSupportService(supportService);
        product.setPcDesLinkName(productBO.getPcDesLinkName());
        product.setPcDesLinkUrl(productBO.getPcDesLinkUrl());
        product.setQkkDesLinkName(productBO.getLookDesLinkName());
        product.setQkkDesLinkUrl(productBO.getLookDesLinkUrl());
        product.setSellingPoint(productBO.getSellingPoint());
        product.setIsRecommendByLikeProduct(productBO.getIsRecommendByLike());
        product.setInuser("九机同步");
        //服务介绍跳转链接
        product.setServiceIntroduceLink(productBO.getServiceIntroduceLink());
    }

    /**
     * 获取分类树
     */
    private String getCidFamily(Integer cid, Integer cidExtend) {
        String cidFamily = "";
        Category category = categoryService.getById(cid);
        if (category != null) {
            if (StringUtils.isBlank(category.getPath())) {
                cidFamily = "," + cid + ",";
            } else {
                cidFamily = category.getPath() + cid + ",";
                if (cidExtend != null && cidExtend != 0) {
                    cidFamily = cidFamily + cidExtend + ",";
                }
            }
        }
        return cidFamily;
    }

    /**
     * 保存商品基本信息 包含wcf和mongo操作 product -> productprice -> productStInfo -> ProductSearchKey ->
     * SearchKey -> productFetureInfo -> (product_cs)
     *
     * @param product       商品
     * @param productBO     原始对象
     * @param isSynchronize 是否是同步
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBaseInfo(Product product, ProductBO productBO, boolean isSynchronize) {
        // 添加商品表 product
        if (isSynchronize) {
            Product existProduct = this.getById(product.getId());
            if (existProduct == null) {
                baseMapper.insertProduct(product);
            } else {
                // 采货王需要保持商品信息完全一致
                updateById(product);
//                // 强制更新分类
//                existProduct.setCid(product.getCid());
//                existProduct.setCidFamily(product.getCidFamily());
//                LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
//                updateWrapper.set(Product::getCid, product.getCid())
//                        .set(Product::getCidFamily, product.getCidFamily())
//                        .set(Product::getLastModifyTime, new Date())
//                        .set(Product::getName, product.getName())
//                        .set(Product::getShotName, product.getShotName())
//                        .set(Product::getSellingPoint, product.getSellingPoint())
//                        .set(Product::getDetail, product.getDetail())
//                        .set(Product::getPsort, product.getPsort())
//                        .eq(Product::getId, product.getId());
//                // 更新数据库
//                update(updateWrapper);
            }
        } else {
            if (product.getId() != null && product.getId() == 0) {
                product.setId(null);
            }
            saveOrUpdate(product);
        }
        Long productId = product.getId();

        //添加品牌和分类关系
        BrandCategory one = brandCategoryService.getOne(new LambdaUpdateWrapper<BrandCategory>()
                .eq(BrandCategory::getCategoryId, productBO.getCid())
                .eq(BrandCategory::getBrandId, productBO.getBrandId()), false);
        if (one == null) {
            one = new BrandCategory();
            one.setCategoryId(productBO.getCid());
            one.setBrandId(productBO.getBrandId());
            one.setRank(0);
            brandCategoryService.save(one);
        }
        //添加sku列表
        List<AdmSkuBO> admSkuBOList = productBO.getAdmSkuBOList();
        if (CollectionUtils.isNotEmpty(admSkuBOList)) {
            admSkuBOList.forEach(s -> {
                s.setProductid(productId);
            });
            productPriceService.batchUpdateSku(productBO.getAdmSkuBOList(), isSynchronize);
        }

    }

    /**
     * 更新商品基本信息 包含wcf和mongo操作
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBaseInfo(Product product, ProductBO productBO) {
        // 更新商品表 product
        baseMapper.updateProductById(product);
        Long productId = product.getId();

        //添加品牌和分类关系
        BrandCategory one = brandCategoryService.getOne(new LambdaUpdateWrapper<BrandCategory>()
                .eq(BrandCategory::getCategoryId, productBO.getCid())
                .eq(BrandCategory::getBrandId, productBO.getBrandId()), false);
        if (one == null) {
            one = new BrandCategory();
            one.setCategoryId(productBO.getCid());
            one.setBrandId(productBO.getBrandId());
            one.setRank(0);
            brandCategoryService.save(one);
        }

        // 更新价格表 productprice
        List<AdmSkuBO> admSkuBOList = productBO.getAdmSkuBOList();
        admSkuBOList.forEach(s -> {
            s.setProductid(productId);
        });
        productPriceService.batchUpdateSku(admSkuBOList, false);
    }


    /**
     * 更新缓存中ppid明细
     */
    private void flushRedisCacheAndES(Long productId) {
        List<Long> ppids = productInfoService.list(new QueryWrapper<ProductInfo>()
                .select(ProductInfo.P_PRICE_ID)
                .eq(ProductInfo.PRODUCT_ID, productId))
                .stream().map(ProductInfo::getPpriceid)
                .collect(Collectors.toList());
        //更新缓存
        if (CollectionUtils.isNotEmpty(ppids)) {
            ppids.forEach(p -> productPriceService.setProductInRedisToCache(p));
        }
        //更新搜索引擎ES
        productSearchService.addProductBatch(ppids, false);
    }

}

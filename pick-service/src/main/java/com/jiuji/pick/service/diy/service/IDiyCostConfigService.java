package com.jiuji.pick.service.diy.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.cloud.office.vo.PartnerBalanceRes;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.diy.entity.DiyCostConfig;
import com.jiuji.pick.service.diy.vo.req.DiyCostConfigBatchReq;
import com.jiuji.pick.service.diy.vo.req.DiyCostConfigSearchReq;
import com.jiuji.pick.service.diy.vo.req.TenantBalanceReq;
import com.jiuji.pick.service.diy.vo.res.DiyCostConfigRes;
import com.jiuji.pick.service.diy.vo.res.DiyCostConfigSearchRes;
import com.jiuji.pick.service.rpc.vo.*;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-06-16
 */
public interface IDiyCostConfigService extends IService<DiyCostConfig> {

    /**
     * 查询余额
     * @param tenantBalanceReq
     * @return
     */
    Page<PartnerBalanceRes> selectTenantBalance(TenantBalanceReq tenantBalanceReq);

    /**
     * 导出余额查询
     * @param response
     * @param tenantBalanceReq
     */
    void tenantBalanceExport( HttpServletResponse response, TenantBalanceReq tenantBalanceReq);

    Page<DiyCostConfigSearchRes> getCostConfigList(DiyCostConfigSearchReq query);

    Boolean saveOrUpdateDiyCostConfig(DiyCostConfigBatchReq req);

    Page<DiyCostFlowSearchRes> getDiyCostFlowPageList(DiyCostFlowSearchReq query);

    void getDiyCostFlowPageListExport(DiyCostFlowSearchReq req, HttpServletResponse response);

    Result<String> getDiyCostFlowPageListExportDetail(HttpServletResponse response, DiyCostFlowSearchReq req);

    List<DiyCostConfigRes> getDiyCostConfigListByPpriceId(DiyCostConfigSearchReq query);

    DiyCostFlowLeftRes getLeftDiyCostList(DiyCostFlowLeftReq query);

    DiyCostFlowUserRes getNameList(DiyCostFlowUserReq req);


}

/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.service.product.entity.BrandCategory;
import com.jiuji.pick.service.product.mapper.BrandCategoryMapper;
import com.jiuji.pick.service.product.service.BrandCategoryService;
import com.jiuji.pick.service.product.vo.BindBrandToCategoryVO;
import com.jiuji.pick.service.product.vo.BrandBindWithCategoryVO;
import com.jiuji.pick.service.product.vo.BrandCategoryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
@Slf4j
@Service
public class BrandCategoryServiceImpl extends
        ServiceImpl<BrandCategoryMapper, BrandCategory> implements BrandCategoryService {

    @Resource
    private BrandCategoryMapper brandCategoryMapper;

    @Override
    public List<BrandCategoryVO> listAllWithName() {
        return baseMapper.listAllWithName();
    }


    @Override
    public List<Integer> selectCateIdbyBrandId(Integer brandId) {
        return baseMapper.selectCateIdbyBrandId(brandId);
    }

    /**
     * 根据品牌id查询
     *
     * @param brandId 品牌id
     * @return 实体List
     */
    private List<BrandCategory> getListByBrandId(Integer brandId) {
        QueryWrapper<BrandCategory> brandCategoryQueryWrapper = new QueryWrapper<>();
        brandCategoryQueryWrapper.eq("brand_id", brandId);
        return brandCategoryMapper.selectList(brandCategoryQueryWrapper);
    }

    /**
     * 绑定分类到品牌
     *
     * @param associatedCategoryIdList 品牌分类List
     * @param brandId 品牌id
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindCategoryToBrand(List<Integer> associatedCategoryIdList, Integer brandId) {
        try {
            // 目前已经绑定的分类
            // SQL操作 --查询
            List<BrandCategory> brandCategoryList = this.getListByBrandId(brandId);
            // 不再关联的Id的List
            List<Integer> noLongerAssociatedBrandCategoryIdList = brandCategoryList.parallelStream()
                    .filter(a -> !associatedCategoryIdList.contains(a.getCategoryId()))
                    .map(BrandCategory::getId)
                    .collect(Collectors.toList());
            // 不再关联的分类的Id的List
            List<Integer> noLongerAssociatedCategoryIdList = brandCategoryList.parallelStream()
                    .filter(a -> !associatedCategoryIdList.contains(a.getCategoryId()))
                    .map(BrandCategory::getCategoryId)
                    .collect(Collectors.toList());
            // 除去所有不需要的记录
            associatedCategoryIdList.removeAll(brandCategoryList
                    .parallelStream()
                    .filter(bc -> !noLongerAssociatedCategoryIdList.contains(bc.getCategoryId()))
                    .map(BrandCategory::getCategoryId)
                    .collect(Collectors.toList()));
            brandCategoryList.clear();
            for (Integer categoryId : associatedCategoryIdList) {
                BrandCategory brandCategory = new BrandCategory();
                brandCategory.setBrandId(brandId);
                brandCategory.setCategoryId(categoryId);
                brandCategory.setRank(0);
                brandCategoryList.add(brandCategory);
            }
            // 删除不再关联的记录
            if (CollectionUtils.isNotEmpty(noLongerAssociatedBrandCategoryIdList)) {
                // SQL操作 --删除
                brandCategoryMapper.deleteBatchIds(noLongerAssociatedBrandCategoryIdList);
            }
            // SQL操作 --添加新纪录
            // 使用mybatis plus 的saveBatch时会报错，目前暂时使用foreach代替
            brandCategoryList.forEach(brandCategory -> brandCategoryMapper.insert(brandCategory));
            return true;
        } catch (Exception e) {
            log.error("内部错误", e);
            return false;
        }
    }

    /**
     * 绑定品牌到分类
     *
     * @param bindBrandToCategoryVO VO
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindBrandToCategory(BindBrandToCategoryVO bindBrandToCategoryVO) {
        Integer categoryId = bindBrandToCategoryVO.getCategoryId();
        // 新需要绑定的list
        List<BrandBindWithCategoryVO> brandBindWithCategoryVOList = bindBrandToCategoryVO
                .getBrandBindWithCategoryList();
        try {
            // 获取所有已经绑定的List
            QueryWrapper<BrandCategory> brandCategoryQueryWrapper = new QueryWrapper<>();
            brandCategoryQueryWrapper.eq(BrandCategory.CATEGORYID, categoryId);
            // 如果是删除所有关联
            if (CollectionUtils.isEmpty(brandBindWithCategoryVOList)) {
                //SQL 操作 ==》 删除
                brandCategoryMapper.delete(brandCategoryQueryWrapper);
                return true;
            }
            //SQL 操作 ==》 查询
            List<BrandCategory> currentBrandCategoryList = brandCategoryMapper
                    .selectList(brandCategoryQueryWrapper);
            // 删除所有没绑定的List
            Iterator<BrandCategory> brandCategoryIterator = currentBrandCategoryList.iterator();
            while (brandCategoryIterator.hasNext()) {
                BrandBindWithCategoryVO brandBindWithCategoryVO = new BrandBindWithCategoryVO(
                        brandCategoryIterator.next());
                // 排除数据库里已经存在并且没有改动的数据
                if (brandBindWithCategoryVOList.contains(brandBindWithCategoryVO)) {
                    brandBindWithCategoryVOList.remove(brandBindWithCategoryVO);
                    brandCategoryIterator.remove();
                }
            }
            // 获取所有需要增加的BrandCategory
            List<BrandCategory> insertBrandCategoryList = brandBindWithCategoryVOList
                    .parallelStream()
                    .map(brandBindWithCategoryVO -> BrandBindWithCategoryVO
                            .toSuper(categoryId, brandBindWithCategoryVO))
                    .collect(Collectors.toList());
            // 获取所有需要删除的Id
            List<Integer> requireDelBrandCategoryIdList = currentBrandCategoryList
                    .parallelStream()
                    .filter(cbc -> cbc.getId() != null)
                    .map(BrandCategory::getId)
                    .collect(Collectors.toList());
            // 如果存在需要删除的数据
            //SQL 操作 ==》 删除
            if (!org.springframework.util.CollectionUtils.isEmpty(requireDelBrandCategoryIdList)) {
                brandCategoryMapper.deleteBatchIds(requireDelBrandCategoryIdList);
            }
            //SQL 操作 ==》 插入
            insertBrandCategoryList
                    .forEach(brandCategory -> brandCategoryMapper.insert(brandCategory));
            return true;
        } catch (Exception e) {
            log.error("内部错误", e);
        }
        return false;
    }

    @Override
    public List<Integer> getBrandIdsByCid(List<Integer> categoryId) {
        return brandCategoryMapper.getBrandIdsByCid(categoryId);
    }
}

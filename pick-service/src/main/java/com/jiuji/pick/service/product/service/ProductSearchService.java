package com.jiuji.pick.service.product.service;

import com.jiuji.pick.service.product.bo.ProductSearchInEsV2;
import com.jiuji.pick.service.product.vo.ProductSearchRequestVo;
import com.jiuji.pick.service.product.vo.RecommendSearchVo;
import com.jiuji.pick.service.product.vo.SearchResultVo;

import java.util.List;

/**
 * @Author:zuofei
 * @Description
 * @Date 2020-08-18 商品搜索
 */
public interface ProductSearchService {

    /**
     * 搜索商品
     */
    SearchResultVo searchProduct(ProductSearchRequestVo productSearchRequestVo);

    /**
     * 到es中查询数据
     */
    List<ProductSearchInEsV2> searchProductFromEs(String keyword, int areaCode,
                                                  boolean showRepair, boolean and, List<Integer> keywordCids, List<Integer> keywordBrands,
                                                  List<String> analyzer, List<List<String>> params);

    /**
     * 创建新品索引
     */
    boolean createIndex(String index,String pathResource);

    /**
     * 批量插入商品到es
     *
     * @param ppidList     ppidList  指定的ppid进行插入   有ppidlist 则 needAllPpids为false
     * @param needAllPpids 需要所有ppid全量刷新
     * @return 执行结果
     */
    boolean addProductBatch(List<Long> ppidList, boolean needAllPpids);

    /**
     * 重新构建商品数据到ES
     * @return
     */
    boolean rebuildProductToEs();

    /**
     * 重建联想查询数据到ES
     * @return
     */
    boolean  rebuildProductAssociationEs();

    /**
     * 批量删除es中的供应商商品
     *
     * @param bindIds
     * @return
     */
    boolean deleteProductBatch(List<Long> bindIds);

    /**
     * 根据索引删除ES数据
     * @return
     */
    boolean cleanEsDataForIndex(String esIndex);


    /**
     * 添加搜索记录
     */
    Long addSearchHistory(Long userId, String historyName);


    /**
     * 删除用户的浏览记录
     */
    void deleteHistorySearch(String userId);

    /**
     * 删除指定用户指定浏览记录
     *
     * @param userId         userId
     * @param willDelHistory 指定浏览记录
     */
    void deleteHistorySearch(String userId, List<String> willDelHistory);

    /**
     * 查询用户的浏览记录
     */
    List<String> getHistorySearch(String userId);

    /**
     * 根据关键字搜索商品信息
     *
     * @param searchName
     * @param count
     * @return
     */
    List<RecommendSearchVo> getRecommendSearch(String searchName, int count);


    /**
     * 根绝关键字搜索商品信息
     * @param searchName
     * @return
     */
    List<String> getRecommendSearch(String searchName);



}

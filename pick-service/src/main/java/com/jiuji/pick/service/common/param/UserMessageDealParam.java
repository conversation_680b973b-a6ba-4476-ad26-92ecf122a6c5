package com.jiuji.pick.service.common.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @function:
 * @description: UserMessageDealParam.java
 * @date: 2021/05/28
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class UserMessageDealParam {

    private Long userMessageId;
    /**
     * 0：供应商，1：合作伙伴
     */
    @NotNull(message = "用户类型type不能为空")
    private Integer type;

    /**
     * 0：单条已读，1：全部已读
     */
    @NotNull(message = "操作类型不能为空")
    private Integer operateType;

}

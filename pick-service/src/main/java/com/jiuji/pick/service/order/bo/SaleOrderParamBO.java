package com.jiuji.pick.service.order.bo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @function:
 * @description: SaleOrderParamBO.java
 * @date: 2021/07/02
 * @author: sun<PERSON>yun
 * @version: 1.0
 */
@Data
public class SaleOrderParamBO {

    @JSONField(name = "sub")
    private SaleOrderParam saleOrderParam;
    @JSONField(name = "basket")
    private List<SaleProductParam> saleProductParamList;

    @Data
    public static class SaleOrderParam {

        @JSONField(name = "userid")
        private Integer userId;
        private String area;
        @JSONField(name = "areaid")
        private Long areaId;
        @JSONField(name = "subtype")
        private Integer subType;
        @JSONField(name = "sub_pay")
        private Integer subPay;
        private String comment;
        @JSONField(name = "sub_to")
        private String subTo;
        @JSONField(name = "sub_tel")
        private String subTel;
        @JSONField(name = "sub_adds")
        private String subAdds;
        @JSONField(name = "sub_mobile")
        private String subMobile;
        @JSONField(name = "cityid")
        private Integer cityId;
        private Integer delivery;
        @JSONField(name = "feeM")
        private String totalDeliveryFee;
        @JSONField(name = "yingfuM")
        private String totalPrice;
        @JSONField(name = "Inuser")
        private String inUser;
        @JSONField(name = "UserName")
        private String userName;
        @JSONField(name = "userclass")
        private Integer userClass;

    }

    @Data
    public static class SaleProductParam {
        @JSONField(name = "ppriceid")
        private Long ppid;
        @JSONField(name = "basket_count")
        private Integer productCount;
        @JSONField(name = "product_peizhi")
        private String productTitle;
        private String price;
        @JSONField(name = "price1")
        private String salePrice;
        @JSONField(name = "inprice")
        private String costPrice;
        private Integer type;
        private String seller;
        @JSONField(name = "ismobile")
        private Integer isMobile;
    }
}

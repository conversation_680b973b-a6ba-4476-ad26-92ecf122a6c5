package com.jiuji.pick.service.product.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/08
 * @Time 11:40
 */
@Data
public class QueryIndexWhiteConfigVo {

   private Long id;

   /**
    * ppid 多个英文逗号分隔
    */
   private String ppid;

   /**
    * 用户id 多个英文逗号分隔
    */
   @TableField("user_Ids")
   private String userIds;

   /**
    * 限制对象0：合作伙伴 1：供应商
    */
   private Integer restrictTarget;

   /**
    * 限制类型0：白名单 1：黑名单
    */
   private Integer restrictType;





}

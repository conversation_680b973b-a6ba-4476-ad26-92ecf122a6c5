package com.jiuji.pick.service.order.service.impl;

import com.google.common.collect.Maps;
import com.jiuji.pick.common.enums.PartnerUserSourceEnum;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * @function:
 * @description: OrderFactoryProducer.java
 * @date: 2021/10/13
 * @author: sunfayun
 * @version: 1.0
 */
@Component
public class OrderFactoryProducer {

    private final static Map<Integer, AbstractOrderFactory> factoryMap = Maps.newHashMap();

    @Resource
    private OrderServiceFactory orderServiceFactory;
    @Resource
    private NeoOrderServiceFactory neoOrderServiceFactory;

    @PostConstruct
    private void initFactoryMap() {
        factoryMap.put(PartnerUserSourceEnum.OA.getCode(), orderServiceFactory);
        factoryMap.put(PartnerUserSourceEnum.SMALL_SASS.getCode(), neoOrderServiceFactory);
    }

    public AbstractOrderFactory getOrderFactory(int partnerType) {
        return factoryMap.get(partnerType);
    }

}

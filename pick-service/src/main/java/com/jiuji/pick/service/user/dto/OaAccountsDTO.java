package com.jiuji.pick.service.user.dto;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 对公或对私账户
 *
 * <AUTHOR>
 * @since 2021-5-21
 */
@Data
public class OaAccountsDTO {

    /**
     * 账号
     */
    @JSONField(name = "accountnumber")
    private String accountNumber;

    /**
     * 户名
     */
    private String username;

    /**
     * 开户行
     */
    @JSONField(name = "openingbank")
    private String openingBank;

    /**
     * 创建时间
     */
    @JSONField(name = "dtime")
    private LocalDateTime dTime;

    /**
     * 对公对私  1-对公 2-对私
     */
    @JSONField(name = "isgs")
    private Integer type;

//    /**
//     * 城 市
//     */
//    private String city;
}

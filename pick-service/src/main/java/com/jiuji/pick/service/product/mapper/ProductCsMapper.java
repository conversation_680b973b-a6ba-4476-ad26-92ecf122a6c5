/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.bo.ProductCsBO;
import com.jiuji.pick.service.product.entity.ProductCs;
import com.jiuji.pick.service.product.vo.ProductCsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
public interface ProductCsMapper extends BaseMapper<ProductCs> {

    int insertProductCs(ProductCs productCs);


    /**
     * 根据商品或者PPID查询参数
     */
    @SqlParser(filter = true)
    List<ProductCsBO> getParamByGoodsIdAndType(@Param("goodsId") Long goodsId,
                                               @Param("type") Integer type, @Param("cateType") Integer cateType);



    List<ProductCsVO> getProductAllType(@Param("ppids")List<Long> ppids);

    List<ProductCsVO> getProductCsByPpid(@Param("ppids")List<Long> ppids);
}

package com.jiuji.pick.service.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.common.entity.NotifyLogInfo;

/**
 * <p>
 * 和外部系统交互日志记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public interface NotifyLogInfoService extends IService<NotifyLogInfo> {

    /**
     * 保存交互通知日志
     * @param notifyLogInfo
     */
    void saveNotifyLogInfo(NotifyLogInfo notifyLogInfo);


}

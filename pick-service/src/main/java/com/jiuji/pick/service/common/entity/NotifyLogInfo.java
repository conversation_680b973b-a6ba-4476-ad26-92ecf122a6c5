package com.jiuji.pick.service.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;

import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 和外部系统交互日志记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_notify_log_info")
public class NotifyLogInfo extends Model<NotifyLogInfo> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long xTenant;

    /**
     * 订单号
     */
    private Long orderNo;

    /**
     * 通知类型，1：采货王通知外部，2：外部通知采货王
     */
    private Integer notifyType;

    /**
     * 通知名称
     */
    private String notifyName;

    /**
     * 通知URL
     */
    private String notifyUrl;

    /**
     * 通知参数
     */
    private String notifyParam;

    /**
     * 通知结果
     */
    private String notifyResult;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

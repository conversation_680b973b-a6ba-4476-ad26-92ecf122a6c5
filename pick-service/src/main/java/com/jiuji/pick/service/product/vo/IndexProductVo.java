package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/06
 * @Time 9:54
 */
@Data
public class IndexProductVo {

    private Long id;

    private Long ppid;

    private Long productId;

    private Long jiuJiProductId;

    private Long supplierId;

    private Long supplierProductId;

    private String companyName;

    private String address;

    private String bpic;

    private Integer source;

    private Integer productStatus;

    private Integer productType;

    private BigDecimal advicePrice;

    private BigDecimal buyNoTaxPrice;
    private BigDecimal buyTaxPrice;



    private String productName;

    /**
     * 售后省名称
     */
    private String afterSaleProvinceName;

    /**
     * 售后城市名称
     */
    private String afterSaleCityName;

    /**
     * 售后区名称
     */
    private String afterSaleDistrictName;

    /**
     * 九机销量
     */
    private Integer saleCount;

    private Integer sort;

    private LocalDateTime productUpTime;
}

package com.jiuji.pick.service.order.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CreateSaleOrderVo {

   @NotNull(message = "订单id不能为空")
   private String orderIds;

   private String areaCode;
//   /**
//    * 销售人姓名
//    */
//   private String saleName;
//
//   /**
//    * 销售人工号
//    */
//   private Integer saleJobNumber;


   /**
    * 供应商ID
    */
   private Long supplierId;

}

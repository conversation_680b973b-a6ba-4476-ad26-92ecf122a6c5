/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.service.product.bo.ProductCsBO;
import com.jiuji.pick.service.product.bo.ProductCsParamDTO;
import com.jiuji.pick.service.product.entity.*;
import com.jiuji.pick.service.product.mapper.ParamValueMapper;
import com.jiuji.pick.service.product.mapper.ProductCsMapper;
import com.jiuji.pick.service.product.mapper.ProductInfoMapper;
import com.jiuji.pick.service.product.service.*;
import com.jiuji.pick.service.product.vo.ProductCsVO;
import com.jiuji.pick.service.product.vo.ProductParamInfoVO;
import com.jiuji.pick.service.product.vo.UpdateProductParamVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
@Service
@Slf4j
public class ProductCsServiceImpl extends ServiceImpl<ProductCsMapper, ProductCs> implements ProductCsService {

    @Resource
    private ParamGroupService paramGroupService;

    @Resource
    private ParamValueService paramValueService;

    @Resource
    private ProductCsBasicService productCsBasicService;

    @Resource
    private ProductService productService;

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private ProductInfoMapper productInfoMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ParamValueMapper paramValueMapper;

    @Autowired
    private CurrentRequestComponent currentRequestComponent;

    @Resource
    private ProductCsMapper productCsMapper;


    @Override
    public boolean updateProductParam(Long productId, List<UpdateProductParamVO.ParamVo> param,
                                      Integer cateType, Integer staffId) {
        List<ProductCsBO> originParam = baseMapper.getParamByGoodsIdAndType(productId, 1, cateType);
        this.handleParamList(originParam);
        boolean result = handleParam(originParam, param, productId, 1, cateType, staffId);
        if (result) {
            //更新商品搜索参数关键词
            updateProductParamSearchKey(productId);
        }
        return result;
    }

    @Override
    public boolean updateSkuParam(Long ppid, List<UpdateProductParamVO.ParamVo> param,
                                  Integer staffId) {
        int type = 0, cateType = 0;
        List<ProductCsBO> originParam = baseMapper.getParamByGoodsIdAndType(ppid, type, cateType);
        this.handleParamList(originParam);
        return handleParam(originParam, param, ppid, type, cateType, staffId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateProductOrSkuParam(UpdateProductParamVO param, boolean isSync) {
        if(param.getPpid() == null && param.getProductId() == null){
            return Result.paramError("error","productId和ppid不能同时为空");
        }
        if(param.getPpid() != null && param.getProductId() != null){
            return Result.paramError("error","productId和ppid不能同时不为空");
        }
        Integer staffId = currentRequestComponent.getStaffUserId();
        if(param.getProductId() != null){
            boolean result = true;
            //0---主分类参数
            if(CollectionUtils.isNotEmpty(param.getParam())){
                boolean mainCateParamsResult = this.updateProductParam(param.getProductId(),param.getParam(),0, staffId);
                result = result && mainCateParamsResult;
            }
            //1---拓展分类参数
            if(CollectionUtils.isNotEmpty(param.getExtendCateParam())){
                boolean extendCateParamsResult = this.updateProductParam(param.getProductId(),param.getExtendCateParam(),1, staffId);
                result = result && extendCateParamsResult;
            }
            return Result.success(result);
        }
        return Result.success(this.updateSkuParam(param.getPpid(),param.getParam(),staffId));
    }

    @Override
    public List<String> getProductSelectParam(Long productId) {
        Product product = productService.getById(productId);
        if (product == null || !product.getIsMobile()) {//只有大件商品才需要参数搜索关键词
            return new ArrayList<>();
        }
        //多选项
        List<String> paramValueIdList = paramValueMapper.getProductSelectParam(productId);
        if (CollectionUtils.isEmpty(paramValueIdList)) {
            return new ArrayList<>();
        }
        String paramValueIdStr = CommonUtil.covertStringList2Str(paramValueIdList);
        QueryWrapper<ParamValue> query = new QueryWrapper<>();
        query.in(ParamValue.ID, CommonUtil.covertIdStr(paramValueIdStr));
        query.select(ParamValue.VALUE);
        List<ParamValue> paramValueList = paramValueService.list(query);
        if (CollectionUtils.isNotEmpty(paramValueList)) {
            List<String> list = paramValueList.stream()
                    .filter(e -> e != null && StringUtils.isNotBlank(e.getValue()))
                    .map(e -> e.getValue()).collect(Collectors.toList());
            //过滤掉包含支持的参数
            list = list.stream().filter(e -> StringUtils.isNotBlank(e) && !e.contains("支持") && !e
                    .contains("其他")).collect(Collectors.toList());
            return list;
        }
        return new ArrayList<>();
    }

    /**
     * 查询所有inpputtype=2,3 多选参数的值
     * @return
     */
    @Override
    public Map<Long, String> getProductParamValue(List<Long> ppid) {
        // ppid - ProductCsVO
        Map<Long,List<ProductCsVO>> m = new HashMap<>();
        // ppid - csValue
        Map<Long,String> result = new HashMap<>();
        List<ProductCsVO> productAllType = baseMapper.getProductAllType(ppid);
        for (ProductCsVO productCsVO:productAllType) {
            if (m.containsKey(productCsVO.getPpriceid())){
                List<ProductCsVO> productCsVOList = m.get(productCsVO.getPpriceid());
                productCsVOList.add(productCsVO);
            }else{
                List<ProductCsVO> productCsVOS = new ArrayList<>();
                productCsVOS.add(productCsVO);
                m.put(productCsVO.getPpriceid(),productCsVOS);
            }
        }

        List<ProductCsVO> productCsByPpid = baseMapper.getProductCsByPpid(ppid);
        for (ProductCsVO productCsVO:productCsByPpid) {
            if (m.containsKey(productCsVO.getPpriceid())){
                List<ProductCsVO> productCsVOS = m.get(productCsVO.getPpriceid());
                for (ProductCsVO p:productCsVOS) {
                    //替换CsValue
                    if (productCsVO.getPpriceid().equals(p.getPpriceid()) && productCsVO.getCsid().equals(p.getCsid())){
                        p.setCsValue(productCsVO.getCsValue());
                    }
                }
            } else{
                List<ProductCsVO> productCsVOS = new ArrayList<>();
                productCsVOS.add(productCsVO);
                m.put(productCsVO.getPpriceid(),productCsVOS);
            }
        }

        Set<Long> longs = m.keySet();
        for (Long l:longs) {
            List<ProductCsVO> csVOList1 = m.get(l);
            StringBuffer stringBuffer = new StringBuffer();
            for(int i=0;i<csVOList1.size();i++){
                if (i != csVOList1.size()-1 && StringUtils.isNotEmpty(csVOList1.get(i).getCsValue())){
                    stringBuffer.append(csVOList1.get(i).getCsValue()).append(",");
                }else if(StringUtils.isNotEmpty(csVOList1.get(i).getCsValue())){
                    stringBuffer.append(csVOList1.get(i).getCsValue());
                }
            }
            result.put(l,stringBuffer.toString());
        }
        return result;

    }
    /**
     * 构建商品参数数据
     *
     * @param list 商品已选参数
     * @param categoryId 分类id
     * @return 商品参数
     */
    private List<ProductParamInfoVO> buildParam(List<ProductCs> list, Integer categoryId) {
        // isDisplay的参数id
        Set<Integer> displayParamSet = list.stream().filter(ProductCs::getIsDisplay)
                .map(ProductCs::getCsid).collect(Collectors.toSet());
        List<ParamGroup> mainParamList = paramGroupService
                .list(new QueryWrapper<ParamGroup>().eq(ParamGroup.CATEID, categoryId));
        List<Integer> mainId = mainParamList.stream().map(ParamGroup::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainId)) {
            return new ArrayList<>();
        }
        // 主参数列表
        List<ProductParamInfoVO> resultList = mainParamList.stream()
                .map(li -> {
                    ProductParamInfoVO info = new ProductParamInfoVO();
                    info.setParentParamId(li.getId());
                    info.setName(li.getGroupName());
                    info.setSort(li.getRank());
                    return info;
                }).sorted(Comparator.comparing(ProductParamInfoVO::getSort))
                .collect(Collectors.toList());
        Map<Integer, List<ProductParamInfoVO.ChildrenParam>> childrenParamMap = new HashMap<>(
                mainId.size());
        // 参数类型是单选多选的需要查询具体选项的
        List<Integer> needSelectParamValueList = new ArrayList<>();
        // 子参数构建
        productCsBasicService
                .list(new QueryWrapper<ProductCsBasic>().in(ProductCsBasic.GROUPS, mainId))
                .stream()
                .collect(Collectors.groupingBy(ProductCsBasic::getGroups))
                .forEach((k, v) -> {
                    List<ProductParamInfoVO.ChildrenParam> collect = v.stream().map(li -> {
                        ProductParamInfoVO.ChildrenParam child = new ProductParamInfoVO.ChildrenParam();
                        if (li.getRank() == null) {
                            child.setSort(0);
                        } else {
                            child.setSort(li.getRank());
                        }
                        child.setParamId(li.getId());
                        child.setInputType(li.getInputtype());
                        child.setDisplayFlag(displayParamSet.contains(li.getId()));
                        // 单选多选的需要查询具体的参数
                        if (ProductCsBasic.INCLUDE_ITEM_INPUT_TYPE.contains(li.getInputtype())) {
                            needSelectParamValueList.add(li.getId());
                        }
                        child.setName(li.getName());
                        return child;
                    }).sorted(Comparator.comparing(ProductParamInfoVO.ChildrenParam::getSort))
                            .collect(Collectors.toList());
                    childrenParamMap.put(k, collect);
                });
        // 子参数赋值
        Map<Integer, List<Integer>> selectParamMap = new HashMap<>();
        Map<Integer, String> stringParamMap = new HashMap<>();
        list.stream().filter(li -> needSelectParamValueList.contains(li.getCsid()))
                .forEach(li -> selectParamMap
                        .put(li.getCsid(), CommonUtil.covertIdStr(li.getCsValue())));
        list.stream().filter(li -> !needSelectParamValueList.contains(li.getCsid()))
                .forEach(li -> stringParamMap.put(li.getCsid(), li.getCsValue()));
        // 单选多选下拉框值查询
        Map<Integer, List<ProductParamInfoVO.Select>> itemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(needSelectParamValueList)) {
            paramValueService.list(new QueryWrapper<ParamValue>()
                    .in(ParamValue.PARAMID, needSelectParamValueList))
                    .stream()
                    .collect(Collectors.groupingBy(ParamValue::getParamId))
                    .forEach((k, v) -> {
                        List<ProductParamInfoVO.Select> collect = v.stream().map(li -> {
                            ProductParamInfoVO.Select select = new ProductParamInfoVO.Select();
                            select.setId(li.getId());
                            select.setName(li.getValue());
                            select.setSort(li.getRank());
                            List<Integer> value = selectParamMap.get(k);
                            if (CollectionUtils.isNotEmpty(value) && value.contains(li.getId())) {
                                select.setSelectedFlag(true);
                            } else {
                                select.setSelectedFlag(false);
                            }
                            return select;
                        }).sorted(Comparator.comparing(ProductParamInfoVO.Select::getSort))
                                .collect(Collectors.toList());
                        itemMap.put(k, collect);
                    });
        }
        // 数据关联渲染
        resultList.forEach(parent -> {
            List<ProductParamInfoVO.ChildrenParam> childrenParams = childrenParamMap
                    .get(parent.getParentParamId());
            if (CollectionUtils.isNotEmpty(childrenParams)) {
                parent.setChildrenParamList(childrenParams);
                childrenParams.forEach(li -> {
                    if (ProductCsBasic.INCLUDE_ITEM_INPUT_TYPE.contains(li.getInputType())) {
                        List<ProductParamInfoVO.Select> selects = itemMap.get(li.getParamId());
                        selects = CollectionUtils.isNotEmpty(selects) ? selects : new ArrayList<>();
                        li.setInfo(selects);
                    } else {
                        String info = stringParamMap.get(li.getParamId());
                        li.setInfo(info == null ? "" : info);
                    }
                });
            }
        });
        return resultList;
    }

    /**
     * 处理商品参数类型为3的参数集
     */
    public void handleParamList(List<ProductCsBO> originParam) {
        if (CollectionUtils.isEmpty(originParam)) {
            return;
        }
        //获取所有参数的id
        List<Integer> ids = new ArrayList<>();
        originParam.stream()
                .filter(x -> x.getInputType() != null && Integer.valueOf(x.getInputType()) == 3)
                .forEach(x -> {
                    //把类型为3的参数所对应的参数集id分割,放到ids里
                    ids.addAll(CommonUtil.covertIdStr(x.getCsValue()));//
                });

        if (CollectionUtils.isNotEmpty(ids)) {
            List<ProductCsParamDTO> params = paramValueMapper.getParamValueByIds(
                    new QueryWrapper<ParamValue>()
                            .in(ParamValue.ID, ids));
            //根据id的list从数据库获取value list
            Map<Long, String> values = params.stream().collect(Collectors
                    .toMap(ProductCsParamDTO::getId,
                            ProductCsParamDTO::getValue));//把value list转换为map
            //把查到的数据放到返回的结果集的对应paramValue中
            originParam.stream().filter(x -> x.getInputType() != null && Integer.valueOf(x.getInputType()) == 3).forEach(x -> {
                String str = CommonUtil.covertIdStr(x.getCsValue())
                        .stream()
                        .map(s -> values.get(Long.valueOf(s)))
                        .collect(Collectors.joining(","));
                x.setParamValue(str);
            });

        }

    }

    /**
     * 修改参数
     *
     * @param originParam 原参数
     * @param currentParam 当前修改提交的参数
     * @param goodsId 参数作用商品的id 可能是productId 可能是ppid
     * @param type 1 goodsId为productId 0 goodsId为ppid
     * @return 修改结果
     */
    private boolean handleParam(List<ProductCsBO> originParam,
                                List<UpdateProductParamVO.ParamVo> currentParam, Long goodsId, Integer type,
                                Integer cateType, Integer staffId) {
        // 当前参数无则清空全部参数--目前只有ppid会走此逻辑
        if (CollectionUtils.isEmpty(currentParam) && CollectionUtils.isNotEmpty(originParam)) {
            List<Integer> ids = originParam.stream().map(ProductCsBO::getId)
                    .collect(Collectors.toList());
            removeByIds(ids);
            // 记录日志
            StringBuilder sb = new StringBuilder("清空全部参数！\n");
            for (ProductCsBO cs : originParam) {
                sb.append("参数:").append(cs.getParamName()).append(" ")
                        .append("参数值:").append(cs.getParamValue()).append(" ")
                        .append("是否显示").append(cs.getIsDisplay()).append("\n");
            }
            return true;
        }
        Map<Integer, UpdateProductParamVO.ParamVo> currentParamMap = new HashMap<>(
                currentParam.size());
        List<Integer> paramId = currentParam.stream().map(li -> {
            currentParamMap.put(li.getParamDetailId(), li);
            return li.getParamDetailId();
        }).collect(Collectors.toList());
        List<Integer> originParamIds = originParam.stream().map(ProductCs::getCsid)
                .collect(Collectors.toList());
        // 老的有的新的没有要删除老的
        List<ProductCsBO> deleteList = originParam.stream()
                .filter(li -> !paramId.contains(li.getCsid())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            List<Integer> ids = deleteList.stream().map(ProductCsBO::getId)
                    .collect(Collectors.toList());
            removeByIds(ids);
            StringBuilder sb = new StringBuilder("删除参数！\n");
            for (ProductCsBO cs : deleteList) {
                sb.append("参数:").append(cs.getParamName()).append(" ")
                        .append("参数值:").append(cs.getParamValue()).append(" ")
                        .append("是否显示:").append(cs.getIsDisplay()).append("\n");
            }
        }
        // 老的新的都有的要用新的值覆盖老的值
        List<ProductCsBO> existList = originParam.stream()
                .filter(li -> paramId.contains(li.getCsid())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existList)) {
            List<ProductCs> csList = new ArrayList<>();
            existList.forEach(li -> {
                UpdateProductParamVO.ParamVo paramVo = currentParamMap.get(li.getCsid());
                if (paramVo != null) {
                    ProductCs productCs = new ProductCs();
                    BeanUtils.copyProperties(li, productCs);
                    productCs.setCsValue(paramVo.getValue());
                    productCs.setIsDisplay(
                            paramVo.getDisplayFlag() == null ? false : paramVo.getDisplayFlag());
                    csList.add(productCs);
                }
            });
            this.updateBatchById(csList);
        }
        // 新的值直接插入
        currentParam.stream().filter(li -> !originParamIds.contains(li.getParamDetailId()))
                .forEach(li -> {
                    ProductCs cs = new ProductCs();
                    cs.setProductId(goodsId);
                    cs.setType(type);
                    cs.setCsid(li.getParamDetailId());
                    cs.setCsValue(li.getValue());
                    cs.setIsDisplay(li.getDisplayFlag() == null ? false : li.getDisplayFlag());
                    cs.setCateType(cateType);
                    cs.insert();
                });
        return true;
    }


    /**
     * 更新商品搜索关键词
     */
    public boolean updateProductParamSearchKey(Long productId) {
        //商品的多选项参数，构造到商品的参数搜索关键词中，参与搜索
        try {
            List<String> paramList = getProductSelectParam(productId);
            if (CollectionUtils.isNotEmpty(paramList)) {
                String productMultSelectCs = paramList.stream().filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(","));
                Product product = new Product();
                product.setId(productId);
                product.setParamSearchKey(productMultSelectCs);
                productService.updateById(product);
            }
        } catch (Exception e) {
            log.error("更新商品参数搜索关键词异常", e);
            return false;
        }
        return true;
    }
}

package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.service.product.entity.SupplierProductDetail;
import com.jiuji.pick.service.product.param.QuerySupplierApplyProductParam;
import com.jiuji.pick.service.product.param.QuerySupplierProductListParam;
import com.jiuji.pick.service.product.vo.QuerySupplierApplyProductListVo;
import com.jiuji.pick.service.product.vo.QuerySupplierProductListVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface SupplierProductDetailMapper extends BaseMapper<SupplierProductDetail> {

    Page<QuerySupplierProductListVo> querySupplierProductList(@Param("page") Page<QuerySupplierProductListVo> page, @Param("param") QuerySupplierProductListParam param);

    Page<QuerySupplierApplyProductListVo> querySupplierApplyProductList(@Param("page") Page<QuerySupplierApplyProductListVo> page, @Param("param") QuerySupplierApplyProductParam param);

    List<SupplierProductDetail> noLogicList(@Param("supplierProductIds") Collection<Long> supplierProductIds);
}

package com.jiuji.pick.service.diy.vo.req;

import com.jiuji.pick.common.param.BasePageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 订单列表查询参数
 *
 * <AUTHOR>
 * @since 2021-6-2
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DiyCostConfigSearchReq extends BasePageParam {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * ppid
     */
    private Long ppriceId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     *
     */
    private String searchValue;


    /**
     *
     *
     */
    private Integer searchType;



    /**
     * ppid
     */
    private List<Long> ppriceIdList;

}

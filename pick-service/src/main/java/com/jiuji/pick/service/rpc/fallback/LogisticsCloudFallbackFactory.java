package com.jiuji.pick.service.rpc.fallback;


import cn.hutool.json.JSONUtil;
import com.jiuji.pick.service.rpc.cloud.LogisticsCloud;
import com.jiuji.pick.service.rpc.vo.CommonQueryRoutReq;
import com.jiuji.pick.service.rpc.vo.QueryRoutTrackRes;
import com.jiuji.tc.common.vo.R;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class LogisticsCloudFallbackFactory implements FallbackFactory<LogisticsCloud> {
    private final static String ERROR_LOG = "物流轨迹中台查询，url:{},params:{}";
    @Override
    public LogisticsCloud create(Throwable throwable) {

        return new LogisticsCloud (){
            @Override
            public R<QueryRoutTrackRes> queryRoute(CommonQueryRoutReq commonQueryRoutReq, String token) {
                log.error(ERROR_LOG, "api/logistics-center-route/query-route/v1", JSONUtil.toJsonStr(commonQueryRoutReq));
                return R.error();
            }

            @Override
            public void sendNewsTwo(String comment) {
                log.error( "采货王异常监控{}", comment);
            }


        };
    }
}

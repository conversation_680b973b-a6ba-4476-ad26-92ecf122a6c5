package com.jiuji.pick.service.order.service;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.vo.CreateSaleOrderVo;
import com.jiuji.pick.service.rpc.vo.CommonQueryRoutReq;
import com.jiuji.pick.service.rpc.vo.QueryRoutTrackRes;
import com.jiuji.tc.common.vo.R;
/**
 * @function:
 * @description: SaleOrderService.java
 * @date: 2021/07/01
 * @author: sunfayun
 * @version: 1.0
 */
public interface SaleOrderService {

    /**
     * 创建销售单
     * @param orderIds
     * @return
     */
    Result<String> createSaleOrder(String orderIds, String areaCode);

    /**
     * 创建销售单2
     * @param createSaleOrderVo
     * @return
     */
    Result<String> createSaleOrder(CreateSaleOrderVo createSaleOrderVo);


    R<QueryRoutTrackRes> queryRoute(CommonQueryRoutReq commonQueryRoutReq);

}

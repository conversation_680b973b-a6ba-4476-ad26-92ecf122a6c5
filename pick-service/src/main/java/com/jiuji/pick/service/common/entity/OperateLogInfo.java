package com.jiuji.pick.service.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_operate_log_info")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperateLogInfo extends Model<OperateLogInfo> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联业务单号
     */
    private String relateId;

    /**
     * 日志类型，1用户模块，2商品模块，3订单模块，4公共模块
     */
    private Integer type;

    /**
     * 操作人ID
     */
    private Long optUserId;

    /**
     * 操作人姓名
     */
    private String optUserName;

    /**
     * 供应商或者合作伙伴id
     */
    private Long userId;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 展示类型，0不展示，1所有用户，2供应商用户，3合作伙伴
     */
    private Integer showType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 删除标识
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

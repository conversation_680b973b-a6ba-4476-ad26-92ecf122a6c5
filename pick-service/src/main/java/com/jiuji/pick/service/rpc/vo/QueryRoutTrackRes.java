package com.jiuji.pick.service.rpc.vo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jiuji.tc.common.vo.ResultCode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 统一路由轨迹查询返回实体
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/14 17:54
 */
@Data
public class QueryRoutTrackRes extends LogisticsBase {

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 快递名称
     */
    private String expressName;

    /**
     * 快递公司拼音
     */
    private String com;

    /**
     * 错误码
     */
    private Integer errCode;


    private List<RoutDataVo> data;

    public QueryRoutTrackRes error(String name, List<RoutDataVo> dataVOList) {
        this.errCode = ResultCode.SERVER_ERROR;
        this.expressName = name;
        this.waybillNo = null;
        if (ObjectUtil.isEmpty(dataVOList)) {
            List<RoutDataVo> errorList = new ArrayList<>();
            errorList.add(new RoutDataVo().setTime(DateUtil.now()).setContent("无法查询到路由轨迹!"));
            this.data = errorList;
        } else {
            this.data = dataVOList;
        }
        return this;
    }


}

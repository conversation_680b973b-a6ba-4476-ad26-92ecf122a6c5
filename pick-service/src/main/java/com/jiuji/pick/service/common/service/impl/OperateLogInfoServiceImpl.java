package com.jiuji.pick.service.common.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.OperateLogInfo;
import com.jiuji.pick.service.common.mapper.OperateLogInfoMapper;
import com.jiuji.pick.service.common.param.ConditionalQueryLogInfoParam;
import com.jiuji.pick.service.common.service.OperateLogInfoService;
import com.jiuji.pick.service.common.vo.QueryOperateLogVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
@Service
public class OperateLogInfoServiceImpl extends ServiceImpl<OperateLogInfoMapper, OperateLogInfo> implements OperateLogInfoService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Override
    public boolean addLog(String relateId, Integer type, Long optUserId, String optUserName, Long userId, String content, Integer showType) {
        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setRelateId(relateId);
        logInfo.setType(type);
        logInfo.setUserId(userId);
        logInfo.setOptUserId(optUserId);
        logInfo.setOptUserName(optUserName);
        logInfo.setContent(content);
        logInfo.setShowType(showType);
        return baseMapper.insert(logInfo) > 0;
    }

    /**
     * 管理员操作日志列表查询
     * @param param
     * @return
     */
    @Override
    public Result<Page<QueryOperateLogVo>> queryAdmLogInfo(ConditionalQueryLogInfoParam param) {
        Page<QueryOperateLogVo> page = new Page<>(param.getCurrentPage(), param.getSize());
        Page<QueryOperateLogVo> operateLogVoPage = baseMapper.queryAdmLogInfo(page, param);
        if(operateLogVoPage == null || CollectionUtils.isEmpty(operateLogVoPage.getRecords())) {
            return Result.noData(page);
        }
        return Result.success(operateLogVoPage);
    }

    /**
     * 供应商操作日志列表查询
     * @param param
     * @return
     */
    @Override
    public Result<Page<QueryOperateLogVo>> querySupplierLogInfo(ConditionalQueryLogInfoParam param) {
        Long supplierId = currentRequestComponent.getSupplierId();
        if(supplierId == null) {
            return Result.notLoginError();
        }
        Page<QueryOperateLogVo> page = new Page<>(param.getCurrentPage(), param.getSize());
        Page<QueryOperateLogVo> operateLogVoPage = baseMapper.querySupplierLogInfo(page, param, supplierId);
        if(operateLogVoPage == null || CollectionUtils.isEmpty(operateLogVoPage.getRecords())) {
            return Result.noData(page);
        }
        return Result.success(operateLogVoPage);
    }

}

package com.jiuji.pick.service.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.component.minifile.MiniFileConfig;
import com.jiuji.pick.common.constant.RedisKey;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.utils.ImageUtil;
import com.jiuji.pick.service.product.bo.ProductInRedis;
import com.jiuji.pick.service.product.bo.ProductSearchInEsV2;
import com.jiuji.pick.service.product.entity.Brand;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.entity.CategorySearchWord;
import com.jiuji.pick.service.product.entity.EsIndexAssociation;
import com.jiuji.pick.service.product.mapper.BrandCategoryMapper;
import com.jiuji.pick.service.product.mapper.PickProductMapper;
import com.jiuji.pick.service.product.mapper.ProductInfoMapper;
import com.jiuji.pick.service.product.service.*;
import com.jiuji.pick.service.product.vo.BrandCategoryVO;
import com.jiuji.pick.service.product.vo.ProductSearchRequestVo;
import com.jiuji.pick.service.product.vo.RecommendSearchVo;
import com.jiuji.pick.service.product.vo.SearchResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author:zuofei
 * @Description
 * @Date 2020-08-18
 */
@Slf4j
@Service
public class ProductSearchServiceImpl implements ProductSearchService {

    //索引前缀
    public final static String ES_INDEX_NAME = "pick_product_search";
    public final static String ES_INDEX_ASSOCIATION = "pick_product_association";
    //索引type
    private static final String TYPE = "_doc";

    private static final int SIZE = 2000;

    @Resource
    private RestHighLevelClient restHighLevelClient;
    @Resource
    private ProductInfoMapper productInfoMapper;
    @Autowired
    private CurrentRequestComponent currentRequestComponent;
    @Autowired
    private BrandService brandService;
    @Autowired
    private ParamValueService paramValueService;
    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    private CategoryService categoryService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ProductCsService productCsService;
    @Autowired
    private BrandCategoryService brandCategoryService;
    @Autowired
    private CategorySearchWordService categorySearchWordService;
    @Resource
    private BrandCategoryMapper brandCategoryMapper;
    @Resource
    private MiniFileConfig miniFileConfig;
    @Resource
    private PickProductMapper pickProductMapper;

    /**
     * 对商品进行过滤
     *
     * @param pageContent
     * @return
     */
    private List<ProductSearchInEsV2> getPageContentNew(List<ProductSearchInEsV2> pageContent) {
        List<ProductSearchInEsV2> pageContentNew = new ArrayList<>();
        if (CollectionUtils.isEmpty(pageContent)) {
            return pageContentNew;
        }
        //商品首先根据供应商进行分组
        Map<String, List<ProductSearchInEsV2>> map = pageContent.stream().collect(Collectors.groupingBy(ProductSearchInEsV2::getSupplierName));
        for (Map.Entry<String, List<ProductSearchInEsV2>> supplierProduct : map.entrySet()) {
            //获取到供应商下的商品
            List<ProductSearchInEsV2> productList = supplierProduct.getValue();
            if (CollectionUtils.isEmpty(productList)) {
                continue;
            }
            //商品再根据productId进行分组
            Map<Long, List<ProductSearchInEsV2>> productIdMap = productList.stream().collect(Collectors.groupingBy(ProductSearchInEsV2::getProductId));
            for (Map.Entry<Long, List<ProductSearchInEsV2>> product : productIdMap.entrySet()) {
                List<ProductSearchInEsV2> value = product.getValue();
                if (CollectionUtils.isEmpty(value)) {
                    continue;
                }
                //把商品根据销量进行排序
                List<ProductSearchInEsV2> valueSort = value.stream().sorted(Comparator.comparing(ProductSearchInEsV2::getSales).reversed()).collect(Collectors.toList());
                pageContentNew.add(valueSort.get(0));
            }
        }
        return pageContentNew;
    }


    @Override
    public SearchResultVo searchProduct(ProductSearchRequestVo productSearchRequestVo) {
        String[] colls = StringUtils.split(productSearchRequestVo.getColl(), "-");
        if (colls == null || colls.length == 0) {
            // 如果没有筛选参数,给个默认的,以免后面到处都要判空
            colls = new String[]{"0", "0", "0", "0", "0", "0_0"};
        } else if (colls.length < 5) {
            String[] newColls = new String[5];
            newColls[0] = colls[0];
            newColls[1] = colls.length >= 2 ? colls[1] : "0";
            // 如果参数不满5个,补0
            Arrays.fill(newColls, 2, 5, "0");
            colls = newColls;
        }

        //分类
        int category = 0;
        List<Integer> categorys = Arrays.stream(StringUtils.split(colls[0], "_"))
                .filter(NumberUtils::isCreatable).map(Integer::valueOf).filter(e -> e != 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(categorys)) {
            category = categorys.get(0);
        }

        //品牌
        List<Integer> brands = Arrays.stream(StringUtils.split(colls[1], "_"))
                .filter(NumberUtils::isCreatable).map(Integer::valueOf).filter(e -> e != 0)
                .collect(Collectors.toList());//品牌

        // 倒数第一个是价格筛选
        String price = colls[colls.length - 1];
        String[] priceInterval = StringUtils.split(price, "_");//价格区间
        int priceStart = 0;
        int priceEnd = 0;
        // 价格区间处理
        if (priceInterval.length != 1) {
            int maxLength = String.valueOf(Integer.MAX_VALUE).length();
            if (NumberUtils.isCreatable(priceInterval[0])
                    && priceInterval[0].length() < maxLength) {
                priceStart = Double.valueOf(priceInterval[0]).intValue();
            }
            if (NumberUtils.isCreatable(priceInterval[1])
                    && priceInterval[0].length() < maxLength) {
                priceEnd = Double.valueOf(priceInterval[1]).intValue();
            }
        }

        // 排序参数处理
        String orderByStr = colls[colls.length - 2];
        int orderBy = 0;//排序顺序 0 倒序 1顺序
        if (StringUtils.isNumeric(orderByStr)) {
            orderBy = Integer.parseInt(orderByStr);
        }

        String orderTypeStr = colls[colls.length - 3];
        //排序类别 0 综合排序 1 价格 2最新上架 3 销量
        int orderType = StringUtils.isNumeric(orderTypeStr) ? Integer.parseInt(orderTypeStr) : 0;

        int areaCode = currentRequestComponent.getCurrentCityId();

        List<String> searchColls = Arrays.stream(colls)
                .skip(2)
                .limit((long) (colls.length - 5))
                .filter(e -> !StringUtils.equals("0", e))
                .collect(Collectors.toList());
        // 处理参数筛选
        List<List<String>> searchParams = searchColls.stream()
                .map(e -> Arrays.stream(StringUtils.split(e, "_")).collect(Collectors.toList()))
                .collect(Collectors.toList());

        // 基本的结果集构建,即使搜索无结果,也需要返回一个结果集
        SearchResultVo searchResultVo = getSearchDefaultResult(areaCode, category);
        //到ES中搜索
        List<ProductSearchInEsV2> pageContentOld = searchProductFromEs(
                productSearchRequestVo.getKeyword(), areaCode, true, true, categorys, brands,
                new ArrayList<>(), searchParams);
        List<ProductSearchInEsV2> pageContent = getPageContentNew(pageContentOld);
        int finalPriceStart = priceStart;
        int finalPriceEnd = priceEnd;

        pageContent = pageContent.stream().filter(it -> {
            if (finalPriceStart == 0 && finalPriceEnd == 0) {
                return true;
            }
            if (Optional.ofNullable(it.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO).compareTo(new BigDecimal(finalPriceStart)) >= 0
                    && (Optional.ofNullable(it.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO).compareTo(new BigDecimal(finalPriceEnd)) <= 0 || finalPriceEnd == 0)) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        pageContent.stream().forEach(it -> it.setName(it.getName() + " " + it.getColor()));
        //搜索排序类别 0 综合排序 1 价格 2最新上架 3 销量
        switch (orderType) {
            case WebConstant.SEARCH_ORDER_TYPE.PRICE:
                pageContent = pageContent.parallelStream()
                        .sorted(Comparator.comparing((ProductSearchInEsV2 item)-> Optional.ofNullable(item.getBuyNoTaxPrice()).orElse(item.getBuyTaxPrice())))
                        .collect(Collectors.toList());
                break;
            case WebConstant.SEARCH_ORDER_TYPE.SALES:
                pageContent = pageContent.parallelStream()
                        .sorted(Comparator.comparing(ProductSearchInEsV2::getSales))
                        .collect(Collectors.toList());
                break;
            case WebConstant.SEARCH_ORDER_TYPE.NEW_PRIORITY:
                pageContent = pageContent.parallelStream()
                        .sorted(Comparator.comparing(ProductSearchInEsV2::getProductUpTime))
                        .collect(Collectors.toList());
                break;
            default:
                pageContent = pageContent.parallelStream()
                        .sorted(Comparator.comparing((ProductSearchInEsV2 productSearchInEsV2)
                                -> Objects.isNull(productSearchInEsV2.getPickSort())?0:productSearchInEsV2.getPickSort()).reversed())
                        .sorted(Comparator.comparing((ProductSearchInEsV2 productSearchInEsV21)
                                -> Objects.isNull(productSearchInEsV21.getSales())?0:productSearchInEsV21.getSales()).reversed())
                        .collect(Collectors.toList());
        }

        // 倒序
        if (orderBy == WebConstant.SEARCH_ORDER.ASC) {
            Collections.reverse(pageContent);
        }

        //#######结果中提取筛选项开始
        //筛选项，需要的是搜索时没有筛选项的结果聚合而成
        List<ProductSearchInEsV2> notParampageContent = null;
        if (StringUtils.isNotBlank(productSearchRequestVo.getKeyword())) {
            notParampageContent = searchProductFromEs(
                    productSearchRequestVo.getKeyword(), areaCode, true, true, null, null,
                    new ArrayList<>(), null);
        } else {
            notParampageContent = pageContent;
        }
        notParampageContent = notParampageContent.parallelStream()
                .sorted(Comparator.comparing(ProductSearchInEsV2::getScore).reversed())
                .collect(Collectors.toList());
        // 搜索结果集中有的分类才显示给前端做筛选项
        Map<Integer, Integer> cateMap = new HashMap<>();
        if (category != 0) {
            cateMap.put(category, 0);
        } else {
            cateMap = notParampageContent.stream().distinct()
                    .collect(Collectors.groupingBy(ProductSearchInEsV2::getCategoryId)).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, it -> it.getValue().size()));
        }


        final List<SearchResultVo.Cate> searchCate = ((ProductSearchServiceImpl) AopContext
                .currentProxy()).getSearchCate(cateMap);
        searchResultVo.setCate(searchCate);

        //推荐分类
        final List<Integer> cids = notParampageContent.stream().map(ProductSearchInEsV2::getCategoryId).distinct()
                .limit(4).collect(Collectors.toList());
        final List<SearchResultVo.RecommendCate> recommendCate = ((ProductSearchServiceImpl) AopContext
                .currentProxy()).getSearchRecommendCate(cids);
        searchResultVo.setRecommendCate(recommendCate);

        if (category == 0 && CollectionUtils.isNotEmpty(recommendCate)) {
            category = recommendCate.get(0).getId();
        }

        if (category != 0) {
            //搜索筛选项品牌、参数......
            final int finalCategory = category;
            List<Integer> brandIds = notParampageContent.stream()
                    .filter(it -> it.getCategoryId() == finalCategory).map(it -> it.getBrandId())
                    .distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(brandIds)) {//商品没有过滤出品牌，就获取分类关联的品牌
                brandIds = brandCategoryService.getBrandIdsByCid(Collections.singletonList(category));
            }
            if (CollectionUtils.isEmpty(brandIds) && CollectionUtils.isNotEmpty(brands)) {//还没有品牌，就看看请求上有没有品牌
                brandIds = brands;
            }
            searchResultVo.setScreening(getSearchBrandScreening(brandIds, category));
        }
        //#######结果中提取筛选项结束

        //构造商品信息##############start
        SearchResultVo.Product productPageVo = new SearchResultVo.Product();
        int currentPage = productSearchRequestVo.getCurrentPage();
        productPageVo.setCurrentPage(currentPage);
        int size = pageContent.size();
        productPageVo.setTotalPage((size + 20 - 1) / 20);
        productPageVo.setTotalCount(size);
        int start = Math.min(currentPage * 20 - 20, size);
        int end = Math.min(currentPage * 20, size);
        List<ProductSearchInEsV2> pageList = pageContent.subList(start, end);
        List<SearchResultVo.Product.SearchProduct> searchProductList = getSearchProductVoList(pageList);
        productPageVo.setList(searchProductList);
        searchResultVo.setProduct(productPageVo);
        //构造商品信息##############end

        return searchResultVo;
    }

    public static void main(String[] args) {
        List<Integer> pp = new ArrayList<>();
        pp.add(null);
        System.out.println(pp.toString());
    }

    /**
     * 构造返回给前端的商品对象
     */

    public List<SearchResultVo.Product.SearchProduct> getSearchProductVoList(List<ProductSearchInEsV2> esList) {

        List<Long> ppidList = esList.stream().filter(Objects::nonNull).map(ProductSearchInEsV2::getPpid).distinct().collect(Collectors.toList());
        List<ProductInRedis> productInRedisList = productInfoService.batchGetProductInRedisList(ppidList);
        Map<Long, ProductInRedis> productInRedisMap = productInRedisList.stream().collect(Collectors.toMap(ProductInRedis::getPPriceId, v -> v));
        Map<Long, List<String>> productTags = paramValueService.getSpecParamsByMap(ppidList);

        Map<Integer, Boolean> yuyueStatus = new HashMap<>();

        List<SearchResultVo.Product.SearchProduct> productList = new ArrayList<>();

        for (ProductSearchInEsV2 v2 : esList) {
            ProductInRedis p = productInRedisMap.get(v2.getPpid());
            SearchResultVo.Product.SearchProduct productSearchVo = new SearchResultVo.Product.SearchProduct();
            productSearchVo.setCid(p.getCid());
            productSearchVo.setSuppllierDetailId(v2.getSuppllierDetailId());
            Long productid = p.getProductId();
            productSearchVo.setProductId(productid);
            Long ppriceid = p.getPPriceId();
            productSearchVo.setPpid(ppriceid);
            productSearchVo.setProductType(v2.getProductType());
            productSearchVo.setPrice(v2.getBuyNoTaxPrice());
            productSearchVo.setAdvicePrice(v2.getAdvicePrice());
            productSearchVo.setBuyNoTaxPrice(v2.getBuyNoTaxPrice());
            productSearchVo.setBuyTaxPrice(v2.getBuyTaxPrice());
            productSearchVo.setSaleCount(v2.getSales());
            productSearchVo.setProfile(p.getDecription());
            String name = RegExUtils.removePattern(p.getProductName(), WebConstant.REG_EXP.BRACKET_SURROUND);
            name = StringUtils.remove(name, "国行版");
            String color = p.getProductColor();
            if (StringUtils.isNotBlank(color) && !StringUtils.equalsIgnoreCase("null", color)) {
                if (color.contains("[") && color.contains("]")) {
                    color = color.substring(0, color.indexOf("[")) + color
                            .substring(color.indexOf("]") + 1);
                }
                name = name + " " + color;
            }
            name = RegExUtils.replaceAll(name, "\\s+", " ");
            productSearchVo.setName(name);
            productSearchVo.setProductName(p.getProductName());
            productSearchVo.setProductColor(p.getProductColor());
            productSearchVo.setIsMobile(p.getIsMobile());
            productSearchVo.setImagePath(ImageUtil.getProductImageUrl(miniFileConfig.getPathbase(), p.getBPic(), WebConstant.PICTURE_SIZE.PIC_440x440));

            productSearchVo.setTag(productTags.get(ppriceid));

            if (p.getQue() != null) {
                SearchResultVo.Product.SearchProduct.ProductStatus productStatus = getProductStatus(
                        yuyueStatus, ppriceid, p.getQue(), p.getDisplay());
                productSearchVo.setProductStatus(productStatus);
            }
            productSearchVo.setSupplierName(v2.getSupplierName());
            productSearchVo.setSupplierProvinceName(v2.getSupplierProvinceName());
            productSearchVo.setSupplierCityName(v2.getSupplierCityName());
            productSearchVo.setAfterSaleProvinceName(v2.getAfterSaleProvinceName());
            productSearchVo.setAfterSaleCityName(v2.getAfterSaleCityName());
            productSearchVo.setAfterSaleDistrictName(v2.getAfterSaleDistrictName());
            productList.add(productSearchVo);
        }
        return productList;
    }

    public SearchResultVo.Product.SearchProduct.ProductStatus getProductStatus(
            Map<Integer, Boolean> yuyueStatus, Long ppriceid, Integer que, Boolean display) {
        SearchResultVo.Product.SearchProduct.ProductStatus productStatus = new SearchResultVo.Product.SearchProduct.ProductStatus();
        if (display == null || !display) {
            productStatus.setText("下架");
            productStatus.setColor("#000000");
            productStatus.setAlpha(0.4);
            return productStatus;
        }
        if (que == WebConstant.PRODUCT_STATE.STOCKOUT) {
            productStatus.setText("缺货");
            productStatus.setColor("#000000");
            productStatus.setAlpha(0.4);
        } else if (que == WebConstant.PRODUCT_STATE.SUBSCRIBE) {
            productStatus.setAlpha(0.5);
            final Boolean y = yuyueStatus.get(ppriceid.intValue());
            if (y != null && y) {
                productStatus.setText("预售");
            } else {
                productStatus.setText("预约");
            }
            productStatus.setColor("#C80F1E");
        } else if (que == WebConstant.PRODUCT_STATE.NORMAL) {
            productStatus.setAlpha(0.5);
            final Boolean y = yuyueStatus.get(ppriceid.intValue());
            if (y != null && y) {
                productStatus.setText("订金预订");
            }
            productStatus.setColor("#C80F1E");
        }
        return productStatus;
    }


    @Override
    public List<ProductSearchInEsV2> searchProductFromEs(String keyword, int areaCode,
                                                         boolean showRepair, boolean and, List<Integer> keywordCids, List<Integer> keywordBrands,
                                                         List<String> analyzer, List<List<String>> params) {

        if (!indexExists(getEsIndex())) {
            addProductBatch(null, true);
        }
        final SearchSourceBuilder query = getQuery(keyword, areaCode, showRepair, and, keywordCids,
                keywordBrands, params);
        List<ProductSearchInEsV2> result = new ArrayList<>();
        try {
            SearchRequest request = new SearchRequest(getEsIndex());
            request.source(query);
            final SearchResponse searchResponse = restHighLevelClient
                    .search(request, RequestOptions.DEFAULT);
            for (SearchHit hit : searchResponse.getHits()) {
                ProductSearchInEsV2 p = new ProductSearchInEsV2();
                BeanUtilsBean.getInstance().populate(p, hit.getSourceAsMap());

                // 每个商品的分数构建,由于既要考虑人工干扰又要考虑关键字搜索结果准确性,做了一个折中小算法
                double score = 0.8 * (Math.log(hit.getScore()) / Math.log(2))
                        + 3 * Math.log(p.getDefaultSort()) / Math.log(2);
                // 如果手机维修,降低分数,排在后面
               /* if (StringUtils.isNotBlank(p.getCidFamily()) && p.getCidFamily().contains(",23,")) {
                    score *= 0.1;
                }*/
                p.setScore(score);
                // 高亮匹配关键字,给前端显示搜索推荐用的
                final Map<String, HighlightField> highlightFields = hit.getHighlightFields();
                if (highlightFields != null && !highlightFields.isEmpty()) {
                    highlightFields.forEach((k, v) -> analyzer.addAll(CommonUtil
                            .getChecks(v.getFragments()[0].string(), "<em>(.*?)</em>", 1)));
                }
                result.add(p);
            }
        } catch (Exception e) {
            log.error("[searchProductFromEs] 从ES查询数据异常", e);
        }
        return result;
    }


    private SearchResultVo getSearchDefaultResult(int areaCode, int category) {
        SearchResultVo searchResultApiVoV2 = new SearchResultVo();
        searchResultApiVoV2.setProduct(new SearchResultVo.Product(0, 1, 1, new ArrayList<>()));
        searchResultApiVoV2.setCate(new ArrayList<>());
        searchResultApiVoV2.setSort(getSorts());
        return searchResultApiVoV2;
    }

    private List<SearchResultVo.SearchSort> getSorts() {
        List<SearchResultVo.SearchSort> searchSorts = new ArrayList<>();
        SearchResultVo.SearchSort searchSort = new SearchResultVo.SearchSort();
        List<SearchResultVo.SearchSort.Item> items = new ArrayList<>();
        //排序类别 0 综合排序 1 价格 2最新上架 3 销量
        List<Integer> order = new ArrayList<>();
        order.add(0);
        items.add(new SearchResultVo.SearchSort.Item(0, "综合", "综合排序", order));
        items.add(new SearchResultVo.SearchSort.Item(3, "销量", "按销量排序", order));
        searchSort.setItem(items);
        searchSorts.add(searchSort);
        SearchResultVo.SearchSort searchSort1 = new SearchResultVo.SearchSort();
        List<SearchResultVo.SearchSort.Item> items1 = new ArrayList<>();
        List<Integer> order1 = new ArrayList<>();
        order1.add(1);
        order1.add(0);

        items1.add(new SearchResultVo.SearchSort.Item(1, "价格", "价格", order1));
        searchSort1.setItem(items1);
        searchSorts.add(searchSort1);
        SearchResultVo.SearchSort searchSort2 = new SearchResultVo.SearchSort();
        List<SearchResultVo.SearchSort.Item> items2 = new ArrayList<>();
        items2.add(new SearchResultVo.SearchSort.Item(2, "最新上架", "最新上架", order));
        searchSort2.setItem(items2);
        searchSorts.add(searchSort2);
        return searchSorts;
    }

    private List<SearchResultVo.Screening> getSearchBrandScreening(List<Integer> brandIds,
                                                                   int category) {

        List<SearchResultVo.Screening> productParams = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(brandIds)) {
            SearchResultVo.Screening brandScreening = new SearchResultVo.Screening();
            brandScreening.setId(1);
            brandScreening.setName("品牌");
            List<Brand> brandList = brandService.listWithCategorySort(brandIds, category);
            brandList = brandList.stream().filter(e -> StringUtils.isNotBlank(e.getUrl())).distinct().limit(20).collect(Collectors.toList());
            brandScreening.setValue(brandList.stream()
                    .map(it -> new SearchResultVo.Screening.Value(String.valueOf(it.getId()), "",
                            it.getName(), miniFileConfig.getPathbase() + it.getUrl()))
                    .collect(Collectors.toList()));
            productParams.add(brandScreening);
        }

        productParams.addAll(paramValueService.getCateSelectParamsScreening(category));

        for (int i = 0; i < productParams.size(); i++) {
            SearchResultVo.Screening screening = productParams.get(i);
            if (screening != null && CollectionUtils.isNotEmpty(screening.getValue())) {
                screening.getValue().forEach(v -> {
                    String name = RegExUtils
                            .replaceAll(v.getName(), WebConstant.REG_EXP.BRACKET_SURROUND, "");
                    v.setName(name);
                });
                screening.setName(screening.getName());
                screening.setId(i + 1);
            }
        }
        return productParams;
    }


    /**
     * 搜索
     */
    @Cached(name = "getSearchCate", expire = 10, timeUnit = TimeUnit.MINUTES)
    public List<SearchResultVo.Cate> getSearchCate(Map<Integer, Integer> cateMap) {
        List<Category> categories = null;
        if (MapUtils.isEmpty(cateMap)) {
            return new ArrayList<>();

        }
        categories = categoryService
                .list(new QueryWrapper<Category>().in(Category.ID, cateMap.keySet())
                        .eq(Category.DISPLAY, true).orderByAsc(Category.RANK));

        List<Integer> allIds = categories.stream().flatMap(it -> {
            if (StringUtils.isBlank(it.getPath())) {
                return Stream.of(it.getId());
            }
            final List<Integer> ids = CommonUtil.covertIdStr(it.getPath());
            return ids.stream();
        }).collect(Collectors.toList());
        allIds.addAll(categories.stream().map(it -> it.getId()).distinct().collect(Collectors.toList()));
        List<SearchResultVo.Cate> lowestLevel = categories.stream().map(it -> {
            final SearchResultVo.Cate c = new SearchResultVo.Cate();
            c.setId(it.getId());
            c.setPath(it.getPath());
            c.setName(it.getName());
            c.setParentId(it.getParentId());
            c.setCount(cateMap.getOrDefault(it.getId(), 0));
            return c;
        }).collect(Collectors.toList());

        List<Category> all = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allIds)) {
            all = categoryService.list(new QueryWrapper<Category>().in(Category.ID, allIds)
                    .eq(Category.DISPLAY, true)
                    .orderByAsc(Category.RANK));
        }

        final Map<Integer, List<SearchResultVo.Cate>> allMap = all.stream().map(it -> {
            final SearchResultVo.Cate c = new SearchResultVo.Cate();
            c.setId(it.getId());
            c.setName(it.getName());
            c.setParentId(it.getParentId());
            c.setPath(it.getPath());
            Integer count = cateMap.get(it.getId());
            if (count == null) {
                count = lowestLevel.stream().filter(l -> StringUtils.isNotBlank(l.getPath()) && l.getPath().contains("," + it.getId() + ",")).mapToInt(l -> l.getCount()).sum();
            }
            c.setCount(count);
            return c;
        }).collect(Collectors.groupingBy(it -> it.getParentId()));
        return getCategoryTree(allMap, 0);
    }

    @Cached(name = "getSearchRecommendCate", expire = 10, timeUnit = TimeUnit.MINUTES)
    public List<SearchResultVo.RecommendCate> getSearchRecommendCate(List<Integer> cids) {
        if (CollectionUtils.isEmpty(cids)) {
            return new ArrayList<>();
        }
        //在limit为2时，存在查询到的前两个分类都为不可显示，容灾处理为4
        final List<Category> categories = categoryService
                .list(new QueryWrapper<Category>().select(Category.ID, Category.NAME)
                        .in(Category.ID, cids).eq(Category.DISPLAY, true)
                        .eq(Category.ISSHOW, true));
        return categories.stream()
                .map(it -> new SearchResultVo.RecommendCate(it.getId(), it.getName()))
                .collect(Collectors.toList());
    }


    private List<SearchResultVo.Cate> getCategoryTree(Map<Integer, List<SearchResultVo.Cate>> group,
                                                      Integer pid) {
        final List<SearchResultVo.Cate> cates = group.get(pid);
        if (CollectionUtils.isEmpty(cates)) {
            return new ArrayList<>();
        }
        cates.forEach(it -> it.setChildren(getCategoryTree(group, it.getId())));
        return cates;
    }

    /**
     * 完成示例语句见 /reources/elasticsearch/query.json
     *
     * @param showRepair 是否搜索配件
     */
    private SearchSourceBuilder getQuery(String keyword, int areaCode, boolean showRepair,
                                         boolean and, List<Integer> categorys, List<Integer> brands, List<List<String>> params) {

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        // 最上级查询
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 过滤，第二级
        final BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
        //分类
        if (CollectionUtils.isNotEmpty(categorys)) {
            String categoryStr = categorys.stream().map(String::valueOf)
                    .collect(Collectors.joining(","));
            filterBuilder.must(QueryBuilders
                    .multiMatchQuery(categoryStr, "cidFamily.comma", "cidExtend.comma")
                    .type(MultiMatchQueryBuilder.Type.BEST_FIELDS)
                    .operator(Operator.OR));
        }

        //参数筛选  同一个字list中的参数是或运算，整个大list中是与运算
        if (CollectionUtils.isNotEmpty(params)) {
            params.forEach(e -> {
                if (CollectionUtils.isNotEmpty(e)) {
                    String paramValues = e.stream().map(String::valueOf)
                            .collect(Collectors.joining(","));
                    filterBuilder.must(QueryBuilders
                            .multiMatchQuery(paramValues, "params")
                            .type(MultiMatchQueryBuilder.Type.BEST_FIELDS)
                            .operator(Operator.OR));
                }
            });

        }

        //品牌搜索
        if (CollectionUtils.isNotEmpty(brands)) {
            filterBuilder.must(QueryBuilders.termsQuery("brandId", brands));
        }

        boolQueryBuilder.filter(filterBuilder);

        if (!and) {
            final HighlightBuilder highlightBuilder = new HighlightBuilder();
            highlightBuilder.field(new HighlightBuilder.Field("name.ik"));
            highlightBuilder.field(new HighlightBuilder.Field("searchKey.ik"));
            searchSourceBuilder.highlighter(highlightBuilder);
        }

        if (StringUtils.isBlank(keyword)) {
            boolQueryBuilder.must(QueryBuilders.matchAllQuery());
        } else {
            // 关键字查询，第二级
            final BoolQueryBuilder queryBuilder = getKeywordQueryBuilder(keyword,
                    and ? Operator.AND : Operator.OR);
            boolQueryBuilder.must(queryBuilder);
        }

//        维修配件过滤
       /* if (!showRepair) {
            boolQueryBuilder.mustNot(QueryBuilders.matchQuery("cidFamily.comma", "23"));
        }*/

        searchSourceBuilder.size(SIZE);
        searchSourceBuilder.from(0);
        searchSourceBuilder.query(boolQueryBuilder);

        return searchSourceBuilder;
    }


    private static BoolQueryBuilder getKeywordQueryBuilder(String keyword, Operator operator) {
        final BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.should(QueryBuilders
                .multiMatchQuery(keyword, "searchKey.ik")//es会默认boost1.0F
                .field("name.ik", 6.0F)
                .type(MultiMatchQueryBuilder.Type.BEST_FIELDS)
                .operator(operator));//and，分词后能找到keyword中的所有字眼；or 分词后只要能找到部分字眼就可以
        queryBuilder.should(QueryBuilders
                .wildcardQuery("name.keyword",
                        "*" + StringUtils.replace(StringUtils.left(keyword, 15), " ", "*") + "*")
                .boost(10.0F)
        );
        return queryBuilder;
    }


    @Override
    public boolean createIndex(String index, String pathResource) {
        try {
            CreateIndexRequest request = new CreateIndexRequest(index);
            ClassPathResource resource = new ClassPathResource(pathResource);
            InputStream inputStream = resource.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder mapping = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                mapping.append(line);
            }
            request.source(mapping.toString(), XContentType.JSON);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(request, RequestOptions.DEFAULT);
            return createIndexResponse.isAcknowledged();
        } catch (IOException e) {
            log.error("创建新品索引异常,index:{}", index);
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 批量插入商品到es
     *
     * @param ppidList ppidList
     * @return 执行结果
     */
    @Override
    public boolean addProductBatch(List<Long> ppidList, boolean needAllPpids) {
        if (CollectionUtils.isNotEmpty(ppidList) || needAllPpids) {
            //检查索引不存在就创建一下
            String esIndex = getEsIndex();
            if (!indexExists(esIndex)) {
                boolean res = createIndex(esIndex, "esMapping/newProductMapping.json");
                if (!res) {
                    log.error("批量插入商品到es，创建es索引失败。esIndex：{},ppidList:{},needAllPpids:{}", esIndex, JSON.toJSON(ppidList), needAllPpids);
                }
            }
            try {
                BulkRequest bulkRequest = buildProductSearchInEsBulk(ppidList, needAllPpids);
                if (bulkRequest != null) {
                    BulkResponse bulkResponse = restHighLevelClient
                            .bulk(bulkRequest, RequestOptions.DEFAULT);
                    return RestStatus.OK.getStatus() == bulkResponse.status().getStatus();
                }

            } catch (IOException e) {
                log.error("es添加商品失败,ppidList:{},needAllPpids:{}", JSON.toJSONString(ppidList), needAllPpids);
            }
        }
        return false;
    }

    /**
     * 联想词索引
     */
    @Override
    public boolean rebuildProductAssociationEs() {
        String esIndexAssociation = getEsIndexAssociation();
        if (!indexExists(esIndexAssociation)) {
            boolean res1 = createIndex(esIndexAssociation, "esMapping/productListMapping.json");
            if (!res1) {
                log.error("重建商品联想查询到es，创建es索引失败。esIndex：{}", esIndexAssociation);
            }
        } else {
            // 索引存在，清除ES数据
            cleanEsDataForIndex(esIndexAssociation);
        }
        BulkRequest bulkRequest = buildProductSearchInEsBulkAssociation();
        try {
            BulkResponse bulkResponse =  restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            return RestStatus.OK.getStatus() == bulkResponse.status().getStatus();
        } catch (IOException e) {
            log.error("重建商品联想查询ES数据发生异常，exception:", e);
        }
        return false;
    }


    @Override
    public boolean rebuildProductToEs() {
        //检查索引不存在就创建一下
        String esIndex = getEsIndex();
        if (!indexExists(esIndex)) {
            boolean res = createIndex(esIndex, "esMapping/newProductMapping.json");
            if (!res) {
                log.error("重建商品到es，创建es索引失败。esIndex：{}", esIndex);
            }

        } else {
            // 索引存在，清除ES数据
            cleanEsDataForIndex(esIndex);
        }
        try {
            BulkRequest bulkRequest = buildProductSearchInEsBulk(null, Boolean.TRUE);
            if (bulkRequest != null) {
                BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
                return RestStatus.OK.getStatus() == bulkResponse.status().getStatus();
            }
        } catch (IOException e) {
            log.error("重建ES数据发生异常，exception:", e);
        }
        return rebuildProductAssociationEs();
    }

    @Override
    public Long addSearchHistory(Long userId, String historyName) {
        String key = RedisKey.USER_SEARCH_HISTORY + userId;
        String[] historyArr = StringUtils.split(historyName, ",");
        Boolean flag = (userId != null && userId <= 0) || StringUtils.isEmpty(historyName) || userId == null;
        if (flag) {
            return 0L;
        }
        Set<ZSetOperations.TypedTuple<String>> tuples = new HashSet<>();
        for (String name : historyArr) {
            ZSetOperations.TypedTuple<String> objectTypedTuple = new DefaultTypedTuple<>(name,
                    (double) DateUtil.getTicksByTimestamp());
            tuples.add(objectTypedTuple);
        }
        stringRedisTemplate.opsForZSet().add(key, tuples);
        // 新增删除指定浏览记录后浏览记录最多只保留10个
        Long count = stringRedisTemplate.opsForZSet().zCard(key);
        if (count != null && count > 10) {
            stringRedisTemplate.opsForZSet().removeRange(key, 0, count - 11);
        }
        return 1L;
    }

    @Override
    public void deleteHistorySearch(String userId) {
        String key = RedisKey.USER_SEARCH_HISTORY + userId;
        stringRedisTemplate.delete(key);
    }

    @Override
    public void deleteHistorySearch(String userId, List<String> willDelHistory) {
        String key = RedisKey.USER_SEARCH_HISTORY + userId;
        willDelHistory.forEach(li -> stringRedisTemplate.opsForZSet().remove(key, li));
    }

    @Override
    public List<String> getHistorySearch(String userId) {
        String key = RedisKey.USER_SEARCH_HISTORY + userId;
        Set<String> set = stringRedisTemplate.opsForZSet().reverseRange(key, 0, -1);
        return new ArrayList<>(set);
    }


    @Override
    public List<String> getRecommendSearch(String searchName) {
        return brandCategoryMapper.getProductName(searchName);
    }

    @Override
    @Cached(name = "getRecommendSearch", expire = 10, timeUnit = TimeUnit.MINUTES)
    public List<RecommendSearchVo> getRecommendSearch(String searchName, int count) {
        if (StringUtils.isBlank(searchName) || count <= 0) {
            return new ArrayList<>();
        }
        // 默认走新品逻辑
        List<BrandCategoryVO> brandCategoryVOS = brandCategoryService.listAllWithName();
        final Map<String, Integer> brandNameAndIdMap = getBrandNameAndIdMap(brandCategoryVOS);
        Integer brandId = brandNameAndIdMap.get(searchName.toLowerCase().replace(" ", ""));
        if (brandId != null) {
            Integer finalBrandId = brandId;
            return brandCategoryVOS
                    .stream()
                    .filter(it -> Objects.equals(it.getBrandId(), finalBrandId))
                    .sorted(Comparator.comparing(BrandCategoryVO::getCategoryId))
                    .limit(10)
                    .map(BrandCategoryVO::getCategoryName)
                    .map(it -> {
                        final RecommendSearchVo recommendSearchVo = new RecommendSearchVo();
                        recommendSearchVo.setName(searchName + it);
                        return recommendSearchVo;
                    })
                    .collect(Collectors.toList());
        }
        // 如果分类匹配，则返回分类对应的品牌+分类名 例如 手机，返回小米手机，华为手机等
        Integer categoryId = brandCategoryVOS.stream().filter(it -> searchName.replace(" ", "").equals(it.getCategoryName()))
                .findFirst().map(BrandCategoryVO::getCategoryId).orElse(null);
        if (categoryId == null) {
            categoryId = categorySearchWordService.listAll().stream()
                    .filter(it -> searchName.equals(it.getSearchWord()))
                    .map(CategorySearchWord::getCategoryId)
                    .findFirst().orElse(null);
        }
        if (categoryId != null) {
            Integer cid = categoryId;
            return brandCategoryVOS
                    .stream()
                    .filter(it -> Objects.equals(it.getCategoryId(), cid))
                    .sorted(Comparator.comparing(BrandCategoryVO::getRank))
                    .map(BrandCategoryVO::getBrandName)
                    .map(it -> RegExUtils.removeAll(it, WebConstant.REG_EXP.BRACKET_SURROUND))
                    .map(it -> {
                        final RecommendSearchVo recommendSearchVo = new RecommendSearchVo();
                        if (StringUtils.endsWith(it, searchName)) {
                            recommendSearchVo.setName(it);
                        } else {
                            recommendSearchVo.setName(it + searchName);
                        }
                        return recommendSearchVo;
                    })
                    .collect(Collectors.toList());
        }
        List<RecommendSearchVo> recommendList = brandCategoryMapper.getRecommendSearch(StringUtils.remove(searchName, " "), null);
        if (CollectionUtils.isEmpty(recommendList)) {
            return new ArrayList<>();
        }
        //取前count条作为显示
        return new ArrayList<>(recommendList.size() > count ? recommendList.subList(0, count) : recommendList);
    }

    private static Map<String, Integer> getBrandNameAndIdMap(List<BrandCategoryVO> brandCategoryVOS) {
        final Map<String, Integer> brandNameAndIdMap = new HashMap<>();
        brandCategoryVOS.forEach(e -> {
            String chinese = RegExUtils.removeAll(e.getBrandName(), WebConstant.REG_EXP.BRACKET_SURROUND);
            String english = e.getBrandName().replaceAll("^.*[\\(|（]", "").replaceAll("[\\)|）]\\s*$", "");
            brandNameAndIdMap.put(chinese, e.getBrandId());
            brandNameAndIdMap.put(english.toLowerCase(), e.getBrandId());
        });
        return brandNameAndIdMap;
    }

    /**
     * 异构存储到es的对象
     */
    private BulkRequest buildProductSearchInEsBulk(List<Long> ppidList, boolean needAllPpids) {
        List<ProductSearchInEsV2> productSearchInEsV2List = productInfoMapper.getProductSearchInES(ppidList, needAllPpids ? 1 : 0);
        if (CollectionUtils.isEmpty(productSearchInEsV2List)) {
            return null;
        }
        Map<Long, String> productParamValueMap = null;
        if (needAllPpids) {
            ppidList = productSearchInEsV2List.stream().map(ProductSearchInEsV2::getPpid).collect(Collectors.toList());
            productParamValueMap = productCsService.getProductParamValue(ppidList);
        } else {
            productParamValueMap = productCsService.getProductParamValue(ppidList);
        }
        BulkRequest bulkRequest = new BulkRequest();
        for (ProductSearchInEsV2 e : productSearchInEsV2List) {
            e.setParams(productParamValueMap.get(e.getPpid()) == null ? "" : productParamValueMap.get(e.getPpid()));
//            e.setSales(0);//目前没有销量
            StringBuilder searchKey = new StringBuilder();
            searchKey.append(e.getPpid()).append(",").append(e.getProductId()).append(",").append(e.getSearchKey()).append(",").append(e.getColor());
            e.setSearchKey(searchKey.toString());
            IndexRequest indexRequest = new IndexRequest(getEsIndex(), TYPE,
                    e.getBindId().toString());
            indexRequest.source(JSON.toJSONString(e), XContentType.JSON);
            bulkRequest.add(indexRequest);
        }
        return bulkRequest;
    }

    /**
     * 异构存储到es的联想词
     */
    private BulkRequest buildProductSearchInEsBulkAssociation() {
        //数据库查询数据
        List<EsIndexAssociation> productSearchInAssociation = productInfoMapper.getProductSearchInAssociation();
        BulkRequest bulkRequest = new BulkRequest();
        for (EsIndexAssociation item : productSearchInAssociation) {
            String searchKey = item.getSearchKey();
            if(StringUtils.isEmpty(searchKey)){
                item.setSearchKey(item.getProductName());
            }
            IndexRequest indexRequest = new IndexRequest(getEsIndexAssociation(), TYPE, item.getPpid().toString());
            indexRequest.source(JSON.toJSONString(item), XContentType.JSON);
            bulkRequest.add(indexRequest);
        }
        return bulkRequest;
    }


    /**
     * 批量删除es上的商品 6.5以上支持deleteByQuery https://www.elastic.co/guide/en/elasticsearch/client/java-rest/6.5/java-rest-high-document-delete.html#_optional_arguments_3
     *
     * @param bindIdList
     * @return 执行结果
     */
    @Override
    public boolean deleteProductBatch(List<Long> bindIdList) {
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(getEsIndex());
        deleteByQueryRequest.setQuery(QueryBuilders.termsQuery("bindId", bindIdList));
        deleteByQueryRequest.setTimeout(TimeValue.timeValueMinutes(1));
        try {
            BulkByScrollResponse bulkByScrollResponse = restHighLevelClient
                    .deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
            return bulkByScrollResponse.getDeleted() > 0;
        } catch (IOException e) {
            log.error("es删除失败", e);
        }
        return false;
    }

    @Override
    public boolean cleanEsDataForIndex(String esIndex) {
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(esIndex);
        deleteByQueryRequest.setQuery(QueryBuilders.matchAllQuery());
        deleteByQueryRequest.setTimeout(TimeValue.timeValueMinutes(1));
        try {
            BulkByScrollResponse bulkByScrollResponse = restHighLevelClient
                    .deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
            return bulkByScrollResponse.getDeleted() > 0;
        } catch (IOException e) {
            log.error("清空es失败", e);
        }
        return false;
    }

    /**
     * 检查索引是否存在
     *
     * @return 检测结果
     */
    private boolean indexExists(String indexName) {
        GetIndexRequest request = new GetIndexRequest();
        request.indices(indexName);
        try {
            return restHighLevelClient.indices().exists(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("es索引检测失败,{} 该索引名称不存在", indexName);
            e.printStackTrace();
        }
        return false;
    }

    private boolean deleteIndex(String indexName) {
        DeleteIndexRequest request = new DeleteIndexRequest();
        request.indices(indexName);
        try {
            AcknowledgedResponse response = restHighLevelClient.indices().delete(request, RequestOptions.DEFAULT);
            return response.isAcknowledged();
        } catch (IOException e) {
            log.error("es索引删除失败，indexName:{}", indexName);
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 获取租户对应的索引名
     */
    private String getEsIndex() {
        //return Namespaces.suffix(ES_INDEX_NAME);
        return ES_INDEX_NAME;//采货王不需要动态前缀
    }

    /**
     * 获取租户对应的索引名
     */
    private String getEsIndexAssociation() {

        return ES_INDEX_ASSOCIATION;
    }

}

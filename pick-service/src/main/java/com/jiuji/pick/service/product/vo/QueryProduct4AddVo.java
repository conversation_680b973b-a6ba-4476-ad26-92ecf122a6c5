package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @function:
 * @description: QueryProduct4AddVo.java
 * @date: 2021/04/29
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class QueryProduct4AddVo {

    private Long jiuJiProductId;
    private Long cid;
    private String categoryName;
    private String brandName;
    private String specName;
    private String productName;
    private String barCode;
    private BigDecimal advicePrice;
    private String productFuture;
    private String productConfig;
    private Integer productType;
    private String productTypeStr;
    private Integer defaultArea;
    private Integer hotArea;
    private Integer happyArea;
    private Integer recommendArea;
    private String attachmentIds;
    private Integer pickProductId;
    private Boolean isMobile;
    private List<AttachmentVo> attachmentList;
    private List<IndexProductDetailVo.FileInfo> imageList;

}

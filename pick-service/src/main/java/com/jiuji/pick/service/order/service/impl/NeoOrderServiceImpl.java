package com.jiuji.pick.service.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.config.apollo.SysConfig;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.constant.OrderTipConstant;
import com.jiuji.pick.common.enums.LogisticsCompanyEnum;
import com.jiuji.pick.common.enums.OrderLogTypeEnum;
import com.jiuji.pick.common.enums.OrderStatusEnum;
import com.jiuji.pick.common.enums.OrderTypeEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.utils.HttpClientUtils;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.DeliveryFeeArea;
import com.jiuji.pick.service.common.entity.NotifyLogInfo;
import com.jiuji.pick.service.common.service.DeliveryFeeAreaService;
import com.jiuji.pick.service.common.service.NotifyLogInfoService;
import com.jiuji.pick.service.order.bo.DeliveryProductBO;
import com.jiuji.pick.service.order.dto.NeoOrderDTO;
import com.jiuji.pick.service.order.dto.NeoOrderDeliveryDTO;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.entity.ProductOrderVersion;
import com.jiuji.pick.service.order.param.CartOrderParam;
import com.jiuji.pick.service.order.param.OrderDeliveryParam;
import com.jiuji.pick.service.order.param.PurchaseKingModifyPriceDTO;
import com.jiuji.pick.service.order.service.*;
import com.jiuji.pick.service.order.vo.CartInfoVO;
import com.jiuji.pick.service.order.vo.OaSalePush;
import com.jiuji.pick.service.order.vo.UpdateOrderPriceVo;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.service.PartnerUserService;
import com.jiuji.pick.service.user.service.SupplierChannelService;
import com.jiuji.tc.common.vo.R;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @function:
 * @description: NeoOrderServiceImpl.java
 * @date: 2021/10/13
 * @author: sunfayun
 * @version: 1.0
 */
@Service(value = "NeoOrderServiceImpl")
@Slf4j
public class NeoOrderServiceImpl implements NeoOrderService {

    public static final ListeningExecutorService LISTENING_EXECUTOR_SERVICE = MoreExecutors.listeningDecorator(new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(100)));
    @Value("${lmstfy.mult.first-lmstfy-client.oaSaleQueue}")
    private String OASALE_QUEUE;

    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;
    @Resource
    private SupplierChannelService supplierChannelService;
    @Resource
    private DeliveryFeeAreaService deliveryFeeAreaService;
    @Resource
    private CartInfoService cartInfoService;
    @Resource
    private OrderInfoService orderInfoService;
    @Resource
    private OrderInfoLogService orderInfoLogService;
    @Resource
    private OrderDetailInfoService orderDetailInfoService;
    @Resource
    private ProductOrderVersionService productOrderVersionService;
    @Resource
    private NeoService neoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private NotifyLogInfoService notifyLogInfoService;
    @Resource
    private PartnerUserService partnerUserService;
    @Resource
    private SysConfig sysConfig;

    /**
     * 订单改价之前的校验
     *
     * @param updateOrderPriceVoList
     */
    private void checkOrder(UpdateOrderPriceVo updateOrderPriceVoList) {
        Long orderId = Optional.ofNullable(updateOrderPriceVoList.getOrderId()).orElseThrow(() -> new BizException("订单id不能为空"));
        OrderInfo orderInfo = Optional.ofNullable(orderInfoService.getById(orderId)).orElseThrow(() -> new BizException("订单" + orderId + "无效"));
        Integer orderType = orderInfo.getOrderType();
        Long orderNo = orderInfo.getOrderNo();
        updateOrderPriceVoList.setOrderNo(orderNo);
        if (OrderTypeEnum.SMALL.getCode() != orderType) {
            throw new BizException("订单" + orderNo + "不属于小件类型订单，暂不支持改价功能");
        }
        //判断订单类型是不是小件
        Integer orderStatus = orderInfo.getOrderStatus();
        List<Integer> status = Arrays.asList(OrderStatusEnum.DEFAULT.getCode(), OrderStatusEnum.AUDITED.getCode());
        if (!status.contains(orderStatus)) {
            throw new BizException("订单" + orderNo + "状态不支持改价功能，（取消，已发货，完成状态下不持支该改价）");
        }
        List<UpdateOrderPriceVo.UpdateOrderPriceDetailVo> detailVoList = updateOrderPriceVoList.getDetailVoList();
        if (CollectionUtils.isEmpty(detailVoList)) {
            throw new BizException("改价详情不能为空");
        }

        productOrderVersionService.checkOrderUpdatePrice(updateOrderPriceVoList);

    }

    /**
     * 小件订单改价
     *
     * @param updateOrderPriceVoList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPrice(UpdateOrderPriceVo updateOrderPriceVoList) {
        // 改价之前的校验
        checkOrder(updateOrderPriceVoList);
        StringBuilder comment = new StringBuilder();
        OrderInfo orderInfo = orderInfoService.getById(updateOrderPriceVoList.getOrderId());
        BigDecimal totalPriceOld = orderInfo.getTotalPrice();
        BigDecimal totalPriceNew = new BigDecimal("0.00");
        List<UpdateOrderPriceVo.UpdateOrderPriceDetailVo> detailVoList = updateOrderPriceVoList.getDetailVoList();
        for (UpdateOrderPriceVo.UpdateOrderPriceDetailVo item : detailVoList) {
            BigDecimal productNewPrice = item.getProductNewPrice();
            BigDecimal productOldPrice = item.getProductOldPrice();
            BigDecimal multiply = productNewPrice.multiply(new BigDecimal(item.getBuyAmount() + ""));
            totalPriceNew=totalPriceNew.add(multiply);
            //OrderDetailInfo表修改
            orderDetailInfoService.lambdaUpdate().eq(OrderDetailInfo::getId, item.getOrderDetailInfoId())
                    .set(OrderDetailInfo::getProductPrice, productNewPrice).update();

            //拼接修改详情日志
            if(productOldPrice.compareTo(productNewPrice)!=0){
                comment.append(item.getProductName()).append("价格由【").append(productOldPrice).append("】改为【").append(productNewPrice).append("】。");
            }

        }
        //OrderInfo表修改
        orderInfoService.lambdaUpdate().eq(OrderInfo::getId, orderInfo.getId())
                .eq(OrderInfo::getTotalPrice, totalPriceOld)
                .set(OrderInfo::getTotalPrice, totalPriceNew)
                .update();
        //拼接修改总价日志
        if(totalPriceOld.compareTo(totalPriceNew)!=0){
            comment.append("订单总价由【").append(totalPriceOld).append("】修改为【").append(totalPriceNew).append("】。");
        }


        //参数封装
        PurchaseKingModifyPriceDTO purchaseKingModifyPriceDTO = new PurchaseKingModifyPriceDTO();
        purchaseKingModifyPriceDTO.setPurchaseId(updateOrderPriceVoList.getOrderNo());
        ArrayList<PurchaseKingModifyPriceDTO.GoodsModifyPriceInfo> goodsModifyPriceInfos = new ArrayList<>();
        detailVoList.forEach((UpdateOrderPriceVo.UpdateOrderPriceDetailVo item)->{
            PurchaseKingModifyPriceDTO.GoodsModifyPriceInfo goodsModifyPriceInfo = new PurchaseKingModifyPriceDTO.GoodsModifyPriceInfo();
            goodsModifyPriceInfo.setPpId(item.getPpriceid().longValue());
            goodsModifyPriceInfo.setPrice(item.getProductNewPrice());
            goodsModifyPriceInfos.add(goodsModifyPriceInfo);
        });
        purchaseKingModifyPriceDTO.setGoodsModifyPriceInfoList(goodsModifyPriceInfos);

        //改价同步
        SupplierTokenInfo tokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck())
                .orElseThrow(() -> new BizException("登录信息失效，请重新登录"));
        PartnerUser partnerUser = Optional.ofNullable(partnerUserService.getById(orderInfo.getPartnerId())).orElseThrow(() -> new BizException("获取合作伙伴信息有误"));
        String host = Optional.ofNullable(partnerUser.getHost()).orElseThrow(()->new BizException("获取域名为空"));
        String url = CommonConstant.HTTPS + host + "/small-oa/api/purchaseKing/modifyPrice/v1";
        String res = "";
        try {
            res = HttpClientUtils.postJson(url, JSON.toJSONString(purchaseKingModifyPriceDTO));
        } catch (Exception e) {
            log.error("小型改价同步异常{}",e.getMessage(),e);
            throw new BizException("小型改价同步异常");
        }finally {
            //接口调用日志记录
            NotifyLogInfo notifyLogInfo = new NotifyLogInfo();
            notifyLogInfo.setNotifyParam(JSON.toJSONString(purchaseKingModifyPriceDTO))
                    .setNotifyResult(res).setCreateTime(LocalDateTime.now())
                    .setXTenant(orderInfo.getXtenantId()).setOrderNo(orderInfo.getOrderNo())
                    .setNotifyName("小型改价同步").setUpdateTime(LocalDateTime.now());
            notifyLogInfoService.saveNotifyLogInfo(notifyLogInfo);

        }
        if(StringUtils.isEmpty(res)){
            throw new BizException("小型改价同步数据为空");
        }
        R r = JSONUtil.toBean(JSONUtil.toJsonStr(res), R.class);
        int code = r.getCode();
        if(code!=0){
            throw new BizException("小型改价同步异常"+r.getUserMsg());
        }
        //操作日志记录
        orderInfoLogService.saveOrderLog(comment.toString(), OrderLogTypeEnum.OTHER, orderInfo.getId(),
                tokenInfo.getId(), tokenInfo.getLoginName());
    }




    @Override
    public String createOrder(CartInfoVO.CartInfoData cartInfoData, PartnerTokenInfo partnerTokenInfo, CartOrderParam cartOrderParam) {
        // 参数预设，区分含税未税
        List<NeoOrderDTO.PurchaseProduct> productListTax = Lists.newArrayList();
        List<NeoOrderDTO.PurchaseProduct> productListNoTax = Lists.newArrayList();
        List<OrderDetailInfo> orderDetailInfoListTax = Lists.newArrayList();
        List<OrderDetailInfo> orderDetailInfoListNoTax = Lists.newArrayList();
        List<ProductOrderVersion> productOrderVersionListTax = Lists.newArrayList();
        List<ProductOrderVersion> productOrderVersionListNoTax = Lists.newArrayList();
        List<Long> cartIdsTax = Lists.newArrayList();
        List<Long> cartIdsNoTax = Lists.newArrayList();
        BigDecimal totalPriceTax = BigDecimal.ZERO;
        BigDecimal totalPriceNoTax = BigDecimal.ZERO;
        Long supplierId = cartInfoData.getSupplierId();
        Integer productType = cartInfoData.getProductType();
        DeliveryFeeArea deliveryFeeArea = deliveryFeeAreaService.getFeeArea(cartOrderParam.getCityId());

        // 获取渠道ID
        String channelId = supplierChannelService.getNeoChannelId(partnerTokenInfo.getXtenant(), supplierId, partnerTokenInfo.getHost(), partnerTokenInfo.getToken());
        if (StringUtils.isBlank(channelId)) {
            return cartInfoData.getSupplierName() + " 渠道id获取失败!";
        }
        NeoOrderDTO neoOrderTaxDTO = createNeoOrderDTO(cartInfoData.getSupplierName(), channelId, cartOrderParam.getAddressId());
        // 构建商品&订单数据
        for (CartInfoVO.CartProduct cartProduct : cartInfoData.getProductList()) {

            // 采购价
            Integer priceType = cartProduct.getPriceType();
            BigDecimal price = Integer.valueOf(1).equals(priceType) ? Optional.ofNullable(cartProduct.getBuyTaxPrice()).orElse(BigDecimal.ZERO) : Optional.ofNullable(cartProduct.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO);

            // 订单详情
            OrderDetailInfo orderDetailInfo = orderInfoService.createSaveInfo(cartProduct, partnerTokenInfo, supplierId, price, null, OrderDetailInfo.class);
            // POV
            ProductOrderVersion productOrderVersion = orderInfoService.createSaveInfo(cartProduct, partnerTokenInfo, supplierId, null, priceType, ProductOrderVersion.class);
            // 采购商品
            NeoOrderDTO.PurchaseProduct purchaseProduct = createProductGroup(cartProduct, price);

            // 物流费
            if (cartProduct.getRemoteDeliveryFee() == 1 && deliveryFeeArea != null) {
                BigDecimal deliveryFee = deliveryFeeArea.getDeliveryFee().multiply(BigDecimal.valueOf(cartProduct.getProductCount()));
                purchaseProduct.setDeliveryFee(deliveryFeeArea.getDeliveryFee());
                orderDetailInfo.setDeliveryFee(deliveryFee);
                if (Integer.valueOf(1).equals(priceType)) {
                    totalPriceTax = totalPriceTax.add(deliveryFee);
                } else {
                    totalPriceNoTax = totalPriceNoTax.add(deliveryFee);
                }
            }

            // 这里根据NEO系统情况 进行含税和未税拆分
            if (Integer.valueOf(1).equals(priceType)) {
                cartIdsTax.add(cartProduct.getId());
                productListTax.add(purchaseProduct);
                orderDetailInfoListTax.add(orderDetailInfo);
                productOrderVersionListTax.add(productOrderVersion);
                totalPriceTax = totalPriceTax.add(BigDecimal.valueOf(cartProduct.getProductCount()).multiply(price));
            } else {
                cartIdsNoTax.add(cartProduct.getId());
                productListNoTax.add(purchaseProduct);
                orderDetailInfoListNoTax.add(orderDetailInfo);
                productOrderVersionListNoTax.add(productOrderVersion);
                totalPriceNoTax = totalPriceNoTax.add(BigDecimal.valueOf(cartProduct.getProductCount()).multiply(price));
            }
        }
        neoOrderTaxDTO.setProduct(productListTax);
        NeoOrderDTO neoOrderNoTaxDTO = new NeoOrderDTO();
        BeanUtils.copyProperties(neoOrderTaxDTO, neoOrderNoTaxDTO);
        neoOrderNoTaxDTO.setProduct(productListNoTax);
        neoOrderNoTaxDTO.setInvoiceFlag(false);

        OrderInfo orderInfoTax = createOrderInfo(partnerTokenInfo, channelId, cartOrderParam, supplierId, neoOrderTaxDTO, productType, totalPriceTax);
        OrderInfo orderInfoNoTax = new OrderInfo();
        BeanUtils.copyProperties(orderInfoTax, orderInfoNoTax);
        orderInfoNoTax.setTotalPrice(totalPriceNoTax);

        String result;
        String taxResult = StringUtils.EMPTY;
        String noTaxResult = StringUtils.EMPTY;
        try {
            ListenableFuture<String> taxFuture = LISTENING_EXECUTOR_SERVICE.submit(() -> saveAndSync(neoOrderTaxDTO, partnerTokenInfo, orderInfoTax,
                    orderDetailInfoListTax, productOrderVersionListTax, cartIdsTax, cartInfoData.getSupplierName()));
            ListenableFuture<String> noTaxFuture = LISTENING_EXECUTOR_SERVICE.submit(() -> saveAndSync(neoOrderNoTaxDTO, partnerTokenInfo, orderInfoNoTax,
                    orderDetailInfoListNoTax, productOrderVersionListNoTax, cartIdsNoTax, cartInfoData.getSupplierName()));
            taxResult = taxFuture.get();
            noTaxResult = noTaxFuture.get();
        } catch (Exception e) {
            log.error("调用NEO创建订单异常,exception{}, result1:{}, result2:{}", e, taxResult, noTaxResult);
            Thread.currentThread().interrupt();
            return cartInfoData.getSupplierName() + " 的采购单生成失败！";
        }
        result = taxResult.equals(noTaxResult) ? taxResult : taxResult + "|" + noTaxResult;
        return result;
    }

    private String saveAndSync(NeoOrderDTO neoOrderDTO, PartnerTokenInfo partnerTokenInfo, OrderInfo orderInfo, List<OrderDetailInfo> orderDetailInfoList,
                               List<ProductOrderVersion> productOrderVersions, List<Long> cartIds, String supplierName) {
        if (CollectionUtils.isEmpty(neoOrderDTO.getProduct())) {
            return StringUtils.EMPTY;
        }
        boolean saveResult = saveOrderInfo(orderInfo, orderDetailInfoList, productOrderVersions);
        if (!saveResult) {
            return OrderTipConstant.SAVE_ERROR;
        }
        orderInfoLogService.saveOrderLog("订单已生成，等待生成采购单", OrderLogTypeEnum.CREATE, orderInfo.getId(),
                partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());
        return saveNeoOrder(neoOrderDTO, partnerTokenInfo, orderInfo, supplierName, productOrderVersions, orderDetailInfoList, cartIds);
    }

    @Override
    public Result<String> cancelOrder(Long orderNo, Long xTenant) {
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        Result<Boolean> cancelResult = neoService.cancelNeoOrder(orderNo, xTenant, partnerTokenInfo.getHost(), partnerTokenInfo.getToken());
        if (ObjectUtil.isNull(cancelResult)) {
            return null;
        }
        if (!cancelResult.isSucceed() || Boolean.FALSE.equals(cancelResult.getData())) {
            return Result.error(cancelResult.getUserMsg());
        }
        return Result.success();
    }

    @Override
    public Result<String> deliveryProduct(List<DeliveryProductBO> deliveryProductBOList, OrderInfo orderInfo, OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo) {
        NeoOrderDeliveryDTO neoOrderDeliveryDTO = buildOrderDeliveryDTO(deliveryProductBOList, orderInfo, param, supplierTokenInfo);
        Result<Boolean> result = neoService.neoOrderDelivery(orderInfo, neoOrderDeliveryDTO);
        if (ObjectUtil.isNull(result)) {
            return null;
        }
        if (!result.isSucceed() || Boolean.FALSE.equals(result.getData())) {
            return Result.error(result.getUserMsg());
        }
        return Result.success();
    }

    public NeoOrderDTO createNeoOrderDTO(String supplierName, String channelId, String areaId) {
        NeoOrderDTO neoOrderDTO = new NeoOrderDTO();
        neoOrderDTO.setTitle(supplierName + "采购单");
        neoOrderDTO.setInSourceId(Long.valueOf(channelId));
        neoOrderDTO.setAreaId(Long.valueOf(areaId));
        neoOrderDTO.setInvoiceFlag(true);
        return neoOrderDTO;
    }

    public NeoOrderDTO.PurchaseProduct createProductGroup(CartInfoVO.CartProduct cartProduct, BigDecimal price) {
        NeoOrderDTO.PurchaseProduct purchaseProduct = new NeoOrderDTO.PurchaseProduct();
        purchaseProduct.setPpid(cartProduct.getPpid());
        purchaseProduct.setCounts(cartProduct.getProductCount().longValue());
        purchaseProduct.setSampleProduct(MagicalValueConstant.INT_0);
        purchaseProduct.setUnitPrice(price);
        purchaseProduct.setName(cartProduct.getProductName());
        purchaseProduct.setIsMobile(cartProduct.getIsMobile());
        return purchaseProduct;
    }

    public static OrderInfo createOrderInfo(PartnerTokenInfo partnerTokenInfo, String channelId, CartOrderParam cartOrderParam,
                                             Long supplierId, NeoOrderDTO neoOrderDTO, Integer orderType, BigDecimal totalPrice) {
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderTitle(neoOrderDTO.getTitle());
        orderInfo.setChannelId(channelId);
        orderInfo.setSupplierId(supplierId);
        orderInfo.setOrderType(orderType);
        orderInfo.setTotalPrice(totalPrice);
        orderInfo.setPartnerId(partnerTokenInfo.getId());
        orderInfo.setXtenantId(partnerTokenInfo.getXtenant());
        orderInfo.setChannelType(MagicalValueConstant.INT_1);
        orderInfo.setOrderStatus(OrderStatusEnum.DEFAULT.getCode());
        orderInfo.setContactPerson(cartOrderParam.getContactPerson());
        orderInfo.setContactPhone(cartOrderParam.getContactPhone());
        orderInfo.setReceivePoiName(cartOrderParam.getAddressName());
        orderInfo.setReceivePoiId(cartOrderParam.getAddressId());
        orderInfo.setReceivePoiCityId(cartOrderParam.getCityId());
        orderInfo.setReceiveAddress(cartOrderParam.getReceiveAddress());
        return orderInfo;
    }

    public boolean saveOrderInfo(OrderInfo orderInfo, List<OrderDetailInfo> orderDetailInfoList, List<ProductOrderVersion> productOrderVersionList) {
        // 保存信息
        boolean saveResult = true;
        boolean insert = orderInfo.insert();
        orderDetailInfoList.forEach(orderDetailInfo -> orderDetailInfo.setOrderId(orderInfo.getId()));
        productOrderVersionList.forEach(productOrderVersion -> productOrderVersion.setOrderId(orderInfo.getId()));
        if (!insert) {
            saveResult = false;
        }
        boolean save1 = orderDetailInfoService.saveBatch(orderDetailInfoList);
        if (!save1) {
            orderInfo.deleteById();
            saveResult = false;
        }
        boolean save2 = productOrderVersionService.saveBatch(productOrderVersionList);
        if (!save2) {
            orderInfo.deleteById();
            saveResult = false;
        }
        return saveResult;
    }

    private String saveNeoOrder(NeoOrderDTO neoOrderDTO, PartnerTokenInfo partnerTokenInfo, OrderInfo orderInfo, String supplierName,
                                List<ProductOrderVersion> productOrderVersionList, List<OrderDetailInfo> orderDetailInfoList, List<Long> cartIdList) {
        Result<String> neoOrderResult = neoService.createNeoOrder(neoOrderDTO, partnerTokenInfo.getXtenant(), partnerTokenInfo.getHost(), partnerTokenInfo.getToken());
        if (!neoOrderResult.isSucceed()) {
            // 保存日志
            orderInfoLogService.saveOrderLog("生成采购单失败，返回结果：" + neoOrderResult.getMsg(), OrderLogTypeEnum.GENERATE_NO, orderInfo.getId(),
                    partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());
            // 保存失败，删除相关信息
            orderInfo.deleteById();
            productOrderVersionService.removeByIds(productOrderVersionList.stream().map(ProductOrderVersion::getId).collect(Collectors.toList()));
            orderDetailInfoService.removeByIds(orderDetailInfoList.stream().map(OrderDetailInfo::getId).collect(Collectors.toList()));
            return neoOrderResult.getUserMsg();
        }
        Long orderNo = Long.valueOf(neoOrderResult.getData());
        orderInfo.setOrderNo(orderNo);
        orderInfo.setOrderStatus(OrderStatusEnum.AUDITED.getCode());
        boolean update = orderInfo.updateById();
        // 保存操作日志
        orderInfoLogService.saveOrderLog("采购单已生成，采购单号：" + orderNo, OrderLogTypeEnum.GENERATE_NO, orderInfo.getId(),
                partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());
        //todo 小型发送延迟队列
        OaSalePush oaSalePush = new OaSalePush();
        oaSalePush.setSubId(orderInfo.getId())
                .setSupplierId(orderInfo.getSupplierId());
        try {
            //计算延迟队列时间和统计推送次数
            int calculationDelaySecond = sysConfig.getDelayPushTime();
            String publish = firstLmstfyClient.publish(OASALE_QUEUE, JSONUtil.toJsonStr(oaSalePush).getBytes(), 0, (short) 1, calculationDelaySecond);
            log.warn("中型使用订单号 发送延迟队列 并且是小件队列推送成功，队列名称{}，推送参数{}，返回结果{}",OASALE_QUEUE,JSONUtil.toJsonStr(oaSalePush),publish);
        } catch (LmstfyException e){
            log.error("中型使用订单号 发送延迟队列 并且是小件队列推送异常，队列名称{}，推送参数{}",OASALE_QUEUE,JSONUtil.toJsonStr(oaSalePush),e);
        }
        // 更新
        productOrderVersionList.forEach(productOrderVersion -> productOrderVersion.setOrderNo(orderNo));
        orderDetailInfoList.forEach(orderDetailInfo -> orderDetailInfo.setOrderNo(orderNo));
        boolean updatePov = productOrderVersionService.updateBatchById(productOrderVersionList);
        boolean updateOdi = orderDetailInfoService.updateBatchById(orderDetailInfoList);
        boolean remove = cartInfoService.removeByIds(cartIdList);
        orderInfoLogService.errorLog(update, updatePov, updateOdi, remove, orderInfo, orderNo, cartIdList);

        return StringUtils.EMPTY;
    }

    private NeoOrderDeliveryDTO buildOrderDeliveryDTO(List<DeliveryProductBO> deliveryProductBOList, OrderInfo orderInfo,
                                                      OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo) {
        NeoOrderDeliveryDTO neoOrderDeliveryDTO = new NeoOrderDeliveryDTO();
        neoOrderDeliveryDTO.setId(orderInfo.getOrderNo());
        neoOrderDeliveryDTO.setDeliveryId(param.getDeliveryNo());
        //如果过快递选择其他的情况,就把备注的快递传过去
        if(LogisticsCompanyEnum.OTHER.getSpell().equals(param.getDeliveryType())){
            String expressName = Optional.ofNullable(param.getRemarks())
                    .orElse(LogisticsCompanyEnum.getChinese(param.getDeliveryType()));
            neoOrderDeliveryDTO.setDeliveryCompany(expressName);
        } else {
            neoOrderDeliveryDTO.setDeliveryCompany(LogisticsCompanyEnum.getChinese(param.getDeliveryType()));
        }
        //neoOrderDeliveryDTO.setDeliveryCompany(LogisticsCompanyEnum.getChinese(param.getDeliveryType()));
        neoOrderDeliveryDTO.setReceiveName(orderInfo.getContactPerson());
        neoOrderDeliveryDTO.setReceivePhone(orderInfo.getContactPhone());
        neoOrderDeliveryDTO.setSender(supplierTokenInfo.getLoginName());
        neoOrderDeliveryDTO.setArriveTime(LocalDateTime.now().plusDays(MagicalValueConstant.INT_7).format(DateTimeFormatter.ofPattern(DateUtil.ZH_CN_DATETIME_PATTERN)));
        List<NeoOrderDeliveryDTO.GoodsInfo> goodsList = Lists.newArrayList();
        for (DeliveryProductBO deliveryProductBO : deliveryProductBOList) {
            NeoOrderDeliveryDTO.GoodsInfo goodsInfo = new NeoOrderDeliveryDTO.GoodsInfo();
            goodsInfo.setPpId(deliveryProductBO.getPpid());
            goodsInfo.setGoodsCounts(deliveryProductBO.getCount());
            goodsList.add(goodsInfo);
        }
        neoOrderDeliveryDTO.setGoodsInfoList(goodsList);
        return neoOrderDeliveryDTO;
    }

}

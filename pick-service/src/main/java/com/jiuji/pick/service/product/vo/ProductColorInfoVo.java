package com.jiuji.pick.service.product.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/8/21 17:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ProductColorInfoVo {
    /**
     * ppid
     */
    private Long ppriceid;


    /**
     * 商品规格
     */
    private String productColor;


    /**
     * 规格id拼接的字符串
     */
    private String productColorIds;
}

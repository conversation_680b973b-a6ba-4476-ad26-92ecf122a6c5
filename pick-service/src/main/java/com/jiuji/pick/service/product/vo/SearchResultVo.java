package com.jiuji.pick.service.product.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author:zuofei
 * @Description
 * @Date 2020-08-19
 */
@Setter
@Getter
@NoArgsConstructor
public class SearchResultVo {

    /**
     * 分词
     */
    private List<String> analyzer;
    /**
     * 商品
     */
    private Product product;

    private List<Screening> screening;
    /**
     * 更多分类
     */
    private List<Cate> cate;
    /**
     * 推荐分类
     */
    private List<RecommendCate> recommendCate;
    /**
     * 排序内容
     */
    private List<SearchSort> sort;


    @AllArgsConstructor
    @Data
    public static class Product {

        /**
         * currentPage : 1 totalPage : 4 list : [{"productId":16318,"ppid":56039,"imagePath":"http://img2.ch999img.com/pic/product/160x160/20160330153709550.jpg","promotionImage":"http://img2.ch999img.com/static/images/goods/618.png","name":"iPhone
         * 6（A1586）国行版","profile":"【火热销售】高性价比之选,新32GB版本，64位A8芯片！","price":2560,"installment":"低至￥251.73x12期","consultCount":6196,"commentCount":770,"tag":["4.7英寸","正面指纹识别"],"hasAppPrice":false,"bargain":{"price":"3799.00","url":"https://m.9ji.com/bargain/?proid=25003"},"secondHand":{"price":"3799.00","url":"https://huishou.9ji.com/m/index?ppid_zz=43859&eslp=1"}}]
         */
        private Integer totalCount;
        private Integer currentPage;
        private Integer totalPage;
        private List<SearchProduct> list;

        public Product() {
        }

        public Integer getTotalPage() {
            return totalPage > 100 ? 100 : totalPage;
        }

        public Integer getTotalCount() {
            if (totalCount == null) {
                return 0;
            }
            return totalCount > 2000 ? 2000 : totalCount;
        }

        @Data
        public static class SearchProduct {

            /**
             * productId : 16318 ppid : 56039 imagePath : http://img2.ch999img.com/pic/product/160x160/20160330153709550.jpg
             * promotionImage : http://img2.ch999img.com/static/images/goods/618.png name : iPhone
             * 6（A1586）国行版 profile : 【火热销售】高性价比之选,新32GB版本，64位A8芯片！ price : 2560 installment :
             * 低至￥251.73x12期 consultCount : 6196 commentCount : 770 tag : ["4.7英寸","正面指纹识别"]
             */
            private Long suppllierDetailId;
            private Long productId;
            private Long ppid;
            private String imagePath;
            private String name;
            private String profile;
            private Integer productType;
            private BigDecimal price;
            private BigDecimal advicePrice;
            private BigDecimal buyNoTaxPrice;
            private BigDecimal buyTaxPrice;
            private Integer saleCount;
            private String productName;
            private String productColor;
            private Boolean isMobile;
            private ProductStatus productStatus;

            private List<String> tag;

            // 标签  如 减300
            private List<HintTag> hintTags;
            /**
             * 满减增加的特殊标签格式
             */
            private List<HintTag> discountTags;

            private Integer cid;

            @JsonIgnore
            private Integer categoryId;

            @JsonIgnore
            private Integer brandId;
            /**
             * 供应商名称
             */
            private String supplierName;

            /**
             * 供应商地址省
             */
            private String supplierProvinceName;

            /**
             * 供应商地址市
             */
            private String supplierCityName;

            /**
             * 售后省名称
             */
            private String afterSaleProvinceName;

            /**
             * 售后城市名称
             */
            private String afterSaleCityName;

            /**
             * 售后区名称
             */
            private String afterSaleDistrictName;

            @Data
            public static class HintTag {

                /**
                 * 类型,1:有前缀,前缀标红  2:无前缀,仅显示文本内容  3.有后缀 前缀标白，text标红 个人中心其他订单ing样式 4.纯文本 红色背景 白色文字
                 * 5.红框里有文字,后面接无框文字(exStr)
                 */
                private Integer type;
                private String prefix;
                private String suffix;
                private String text;
                private String exStr;

                private String link;

                public HintTag() {
                }

                public HintTag(Integer type, String prefix, String text) {
                    this.type = type;
                    this.prefix = prefix;
                    this.text = text;
                }

                public HintTag(Integer type, String suffix, String prefix, String text) {
                    this.type = type;
                    this.suffix = suffix;
                    this.text = text;
                    this.prefix = prefix;
                }
            }

            public SearchProduct() {
            }

            @AllArgsConstructor
            @NoArgsConstructor
            @Data
            public static class ProductStatus {

                private String text;
                private String color;
                private Double alpha;
            }

        }
    }

    @Data
    public static class Screening {

        /**
         * id : 1 name : 品牌 value : [{"id":2,"name":"苹果"},{"id":3,"name":"三星"}]
         */

        private Integer id;
        private String name;
        private List<Value> value;

        public Screening() {
        }

        @AllArgsConstructor
        @Data
        public static class Value {

            /**
             * id : 2 name : 苹果
             */

            private String id;
            private String value;
            private String name;
            private String imagePath;

            public Value() {
            }
        }
    }

    @Data
    public static class SearchSort {

        private List<Item> item;

        public SearchSort() {
        }

        @AllArgsConstructor
        @NoArgsConstructor
        @Data
        public static class Item {

            /**
             * id : 0 name : 综合 fullName : 综合排序 order : [0]
             */

            private Integer id;
            private String name;
            private String fullName;
            private List<Integer> order;
        }
    }

    @Data
    @NoArgsConstructor
    public static class Cate {

        private Integer id;
        private String path;
        private Integer parentId;
        private String name;
        private Integer count;
        private List<Cate> children;
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class RecommendCate {

        private Integer id;
        private String name;

    }
}

package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019-05-29 15:33
 */
@Data
@Accessors(chain = true)
@TableName(value = "t_product_index")
public class ProductIndex extends Model<ProductIndex> {

    @TableId(value = "ppriceid")
    private Long ppriceid;

    private Long productid;

    private int sortValue;

    @Override
    protected Serializable pkVal() {
        return this.ppriceid;
    }
}

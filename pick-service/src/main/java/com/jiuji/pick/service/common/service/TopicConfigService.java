package com.jiuji.pick.service.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.TopicConfig;
import com.jiuji.pick.service.common.vo.TopicConfigVO;

import java.util.List;

/**
 * <p>
 * 专题配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
public interface TopicConfigService extends IService<TopicConfig> {

    /**
     * 获取专题页配置列表
     * @param enabled  enabled
     * @param name name
     * @return list
     */
    Result<List<TopicConfigVO>> getList(Boolean enabled, String name);

    /**
     * 专题页配置编辑
     * @param config config
     * @return Boolean
     */
    Result<Boolean> edit(TopicConfig config);

    /**
     * 专题页配置删除
     * @param id id
     * @return Boolean
     */
    Result<Boolean> del(Integer id);

    /**
     * 专题页配置开关切换
     * @param id id
     * @return Boolean
     */
    Result<Boolean> enabled(Integer id);
}

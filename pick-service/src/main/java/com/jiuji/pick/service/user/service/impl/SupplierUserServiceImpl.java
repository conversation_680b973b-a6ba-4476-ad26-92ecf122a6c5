package com.jiuji.pick.service.user.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.secure.MD5Util;
import com.google.common.collect.Maps;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.*;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.param.FileUploadParam;
import com.jiuji.pick.service.common.param.MessageInfoSaveParam;
import com.jiuji.pick.service.common.service.AttachmentInfoService;
import com.jiuji.pick.service.common.service.MessageInfoService;
import com.jiuji.pick.service.common.service.OperateLogInfoService;
import com.jiuji.pick.service.product.service.ProductBindService;
import com.jiuji.pick.service.product.vo.SupplierChannelCountVO;
import com.jiuji.pick.service.product.vo.SupplierProductCountVO;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.entity.SupplierUserAccount;
import com.jiuji.pick.service.user.entity.SupplierUserContact;
import com.jiuji.pick.service.user.entity.SupplierUserQualification;
import com.jiuji.pick.service.user.mapper.SupplierUserMapper;
import com.jiuji.pick.service.user.query.SupplierUserCheckQuery;
import com.jiuji.pick.service.user.service.*;
import com.jiuji.pick.service.user.vo.SupplierUserDetailVO;
import com.jiuji.pick.service.user.vo.SupplierUserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.security.MessageDigest;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.jiuji.pick.common.constant.CommonConstant.WEB_GET_AREA_NAME_URL;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
@Slf4j
@Service
public class SupplierUserServiceImpl extends ServiceImpl<SupplierUserMapper, SupplierUser> implements SupplierUserService {

    @Resource
    private SupplierUserAccountService supplierUserAccountService;
    @Resource
    private SupplierUserQualificationService supplierUserQualificationService;
    @Resource
    private SupplierUserContactService supplierUserContactService;
    @Resource
    private OperateLogInfoService operateLogInfoService;
    @Resource
    private ProductBindService productBindService;
    @Resource
    private SupplierChannelService supplierChannelService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MessageInfoService messageInfoService;
    @Resource
    private AttachmentInfoService attachmentInfoService;
    @Resource
    private HttpServletRequest request;

    @Value("${supplier.default.password}")
    private String supplierDefaultPassword;
    @Value("${oa.sync.supplier.url}")
    private String oaSyncSupplierUrl;


    @Override
    public SupplierUserDetailVO getDetailInfo(Long id) {
        if(id <= 0){
            return null;
        }
        SupplierUserDetailVO detailVO = new SupplierUserDetailVO();
        SupplierUser supplierUser = baseMapper.selectById(id);
        if(Objects.isNull(supplierUser)){
            return null;
        }

        List<SupplierUserAccount> accountList = supplierUserAccountService.listBySupplierUserId(id);
        List<SupplierUserContact> contactList = supplierUserContactService.listBySupplierUserId(id);
        SupplierUserQualification qualification = supplierUserQualificationService.getBySupplierUserId(id);
        // 资质信息中 商品分类，取最后一个categoryId
        if(Objects.nonNull(qualification) && StringUtils.isNotEmpty(qualification.getCategoryId())){
            String[] args = qualification.getCategoryId().split(",");
            qualification.setCategoryId(args[args.length -1]);
        }
        List<AttachmentInfo> attachmentInfoList = attachmentInfoService.list(new LambdaQueryWrapper<AttachmentInfo>().eq(AttachmentInfo::getRelateId, supplierUser.getId()).eq(AttachmentInfo::getType, AttachTypeEnum.SUPPLIER.getCode()));

        detailVO.setSupplierUser(supplierUser);
        detailVO.setSupplierUserQualification(qualification);
        detailVO.setSupplierUserAccountList(accountList);
        detailVO.setSupplierUserContactList(contactList);
        detailVO.setAttachmentList(attachmentInfoList);
        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdateDetail(SupplierUserDetailVO detailVO) {
        if(Objects.isNull(detailVO)){
            return Boolean.FALSE;
        }
        SupplierUser supplierUser = detailVO.getSupplierUser();
        if(Objects.isNull(supplierUser)){
            return Boolean.FALSE;
        }
        // 存储基本信息
        this.saveOrUpdate(supplierUser);

        // 资质
        SupplierUserQualification qualification = detailVO.getSupplierUserQualification();
        if(Objects.nonNull(qualification)){
            if(StringUtils.isNotBlank(qualification.getRegisteredFund()) && !RegexUtils.checkPositiveRealNumber(qualification.getRegisteredFund())) {
                throw new BizException("注册资金只能是数字");
            }
            if(StringUtils.isNotBlank(qualification.getAfterSalePhone()) && !RegexUtils.checkMobile(qualification.getAfterSalePhone())) {
                throw new BizException("售后联系人手机号错误");
            }
            if(StringUtils.isNotBlank(qualification.getLeaderPhone()) && !RegexUtils.checkMobile(qualification.getLeaderPhone())) {
                throw new BizException("大小件商品负责人联系手机号错误");
            }
            if(StringUtils.isNotBlank(qualification.getFinancePhone()) && !RegexUtils.checkMobile(qualification.getFinancePhone())) {
                throw new BizException("财务负责人手机号错误");
            }
            if(StringUtils.isNotBlank(qualification.getVirtualPhone()) && !RegexUtils.checkMobile(qualification.getVirtualPhone())) {
                throw new BizException("虚拟商品负责人手机号错误");
            }
            if(StringUtils.isNotBlank(qualification.getAssetsPhone()) && !RegexUtils.checkMobile(qualification.getAssetsPhone())) {
                throw new BizException("资产商品负责人手机号错误");
            }
            if (StringUtils.isNotBlank(qualification.getAssetsPhone()) && !Objects.equals(supplierUser.getId(), CommonConstant.SHENZHEN_JIUXUN_SUPPLIER_ID)) {
                throw new BizException("当前供应商不支持填写资产商品负责人手机号");
            }
            qualification.setSupplierUserId(supplierUser.getId());
            supplierUserQualificationService.saveOrUpdate(qualification);
        }

        // 附件信息
        if(CollectionUtils.isNotEmpty(detailVO.getAttachmentList())){
            // 附件参数
            List<FileUploadParam> fileList = detailVO.getAttachmentList().stream().map(attachmentInfo -> {
                FileUploadParam fileUploadParam = new FileUploadParam();
                fileUploadParam.setFid(attachmentInfo.getFid());
                fileUploadParam.setFileName(attachmentInfo.getFileName());
                fileUploadParam.setFileUrl(attachmentInfo.getFilePath());
                return fileUploadParam;
            }).collect(Collectors.toList());
            // 数据库中的附件信息
            List<AttachmentInfo> attachmentInfoList = attachmentInfoService.list(new LambdaQueryWrapper<AttachmentInfo>().eq(AttachmentInfo::getRelateId, supplierUser.getId()).eq(AttachmentInfo::getType, AttachTypeEnum.SUPPLIER.getCode()));
            attachmentInfoService.handleAndSaveAttachment(attachmentInfoList, fileList, supplierUser.getId(), AttachTypeEnum.SUPPLIER.getCode());
        }

        // 账户信息
        List<SupplierUserAccount> accountList = detailVO.getSupplierUserAccountList();
        if(CollectionUtils.isNotEmpty(accountList)){
            List<Long> accountDeleteIdList = Lists.newArrayList();
            accountList.forEach(account -> {
                account.setSupplierUserId(supplierUser.getId());
                if(null != account.getDelFlag() && account.getDelFlag() && null != account.getId()){
                    accountDeleteIdList.add(account.getId());
                }
            });
            supplierUserAccountService.saveOrUpdateBatch(accountList);
            // 删除信息
            if(CollectionUtils.isNotEmpty(accountDeleteIdList)){
                supplierUserAccountService.removeByIds(accountDeleteIdList);
            }
        }
        // 联系人信息
        List<SupplierUserContact> contactList = detailVO.getSupplierUserContactList();
        if(CollectionUtils.isNotEmpty(contactList)){
            // 判断联系人手机号是否正确
            for (SupplierUserContact supplierUserContact : contactList) {
                if(StringUtils.isNotBlank(supplierUserContact.getPhone()) && !RegexUtils.checkMobile(supplierUserContact.getPhone())) {
                    throw new BizException("业务联系人手机号错误");
                }
            }
            List<Long> contactDeleteIdList = Lists.newArrayList();
            contactList.forEach(contact -> {
                contact.setSupplierUserId(supplierUser.getId());
                if(null != contact.getDelFlag() && contact.getDelFlag() && null != contact.getId()){
                    contactDeleteIdList.add(contact.getId());
                }
            });
            supplierUserContactService.saveOrUpdateBatch(contactList);
            // 删除信息
            if(CollectionUtils.isNotEmpty(contactDeleteIdList)){
                supplierUserContactService.removeByIds(contactDeleteIdList);
            }
        }
        return Boolean.TRUE;
    }


    @Override
    public Result<String> changePassword(Long id, String newPassword, String oldPassword, Long userId, String userName) {
        if(null == id || StringUtils.isEmpty(newPassword) || StringUtils.isEmpty(oldPassword)){
            return Result.error("参数为空");
        }

        SupplierUser user = getById(id);
        if(Objects.isNull(user)){
            return Result.error("用户不存在！");
        }

        String newPassWordMD5 = MD5Util.GetMD5Code(newPassword);
        String oldPasswordMD5 = MD5Util.GetMD5Code(oldPassword);
        if(!MessageDigest.isEqual(oldPasswordMD5.getBytes(), user.getPassword().getBytes())){
            return Result.error("旧密码不正确！");
        }

        if(MessageDigest.isEqual(newPassWordMD5.getBytes(), user.getPassword().getBytes())){
            return Result.error("新密码和旧密码不能一致！");
        }

        SupplierUser entity = new SupplierUser();
        entity.setId(id);
        entity.setPassword(newPassWordMD5);
        boolean isOK = baseMapper.updateById(entity) > 0;
        // 添加日志
        if(isOK){
            operateLogInfoService.addLog(String.valueOf(id), ModuleEnum.USER.getCode(), userId, userName, userId, "修改密码", LogShowTypeEnum.ADMIN.getCode());
        }
        return Result.success();
    }

    @Override
    public boolean pass(Long id, String remark, Long userId, String userName) {
        if(null == id){
            return Boolean.FALSE;
        }
        SupplierUser entity = new SupplierUser();
        entity.setId(id);
        entity.setRemark(remark);
        entity.setStatus(SupplierUserStatusEnum.PASS.getCode());
        boolean isOK =  baseMapper.updateById(entity) > 0;
        if(isOK){
            operateLogInfoService.addLog(String.valueOf(id), ModuleEnum.USER.getCode(), userId, userName, userId, "审核通过", LogShowTypeEnum.ADMIN.getCode());
            // 发送站内信
            MessageInfoSaveParam param = new MessageInfoSaveParam();
            param.setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode());
            param.setTitle("认证审核通过通知");
            param.setContent("采货王提醒您：您的供应商认证已审核通过，可以到平台库申请供应商商品了");
            param.setUserIdStr(String.valueOf(id));
            param.setUserType(MessagePushUserTypeEnum.SUPPLIER.getCode());
            Result result = messageInfoService.saveMessageInfo(param);
        }
        return isOK;
    }

    @Override
    public boolean reject(Long id, String remark, Long userId, String userName) {
        if(null == id){
            return Boolean.FALSE;
        }
        SupplierUser entity = new SupplierUser();
        entity.setId(id);
        entity.setRemark(remark);
        entity.setStatus(SupplierUserStatusEnum.REJECT.getCode());

        boolean isOK = baseMapper.updateById(entity) > 0;

        // 添加日志
        if(isOK){
            operateLogInfoService.addLog(String.valueOf(id), ModuleEnum.USER.getCode(), userId, userName, id, "审核拒绝", LogShowTypeEnum.ADMIN.getCode());
            // 发送站内信
            MessageInfoSaveParam param = new MessageInfoSaveParam();
            param.setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode());
            param.setTitle("认证审核未通过通知");
            param.setContent("采货王提醒您：您的供应商认证审核未通过，原因：" + remark);
            param.setUserIdStr(String.valueOf(id));
            param.setUserType(MessagePushUserTypeEnum.SUPPLIER.getCode());
            Result result = messageInfoService.saveMessageInfo(param);
        }
        return isOK;
    }

    @Override
    public boolean delete(Long id, Long userId, String userName) {
        if(null == id){
            return Boolean.FALSE;
        }
        boolean isOK = baseMapper.deleteById(id) > 0;
        // 添加日志
        if(isOK){
            operateLogInfoService.addLog(String.valueOf(id), ModuleEnum.USER.getCode(), userId, userName, id, "删除", LogShowTypeEnum.ADMIN.getCode());
        }
        return isOK;
    }

    @Override
    public IPage<SupplierUserVO> listPageCheck(SupplierUserCheckQuery checkQuery) {
        Page<SupplierUser> page = new Page<>(checkQuery.getCurrent(), checkQuery.getSize());

        LambdaQueryWrapper<SupplierUser> query = new LambdaQueryWrapper<>();
        // 更新时间倒叙
        query.orderByDesc(SupplierUser::getUpdateTime);
        // 审核列表只是待审核状态
        query.eq(SupplierUser::getStatus, SupplierUserStatusEnum.UNCHECKED.getCode());

        if(StringUtils.isNotEmpty(checkQuery.getName())){
            query.eq(SupplierUser::getName, checkQuery.getName());
        }
        if(null != checkQuery.getId()){
            query.eq(SupplierUser::getId, checkQuery.getId());
        }
        if(StringUtils.isNotEmpty(checkQuery.getCreateTimeStart())){
            query.ge(SupplierUser::getCreateTime, checkQuery.getCreateTimeStart());
        }
        if(StringUtils.isNotEmpty(checkQuery.getCreateTimeEnd())){
            query.le(SupplierUser::getCreateTime, checkQuery.getCreateTimeEnd());
        }
        IPage<SupplierUser> iPage = baseMapper.selectPage(page, query);
        List<SupplierUserVO> userVOList  = Lists.newArrayList();
        IPage<SupplierUserVO> userVOIPage = new Page<>(iPage.getCurrent(), iPage.getSize(), iPage.getTotal());

        for(SupplierUser user : iPage.getRecords()){
            SupplierUserVO userVO = BeanCopyUtil.copy(user, SupplierUserVO.class);
            userVO.setStatusDesc(SupplierUserStatusEnum.pareDesc(userVO.getStatus()));
            userVOList.add(userVO);
        }
        userVOIPage.setRecords(userVOList);
        return userVOIPage;
    }

    @Override
    public IPage<SupplierUserVO> listPageAdmin(SupplierUserCheckQuery checkQuery) {
        Page<SupplierUser> page = new Page<>(checkQuery.getCurrent(), checkQuery.getSize());

        LambdaQueryWrapper<SupplierUser> query = new LambdaQueryWrapper<>();
        // 创建时间倒叙
        query.orderByDesc(SupplierUser::getCreateTime);
        if(StringUtils.isNotEmpty(checkQuery.getName())){
            query.like(SupplierUser::getName, checkQuery.getName());
        }
        if(null != checkQuery.getStatus()){
            query.eq(SupplierUser::getStatus, checkQuery.getStatus());
        }
        if(null != checkQuery.getId()){
            query.eq(SupplierUser::getId, checkQuery.getId());
        }
        if(StringUtils.isNotEmpty(checkQuery.getCreateTimeStart())){
            query.ge(SupplierUser::getCreateTime, checkQuery.getCreateTimeStart());
        }
        if(StringUtils.isNotEmpty(checkQuery.getCreateTimeEnd())){
            query.le(SupplierUser::getCreateTime, checkQuery.getCreateTimeEnd());
        }
        IPage<SupplierUser> iPage = baseMapper.selectPage(page, query);
        List<SupplierUserVO> userVOList  = Lists.newArrayList();
        IPage<SupplierUserVO> userVOIPage = new Page<>(iPage.getCurrent(), iPage.getSize(), iPage.getTotal());
        if(CollectionUtils.isEmpty(iPage.getRecords())){
            return userVOIPage;
        }
        List<Long> supplierUserList = iPage.getRecords().stream().map(SupplierUser::getId).collect(Collectors.toList());
        // 统计商品个数
        List<SupplierProductCountVO> productCountVOList = productBindService.supplierProductCount(supplierUserList);
        Map<Long, Integer> productCountMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(productCountVOList)){
            productCountMap = productCountVOList.stream().collect(Collectors.toMap(SupplierProductCountVO::getSupplierId, y -> y.getProductNum()));
        }
        // 统计渠道个数
        List<SupplierChannelCountVO> channelCountVOList = supplierChannelService.supplierChannelCount(supplierUserList);
        Map<Long, Integer> channelCountMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(channelCountVOList)){
            channelCountMap = channelCountVOList.stream().collect(Collectors.toMap(SupplierChannelCountVO::getSupplierId, y -> y.getChannelNum()));
        }

        for(SupplierUser user : iPage.getRecords()){
            SupplierUserVO userVO = BeanCopyUtil.copy(user, SupplierUserVO.class);
            userVO.setStatusDesc(SupplierUserStatusEnum.pareDesc(userVO.getStatus()));
            // 商品个数
            userVO.setProductNum(productCountMap.get(user.getId()));
            // 渠道个数
            userVO.setChannelNum(channelCountMap.get(user.getId()));
            userVOList.add(userVO);
        }
        userVOIPage.setRecords(userVOList);
        return userVOIPage;
    }

    @Override
    public SupplierUser getByLoginName(String loginName) {
        if(StringUtils.isEmpty(loginName)){
            return null;
        }
        LambdaQueryWrapper<SupplierUser> query = new LambdaQueryWrapper<>();
        query.eq(SupplierUser::getLoginName, loginName);
        return baseMapper.selectList(query).stream().findFirst().orElse(null);
    }

    @Override
    public SupplierUser getByName(String name) {
        if(StringUtils.isEmpty(name)){
            return null;
        }
        LambdaQueryWrapper<SupplierUser> query = new LambdaQueryWrapper<>();
        query.eq(SupplierUser::getName, name);
        return baseMapper.selectList(query).stream().findFirst().orElse(null);
    }

    @Override
    public String simulateLogin() {
        SupplierUser supplierUser = this.getById(300016);
        return getSupplierUserToken(supplierUser);
    }

    @Override
    public Result<String> login(String loginName, String password) {
        if(StringUtils.isEmpty(loginName) || StringUtils.isEmpty(password)){
            return Result.error("请输入用户名密码！");
        }
        SupplierUser supplierUser = getByLoginName(loginName);
        if(Objects.isNull(supplierUser)){
            return Result.error("用户名或密码不正确！");
        }
        String md5Code = MD5Util.GetMD5Code(password);
        if(!md5Code.equals(supplierUser.getPassword())){
            return Result.error("用户名或密码不正确！");
        }
        return Result.success(getSupplierUserToken(supplierUser));
    }

    /**
     * 获取登录token
     * @param supplierUser
     * @return
     */
    private String getSupplierUserToken(SupplierUser supplierUser){
        // 生成token
        String token = MD5Util.GetMD5Code(supplierUser.getLoginName() + System.currentTimeMillis());
        SupplierTokenInfo supplierTokenInfo = new SupplierTokenInfo();
        supplierTokenInfo.setId(supplierUser.getId());
        supplierTokenInfo.setLoginName(supplierUser.getLoginName());
        supplierTokenInfo.setName(supplierUser.getName());
        supplierTokenInfo.setStatus(supplierUser.getStatus());
        supplierTokenInfo.setType(supplierUser.getType());
        Date nowDate = new Date();
        supplierTokenInfo.setLoginTime(nowDate);
        supplierTokenInfo.setExpireDate(DateUtil.addDays(nowDate, 7));
        // 存储redis 3天有效期
        String tokenStr = JSONObject.toJSONString(supplierTokenInfo);
        stringRedisTemplate.opsForValue().set(WebConstant.SUPPLIER_KEY_PREFIX_LOGIN_TOKEN + token, tokenStr, 3, TimeUnit.DAYS);
        return token;
    }



    @Override
    public Result<String> logout(String token) {
        if(StringUtils.isEmpty(token)){
            return Result.error("token参数为空");
        }
        stringRedisTemplate.delete(WebConstant.SUPPLIER_KEY_PREFIX_LOGIN_TOKEN + token);
        return Result.success();
    }

    @Override
    public Result<String> hasRepeatData(Long id, String loginName, String name) {

        SupplierUser supplierUserLogin = getByLoginName(loginName);
        if(Objects.nonNull(supplierUserLogin)){
            if(null == id){
                return Result.error("登录名已存在！");
            }
            if(null != id && !String.valueOf(id).equals(String.valueOf(supplierUserLogin.getId()))){
                return Result.error("登录名已存在！");
            }
        }
        SupplierUser supplierUserName = getByName(name);
        if(Objects.nonNull(supplierUserName)){
            if(null == id){
                return Result.error("登录名已存在！");
            }
            if(null != id && !String.valueOf(id).equals(String.valueOf(supplierUserName.getId()))){
                return Result.error("名称已存在！");
            }
        }
        return Result.success();
    }



    @Override
    public Result<String> importSupplierByIds(String idStr) {

        if(StringUtils.isEmpty(idStr)){
            return Result.error("参数为空");
        }
        StringBuffer sb = new StringBuffer();
        Integer successNum = 0;
        List<String> failList = Lists.newArrayList();
        List<Integer> idList = CommonUtil.covertIdStr(idStr);
        for(int i=0; i< idList.size(); i++){
            Integer id = idList.get(i);
            String resultStr = HttpClientUtils.get(oaSyncSupplierUrl + id);
            Result<JSONObject> result = JSONObject.parseObject(resultStr, Result.class);
            if(Objects.isNull(result) || !result.isSucceed() || Objects.isNull(result.getData())){
                log.error("获取供应商信息失败 返回结果{} 参数{}", resultStr, id);
                failList.add(String.valueOf(id));
                continue;
            }
            log.info("查询供应商信息 参数{} 返回结果{}", id,  resultStr);

            // 填充信息
            SupplierUserDetailVO detailVO = fillData(String.valueOf(id), result);
            // 验证是否有重复信息
            Result checkResult = hasRepeatData(detailVO.getSupplierUser().getId(), detailVO.getSupplierUser().getLoginName(), detailVO.getSupplierUser().getName());
            if(!checkResult.isSucceed()){
                log.error("获取供应商信息重复 返回结果{} 参数{}", checkResult.getUserMsg(), id);
                failList.add(String.valueOf(id));
                continue;
            }
            ((SupplierUserServiceImpl) AopContext.currentProxy()).saveOrUpdateDetail(detailVO);
            successNum++;
        }
        sb.append("导入成功").append(successNum).append("条！ ");
        if(failList.size() > 0){
            sb.append("失败").append(failList.size()).append("条");
            sb.append("失败id").append(failList.toString());
        }
        return Result.success(sb.toString());
    }


    /***
     * @description: 填充导入信息
     * @Param: [id, result]
     * @author: Lbj
     * @date: 2021/6/7 13:38
     */
    private SupplierUserDetailVO fillData(String id, Result<JSONObject> result){
        SupplierUserDetailVO detailVO = new SupplierUserDetailVO();
        JSONObject data = result.getData();
        // 基础信息
        SupplierUser supplierUser = new SupplierUser();
        supplierUser.setImportId(id);
        supplierUser.setName((String)data.get("company"));
        supplierUser.setShortName((String)data.get("companyJc"));
        supplierUser.setLoginName((String)data.get("company"));
        supplierUser.setPassword(MD5Util.GetMD5Code(supplierDefaultPassword));
        // 导入数据审核状态 = 通过
        supplierUser.setStatus(SupplierUserStatusEnum.PASS.getCode());
        Integer pid = (Integer) data.get("pid");
        supplierUser.setProvinceId(pid);
        if(null != pid && pid > 0){
            supplierUser.setProvinceName(getAreaName(pid));
        }
        Integer cityId = (Integer) data.get("cityid");
        supplierUser.setCityId(cityId);
        if(null != cityId && cityId > 0){
            supplierUser.setCityName(getAreaName(cityId));
        }
        Integer zid = (Integer) data.get("zid");
        supplierUser.setDistrictId(zid);
        if(null != zid && zid > 0){
            supplierUser.setDistrictName(getAreaName(zid));
        }
        supplierUser.setAddress((String)data.get("address"));
        supplierUser.setRemark("OA导入");

        // 开户i信息
        JSONArray financeListArray = data.getJSONArray("financeList");
        List<SupplierUserAccount> accountList = Lists.newArrayList();
        if (Objects.nonNull(financeListArray)) {
            for (int i = 0; i < financeListArray.size(); i++) {
                JSONObject jsonObject = financeListArray.getJSONObject(i);
                if (Objects.nonNull(jsonObject)) {
                    JSONArray accountArray = jsonObject.getJSONArray("accountsList");
                    if (Objects.nonNull(accountArray)) {
                        for (int j = 0; j < accountArray.size(); j++) {
                            SupplierUserAccount accountData = new SupplierUserAccount();
                            JSONObject accountJson = accountArray.getJSONObject(j);
                            accountData.setSupplierUserId(supplierUser.getId());
                            // 对公 对私 枚举值与OA 返回一致 ，不做额外处理
                            accountData.setType((Integer) accountJson.get("isgs"));
                            accountData.setAccountName((String) accountJson.get("username"));
                            accountData.setAccountNum((String) accountJson.get("accountnumber"));
                            accountData.setBankDeposit((String) accountJson.get("openingbank"));
                            accountData.setCityName((String) accountJson.get("city"));

                            accountList.add(accountData);
                        }
                    }
                }
            }
        }
        // 联系人信息
        JSONArray contactsArray = data.getJSONArray("contactsList");
        List<SupplierUserContact> contactList = Lists.newArrayList();
        if(Objects.nonNull(contactsArray)){
            for(int i=0; i< contactsArray.size(); i++){
                SupplierUserContact contactData = new SupplierUserContact();
                JSONObject contactJson = contactsArray.getJSONObject(i);
                contactData.setSupplierUserId(supplierUser.getId());
                contactData.setType("0");
                contactData.setName((String) contactJson.get("username"));
                contactData.setPhone((String) contactJson.get("tel"));

                contactList.add(contactData);
            }
        }
        detailVO.setSupplierUser(supplierUser);
        // 资质i信息
        detailVO.setSupplierUserQualification(fillSupplierQualification(data));
        detailVO.setSupplierUserContactList(contactList);
        detailVO.setSupplierUserAccountList(accountList);
        // 附件信息
        detailVO.setAttachmentList(fillAttachmentInfo(data));
        return detailVO;
    }

    /***
     * @description: 填充资质信息
     * @Param: [data]
     * @author: Lbj
     * @date: 2021/6/7 14:39
     */
    private SupplierUserQualification fillSupplierQualification(JSONObject data){
        SupplierUserQualification qualification = new SupplierUserQualification();
        qualification.setRegisteredFund(data.get("registeredcapital") + "");
        qualification.setNature((Integer)data.get("companynature") + "");
        qualification.setAfterSaleAddress((String)data.get("afterAddress"));
        qualification.setAfterSaleName((String)data.get("shouhoucontacts"));
        qualification.setAfterSalePhone((String)data.get("shouhoumobile"));
        Integer pid = (Integer) data.get("afterProvinceid");
        qualification.setAfterSaleProvinceId(pid);
        if(null != pid && pid > 0){
            qualification.setAfterSaleProvinceName(getAreaName(pid));
        }
        Integer cityId = (Integer) data.get("afterCityid");
        qualification.setAfterSaleCityId(cityId);
        if(null != cityId && cityId > 0){
            qualification.setAfterSaleCityName(getAreaName(cityId));
        }
        Integer zid = (Integer) data.get("afterCountyid");
        qualification.setAfterSaleDistrictId(zid);
        if(null != zid && zid> 0){
            qualification.setAfterSaleDistrictName(getAreaName(zid));
        }
        qualification.setLegalPerson((String)data.get("legalrepresent"));
        qualification.setCategoryId((String)data.get("classification"));
        qualification.setFinanceName((String)data.get("cwFzr"));
        qualification.setFinancePhone((String)data.get("cwLxfs"));
        qualification.setMajorBusinesses((String)data.get("comment1"));
        qualification.setLeaderPhone((String)data.get("mobile"));
        return qualification;
    }

    /***
     * @description: 填充附件信息
     * @Param: [data]
     * @date: 2021/6/7 14:37
     */
    private List<AttachmentInfo> fillAttachmentInfo(JSONObject data){
        List<AttachmentInfo> attachmentInfoList = Lists.newArrayList();
        JSONArray contractJsonArray = data.getJSONArray("attachments");
        if(Objects.nonNull(contractJsonArray)){
            for (int i = 0; i < contractJsonArray.size(); i++) {
                JSONObject contractObject = contractJsonArray.getJSONObject(i);
                if (Objects.nonNull(contractObject)) {
                    AttachmentInfo attachmentInfo = new AttachmentInfo();
                    attachmentInfo.setFid(contractObject.getString("fid"));
                    attachmentInfo.setFileName(contractObject.getString("fileName"));
                    attachmentInfo.setFilePath(contractObject.getString("filePath"));
                    attachmentInfoList.add(attachmentInfo);
                }
            }
        }
        return attachmentInfoList;
    }

    /***
     * @description: 获取区域名称
     * @Param: [code]
     * @author: Lbj
     * @date: 2021/6/7 14:19
     */
    private String getAreaName(Integer code){
        if(null == code){
            return null;
        }
        String resultStr = HttpClientUtils.get(WEB_GET_AREA_NAME_URL + code);
        Result<JSONObject> result = JSONObject.parseObject(resultStr, Result.class);
        if(Objects.isNull(result) || !result.isSucceed() || Objects.isNull(result.getData())){
            log.error("获取主站省市区去信息失败 返回结果{} 参数{}", resultStr, code);
        }
        log.info("查询供应商信息 参数{} 返回结果{}", code,  resultStr);
        return String.valueOf(result.getData());
    }
}

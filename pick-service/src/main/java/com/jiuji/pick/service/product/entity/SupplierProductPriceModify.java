package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @Date 2021/8/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_supplier_product_price_modify")
public class SupplierProductPriceModify extends Model<SupplierProductPriceModify> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 供应商商品详情记录id
     */
    private Long supplierProductDetailId;

    /**
    * 上次采购未税单价
    */
    private BigDecimal lastNoTaxPrice;

    /**
     * 上次采购含税单价
     */
    private BigDecimal lastTaxPrice;

    /**
     * 当前采购未税单价
     */
    private BigDecimal currentNoTaxPrice;

    /**
     * 当前采购含税单价
     */
    private BigDecimal currentTaxPrice;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;

    /**
    * 是否生效
    */
    private Boolean enabled;
}

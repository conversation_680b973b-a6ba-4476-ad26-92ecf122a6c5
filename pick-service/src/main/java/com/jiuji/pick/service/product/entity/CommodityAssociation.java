package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("t_commodity_association")
public class CommodityAssociation {


    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "productId")
    private Integer productId;

    /**
     * 默认显示ppid
     */
    @TableField(value = "ppid")
    private Integer ppid;

    /**
     * 排序值
     */
    @TableField(value = "show_sort")
    private Integer showSort;

    /**
     * 关联标识
     */
    @TableField(value = "association_id")
    private Integer associationId;

    /**
     * 商品名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 版本名称
     */
    @TableField(value = "edition")
    private String edition;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean delFlag;


}

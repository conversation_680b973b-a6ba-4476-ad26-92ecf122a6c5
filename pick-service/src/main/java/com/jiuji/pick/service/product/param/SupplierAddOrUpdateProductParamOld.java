package com.jiuji.pick.service.product.param;

import lombok.Data;


import java.math.BigDecimal;

/**
 * @function:
 * @description: SupplierAddOrUpdateProductParam.java
 * @date: 2021/05/06
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class SupplierAddOrUpdateProductParamOld {

    /**
     * 商品ID
     */

    private Long productId;

    /**
     * 商品ppid
     */

    private Long ppid;

    /**
     * 账期
     */
    private Integer paymentDay;

    /**
     * 物料
     */
    private String material;

    /**
     * 箱规
     */
    private String boxRule;

    /**
     * 质保日期
     */
    private Integer qualityDate;

    /**
     * 采购未税单价
     */

    private BigDecimal buyNoTaxPrice;

    /**
     * 采购含税单价
     */

    private BigDecimal buyTaxPrice;

    /**
     * 实力保障,发货时间
     */
    private Integer deliveryDay;

    /**
     * 实力保障,无理由退货
     */
    private Integer noReasonReturn;

    /**
     * 实力保障,换货时间
     */
    private Integer changeDay;

    /**
     * 实力保障,破损包赔
     */
    private Integer badPay;

    /**
     * 实力保障,少货包赔
     */
    private Integer lackPay;

    /**
     * 售后政策
     */

    private String afterSalePolicy;

    /**
     * 其他政策
     */

    private String otherPolicy;

    /**
     * 偏远地区收费
     */
    private Integer remoteDeliveryFee;

}

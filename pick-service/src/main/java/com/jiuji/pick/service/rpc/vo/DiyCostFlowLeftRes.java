package com.jiuji.pick.service.rpc.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DiyCostFlowLeftRes implements Serializable {

    private String userid;

    private Long xtenant;

    /**
     * 商品id
     */
    private String saveMoney;

    private String erdu;


}

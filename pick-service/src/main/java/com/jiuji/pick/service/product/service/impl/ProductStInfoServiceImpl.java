package com.jiuji.pick.service.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.service.product.bo.ProductStDBO;
import com.jiuji.pick.service.product.entity.ProductPrice;
import com.jiuji.pick.service.product.entity.ProductStInfo;
import com.jiuji.pick.service.product.mapper.ProductStInfoMapper;
import com.jiuji.pick.service.product.service.ProductPriceService;
import com.jiuji.pick.service.product.service.ProductStInfoService;
import com.jiuji.pick.service.product.vo.ProductSpecUpdateVO;
import org.apache.commons.lang3.ObjectUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Service
public class ProductStInfoServiceImpl extends ServiceImpl<ProductStInfoMapper, ProductStInfo> implements ProductStInfoService {

    @Autowired
    private ProductPriceService productPriceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProductSpec(ProductSpecUpdateVO param) {
        // 查询商品是否存在，不存在的数据插入是无意义的
        //ProductInRedis byPpidFromDb = productInfoService.getProductInRedisFromDB(param.getPpid());
        ProductPrice productPrice = productPriceService.getById(param.getPpid());

        if (!ObjectUtils.anyNotNull(productPrice)) {
            return false;
        }
        // 查询改ppid已经设置的参数，新有老无的新增，老有新无的删除
        if (param.getSpecIds() == null) {
            param.setSpecIds(new ArrayList<>());
        }
        List<ProductStInfo> list = this.list(new QueryWrapper<ProductStInfo>()
                .eq(ProductStInfo.PPRICEID, param.getPpid()));
        List<Integer> oldSpecIds = list.stream().map(ProductStInfo::getStandardDetailId)
                .collect(Collectors.toList());
        // 老有新无的删除
        List<ProductStInfo> removeList = list.stream()
                .filter(li -> !param.getSpecIds().contains(li.getStandardDetailId()))
                .collect(Collectors.toList());
        removeList.forEach(this::removeById);
        List<ProductStInfo> addList = param.getSpecIds().stream()
                .filter(li -> !oldSpecIds.contains(li))
                .map(li -> {
                    ProductStInfo info = new ProductStInfo();
                    info.setPpriceid(param.getPpid());
                    info.setStandardDetailId(li);
                    return info;
                }).collect(Collectors.toList());
        try {
            addList.forEach(this::save);
        }catch (Exception e){
            log.error("保存失败，数据已存在",e);
        }
        return true;
    }

    @Override
    public List<ProductStDBO> getProductAllSpecByPid(Long productId) {
        return baseMapper.getProductAllSpecByPid(productId);
    }
}

package com.jiuji.pick.service.diy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DiyCostKindEnum {

    /***
     *
     */
    ONE("1", "DIY保护壳"),
    TWO("2", "严选商城"),
    THREE("3", "充值"),
    PURCHASE("4", "购买"),
    REFUND("5", "退款"),
    ;

    private String code;
    private String desc;


    public static String getDescByCode(String code) {
        for (DiyCostKindEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}

package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_supplier_product_detail")
public class SupplierProductDetail extends Model<SupplierProductDetail> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品ppid
     */
    private Long ppid;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 账期
     */
    @TableField(updateStrategy=FieldStrategy.IGNORED)
    private Integer paymentDay;

    /**
     * 物料
     */
    @TableField(updateStrategy=FieldStrategy.IGNORED)
    private String material;

    /**
     * 箱规
     */
    @TableField(updateStrategy=FieldStrategy.IGNORED)
    private String boxRule;

    /**
     * 质保日期
     */
    private Integer qualityDate;

    /**
     * 采购未税单价
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal buyNoTaxPrice;

    /**
     * 采购含税单价
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal buyTaxPrice;

    /**
     * 实力保障,发货时间
     */
    @TableField(updateStrategy=FieldStrategy.IGNORED)
    private Integer deliveryDay;

    /**
     * 实力保障,无理由退货
     */
    private Integer noReasonReturn;

    /**
     * 实力保障,换货时间
     */
    private Integer changeDay;

    /**
     * 实力保障,破损包赔
     */
    private Integer badPay;

    /**
     * 实力保障,少货包赔
     */
    private Integer lackPay;

    /**
     * 九机库存
     */
    private Integer stockCount;

    /**
     * 售后政策
     */
    private String afterSalePolicy;

    /**
     * 其他政策
     */
    private String otherPolicy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;

    /**
     * 是否收取便宜地区物流费
     */
    @TableField(value = "is_remote_delivery_fee")
    private Integer remoteDeliveryFee;

    /**
     * 起订量
     */
    @TableField(value = "minimum_order_quantity",fill = FieldFill.UPDATE)
    private Integer minimumOrderQuantity;

    /**
     * 排序
     */
    private Integer sort;



    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

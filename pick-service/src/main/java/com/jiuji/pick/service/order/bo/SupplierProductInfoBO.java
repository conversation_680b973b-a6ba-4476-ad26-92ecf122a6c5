package com.jiuji.pick.service.order.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品信息BO（购物车用）
 *
 * <AUTHOR>
 * @since 2021-05-08
 */
@Data
public class SupplierProductInfoBO {

    /**
     * 商品详情id
     */
    private Long productDetailId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * ppid
     */
    private Long ppid;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 未税价格
     */
    private BigDecimal buyNoTaxPrice;

    /**
     * 含税价格
     */
    private BigDecimal buyTaxPrice;

    /**
     * 商品状态
     */
    private Integer productStatus;

    /**
     * 供应商商品绑定状态
     */
    private Integer bindStatus;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String productColor;

    /**
     * 商品图片
     */
    private String bPic;

    /**
     * 商品分类
     */
    private Long productCid;

    /**
     * 商品分类名称
     */
    private String productCidName;

    /**
     * 商品条码
     */
    private String productBarCode;

    /**
     * 是否大小件
     */
    private Boolean isMobile;

    /**
     * 黑名单
     */
    private String blackUsers;

    /**
     * 供应商状态
     */
    private Integer supplierStatus;

    /**
     * 是否收取偏远地区物流费
     */
    private Integer remoteDeliveryFee;

    /**
     * 起订量
     */
    private Integer minimumOrderQuantity;

    /**
     * 箱规
     */
    private String boxRule;

}

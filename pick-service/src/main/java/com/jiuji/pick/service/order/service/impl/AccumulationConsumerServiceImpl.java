package com.jiuji.pick.service.order.service.impl;

import cn.hutool.json.JSONUtil;
import com.jiuji.infra.lmstfy.anotation.LmstfyConsume;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.service.SaleOrderService;
import com.jiuji.pick.service.order.vo.CreateSaleOrderVo;
import com.jiuji.pick.service.order.vo.OaSalePush;
import com.meitu.platform.lmstfy.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 对接延迟队列消费
 * <AUTHOR>
 */
@Slf4j
@Service
public class AccumulationConsumerServiceImpl {

    @Resource
    private SaleOrderService saleOrderService;

    private static final String OASALE_QUEUE = "${lmstfy.mult.first-lmstfy-client.oaSaleQueue}";

    @LmstfyConsume(queues = OASALE_QUEUE, clientBeanName = "firstLmstfyClient")
    public void consumeSmallPiecesAccumulationQueue(Job job) {
        log.warn("OASALE_QUEUE消费到数据：{}", JSONUtil.toJsonStr(job));
        String data = Optional.ofNullable(job).orElse(new Job()).getData();
        if(StringUtils.isEmpty(data)){
            log.warn("OASALE_QUEUE消费到数据为空");
            return ;
        }
        OaSalePush oaSalePush;
        try {
            oaSalePush = JSONUtil.toBean(data, OaSalePush.class);
        } catch (Exception e){
            log.error("消费售后预约单延迟队列反序列化失败，反序列数据为{}",data,e);
            return ;
        }
        log.warn("OASALE_QUEUE消费到数据详情：{}", JSONUtil.toJsonStr(oaSalePush));
        CreateSaleOrderVo vo = new CreateSaleOrderVo();
        Optional.ofNullable(oaSalePush).ifPresent(item->{
            vo.setOrderIds(Optional.ofNullable(item.getSubId()).orElse(Long.MAX_VALUE).toString())
                    .setSupplierId(item.getSupplierId());
            Result<String> saleOrder = saleOrderService.createSaleOrder(vo);
            log.warn("OASALE_QUEUE消费到数据详情获取结果：{}", JSONUtil.toJsonStr(saleOrder));
        });
    }
}

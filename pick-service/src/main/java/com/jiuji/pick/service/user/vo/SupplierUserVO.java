package com.jiuji.pick.service.user.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SupplierUserVO {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 名称
     */
    private String shortName;

    /**
     * 登录名称
     */
    private String loginName;

    /**
     * 负责人联系电话
     */
    private String leaderPhone;

    /**
     * 密码
     */
    private String password;

    /**
     * 供应商状态，
     */
    private Integer status;

    /**
     * 供应商状态，
     */
    private String statusDesc;

    /**
     * 类型 大型、小型、中型
     */
    private String type;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区id
     */
    private Integer districtId;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 地址
     */
    private String address;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    private Boolean delFlag;

    /**
     * 商品数量
     */
    private Integer productNum;
    /**
     * 渠道数量
     */
    private Integer channelNum;
}

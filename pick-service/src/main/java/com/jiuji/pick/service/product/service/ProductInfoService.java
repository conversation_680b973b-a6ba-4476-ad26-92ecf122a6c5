package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.enums.ProductTypeEnum;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.bo.ProductInRedis;
import com.jiuji.pick.service.product.entity.ProductInfo;
import com.jiuji.pick.service.product.vo.ProductSpecVo;
import com.jiuji.pick.service.product.vo.QueryProduct4AddVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 产品信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductInfoService extends IService<ProductInfo> {

    /**
     * 添加商品查询商品基础信息
     * @param ppid
     * @return
     */
    Result<QueryProduct4AddVo> queryProductInfo4Add(Long ppid);

    /**
     * 获取商品规格
     * @param ppid
     * @return
     */
    Result<List<ProductSpecVo>> getProductSpecInfo(Long ppid);

    /**
     * 保存或修改商品信息
     */
    boolean saveOrUpdateProductInfo(Long productId);

    /**
     * 批量获取productInRedis 返回list
     */
    List<ProductInRedis> batchGetProductInRedisList(List<Long> ppids);


    /**
     * 通过ppid获取productInRedis
     */
    ProductInRedis getProductInRedisByPpid(Long ppid);


    /**
     * 通过ppid到数据库获取商品详细缓存对象
     */
    ProductInRedis getProductInRedisFromDB(Long ppid);

    /**
     * pid下的ppid
     */
    List<Long> getPpidsByPid(Long pid);

    /**
     * 获取商品类型
     * @param cid
     * @param isMobile
     * @return
     */
    ProductTypeEnum getProductType(Long cid, Boolean isMobile);

    /**
     * 包含已删除记录 不要随意改动
     * @param pid
     * @return
     */
    List<ProductInfo> getAllProductInfoByPid(@Param("pid") Long pid);
}

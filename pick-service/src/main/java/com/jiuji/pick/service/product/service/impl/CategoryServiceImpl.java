package com.jiuji.pick.service.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.common.component.minifile.MiniFileConfig;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.entity.CategorySearchWord;
import com.jiuji.pick.service.product.entity.Product;
import com.jiuji.pick.service.product.mapper.CategoryMapper;
import com.jiuji.pick.service.product.service.CategoryService;
import com.jiuji.pick.service.product.service.ProductService;
import com.jiuji.pick.service.product.vo.CategoryTreeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Slf4j
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {

    @Autowired
    private ProductService productService;
    @Resource
    private MiniFileConfig miniFileConfig;

    /**
     * 查找分类通过id param id
     */
    @Override
    public Category getOneById(Integer id) {
        Category category = this.getById(id);
        category.setPic(
                StringUtils.isNotBlank(category.getPic()) ? getCorrectPictureUrl(category.getPic(),
                        "pic/category/", "") : category.getPic());
        return category;
    }
    @Override
    public List<CategoryTreeVo> getCategoryTree(Integer categoryType) {
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Category::getDisplay, 1);
        wrapper.eq(Category::getIsShow, 1);
        if(categoryType != null && categoryType == 1) {
            // 查询大件分类
            wrapper.eq(Category::getIsMobile, 0);
        }
        List<Category> categoryList = list(wrapper);
        List<CategoryTreeVo> categoryTreeList = getCategoryChildTree(categoryList, 0);
        return categoryTreeList;
    }

    @Override
    public List<Category> getChildCategory(Integer cid) {
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Category::getDisplay, 1);
        wrapper.eq(Category::getIsShow, 1);
        List<Category> categoryList = list(wrapper);
        return this.getChildCategory(categoryList, cid);
    }

    /**
     * 生成分类关键列表
     */
    @Override
    public Boolean modifyCategorySearchKeyword(Integer cid, String keywordStr) {
        if (StringUtils.isBlank(keywordStr)) {
            return true;
        }
        try {
            String[] keywordArray = keywordStr.replace(" ", "").replace("，", ",").split(",");
            baseMapper.deleteCategorySearchKeyword(cid);
            List<CategorySearchWord> searchWordList = new ArrayList<>();
            if (keywordArray.length > 0) {
                for (String key : keywordArray) {
                    if (key == null || "".equals(key)) {
                        continue;
                    }
                    CategorySearchWord searchWord = new CategorySearchWord();
                    searchWord.setCategoryId(cid);
                    searchWord.setSearchWord(key);
                    searchWordList.add(searchWord);
                }
            }
            return baseMapper.insertCategorySearchWord(searchWordList);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 更新或者新增分类
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> checkAndSave(Category category, boolean isSynchronize) throws Exception {
        //传入值检查
        //格式化校验serviceId
        StringBuilder serviceIds = new StringBuilder();
        String[] arrayId = new String[]{};
        if (category.getServiceIds() != null) {
            arrayId = category.getServiceIds().replace("，", ",").split(",");
            for (String str : arrayId) {
                if (!"".equals(str)) {
                    serviceIds.append(str).append(",");
                }
            }
            if (serviceIds.length() > 0) {
                serviceIds.deleteCharAt(serviceIds.length() - 1);
            }
        }
        category.setServiceIds(serviceIds.toString());

        String formatPrice = category.getPrices() != null ? category.getPrices().replace(" ", "")
                .replace("，", ",") : "";
        category.setPrices(formatPrice);
        String formatNotSupportDelivery =
                category.getNotSupportDelivery() != null ? category.getNotSupportDelivery()
                        .replace(" ", "").replace("，", ",") : "";
        category.setNotSupportDelivery(formatNotSupportDelivery);
        if (category.getName().length() < 2) {
            return Result.error("error", "分类名太短", false);
        }

        if(!isSynchronize){//不是同步的情况，自己创建的情况下校验
            //判断上级分类是否为最下层子级分类
            List<Product> products = productService
                    .list(new LambdaQueryWrapper<Product>().eq(Product::getCid, category.getParentId())
                            .eq(Product::getDisplay, true));
            if (CollectionUtils.isNotEmpty(products)) {
                return Result.error("error", "上级分类下存在商品，请重新选择分类", false);
            }
        }

        if (isSynchronize) {
            // 说明没有该分类
            Integer id = category.getId();
            if (id == null || id <= 0) {
                category.setId(0);
            }
            if (category.getParentId() == null) {
                category.setParentId(0);
            }
            if (category.getChild() == null) {
                category.setChild(0);
            }
        }

        if (saveOrUpdateCategory(category)) {
            //生成分类关键字
            Boolean result = modifyCategorySearchKeyword(category.getId(),
                    category.getSearchKeywords());
            if (result != null && result) {
                return Result.success("success", "成功", true);
            }
            return Result.error("error", "搜索关键字导致报错失败", false);
        } else {
            return Result.error("error", "保存分类时发生错误", false);
        }
    }

    /**
     * 递归获取分类子树
     * @param allCategoryList
     * @param parentId
     * @return
     */
    private List<CategoryTreeVo> getCategoryChildTree(List<Category> allCategoryList, long parentId) {
        List<CategoryTreeVo> categoryTreeList = new ArrayList<>();
        List<Category> childCategoryList = getChildCategory(allCategoryList, parentId);
        if (CollectionUtils.isNotEmpty(childCategoryList)) {
            for (Category category : childCategoryList) {
                CategoryTreeVo categoryTree = new CategoryTreeVo();
                BeanUtils.copyProperties(category, categoryTree);
                categoryTree.setChildTree(getCategoryChildTree(allCategoryList, category.getId()));
                categoryTreeList.add(categoryTree);
            }
        }
        return categoryTreeList;
    }

    /**
     * 过滤出父分类中的子分类
     * @param allCategoryList
     * @param parentId
     * @return
     */
    private List<Category> getChildCategory(List<Category> allCategoryList, long parentId) {
        if(CollectionUtils.isEmpty(allCategoryList)) {
            return Collections.emptyList();
        }
        List<Category> categoryList = allCategoryList.stream().filter(e -> e.getParentId() == parentId).collect(Collectors.toList());
        allCategoryList.removeAll(categoryList);
        return categoryList;
    }

    public boolean saveOrUpdateCategory(Category category) throws Exception {
        //根据父节点，设置路径以及等级
        if (category.getParentId() != null && category.getParentId() > 0) {
            Category parent = getOneById(category.getParentId());
            if (parent == null) {
                return false;
            } else {
                category.setParentId(parent.getId());
                category.setLevel(parent.getLevel() + 1);
                category.setPath((parent.getPath() == null || parent.getPath().length() == 0 ? "," :
                        parent.getPath()) + parent.getId() + ",");
            }
        } else {
            category.setLevel(1);
            category.setPath("");
        }

        if (category.getId() != null && category.getId() > 0) {
            category.setChild(baseMapper.getChildCount(category.getId()));
        }

        boolean success = false;
        //新增分类，与前端约定新增id=0
        if (category.getId() == null || category.getId() == 0) {
            category.setId(null);
            if (category.getIsMobile() == null) {
                category.setIsMobile(false);
            }
            category.setNewsid(0);
            category.setNewsid2(1);
            category.setDisplay(true);
            this.save(category);
        }
        //更新分类信息
        if (category.getId() != null && category.getId() > 0) {
            Category categorySync = getById(category.getId());
            // 同步需要
            if (categorySync == null) {
                getBaseMapper().insertCategory(category);
            } else {
                if (!this.updateById(category)) {
                    return false;
                }
            }
            List<Integer> childCategoryIds = new ArrayList<>();
            childCategoryIds = baseMapper.getChildIdList(category.getId());
            Collections.reverse(childCategoryIds);
            for (Integer id : childCategoryIds) {
                String path = this.baseMapper.getNeedResetPath(id);
                Integer level = 0;
                if (path == null) {
                    path = ",";
                }
                if (path.charAt(0) != ',') {
                    path = ',' + path;
                }
                if (path.charAt(0) != ',') {
                    level = 1;
                } else {
                    level = path.length() - path.replace(",", "").length();
                }
                this.baseMapper.resetSubCategory(path, level, id);
            }
            baseMapper.resetCategoryChild();
            childCategoryIds.add(category.getId());
            for (Integer id : childCategoryIds) {
                baseMapper.resetProductCatgs(id);
            }
            success = true;
        }
        return success;
    }

    /**
     * 获取正确的图片url
     */
    private String getCorrectPictureUrl(String url, String baseUrl, String imageSize) {
        String regVersion1 = "(\\d+)(.jpg|.bmp|.eps|.gif|.mif|.miff|.png|.tif|.tiff|.svg|.wmf|.jpe|.jpeg|.dib|.ico|.tga|.cut|.pic)";
        String regVersion2 = "^(/pic)(\\S+)(.jpg|.bmp|.eps|.gif|.mif|.miff|.png|.tif|.tiff|.svg|.wmf|.jpe|.jpeg|.dib|.ico|.tga|.cut|.pic)";
        String regVersion3 = "^(newstatic)(\\S+)(.jpg|.bmp|.eps|.gif|.mif|.miff|.png|.tif|.tiff|.svg|.wmf|.jpe|.jpeg|.dib|.ico|.tga|.cut|.pic)";

        if (Pattern.matches(regVersion3, url) || Pattern.matches(regVersion2, url)) {
            return miniFileConfig.getUploadurl() + url;
        }
        if (Pattern.matches(regVersion1, url)) {
            if (!StringUtils.isBlank(imageSize)) {
                return miniFileConfig.getUploadurl() + baseUrl + "/" + imageSize + url;
            }
            return miniFileConfig.getUploadurl() + baseUrl + url;
        }
        return null;
    }

}

package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_product_standard")
public class ProductStandard extends Model<ProductStandard> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 分类id
     */
    private Integer cid;

    /**
     * 规格名称，比如规格有：颜色、尺码、品牌、运存、套餐....
     */
    private String name;

    /**
     * 0显示为文字，1 显示为图片
     */
    private Integer showType;

    /**
     * 排序
     */
    private Integer orderId;

    /**
     * 是否删除
     */
    private Boolean isdel;

    /**
     * 前端规格是否可选
     */
    private Integer limitSelect;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

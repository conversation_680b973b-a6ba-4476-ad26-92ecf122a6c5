package com.jiuji.pick.service.product.service;

import java.util.List;

/**
 * @function:
 * @description: RefreshCacheDataService.java
 * @date: 2021/06/01
 * @author: sunfayun
 * @version: 1.0
 */
public interface RefreshCacheDataService {

    /**
     * 刷新ES的数据
     * @param ppidList
     * @param bindIdList
     * @param type
     */
    void refreshEsData(List<Long> ppidList, List<Long> bindIdList, Integer type);

}

package com.jiuji.pick.service.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.ModuleEnum;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.DeliveryFeeArea;
import com.jiuji.pick.service.common.entity.OperateLogInfo;
import com.jiuji.pick.service.common.mapper.DeliveryFeeAreaMapper;
import com.jiuji.pick.service.common.service.DeliveryFeeAreaService;
import com.jiuji.pick.service.common.service.OperateLogInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
@Slf4j
@Service
public class DeliveryFeeAreaServiceImpl extends ServiceImpl<DeliveryFeeAreaMapper, DeliveryFeeArea> implements DeliveryFeeAreaService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Resource
    private OperateLogInfoService operateLogInfoService;

    @Override
    public Result<Boolean> edit(DeliveryFeeArea feeArea) {
        OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
        if (Objects.isNull(oaTokenInfo)) {
            return Result.errorInfo("用户未登录");
        }
        String content;
        if (Objects.isNull(feeArea.getId())) {
            DeliveryFeeArea exist = this.getOne(Wrappers.<DeliveryFeeArea>lambdaQuery().eq(DeliveryFeeArea::getProvinceCode, feeArea.getProvinceCode()),false);
            if (exist != null) {
                return Result.errorInfo("数据已存在");
            }
            this.save(feeArea);
            content = "新增物流费区域信息";
        } else {
            DeliveryFeeArea exist = this.getOne(Wrappers.<DeliveryFeeArea>lambdaQuery().ne(DeliveryFeeArea::getId,feeArea.getId()).eq(DeliveryFeeArea::getProvinceCode, feeArea.getProvinceCode()),false);
            if (exist != null) {
                return Result.errorInfo("数据已存在");
            }
            DeliveryFeeArea oldFeeArea = this.getById(feeArea.getId());
            content = "物流费区域信息：" + CommonUtil.objCompare(oldFeeArea, feeArea, false);
            this.updateById(feeArea);
        }
        // 记录操作日志
        OperateLogInfo bindOperateLogInfo = OperateLogInfo.builder()
                .relateId(String.valueOf(feeArea.getId()))
                .type(ModuleEnum.COMMON.getCode())
                .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                .optUserName(oaTokenInfo.getName())
                .content(content)
                .showType(LogShowTypeEnum.ADMIN.getCode())
                .build();
        operateLogInfoService.saveOrUpdate(bindOperateLogInfo);
        return Result.success();
    }

    @Override
    public Result<Boolean> delete(Integer id) {
        OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
        this.removeById(id);
        // 记录操作日志
        OperateLogInfo bindOperateLogInfo = OperateLogInfo.builder()
                .relateId(String.valueOf(id))
                .type(ModuleEnum.COMMON.getCode())
                .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                .optUserName(oaTokenInfo.getName())
                .content("删除物流费配置")
                .showType(LogShowTypeEnum.ADMIN.getCode())
                .build();
        operateLogInfoService.saveOrUpdate(bindOperateLogInfo);
        return Result.success();
    }

    @Override
    public Result<IPage<DeliveryFeeArea>> getPage(String province, String provinceCode, Integer currentPage, Integer size) {
        Page<DeliveryFeeArea> page = new Page<>(currentPage,size);
        LambdaQueryWrapper<DeliveryFeeArea> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(province)) {
            wrapper.like(DeliveryFeeArea::getProvince,province);
        }
        if (StringUtils.isNotEmpty(provinceCode)) {
            wrapper.eq(DeliveryFeeArea::getProvinceCode,provinceCode);
        }
        wrapper.orderByDesc(DeliveryFeeArea::getId);
        return Result.success(this.page(page,wrapper));
    }

    @Override
    public DeliveryFeeArea getFeeArea(String cityId) {
        String tempCityId = cityId.substring(0,2);
        return this.getOne(Wrappers.<DeliveryFeeArea>lambdaQuery().eq(DeliveryFeeArea::getProvinceCode,tempCityId),false);
    }


}

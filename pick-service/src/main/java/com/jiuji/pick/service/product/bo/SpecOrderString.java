package com.jiuji.pick.service.product.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;
import java.util.Comparator;

/**
 * Created by lee on 17-8-18.
 */


@Getter
@AllArgsConstructor
public class SpecOrderString implements Serializable, Comparator {

    /**
     * 规格名称、或规格明细值
     */
    private String value;
    /**
     * 规格排序
     */
    private int rank;

    @JsonIgnore
    private Integer standDetailId;

    @Override
    public int compare(Object o1, Object o2) {
        SpecOrderString s1 = (SpecOrderString) o1;
        SpecOrderString s2 = (SpecOrderString) o2;
        return s1.getRank() - s2.getRank();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj instanceof SpecOrderString) {
            SpecOrderString spec = (SpecOrderString) obj;
            return this.value.equals((spec).getValue());
        }
        return false;
    }

    @Override
    public int hashCode() {
        return this.value.hashCode();
    }
}

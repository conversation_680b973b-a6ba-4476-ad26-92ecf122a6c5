package com.jiuji.pick.service.product.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Splitter;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.jiuji.cloud.stock.service.ChwQueryCloud;
import com.jiuji.cloud.stock.vo.request.SelectStockByPpidVO;
import com.jiuji.cloud.stock.vo.response.SalesStockVo;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.component.minifile.MiniFileConfig;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.utils.DetailImageDealUtil;
import com.jiuji.pick.common.utils.ImageUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.entity.OperateLogInfo;
import com.jiuji.pick.service.common.service.AttachmentInfoService;
import com.jiuji.pick.service.common.service.OperateLogInfoService;
import com.jiuji.pick.service.order.service.OrderInfoService;
import com.jiuji.pick.service.product.entity.CommodityAssociation;
import com.jiuji.pick.service.product.entity.PickProduct;
import com.jiuji.pick.service.product.entity.SupplierProductDetail;
import com.jiuji.pick.service.product.entity.SupplierProductPriceModify;
import com.jiuji.pick.service.product.mapper.IndexProductMapper;
import com.jiuji.pick.service.product.param.QueryAreaProductPageParam;
import com.jiuji.pick.service.product.param.QuerySortProductParam;
import com.jiuji.pick.service.product.param.UpdateAreaProductParam;
import com.jiuji.pick.service.product.service.*;
import com.jiuji.pick.service.product.vo.*;
import com.jiuji.pick.service.user.entity.Favorite;
import com.jiuji.pick.service.user.service.FavoriteService;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Service
@Slf4j
public class IndexProductServiceImpl  implements IndexProductService {

    private static final ListeningExecutorService LISTENING_EXECUTOR_SERVICE = MoreExecutors.listeningDecorator(new ThreadPoolExecutor(5, 5, 0L, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(100)));

    @Resource
    private IndexProductMapper indexProductMapper;
    @Resource
    private PickProductService pickProductService;
    @Resource
    private MiniFileConfig miniFileConfig;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private AttachmentInfoService attachmentInfoService;
    @Resource
    private OrderInfoService orderInfoService;
    @Resource
    private FavoriteService favoriteService;
    @Resource
    private SupplierProductPriceModifyService priceModifyService;
    @Resource
    private WarrantPolicyService warrantPolicyService;
    @Resource
    private CommodityAssociationService commodityAssociationService;
    @Resource
    private ChwQueryCloud chwQueryCloud;

    @Resource
    private SupplierProductDetailService supplierProductDetailService;
    @Resource
    private OperateLogInfoService operateLogInfoService;

    private static final Integer SPECIFICATIONS_TYPE=1;
    private static final Integer EDITION_TYPE=2;
    private static final Integer STOCK_MAX_NUMBER=50;

    @Override
    public Result<IndexProductListVo> indexProductList() {
        try {
            PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
            if (ObjectUtil.isNull(partnerTokenInfo)) {
                return Result.notLoginError();
            }
            // 查询爆品
            ListenableFuture<List<IndexProductVo>> hotProductFuture = LISTENING_EXECUTOR_SERVICE.submit(() -> getIndexProductVoList(ProductShowAreaEnum.HOT_AREA.getCode(), partnerTokenInfo.getSource()));
            // 查询乐物
            ListenableFuture<List<IndexProductVo>> happyProductFuture = LISTENING_EXECUTOR_SERVICE.submit(() -> getIndexProductVoList(ProductShowAreaEnum.HAPPY_AREA.getCode(), partnerTokenInfo.getSource()));
            // 查询资产
            ListenableFuture<List<IndexProductVo>> assetsProductFuture = LISTENING_EXECUTOR_SERVICE.submit(() -> getIndexProductVoList(ProductShowAreaEnum.ASSETS_AREA.getCode(), partnerTokenInfo.getSource()));
            // 查询推荐
            ListenableFuture<List<IndexProductVo>> recommendProductFuture = LISTENING_EXECUTOR_SERVICE.submit(() -> getIndexProductVoList(ProductShowAreaEnum.RECOMMEND_AREA.getCode(), partnerTokenInfo.getSource()));
            // 查询大件商品专区
            ListenableFuture<List<IndexProductVo>> bulkyProductFuture = LISTENING_EXECUTOR_SERVICE.submit(() -> getIndexProductVoList(ProductShowAreaEnum.BULKY_AREA.getCode(), partnerTokenInfo.getSource()));
            // 查询虚拟商品专区
            ListenableFuture<List<IndexProductVo>> virtualProductFuture = LISTENING_EXECUTOR_SERVICE.submit(() -> getIndexProductVoList(ProductShowAreaEnum.VIRTUAL_AREA.getCode(), partnerTokenInfo.getSource()));
            IndexProductListVo indexProductListVo = new IndexProductListVo();
            indexProductListVo.setHotProductList(hotProductFuture.get(10, TimeUnit.SECONDS));
            indexProductListVo.setHappyProductList(happyProductFuture.get(10, TimeUnit.SECONDS));
            indexProductListVo.setAssetsProductList(assetsProductFuture.get(10, TimeUnit.SECONDS));
            indexProductListVo.setRecommendProductList(recommendProductFuture.get(10, TimeUnit.SECONDS));
            indexProductListVo.setBulkyProductList(bulkyProductFuture.get(10, TimeUnit.SECONDS));
            indexProductListVo.setVirtualProductList(virtualProductFuture.get(10, TimeUnit.SECONDS));
            return Result.success(indexProductListVo);
        } catch (Exception e) {
            log.error("获取首页区域数据异常，exception:",e);
            return Result.errorInfo("服务异常，稍后再试");
        }
    }

    private List<IndexProductVo> getIndexProductVoList(Integer type, Integer partnerType) {
        try {
            List<IndexProductVo> productList = indexProductMapper.queryIndexProduct(type, partnerType);
            if(productList == null || CollectionUtils.isEmpty(productList)) {
                return Collections.emptyList();
            }
            List<IndexProductVo> resultList = Lists.newArrayList();
            // 去重处理
            Map<Long, List<IndexProductVo>> tempMap = productList.stream().collect(Collectors.groupingBy(IndexProductVo::getJiuJiProductId));
            for (Map.Entry<Long, List<IndexProductVo>> longListEntry : tempMap.entrySet()) {
                resultList.add(longListEntry.getValue().get(0));
            }
            // 排序处理，情况划分
            List<IndexProductVo> noZeroSortList = resultList.stream().filter(indexProductVo -> indexProductVo.getSort() != 0).collect(Collectors.toList());
            noZeroSortList = noZeroSortList.stream().sorted(Comparator.comparing(IndexProductVo::getSort).reversed()).collect(Collectors.toList());
            List<IndexProductVo> zeroSortList = resultList.stream().filter(indexProductVo -> indexProductVo.getSort() == 0).collect(Collectors.toList());

            // 新品专区商品排序值为0时，需要先按上架时间筛选出最近上架的是十个商品
            if (type.equals(ProductShowAreaEnum.RECOMMEND_AREA.getCode())) {
                zeroSortList = zeroSortList.stream().sorted(Comparator.comparing(IndexProductVo::getProductUpTime).reversed()).limit(10).collect(Collectors.toList());
            }
            // 去重后前十个商品排序值都不为0
            if (CollectionUtils.isNotEmpty(noZeroSortList) && noZeroSortList.size() >= MagicalValueConstant.INT_10) {
                resultList = noZeroSortList;
            } else if (CollectionUtils.isEmpty(noZeroSortList)){
                // 排序值全为0
                zeroSortList = zeroSortList.stream().sorted(Comparator.comparing(IndexProductVo::getSaleCount).reversed()).collect(Collectors.toList());
                resultList = zeroSortList;
            } else {
                // 有排序值，但不满十个
                List<IndexProductVo> sortList = zeroSortList.stream().sorted(Comparator.comparing(IndexProductVo::getSaleCount).reversed()).collect(Collectors.toList());
                noZeroSortList.addAll(sortList);
                resultList = noZeroSortList;
            }
            resultList = resultList.stream().limit(10).collect(Collectors.toList());
            return this.imageDispose(resultList);
        } catch (Exception e) {
            log.error("查询首页区域商品信息异常，type:{}, exception:", type, e);
            return Collections.emptyList();
        }
    }

    private List<IndexProductVo> imageDispose(List<IndexProductVo> indexProductVoList){
        if(CollectionUtils.isEmpty(indexProductVoList)) {
            return Collections.emptyList();
        }
        indexProductVoList.forEach(it->{
            String productImageUrl = ImageUtil.getProductImageUrl(miniFileConfig.getPathbase(), it.getBpic(), WebConstant.PICTURE_SIZE.PIC_440x440);
            it.setBpic(productImageUrl);
        });
        return indexProductVoList;
    }

    private List<Long> getFilterPpid(){
        List<Long> filterPpidList = new ArrayList<>();
        List<IndexProductVo> indexProductVos = indexProductMapper.queryAreaProductAll();
        if(CollectionUtils.isEmpty(indexProductVos)){
            return filterPpidList;
        }
        //商品首先根据供应商进行分组
        Map<Long, List<IndexProductVo>> map = indexProductVos.stream().collect(Collectors.groupingBy(IndexProductVo::getSupplierId));
        for (Map.Entry<Long, List<IndexProductVo>> supplierProduct:map.entrySet()) {
            //获取到供应商下的商品
            List<IndexProductVo> productList = supplierProduct.getValue();
            if(CollectionUtils.isEmpty(productList)){
                continue;
            }
            //商品再根据productId进行分组
            Map<Long, List<IndexProductVo>> productIdMap = productList.stream().collect(Collectors.groupingBy(IndexProductVo::getProductId));
            for (Map.Entry<Long, List<IndexProductVo>> product:productIdMap.entrySet()) {
                List<IndexProductVo> value = product.getValue();
                if(CollectionUtils.isEmpty(value)){
                    continue;
                }
                //把商品根据销量进行排序
                List<IndexProductVo> valueSort = value.stream().sorted(Comparator.comparing(IndexProductVo::getSaleCount).reversed()).collect(Collectors.toList());
                filterPpidList.add(valueSort.get(0).getPpid());
            }
        }
        return filterPpidList;
    }

    @Override
    public Result<Page<IndexProductVo>> queryProductArea(QuerySortProductParam param) {
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (ObjectUtil.isNull(partnerTokenInfo)) {
            return Result.notLoginError();
        }
        param.setPartnerType(partnerTokenInfo.getSource());
        param.setFilterPpidList(getFilterPpid());
        Page<IndexProductVo> indexProductVos = indexProductMapper.queryAreaProduct(new Page<>(param.getCurrentPage(), param.getSize()),param);
        if(indexProductVos == null || CollectionUtils.isEmpty(indexProductVos.getRecords())) {
            return Result.noData(indexProductVos);
        }
        this.imageDispose(indexProductVos.getRecords());
        return Result.success(indexProductVos);
    }

    @Override
    public Result<IndexProductDetailVo> getIndexProductDetailInfo(Long supplierProductId) {
        if(supplierProductId == null) {
            return Result.errorInfo("请求参数错误");
        }
        Long partnerId = currentRequestComponent.getPartnerId();
        IndexProductDetailVo indexProductDetailVo = indexProductMapper.getProductDetailInfo(supplierProductId);
        if(indexProductDetailVo == null) {
            return Result.errorInfo("商品信息不存在");
        }

        // 查询商品价格改动
        SupplierProductPriceModify priceModify = priceModifyService.getSupplierProductPriceModify(indexProductDetailVo.getSupplierProductId(), true, true);
        log.info("商品改价记录:{}", priceModify);
        if (ObjectUtil.isNotNull(priceModify)) {
            BigDecimal subtract = Optional.ofNullable(priceModify.getCurrentNoTaxPrice()).orElse(BigDecimal.ZERO)
                    .subtract(Optional.ofNullable(priceModify.getLastNoTaxPrice()).orElse(BigDecimal.ZERO));
            indexProductDetailVo.setFloatPrice(subtract);
        } else {
            indexProductDetailVo.setFloatPrice(BigDecimal.ZERO);
        }

        // 查询质保政策
        WarrantyPolicyVO warrantyPolicyVO = warrantPolicyService.getWarrantyPolicy(indexProductDetailVo.getProductType(), true);
        if (ObjectUtil.isNotNull(warrantyPolicyVO)) {
            indexProductDetailVo.setWarrantyPolicyVO(warrantyPolicyVO);
        }

        // 设置库存状态
        Integer stockStatus = this.getStockStatus(indexProductDetailVo);
        indexProductDetailVo.setStockStatus(stockStatus);
        //imageList 展示图片查询
        Long ppid = indexProductDetailVo.getPpid();
        if(ppid!=null){
            List<AttachmentInfo> list = attachmentInfoService.lambdaQuery().eq(AttachmentInfo::getRelateId, ppid)
                    .eq(AttachmentInfo::getType, AttachTypeEnum.PRODUCT_IMAGE.getCode()).list();
            if(!CollectionUtils.isEmpty(list)){
                List<IndexProductDetailVo.FileInfo> imageList = new ArrayList<>();
                list.forEach((AttachmentInfo item)->{
                    IndexProductDetailVo.FileInfo fileInfo = new IndexProductDetailVo.FileInfo();
                    fileInfo.setFileName(item.getFileName());
                    fileInfo.setFilePath(item.getFilePath());
                    imageList.add(fileInfo);
                });
                indexProductDetailVo.setImageList(imageList);
            }
        }
        // 资料处理
        if(StringUtils.isNotBlank(indexProductDetailVo.getAttachmentIds())) {
            List<Long> attachmentIdList = Splitter.on(",").splitToList(indexProductDetailVo.getAttachmentIds()).stream().map(Long::valueOf).collect(Collectors.toList());
            // 查询资料信息
            List<AttachmentInfo> attachmentInfoList = attachmentInfoService.list(new LambdaQueryWrapper<AttachmentInfo>().in(AttachmentInfo::getId, attachmentIdList));
            // 判断合作伙伴是否下过订单
            boolean hasOrder = orderInfoService.hasCompleteOrder4Product(partnerId, indexProductDetailVo.getProductId());
            List<IndexProductDetailVo.FileInfo> fileInfoList = Lists.newArrayList();
            for (AttachmentInfo attachmentInfo : attachmentInfoList) {
                IndexProductDetailVo.FileInfo fileInfo = new IndexProductDetailVo.FileInfo();
                if(hasOrder) {
                    fileInfo.setFilePath(attachmentInfo.getFilePath());
                }
                fileInfo.setFileName(attachmentInfo.getFileName());
                fileInfoList.add(fileInfo);
            }
            indexProductDetailVo.setFileList(fileInfoList);
        }
        // 获取相似商品
        List<SimilarProductVo> similarProductVoList = indexProductMapper.querySimilarProduct(indexProductDetailVo.getProductId(), indexProductDetailVo.getSupplierProductId());
        if(CollectionUtils.isNotEmpty(similarProductVoList)) {
            indexProductDetailVo.setSimilarProductList(similarProductVoList);
        }
        // 获取规格
        IndexProductDetailSpecVo indexProductDetailSpecVo = getIndexProductDetailSpecVo(indexProductDetailVo.getJiuJiProductId(), indexProductDetailVo.getSupplierUserId(), indexProductDetailVo.getPpid(),SPECIFICATIONS_TYPE);
        indexProductDetailVo.setSpecList(indexProductDetailSpecVo);
        //获取版本
        IndexProductDetailSpecVo associationList = getAssociation(indexProductDetailVo);
        indexProductDetailVo.setAssociationList(associationList);

        indexProductDetailVo.setImagePath(ImageUtil.getProductImageUrl(miniFileConfig.getPathbase(),
                indexProductDetailVo.getBPic(), WebConstant.PICTURE_SIZE.PIC_440x440));
        if(indexProductDetailVo.getAdvicePrice() != null) {
            BigDecimal predictProfit = Optional.ofNullable(indexProductDetailVo.getBuyNoTaxPrice()).orElse(indexProductDetailVo.getBuyTaxPrice());
            indexProductDetailVo.setPredictProfit(indexProductDetailVo.getAdvicePrice().subtract(predictProfit));
        }
        // 设置详情图片路径
        indexProductDetailVo.setDetail(DetailImageDealUtil.getPicDetail(indexProductDetailVo.getDetail()));
        Favorite existFavorite = favoriteService.getOne(Wrappers.<Favorite>lambdaQuery().eq(Favorite::getSupplierProductId,supplierProductId).eq(Favorite::getPartnerUserId,partnerId),false);
        indexProductDetailVo.setFavorite(false);
        if (existFavorite != null) {
            indexProductDetailVo.setFavoriteId(existFavorite.getId());
            indexProductDetailVo.setFavorite(true);
        }
        return Result.success(indexProductDetailVo);
    }


    /**
     *
     * @param indexProductDetailVo
     * @return
     */
    private IndexProductDetailSpecVo getAssociation(IndexProductDetailVo indexProductDetailVo){
        IndexProductDetailSpecVo indexProductDetailSpecVo = new IndexProductDetailSpecVo();
        //更具jiuJiProductId查询出商品的版本关联
        Long jiuJiProductId = indexProductDetailVo.getJiuJiProductId();
        List<CommodityAssociation> list = commodityAssociationService.lambdaQuery()
                .eq(CommodityAssociation::getProductId, jiuJiProductId.intValue())
                .list();
        if(CollectionUtils.isEmpty(list)){
            return indexProductDetailSpecVo;
        }
        List<Integer> associationIdList = list.stream().map(CommodityAssociation::getAssociationId).collect(Collectors.toList());
        List<CommodityAssociation> commodityAssociationList = commodityAssociationService.lambdaQuery()
                .in(CommodityAssociation::getAssociationId, associationIdList).list();
        if(CollectionUtils.isEmpty(commodityAssociationList)){
            return indexProductDetailSpecVo;
        }
        List<IndexMiniProductSpecVo> miniProductSpecList = new ArrayList<>();
        Map<Integer, String> map = commodityAssociationList.stream().collect(Collectors.toMap(CommodityAssociation::getPpid, CommodityAssociation::getEdition, (n1, n2) -> n2));
        for (CommodityAssociation item: commodityAssociationList) {
            Integer productId = item.getProductId();
            Integer ppid = item.getPpid();
            Long supplierUserId = indexProductDetailVo.getSupplierUserId();
            Integer showSort = item.getShowSort();
            IndexProductDetailSpecVo indexProductDetailSpec = getIndexProductDetailSpecVo(showSort,productId.longValue(), supplierUserId, ppid.longValue(),map,indexProductDetailVo.getPpid());
            List<IndexMiniProductSpecVo> specList = indexProductDetailSpec.getMiniProductSpecList();
            if(CollectionUtils.isNotEmpty(specList)){
                miniProductSpecList.addAll(specList);
            }
        }
        if(CollectionUtils.isNotEmpty(miniProductSpecList)){
            indexProductDetailSpecVo.setMiniProductSpecList(miniProductSpecList.stream().sorted(Comparator.comparing(IndexMiniProductSpecVo::getShowSort)).collect(Collectors.toList()));
        }
        return indexProductDetailSpecVo;
    }

    /**
     * 获取规格
     * @param jiuJiProductId
     * @param supplierUserId
     * @param ppid
     * @return
     */
    private IndexProductDetailSpecVo getIndexProductDetailSpecVo(Long jiuJiProductId,Long supplierUserId, Long ppid,Integer type ){
        List<IndexMiniProductSpecVo> miniProductSpecVoList;
        if(SPECIFICATIONS_TYPE.equals(type)){
            miniProductSpecVoList = indexProductMapper.getProductDetailSpecInfo(jiuJiProductId, supplierUserId,null);
        } else {
            miniProductSpecVoList = indexProductMapper.getProductDetailSpecInfo(jiuJiProductId, supplierUserId,ppid);
        }
        IndexProductDetailSpecVo indexProductDetailSpecVo = new IndexProductDetailSpecVo();
        if(CollectionUtils.isNotEmpty(miniProductSpecVoList)) {
            for (IndexMiniProductSpecVo indexMiniProductSpecVo : miniProductSpecVoList) {
                if(indexMiniProductSpecVo.getPpid() != null && ppid != null && indexMiniProductSpecVo.getPpid().longValue() == ppid) {
                    indexMiniProductSpecVo.setSelected(Boolean.TRUE);
                } else {
                    indexMiniProductSpecVo.setSelected(Boolean.FALSE);
                }
                //图片处理
                indexMiniProductSpecVo.setBPic(ImageUtil.getProductImageUrl(miniFileConfig.getPathbase(),
                        indexMiniProductSpecVo.getBPic(), WebConstant.PICTURE_SIZE.PIC_440x440));
            }
            indexProductDetailSpecVo.setMiniProductSpecList(miniProductSpecVoList);
        }
        return indexProductDetailSpecVo;

    }



    /**
     * 获取规格
     * @param jiuJiProductId
     * @param supplierUserId
     * @param ppid
     * @return
     */
    private IndexProductDetailSpecVo getIndexProductDetailSpecVo(Integer showSort,Long jiuJiProductId,Long supplierUserId, Long ppid,Map<Integer, String> map,Long originalPpid){
        List<IndexMiniProductSpecVo> miniProductSpecVoList = indexProductMapper.getProductDetailSpecInfoV2(jiuJiProductId, supplierUserId,ppid);
        IndexProductDetailSpecVo indexProductDetailSpecVo = new IndexProductDetailSpecVo();
        if(CollectionUtils.isNotEmpty(miniProductSpecVoList)) {
            //收集所有的ProductId
            List<Long> productIdList = miniProductSpecVoList.stream().map(IndexMiniProductSpecVo::getJiuJiProductId).collect(Collectors.toList());
            List<PickProduct> list = pickProductService.lambdaQuery()
                    .in(PickProduct::getJiuJiProductId, productIdList)
                    .eq(PickProduct::getProductStatus, ProductStatusEnum.UP.getCode())
                    .list();
            //查询收集成map避免循环查询
            Map<Long, List<PickProduct>> productIdMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(list)){
                productIdMap = list.stream().collect(Collectors.groupingBy(PickProduct::getJiuJiProductId));
            }

            for (IndexMiniProductSpecVo indexMiniProductSpecVo : miniProductSpecVoList) {
                if(originalPpid.equals(indexMiniProductSpecVo.getPpid())){
                    indexMiniProductSpecVo.setSelected(Boolean.TRUE);
                } else {
                    indexMiniProductSpecVo.setSelected(Boolean.FALSE);
                }
                //采用包含的关系ProductId包含ppid的逻辑进行是否选中的逻辑判断
                List<PickProduct> pickProductList = productIdMap.get(indexMiniProductSpecVo.getJiuJiProductId());
                if(CollectionUtils.isNotEmpty(pickProductList)){
                    List<Long> ppidList = pickProductList.stream().map(PickProduct::getPpid).collect(Collectors.toList());
                    if(ppidList.contains(originalPpid)){
                        indexMiniProductSpecVo.setSelected(Boolean.TRUE);
                    } else {
                        indexMiniProductSpecVo.setSelected(Boolean.FALSE);
                    }
                }

                Integer key = Optional.ofNullable(indexMiniProductSpecVo.getPpid()).orElse(Long.MAX_VALUE).intValue();
                indexMiniProductSpecVo.setEdition(map.get(key));
                indexMiniProductSpecVo.setShowSort(showSort);
                //图片处理
                indexMiniProductSpecVo.setBPic(ImageUtil.getProductImageUrl(miniFileConfig.getPathbase(),
                        indexMiniProductSpecVo.getBPic(), WebConstant.PICTURE_SIZE.PIC_440x440));
            }
            indexProductDetailSpecVo.setMiniProductSpecList(miniProductSpecVoList);
        }
        return indexProductDetailSpecVo;

    }

    /**
     * 计算库存状态  库销比=（库存 / 销量）*30  库存比≤50则为预定，＞50则为现货(废弃)
     * 商品详情页的库存状态逻辑调整，库存数据获取9X_scc和9X_scc2的库存之和
     * 库存<=0 或者 库存 == null  为缺货
     *  库存  < 200 为预订
     * 库存 >= 200 为现货
     */
    private Integer getStockStatus(IndexProductDetailVo indexProductDetailVo) {
        Long ppid = indexProductDetailVo.getPpid();
        SelectStockByPpidVO selectStockByPpidVO = new SelectStockByPpidVO();
        selectStockByPpidVO.setPpidList(Collections.singletonList(ppid));
        R<Map<Long, SalesStockVo>> result = chwQueryCloud.getStockByPpid(selectStockByPpidVO);
        int code = result.getCode();
        if(code!=0){
            return StockStatusEnum.ZERO.getCode();
        }
        Map<Long, SalesStockVo> map = Optional.ofNullable(result.getData()).orElse(new HashMap<>());
        SalesStockVo salesStockVo = map.get(ppid);
        if(salesStockVo ==null){
            return StockStatusEnum.ZERO.getCode();
        }
        Integer stock = Optional.ofNullable(salesStockVo.getStock()).orElse(Integer.MIN_VALUE);
        if(stock > STOCK_MAX_NUMBER){
            return StockStatusEnum.ONE.getCode();
        }
        return StockStatusEnum.ZERO.getCode();

    }


    @Override
    public Page<AreaProductVO> pageAreaProductVO(QueryAreaProductPageParam pageParam) {
        Page<AreaProductVO> page = new Page<>(pageParam.getCurrentPage(), pageParam.getSize());
        return indexProductMapper.pageAreaProductVO(page,pageParam);
    }

    @Override
    public Boolean areaProductSort(Long id, Integer sort) {
        SupplierProductDetail supplierProductDetail = supplierProductDetailService.getById(id);
        if (Objects.isNull(supplierProductDetail)) {
            log.warn("供应商商品不存在");
            return false;
        }
        supplierProductDetail.setSort(sort);
        supplierProductDetail.setUpdateTime(LocalDateTime.now());
        return supplierProductDetail.updateById();
    }

    /**
     * 修改商品专区
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAreaProduct(UpdateAreaProductParam param) {
        Integer defaultArea = param.getAreaCodeList().contains(1) ? 1 : 0;
        Integer hotArea = param.getAreaCodeList().contains(2) ? 1 : 0;
        Integer happyArea = param.getAreaCodeList().contains(3) ? 1 : 0;
        Integer recommendArea = param.getAreaCodeList().contains(4) ? 1 : 0;
        boolean updateFlag = pickProductService.lambdaUpdate()
                .set(PickProduct::getDefaultArea, defaultArea)
                .set(PickProduct::getHotArea, hotArea)
                .set(PickProduct::getHappyArea, happyArea)
                .set(PickProduct::getRecommendArea, recommendArea)
                .in(PickProduct::getPpid, param.getPpidList())
                .update();
        if (updateFlag) {
            OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
            List<OperateLogInfo> operateLogList = param.getPpidList().stream().map(v -> OperateLogInfo.builder()
                    .relateId(String.valueOf(v))
                    .type(ModuleEnum.PRODUCT.getCode())
                    .userId(Long.valueOf(oaTokenInfo.getUserId()))
                    .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                    .optUserName(oaTokenInfo.getName())
                    .content(StrUtil.format("批量修改商品专区信息defaultArea:{},hotArea:{},happyArea:{},recommendArea:{}", defaultArea, hotArea, happyArea, recommendArea))
                    .showType(LogShowTypeEnum.NOT_SHOW.getCode())
                    .build()).collect(Collectors.toList());
            operateLogInfoService.saveOrUpdateBatch(operateLogList);
        }

        log.info("修改商品专区param={},updateFlag={}", param, updateFlag);
        return updateFlag;
    }

}

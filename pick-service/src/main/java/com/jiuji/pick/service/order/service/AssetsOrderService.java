package com.jiuji.pick.service.order.service;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.entity.OrderInfo;

import java.util.List;

/**
 * @function:
 * @description: AssetsOrderService.java
 * @date: 2021/10/11
 * @author: sunfayun
 * @version: 1.0
 */
public interface AssetsOrderService extends OrderCommonService {

    /**
     * 订单收货
     * @param orderInfoList 待收货订单
     * @return 返回收货失败的订单错误信息
     */
    List<String> orderReceive(List<OrderInfo> orderInfoList);

    /**
     * 根据ppid查询商品类型， 检验类型是否一致
     * @param ppid
     * @param productType   商品类型 ProductTypeEnum
     * @return
     */
    Result<Boolean> checkProductType(Long ppid, Integer productType);

}

package com.jiuji.pick.service.product.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.service.product.entity.CategorySearchWord;
import com.jiuji.pick.service.product.mapper.CategorySearchWordMapper;
import com.jiuji.pick.service.product.service.CategorySearchWordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-20
 */
@Service
public class CategorySearchWordServiceImpl extends ServiceImpl<CategorySearchWordMapper, CategorySearchWord> implements CategorySearchWordService {

    @Override
    public List<Integer> getCategoryIdsByKeyword(List<String> keywords) {
        if (CollectionUtils.isEmpty(keywords)) {
            return new ArrayList<>();
        }
        return baseMapper.getCategoryIdsByKeyword(keywords);
    }

    @Override
    public List<CategorySearchWord> listAll() {
        return list(null);
    }
}

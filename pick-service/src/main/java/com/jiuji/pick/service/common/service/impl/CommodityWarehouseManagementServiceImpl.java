package com.jiuji.pick.service.common.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.LogComparison.AddOrUpdateProductParamEnum;
import com.jiuji.pick.common.enums.LogComparison.LogDifferences;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.common.utils.ReflexUtils;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.param.FileUploadParam;
import com.jiuji.pick.service.common.service.LogService;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.product.bo.AddOrUpdateProductParam;
import com.jiuji.pick.service.product.bo.AddOrUpdateProductParamOld;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("CommodityWarehouseManagementServiceImpl")
public class CommodityWarehouseManagementServiceImpl implements LogService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private NewOperateLogInfoService logInfoService;

    @Override
    public void systemSaveLog(Object param) {
        OATokenInfo oaTokenInfo = Optional.ofNullable(currentRequestComponent.getOATokenInfoBoWithoutCheck())
                .orElse(new OATokenInfo());
        CompletableFuture.runAsync(() -> {
            AddOrUpdateProductParam addOrUpdateProductParam = JSONUtil.toBean(JSONUtil.toJsonStr(param), AddOrUpdateProductParam.class);
            AddOrUpdateProductParamOld paramOld = addOrUpdateProductParam.getParamOld();
            AddOrUpdateProductParamOld paramNew = new AddOrUpdateProductParamOld();
            BeanUtils.copyProperties(addOrUpdateProductParam, paramNew);
            StringBuilder connent = new StringBuilder();
            //比较商品附属资料
            List<FileUploadParam> attachmentListOld = paramOld.getFileList();
            List<FileUploadParam> attachmentListNew = paramNew.getFileList();
            String attachment = getAttachment(attachmentListNew, attachmentListOld, "商品附属资料");
            connent.append(attachment);
            //比较资料展示图片
            List<FileUploadParam> imageListOld = paramOld.getImageList();
            List<FileUploadParam> imageListNew = paramNew.getImageList();
            String image = getAttachment(imageListNew, imageListOld, "资料展示图片");
            connent.append(image);
            //比较上架专区的不同
            String shelfArea = getShelfArea(paramOld, paramNew);
            //比较主体不同
            String different = getDifferent(paramOld, paramNew);
            connent.append(different).append(shelfArea);
            //日志保存
            NewOperateLogInfo newOperateLogInfo = new NewOperateLogInfo();
            Integer userId = oaTokenInfo.getUserId();
            String name = oaTokenInfo.getName();
            newOperateLogInfo.setContent(connent.toString())
                    .setRelateId(paramNew.getPpid() + "")
                    .setOptUserId(userId.longValue())
                    .setOptUserName(name)
                    .setType(NewOperateLogInfoTypeEnum.COMMODITY_WAREHOUSE_MANAGEMENT.getCode())
                    .setCreateTime(LocalDateTime.now())
                    .setShowType(LogShowTypeEnum.ADMIN.getCode());
            logInfoService.saveSystemLog(newOperateLogInfo);
        });
    }


    private String getShelfArea(AddOrUpdateProductParamOld paramOld,AddOrUpdateProductParamOld paramNew){
        String oldString = areaString(paramOld);
        String newString = areaString(paramNew);
        if(oldString.equals(newString)){
            return "";
        }
        return String.format("上架专区由【%s】修改为【%s】",oldString,newString);

    }

    private String areaString(AddOrUpdateProductParamOld param){
        StringJoiner joiner = new StringJoiner("，");
        Integer defaultAreaOld = Optional.ofNullable(param.getDefaultArea()).orElse(0);
        if(defaultAreaOld==1){
            joiner.add(AddOrUpdateProductParamEnum.defaultArea.getMessage());
        }
        Integer happyAreaOld = Optional.ofNullable(param.getHappyArea()).orElse(0);
        if(happyAreaOld==1){
            joiner.add(AddOrUpdateProductParamEnum.happyArea.getMessage());
        }

        Integer hotAreaOld = Optional.ofNullable(param.getHotArea()).orElse(0);
        if(hotAreaOld==1){
            joiner.add(AddOrUpdateProductParamEnum.hotArea.getMessage());
        }
        Integer recommendAreaOld = Optional.ofNullable(param.getRecommendArea()).orElse(0);
        if(recommendAreaOld==1){
            joiner.add(AddOrUpdateProductParamEnum.recommendArea.getMessage());
        }
        return joiner.toString();
    }

    /**
     * 获取两个实体不同
     *
     * @param oldEntity
     * @param newEntity
     * @return
     */
    private String getDifferent(Object oldEntity, Object newEntity) {
        //封装比较参数
        LogDifferences logDifferences = new LogDifferences();
        logDifferences.setOldEntity(oldEntity)
                .setTransformationMap(new HashMap<>(1))
                .setNewEntity(newEntity)
                .setExcludeParams(Arrays.asList(AddOrUpdateProductParamEnum.defaultArea.getCode()
                        ,AddOrUpdateProductParamEnum.happyArea.getCode()
                        ,AddOrUpdateProductParamEnum.hotArea.getCode()
                        ,AddOrUpdateProductParamEnum.recommendArea.getCode()))
                .setParamMap(AddOrUpdateProductParamEnum.getMap());
        String comment = null;
        try {
            comment = ReflexUtils.entityComparison(logDifferences);
        } catch (Exception e) {
            log.error("商品库管理{}", e.getMessage(), e);
        }
        return comment;
    }


    /**
     * 日志记录文件的差异
     *
     * @param attachmentListNew
     * @param attachmentListOld
     * @return
     */
    private String getAttachment(List<FileUploadParam> attachmentListNew, List<FileUploadParam> attachmentListOld, String type) {
        StringBuilder builder = new StringBuilder();
        StringJoiner joinerOld = new StringJoiner("，");
        if (CollectionUtils.isNotEmpty(attachmentListOld)) {
            attachmentListOld.forEach((FileUploadParam item) -> {
                String fileName = item.getFileName();
                joinerOld.add(fileName);
            });
        }
        StringJoiner joinerNew = new StringJoiner("，");
        if (CollectionUtils.isNotEmpty(attachmentListNew)) {
            attachmentListNew.forEach((FileUploadParam item) -> {
                String fileName = item.getFileName();
                joinerNew.add(fileName);
            });
        }
        if (!joinerNew.toString().equals(joinerOld.toString())) {
            builder.append(type).append("由【").append(joinerOld.toString()).append("】修改为【").append(joinerNew.toString()).append("】");
        }
        return builder.toString();
    }
}

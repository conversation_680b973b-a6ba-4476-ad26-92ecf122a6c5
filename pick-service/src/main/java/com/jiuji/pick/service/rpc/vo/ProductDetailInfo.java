package com.jiuji.pick.service.rpc.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductDetailInfo {


    /**
     * 订单id
     */
    private Integer subId;
    /**
     * 商品ppid
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品规格
     */
    private String productColor;
    /**
     * 数量
     */
    private Integer count;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    /**
     * 时间
     */
    private String time;
    /**
     * 备注
     */
    private String remark;

}

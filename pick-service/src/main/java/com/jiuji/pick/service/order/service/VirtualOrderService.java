package com.jiuji.pick.service.order.service;

import com.jiuji.pick.service.order.entity.OrderInfo;

import java.util.List;

/**
 * @function:
 * @description: VirtualOrderService.java
 * @date: 2021/10/11
 * @author: sunfayun
 * @version: 1.0
 */
public interface VirtualOrderService extends OrderCommonService {

    /**
     * 订单收货
     * @param orderInfoList 待收货订单
     * @return 返回收货失败的订单信息
     */
    List<String> orderReceive(List<OrderInfo> orderInfoList);

}

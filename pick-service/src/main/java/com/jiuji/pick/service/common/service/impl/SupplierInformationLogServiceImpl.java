package com.jiuji.pick.service.common.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.AccountTypeEnum;
import com.jiuji.pick.common.enums.LogComparison.*;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.ReflexUtils;
import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.service.LogService;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.service.CategoryService;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.entity.SupplierUserAccount;
import com.jiuji.pick.service.user.entity.SupplierUserContact;
import com.jiuji.pick.service.user.entity.SupplierUserQualification;
import com.jiuji.pick.service.user.vo.SupplierUserDetailOldVO;
import com.jiuji.pick.service.user.vo.SupplierUserDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(value = "SupplierInformationLogServiceImpl")
public class SupplierInformationLogServiceImpl implements LogService {


    @Resource
    private NewOperateLogInfoService logInfoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private CategoryService categoryService;

    /**
     * 系统日志的保存
     *
     * @param param
     */
    @Override
    public void systemSaveLog(Object param, Integer type) {
        //开启线程进行日志的处理（让日志的处理不影响业务的操作）
        Long userId = null;
        String userName = null;
        NewOperateLogInfo newOperateLogInfo = new NewOperateLogInfo();
        if (NewOperateLogInfoTypeEnum.SUPPLIER_INFORMATION.getCode().equals(type)) {
            SupplierTokenInfo tokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck())
                    .orElse(new SupplierTokenInfo());
            userId = tokenInfo.getId();
            userName = tokenInfo.getLoginName();
            newOperateLogInfo.setShowType(LogShowTypeEnum.SUPPLIER.getCode())
                    .setType(type);

        }
        if (NewOperateLogInfoTypeEnum.SUPPLIER_LIST.getCode().equals(type)) {
            OATokenInfo oaTokenInfo = Optional.ofNullable(currentRequestComponent.getOATokenInfoBoWithoutCheck())
                    .orElse(new OATokenInfo());
            userId = oaTokenInfo.getUserId().longValue();
            userName = oaTokenInfo.getName();
            newOperateLogInfo.setShowType(LogShowTypeEnum.ADMIN.getCode())
                    .setType(type);

        }

        newOperateLogInfo.setOptUserName(userName)
                .setOptUserId(userId);
        CompletableFuture.runAsync(() -> {
            //获取到参数的类型
            SupplierUserDetailVO supplierUserDetailVO = JSONUtil.toBean(JSONUtil.toJsonStr(param), SupplierUserDetailVO.class);
            //区分新老数据
            SupplierUserDetailOldVO paramOld = supplierUserDetailVO.getParamOld();
            SupplierUserDetailOldVO paramNew = new SupplierUserDetailOldVO();
            BeanUtils.copyProperties(supplierUserDetailVO, paramNew);
            //开始拼接内容
            StringBuilder connent = new StringBuilder();
            //比较SupplierUser的差异
            SupplierUser supplierUserNew = paramNew.getSupplierUser();
            SupplierUser supplierUserOld = paramOld.getSupplierUser();
            String supplierUser = getDifferent(supplierUserOld,supplierUserNew,SupplierUserEnum.getMap(), null);
            connent.append(supplierUser);
            //比较SupplierUserQualification的差异（商品分类特殊转换）
            SupplierUserQualification supplierUserQualificationNew = paramNew.getSupplierUserQualification();
            SupplierUserQualification supplierUserQualificationOld = paramOld.getSupplierUserQualification();
            transformationCategoryId(supplierUserQualificationNew,supplierUserQualificationOld);
            String supplierUserQualification = getDifferent( supplierUserQualificationOld, supplierUserQualificationNew,SupplierUserQualificationEnum.getMap(), null);
            connent.append(supplierUserQualification);
            //比较AttachmentInfo的差异
            List<AttachmentInfo> attachmentListNew = paramNew.getAttachmentList();
            List<AttachmentInfo> attachmentListOld = paramOld.getAttachmentList();
            String attachment = getAttachment(attachmentListNew, attachmentListOld);
            connent.append(attachment);
            //比较supplierUserContactList的差异
            List<SupplierUserContact> supplierUserContactListNew = paramNew.getSupplierUserContactList();
            List<SupplierUserContact> supplierUserContactListOld = paramOld.getSupplierUserContactList();
            String supplierUserContactList = getSupplierUserContactList(supplierUserContactListNew, supplierUserContactListOld);
            connent.append(supplierUserContactList);
            //比较supplierUserAccountList的差异
            List<SupplierUserAccount> supplierUserAccountListNew = paramNew.getSupplierUserAccountList();
            List<SupplierUserAccount> supplierUserAccountListOld = paramOld.getSupplierUserAccountList();
            String supplierUserAccountList = getSupplierUserAccountList(supplierUserAccountListNew, supplierUserAccountListOld);
            connent.append(supplierUserAccountList);
            SupplierUser supplier = Optional.ofNullable(paramNew.getSupplierUser()).orElse(new SupplierUser());
            newOperateLogInfo.setContent(connent.toString())
                    .setRelateId(supplier.getId() + "")
                    .setCreateTime(LocalDateTime.now());
            logInfoService.saveSystemLog(newOperateLogInfo);
        });
    }

    /**
     * 商品分类转换
     */
    private void transformationCategoryId(SupplierUserQualification supplierUserQualificationNew,SupplierUserQualification supplierUserQualificationOld){
        String newCateId = Optional.ofNullable(supplierUserQualificationNew.getCategoryId()).orElseThrow(() -> new BizException("商品分类不能为空"));
        String oldCateId = Optional.ofNullable(supplierUserQualificationOld.getCategoryId()).orElseThrow(() -> new BizException("商品分类不能为空"));
        if(newCateId.equals(oldCateId)){
            return;
        }
        Category newCategory = Optional.ofNullable(categoryService.getById(Integer.valueOf(newCateId))).orElse(new Category());
        Category oldCategory = Optional.ofNullable(categoryService.getById(Integer.valueOf(oldCateId))).orElse(new Category());
        supplierUserQualificationNew.setCategoryId(newCategory.getName());
        supplierUserQualificationOld.setCategoryId(oldCategory.getName());
    }

    /**
     * 比较supplierUserAccountList的差异
     *
     * @param supplierUserAccountListNew
     * @param supplierUserAccountListOld
     * @return
     */
    private String getSupplierUserAccountList(List<SupplierUserAccount> supplierUserAccountListNew, List<SupplierUserAccount> supplierUserAccountListOld) {
        if (CollectionUtils.isEmpty(supplierUserAccountListNew) && CollectionUtils.isEmpty(supplierUserAccountListOld)) {
            return "";
        }
        //排序之后避免list里面的数据错乱
        List<SupplierUserAccount> newList = supplierUserAccountListNew.stream().sorted(Comparator.comparing(SupplierUserAccount::getId)).collect(Collectors.toList());
        //前端控制数据的逻辑删除所以需要吧逻辑删除数据置空
        if (CollectionUtils.isNotEmpty(newList)){
            newList.forEach((SupplierUserAccount item)->{
                if(item.getDelFlag()){
                    item.setAccountName("").setAccountNum("")
                            .setBankDeposit("").setRemark("")
                            .setProvinceName("").setCityName("")
                            .setDistrictName("").setType(null);
                   }
            });
        }
        List<SupplierUserAccount> oldList = supplierUserAccountListOld.stream().sorted(Comparator.comparing(SupplierUserAccount::getId)).collect(Collectors.toList());
        StringBuilder stringBuilder = new StringBuilder();
        //获取两个集合最大的数量
        int max = Integer.max(newList.size(), oldList.size());
        //当两个list数量一样
        for (int i = 0; i < max; i++) {
            SupplierUserAccount supplierUserAccountNew;
            SupplierUserAccount supplierUserAccountOld;
            if (newList.size() <= i) {
                supplierUserAccountNew = new SupplierUserAccount();
            } else {
                supplierUserAccountNew = newList.get(i);
            }
            if (oldList.size() <= i) {
                supplierUserAccountOld = new SupplierUserAccount();
            } else {
                supplierUserAccountOld = oldList.get(i);
            }
            Map<String, Map<String, String>> transformationMap = new HashMap<>(1);
            Map<String, String> map = AccountTypeEnum.getMap();
            transformationMap.put("type", map);
            String different = getDifferent(supplierUserAccountOld, supplierUserAccountNew, SupplierUserAccountEnum.getMap(), transformationMap);
            stringBuilder.append(different);
        }
        return stringBuilder.toString();
    }


    /**
     * 比较supplierUserContactList的差异
     *
     * @param supplierUserContactListNew
     * @param supplierUserContactListOld
     * @return
     */
    private String getSupplierUserContactList(List<SupplierUserContact> supplierUserContactListNew, List<SupplierUserContact> supplierUserContactListOld) {
        if (CollectionUtils.isEmpty(supplierUserContactListNew) && CollectionUtils.isEmpty(supplierUserContactListOld)) {
            return "";
        }

        if (CollectionUtils.isNotEmpty(supplierUserContactListNew)){
            supplierUserContactListNew.forEach((SupplierUserContact item)->{
                if(item.getDelFlag()){
                    item.setName("").setPhone("")
                            .setRemark("");
                }
            });
        }

        StringBuilder stringBuilder = new StringBuilder();
        //获取两个集合最大的数量
        int max = Integer.max(supplierUserContactListNew.size(), supplierUserContactListOld.size());
        //当两个list数量一样
        for (int i = 0; i < max; i++) {
            SupplierUserContact supplierUserContactNew;
            SupplierUserContact supplierUserContactOld;
            if (supplierUserContactListNew.size() <= i) {
                supplierUserContactNew = new SupplierUserContact();
            } else {
                supplierUserContactNew = supplierUserContactListNew.get(i);
            }
            if (supplierUserContactListOld.size() <= i) {
                supplierUserContactOld = new SupplierUserContact();
            } else {
                supplierUserContactOld = supplierUserContactListOld.get(i);
            }
            String different = getDifferent(supplierUserContactOld, supplierUserContactNew, SupplierUserContactEnum.getMap(), null);
            stringBuilder.append(different);
        }
        return stringBuilder.toString();
    }


    /**
     * 获取两个实体不同
     *
     * @param oldEntity
     * @param newEntity
     * @param paramMap
     * @return
     */
    private String getDifferent(Object oldEntity, Object newEntity, Map paramMap, Map<String, Map<String, String>> transformationMap) {
        //封装比较参数
        LogDifferences logDifferences = new LogDifferences();
        logDifferences.setOldEntity(oldEntity)
                .setTransformationMap(transformationMap)
                .setNewEntity(newEntity)
                .setParamMap(paramMap);
        String comment = null;
        try {
            comment = ReflexUtils.entityComparison(logDifferences);
        } catch (Exception e) {
            log.error("供应商操作日志记录异常{}", e.getMessage(), e);
        }
        return comment;
    }


    /**
     * 日志记录文件的差异
     *
     * @param attachmentListNew
     * @param attachmentListOld
     * @return
     */
    private String getAttachment(List<AttachmentInfo> attachmentListNew, List<AttachmentInfo> attachmentListOld) {
        StringBuilder builder = new StringBuilder();
        StringJoiner joinerOld = new StringJoiner("，");
        if (CollectionUtils.isNotEmpty(attachmentListOld)) {
            attachmentListOld.forEach((AttachmentInfo item) -> {
                String fileName = item.getFileName();
                joinerOld.add(fileName);
            });
        }
        StringJoiner joinerNew = new StringJoiner("，");
        if (CollectionUtils.isNotEmpty(attachmentListNew)) {
            attachmentListNew.forEach((AttachmentInfo item) -> {
                String fileName = item.getFileName();
                joinerNew.add(fileName);
            });
        }
        if (!joinerNew.toString().equals(joinerOld.toString())) {
            builder.append("文件由【").append(joinerOld.toString()).append("】修改为【").append(joinerNew.toString()).append("】");
        }
        return builder.toString();
    }


}

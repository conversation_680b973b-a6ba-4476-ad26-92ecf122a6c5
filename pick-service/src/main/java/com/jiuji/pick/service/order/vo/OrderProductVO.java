package com.jiuji.pick.service.order.vo;

import com.jiuji.cloud.stock.vo.response.StockChwInfo;
import com.jiuji.pick.common.enums.ProductTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderProductVO {


    /**
     * 上一个订单的价格
     */
    private BigDecimal lastOrderPrice;
    /**
     * 九机商品库存情况
     */
    private String stockInfo;
    /**
     * 商品库存信息
     */
    private List<StockChwInfo> stockChwInfos;

    /**
     * 商品详情id
     */
    private Integer orderDetailInfoId;

    /**
     * 商品图片
     */
    private String productImg;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品分类
     */
    private String productColor;

    /**
     * 商品类型
     * @see ProductTypeEnum
     */
    private Integer productType;
    /**
     * 商品分类
     */
    private Long productCid;
    private String productCidName;
    /**
     * 商品条码
     */
    private String productBarCode;
    /**
     * 商品id
     */
    private Long productId;
    /**
     * ppid
     */
    private Long ppid;
    /**
     * 商品价格
     */
    private BigDecimal productPrice;
    /**
     * 商品价格类型
     */
    private Integer priceType;
    /**
     * 购买数量
     */
    private Integer buyAmount;

    /**
     * 发货数量
     */
    private Integer deliveryCount;

    /**
     * 商品资料ids
     */
    private String attachmentIds;

    /**
     * 商品资料
     */
    private List<AttachmentInfoData> attachmentInfoList;

    /**
     * 供应商产品ID
     */
    private Long supplierProductId;

    /**
     * 物流费
     */
    private BigDecimal deliveryFee;

    /**
     * 商品资料实体
     */
    @Data
    public static class AttachmentInfoData {
        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 文件路径
         */
        private String filePath;
    }
}

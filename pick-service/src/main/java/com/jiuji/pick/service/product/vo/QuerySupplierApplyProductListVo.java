package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @function:
 * @description: QuerySupplierApplyProductListVo.java
 * @date: 2021/05/07
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class QuerySupplierApplyProductListVo {

    private Long productId;
    private Long ppid;
    private String productName;
    private Long categoryId;
    private String categoryName;
    private LocalDateTime productUpTime;
    /**
     * 物料
     */
    private String material;
    private String materialName;
    /**
     * 采购未税单价
     */
    private BigDecimal buyNoTaxPrice;

    /**
     * 采购含税单价
     */
    private BigDecimal buyTaxPrice;

    /**
     * 预估利润
     */
    private BigDecimal predictProfit;

    /**
     * 库存
     */
    private Integer stockCount;

    private BigDecimal advicePrice;
    private Integer bindStatus;
    private String bindStatusName;
    private String remark;
    private LocalDateTime createTime;

}

package com.jiuji.pick.service.product.vo;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.pick.service.product.entity.BrandCategory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 将品牌绑定到分类的VO
 * @since 2019-12-19 13:11
 */
@EqualsAndHashCode(callSuper = false)
@Data
@NoArgsConstructor
public class BindBrandToCategoryVO extends Model<BrandCategory> implements Serializable {

    private static final long serialVersionUID = -5953712624161098146L;

    private Integer categoryId;
    private List<BrandBindWithCategoryVO> brandBindWithCategoryList;
}

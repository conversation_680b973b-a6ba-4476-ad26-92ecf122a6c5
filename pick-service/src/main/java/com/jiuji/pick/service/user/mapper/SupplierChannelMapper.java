package com.jiuji.pick.service.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.vo.SupplierChannelCountVO;
import com.jiuji.pick.service.product.vo.SupplierProductCountVO;
import com.jiuji.pick.service.user.entity.SupplierChannel;
import com.jiuji.pick.service.user.vo.SupplierChannelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商渠道关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
public interface SupplierChannelMapper extends BaseMapper<SupplierChannel> {

    /***
     * @description: 统计供应商绑定的渠道数量
     * @Param: [supplierIdList]
     * @author: Lbj
     * @date: 2021/5/7 14:22
     */
    List<SupplierChannelCountVO> supplierChannelCount(@Param("supplierIdList") List<Long> supplierIdList);

    /***
     * @description: 供应商绑定的渠道列表
     * @Param: [supplierIdList]
     * @author: Lbj
     * @date: 2021/5/7 14:22
     */
    List<SupplierChannelVO> listBySupplierId(@Param("supplierId") Long supplierId);
}

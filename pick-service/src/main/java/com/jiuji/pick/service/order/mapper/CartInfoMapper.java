package com.jiuji.pick.service.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.order.bo.SupplierProductInfoBO;
import com.jiuji.pick.service.order.entity.CartInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 购物车信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-06
 */
public interface CartInfoMapper extends BaseMapper<CartInfo> {

    /**
     * 获取供应商信息
     *
     * @param productId
     * @param supplierUserId
     * @return
     */
    SupplierProductInfoBO getSupplierProductInfo(@Param("productId") Long productId,
                                                 @Param("supplierUserId") Long supplierUserId);

    /**
     * 根据商品id集合查询商品信息
     *
     * @param productDetailIdList
     * @return
     */
    List<SupplierProductInfoBO> getSupplierProductInfoByIdList(@Param("list") List<Long> productDetailIdList);

    /**
     * 查询购物车列表
     *
     * @param cartIdList
     * @return
     */
    List<CartInfo> selectByIds(@Param("list")List<Long> cartIdList);
}

/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.vo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ProductCsVO extends Model<ProductCsVO> {


    private Integer id;
    /**
     * 商品ID
     */
    private Long productId;

    /**
     * product_cs_basic对应ID
     */
    private Integer csid;

    /**
     * 商品属性值
     */
    private String csValue;

    private Boolean isDisplay;

    /**
     * 1--productid是商品id ；0--productid是ppid
     */
    private Integer type;

    /**
     * 0---主分类参数 1-- 扩展分类参数
     */
    private Integer cateType;

    private Long ppriceid;


}

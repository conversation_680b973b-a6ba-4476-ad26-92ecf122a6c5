package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_product_price")
public class ProductPrice extends Model<ProductPrice> {

    private static final long serialVersionUID=1L;

    /**
     * 规格ID
     */
    @TableId(value = "ppriceid", type = IdType.AUTO)
    private Long ppriceid;

    /**
     * 商品ID
     */
    private Long productid;

    /**
     * 会员价
     */
    private Double memberPrice;

    private Double vipPrice;

    /**
     * 采购价
     */
    private Double costPrice;

    /**
     * 配置
     */
    private String config;

    /**
     * 0正常，1缺货，2下市
     */
    private Integer que;

    /**
     * 排序
     */
    @TableField("`rank`")
    private Integer rank;

    /**
     * 默认图片小图
     */
    private String bpic;

    /**
     * 价格幅度
     */
    private Double pricefd;

    /**
     * 更新日期
     */
    private Date updatePriceDate;

    private Double priceflag;

    /**
     * 是否删除
     */
    @TableLogic
    private Boolean isdel;

    private Integer ppriceid1;

    private Integer viewsr;

    private Integer viewsWeekr;

    private Double marketPrice;

    /**
     * 0--无 1--热销 2--新品 3--特价 4--推荐 5--直降 6--爆款 7--热销标签
     * 8--新品标签 9--限量标签 10--推荐标签 11--特惠标签 12--爆款标签 13--上门 14--到货不定 15--活动机型
     */
    private Integer cues;

    /**
     * 特价 不能优惠和叠加优惠码
     */
    private Boolean noPromotion;

    private Integer buyLimit;

    private Integer proid;

    private Integer oldPpid;

    private Double pricedp;

    private LocalDateTime pricedptime;

    private String featureTitle;

    private String featureDesc;

    private String markpic;

    private String rgbcolor;

    private Integer initvalue;

    private Double times;

    private Boolean mainColor;

    /**
     * 实体小店价格
     */
    private Double oemPrice;

    /**
     * 69码
     */
    private String barCode;

    private Double vip2Price;

    /**
     * &1>0 代表溢价商品规格  &2>0 代表支持回收的规格   &1废除，目前用作回收标识
     */
    private Integer attributes;

    /**
     * 稀缺
     */
    private Boolean scarcity;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 清库 （回收使用）
     */
    private Integer destock;

    /**
     * 干预小店/实体小店价（0-已页面传入为准 1-自动计算）
     */
    private Integer interveneShopPrice;

    private Integer saleChannel;

    /**
     * 是否启用条码
     */
    private Boolean isBarCode;

    /**
     * 商品标签
     */
    private Integer pLabel;

    private String productColor;

    /**
     * 开售时间
     */
    private LocalDateTime saleStartTime;


    @Override
    protected Serializable pkVal() {
        return this.ppriceid;
    }

}

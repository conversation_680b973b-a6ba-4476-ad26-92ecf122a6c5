package com.jiuji.pick.service.order.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 采购单详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_order_detail_info")
public class OrderDetailInfo extends Model<OrderDetailInfo> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合作伙伴ID
     */
    private Long partnerId;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 购物车ID
     */
    private Long cartId;

    /**
     * 订单表主键
     */
    private Long orderId;

    /**
     * 订单号
     */
    private Long orderNo;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品详情ID
     */
    private Long productDetailId;

    /**
     * 商品价格
     */
    private BigDecimal productPrice;

    /**
     * 购买数量
     */
    private Integer buyAmount;

    /**
     * 发货数量
     */
    private Integer deliveryCount;

    /**
     * 物流费
     */
    private BigDecimal deliveryFee;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

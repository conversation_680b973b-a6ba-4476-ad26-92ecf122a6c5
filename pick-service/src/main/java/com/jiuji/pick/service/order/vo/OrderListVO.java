package com.jiuji.pick.service.order.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单列表VO
 *
 * <AUTHOR>
 * @since 2021-05-20
 */
@Data
public class OrderListVO {

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 采购单号
     */
    private Long orderNo;
    /**
     * 销售人员工号
     */
    private Integer saleJobNumber;
    /**
     * 销售人员名称
     */
    private String saleName;
    /**
     * 采购单名称
     */
    private String orderTitle;
    /**
     * 门店名称
     */
    private String receivePoiName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    /**
     * 金额
     */
    private BigDecimal totalPrice;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 销售单号
     */
    private String saleOrderNo;

    /**
     * 购买或作伙伴名称
     */
    private String partnerName;

    /**
     * 总物流费
     */
    private BigDecimal totalDeliveryFee;

    /**
     * 价格类型
     */
    private Integer priceType;
    /**
     * 价格类型
     */
    private String priceTypeValue;

}

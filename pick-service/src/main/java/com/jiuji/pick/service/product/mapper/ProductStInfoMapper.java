package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.bo.ProductStDBO;
import com.jiuji.pick.service.product.entity.ProductStInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductStInfoMapper extends BaseMapper<ProductStInfo> {


    List<ProductStDBO> getProductAllSpecByPid(@Param("productId") Long productId);
}

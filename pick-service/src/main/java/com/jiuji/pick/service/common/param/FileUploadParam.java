package com.jiuji.pick.service.common.param;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @function:
 * @description: ProductFileUploadParam.java
 * @date: 2021/04/30
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class FileUploadParam {

    @NotBlank(message = "fid不能为空")
    private String fid;
    @NotBlank(message = "文件名称不能为空")
    private String fileName;
    @NotBlank(message = "文件路径不能为空")
    private String fileUrl;

}

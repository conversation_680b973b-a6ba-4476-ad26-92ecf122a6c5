package com.jiuji.pick.service.order.service.impl;

import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.service.order.service.SmallService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SmallServiceImpl implements SmallService {
    @Override
    public String getJumpUrl(PartnerTokenInfo tokenInfo, Long orderNumber) {
        //获取小型跳转租户的域名
        String host = Optional.ofNullable(tokenInfo.getOaHost())
                .orElseThrow(()-> new BizException("系统升级,请先退出登录采货王之后重新登录")) ;
        if(StringUtils.isEmpty(host)){
            return null;
        }
        return "https://"+host+"/#/purchase/"+orderNumber;
    }
}

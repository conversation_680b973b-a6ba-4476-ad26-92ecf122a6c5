package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.service.product.bo.AdmSkuBO;
import com.jiuji.pick.service.product.entity.ProductPrice;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductPriceService extends IService<ProductPrice> {

    /**
     * 批量修改sku
     */
    Result<List<AdmSkuBO>> batchUpdateSku(List<AdmSkuBO> admSkuBOList, boolean isSync);

    /**
     * 将ProductInRedis放入缓存
     */
    void setProductInRedisToCache(long ppid);
}

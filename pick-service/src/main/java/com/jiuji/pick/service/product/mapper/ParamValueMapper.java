/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jiuji.pick.service.product.bo.ProductCsParamDTO;
import com.jiuji.pick.service.product.entity.ParamValue;
import com.jiuji.pick.service.product.vo.SearchResultVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
public interface ParamValueMapper extends BaseMapper<ParamValue> {

    int insertParamValue(ParamValue paramValue);

    /**
     * 获取分类的选择项（单选、多选）参数
     */
    List<SearchResultVo.Screening> getCateSelectParamsScreening(@Param("cid") int category);

    /**
     * 获取商品的选项参数（多选的参数）
     */
    List<String> getProductSelectParam(@Param("productId") Long productId);

    /**
     * 根据ids获取String
     */
    @Select("select id,value from t_param_value ${ew.customSqlSegment}")
    List<ProductCsParamDTO> getParamValueByIds(@Param(Constants.WRAPPER) QueryWrapper wrapper);
}

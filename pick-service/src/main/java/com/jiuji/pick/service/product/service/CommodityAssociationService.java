package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.entity.CommodityAssociation;
import com.jiuji.pick.service.product.vo.AssociationTerms;
import com.jiuji.pick.service.product.vo.AssociationVo;
import com.jiuji.pick.service.product.vo.DeleteInfoVo;
import com.jiuji.pick.service.product.vo.PageInfoVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface CommodityAssociationService extends IService<CommodityAssociation> {

    /**
     * 保存或者修改关联信息
     * @param associationVo
     * @return
     */
    Result<String> saveOrModify(AssociationVo associationVo);

    /**
     * 删除
     * @param deleteInfoVo
     * @return
     */
    Result<String> deleteInfo(@RequestBody DeleteInfoVo deleteInfoVo);

    /**
     * 分页查询
     * @param pageInfoVo
     * @return
     */
    Result<IPage<CommodityAssociation>> pageInfo(PageInfoVo pageInfoVo);

    /**
     * 联想词搜索
     * @param key
     * @return
     */
    Result<List<AssociationTerms>>getAssociationTerms(String key);
    /**
     * 联想词搜索
     * @param key
     * @return
     */
    Result<List<AssociationTerms>>getAssociationTermsV2(String key);
}

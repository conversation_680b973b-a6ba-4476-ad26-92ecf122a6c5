/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.service.product.entity.ProductImage;
import com.jiuji.pick.service.product.mapper.ProductImageMapper;
import com.jiuji.pick.service.product.mapper.ProductInfoMapper;
import com.jiuji.pick.service.product.service.ProductImageService;
import com.jiuji.pick.service.product.service.ProductInfoService;
import com.jiuji.pick.service.product.vo.ProductImageUpdateParam;
import com.jiuji.pick.service.product.vo.ProductImageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2017-08-25
 */
@Service
@Slf4j
public class ProductImageServiceImpl extends
        ServiceImpl<ProductImageMapper, ProductImage> implements ProductImageService {


    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ProductInfoMapper productInfoMapper;

    @Resource
    private ProductInfoService productInfoService;



    @Override
    public boolean updateProductImage(ProductImageUpdateParam param, boolean isSynchronize) {
        List<Integer> updateIds = new ArrayList<>();
        // 修改旧数据
        param.getList().stream().filter(li -> li.getId() != null && li.getId() != 0)
                .forEach(li -> {
                    ProductImage image = new ProductImage();
                    image.setId(li.getId());
                    image.setImageName(li.getName());
                    image.setImagePath(li.getUrl());
                    image.setProductid(li.getProductId());
                    image.setRank(li.getRank());
                    image.setImgType(li.getType());
                    if (li.getSelectFlag() != null && li.getSelectFlag()) {
                        image.setSelectFlag(1);
                    } else {
                        image.setSelectFlag(0);
                    }
                    image.setAreaCode(li.getAreaCode());
                    image.updateById();
                    updateIds.add(li.getId());
                });
        // 删除不要了的数据
        this.list(new QueryWrapper<ProductImage>().eq(ProductImage.PRODUCTID, param.getProductId())
                .eq(ProductImage.IMGTYPE, param.getType()))
                .stream().filter(li -> !updateIds.contains(li.getId())).forEach(Model::deleteById);
        // 保存新数据
        param.getList().stream().filter(li -> li.getId() == null || li.getId() == 0)
                .forEach(li -> {
                    ProductImage image = new ProductImage();
                    image.setImageName(li.getName());
                    image.setImagePath(li.getUrl());
                    image.setProductid(li.getProductId());
                    image.setRank(li.getRank());
                    image.setAreaCode(li.getAreaCode());
                    image.setImgType(li.getType());
                    image.insert();
                });
        if (StringUtils.isNotBlank(param.getPpids())) {  //同步其他ppid
            List<Long> ppidList = CommonUtil.covertIdStr2Long(param.getPpids());
            List<ProductImage> list = this
                    .list(new QueryWrapper<ProductImage>().in(ProductImage.PRODUCTID, ppidList)
                            .eq(ProductImage.IMGTYPE, param.getType()));
            if (CollectionUtils.isEmpty(list)) {  //插入操作
                for (Long ppid : ppidList) {
                    for (ProductImageVO vo : param.getList()) {
                        ProductImage image = new ProductImage();
                        image.setImageName(vo.getName());
                        image.setImagePath(vo.getUrl());
                        image.setProductid(ppid);
                        image.setRank(vo.getRank());
                        image.setAreaCode(vo.getAreaCode());
                        image.setImgType(vo.getType());
                        if (vo.getSelectFlag() != null && vo.getSelectFlag()) {
                            image.setSelectFlag(1);
                        } else {
                            image.setSelectFlag(0);
                        }
                        image.insert();
                    }

                }
            } else { // 更新操作
                List<Integer> ids = list.stream().map(r -> r.getId()).collect(Collectors.toList());
                baseMapper.deleteBatchIds(ids);
                for (Long ppid : ppidList) {
                    for (ProductImageVO vo : param.getList()) {
                        ProductImage image = new ProductImage();
                        image.setImageName(vo.getName());
                        image.setImagePath(vo.getUrl());
                        image.setProductid(ppid);
                        image.setRank(vo.getRank());
                        image.setAreaCode(vo.getAreaCode());
                        image.setImgType(vo.getType());
                        if (vo.getSelectFlag() != null && vo.getSelectFlag()) {
                            image.setSelectFlag(1);
                        } else {
                            image.setSelectFlag(0);
                        }
                        image.insert();
                    }
                }
            }

        }
        return true;
    }
}

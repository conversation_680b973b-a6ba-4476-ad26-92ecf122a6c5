package com.jiuji.pick.service.product.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.config.yml.SystemConfig;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.OperateLogInfo;
import com.jiuji.pick.service.common.service.NewOperateLogFactory;
import com.jiuji.pick.service.common.service.OperateLogInfoService;
import com.jiuji.pick.service.product.entity.*;
import com.jiuji.pick.service.product.mapper.SupplierProductDetailMapper;
import com.jiuji.pick.service.product.param.*;
import com.jiuji.pick.service.product.service.*;
import com.jiuji.pick.service.product.vo.QuerySupplierApplyProductListVo;
import com.jiuji.pick.service.product.vo.QuerySupplierProductListVo;
import com.jiuji.pick.service.rpc.vo.OaStockVo;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.service.SupplierUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Slf4j
@Service
public class SupplierProductDetailServiceImpl extends ServiceImpl<SupplierProductDetailMapper, SupplierProductDetail> implements SupplierProductDetailService {

    @Resource
    private ProductBindService productBindService;
    @Resource
    private OperateLogInfoService operateLogInfoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private SupplierUserService supplierUserService;
    @Resource
    private PickProductService pickProductService;
    @Resource
    private RefreshCacheDataService refreshCacheDataService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private SystemConfig systemConfig;
    @Resource
    private SupplierProductPriceModifyService priceModifyService;
    @Resource
    private NewOperateLogFactory logFactory;

    @Override
    public Result<String> addOrUpdateSupplierProduct(SupplierAddOrUpdateProductParam param) {
        if(param == null) {
            return Result.errorInfo("请求参数错误");
        }
        try {
            // 判断用户是否登录
            SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
            if(supplierTokenInfo == null) {
                return Result.notLoginError();
            }
            // 判断供应商是否存在以及是否审核通过
            SupplierUser supplierUser = supplierUserService.getById(supplierTokenInfo.getId());
            if(supplierUser == null) {
                return Result.errorInfo("用户未注册或已删除");
            }
            if(supplierUser.getStatus() != SupplierUserStatusEnum.PASS.getCode()) {
                return Result.errorInfo("用户还没有审核通过，请审核通过后在进行操作");
            }
            // 查询商品信息是否存在
            SupplierProductDetail supplierProductDetail = this.getOne(new LambdaQueryWrapper<SupplierProductDetail>().eq(SupplierProductDetail::getPpid, param.getPpid()).eq(SupplierProductDetail::getSupplierId, supplierTokenInfo.getId()));
            if(supplierProductDetail == null) {
                supplierProductDetail = new SupplierProductDetail();
            }

            // 判断供应商是申请商品供货还是修改商品信息
            boolean isAdd = supplierProductDetail.getId() == null ? Boolean.TRUE : Boolean.FALSE;
            SupplierProductDetail oldSupplierProductDetail = new SupplierProductDetail();
            if (!isAdd) {
                BeanUtils.copyProperties(supplierProductDetail, oldSupplierProductDetail);
            }

            supplierProductDetail = this.buildSupplierProductDetail(supplierProductDetail, param, supplierTokenInfo);
            // 保存产品信息
            boolean saveResult = this.saveOrUpdate(supplierProductDetail);
            if(saveResult) {
                // 处理商品改价信息
                if (!isAdd) {
                    Boolean modify = this.saveSupplierProductPriceModify(oldSupplierProductDetail, param);
                    if (Boolean.FALSE.equals(modify)) {
                        log.info("供应商未对价格进行修改");
                    } else {
                        log.info("供应商对商品价格进行修改,商品详情Id:{}",oldSupplierProductDetail.getId());
                    }
                }
                // 保存供应商和产品绑定关系
                ProductBind productBind = productBindService.getOne(new LambdaQueryWrapper<ProductBind>().eq(ProductBind::getPpid, param.getPpid()).eq(ProductBind::getSupplierId, supplierTokenInfo.getId()));
                if(productBind == null) {
                    productBind = new ProductBind();
                }
                productBind = this.buildProductBind(productBind, param, supplierTokenInfo, supplierProductDetail);
                productBindService.saveOrUpdate(productBind);
                // 如果是修改绑定状态变成待审核，判断商品是否还有绑定的供应商，没有下架商品
                int bindCount = productBindService.count(new LambdaQueryWrapper<ProductBind>().eq(ProductBind::getPpid, productBind.getPpid()).eq(ProductBind::getBindStatus, BindStatusEnum.BIND.getCode()));
                if(bindCount < 1) {
                    // 下架商品
                    PickProduct pickProduct = pickProductService.getOne(new LambdaQueryWrapper<PickProduct>().eq(PickProduct::getPpid, param.getPpid()).eq(PickProduct::getProductStatus, ProductStatusEnum.UP.getCode()).last("limit 1"));
                    if(pickProduct != null) {
                        pickProduct.setProductStatus(ProductStatusEnum.DOWN.getCode());
                        pickProduct.setUpdateTime(LocalDateTime.now());
                        pickProductService.saveOrUpdate(pickProduct);
                        // 记录商品下架操作日志
                        OperateLogInfo productDownOperateLogInfo = OperateLogInfo.builder()
                                .relateId(String.valueOf(param.getPpid()))
                                .type(ModuleEnum.PRODUCT.getCode())
                                .userId(supplierUser.getId())
                                .optUserId(0L)
                                .optUserName("系统")
                                .content("供应商修改商品，商品绑定状态变为待审核，当前商品只绑定了该供应商，系统自动下架商品")
                                .showType(LogShowTypeEnum.ADMIN.getCode())
                                .build();
                        operateLogInfoService.saveOrUpdate(productDownOperateLogInfo);
                    }
                }
                // 刷新ES数据
                refreshCacheDataService.refreshEsData(null, ImmutableList.of(productBind.getId()), CommonConstant.DELETE_PRODUCT);
                // 记录操作日志
                OperateLogInfo operateLogInfo = OperateLogInfo.builder()
                        .relateId(String.valueOf(param.getPpid()))
                        .type(ModuleEnum.PRODUCT.getCode())
                        .userId(productBind.getSupplierId())
                        .optUserId(supplierTokenInfo.getId())
                        .optUserName(supplierTokenInfo.getLoginName())
                        .content(isAdd ? "供应商申请供货补充商品信息" : "供应商修改供货商品信息")
                        .showType(LogShowTypeEnum.ALL_USER.getCode())
                        .build();
                operateLogInfoService.save(operateLogInfo);
            }
            //操作日志记录详情记录
            SupplierAddOrUpdateProductParamOld paramOld = param.getParamOld();
            if(paramOld!=null){
                logFactory.systemSaveLog(param, NewOperateLogInfoTypeEnum.SUPPLY_MANAGEMENT.getCode());
            }
            return Result.successInfo("保存成功");
        } catch (Exception e) {
            log.error("供应商添加商品发生异常，param:{},exception:", JSONUtil.toJsonStr(param), e);
            return Result.errorInfo("服务异常，稍后再试");
        }
    }

    @Override
    public Result<Page<QuerySupplierProductListVo>> querySupplierProductList(QuerySupplierProductListParam param) {
        if(param == null) {
            return Result.errorInfo("请求参数错误");
        }
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if(supplierTokenInfo == null) {
            return Result.notLoginError();
        }
        if(param.getCategoryId() != null) {
            List<Long> categoryIdList = Lists.newArrayList();
            categoryIdList.add(param.getCategoryId());
            List<Category> categoryList = categoryService.getChildCategory(param.getCategoryId().intValue());
            if(CollectionUtils.isNotEmpty(categoryList)) {
                List<Long> childCategoryIdList = categoryList.stream().map(category -> Long.valueOf(category.getId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(childCategoryIdList)) {
                    categoryIdList.addAll(childCategoryIdList);
                }
            }
            param.setCategoryIdList(categoryIdList);
        }
        param.setSupplierId(supplierTokenInfo.getId());
        Page<QuerySupplierProductListVo> supplierProductListVoPage = baseMapper.querySupplierProductList(new Page<>(param.getCurrentPage(), param.getSize()), param);
        if(supplierProductListVoPage == null || CollectionUtils.isEmpty(supplierProductListVoPage.getRecords())) {
            return Result.noData();
        }
        supplierProductListVoPage.getRecords().forEach(product -> product.setProductStatusName(ProductStatusEnum.getProductStatusName(product.getProductStatus())));
        return Result.success(supplierProductListVoPage);
    }

    @Override
    public Result<Page<QuerySupplierApplyProductListVo>> querySupplierApplyProductList(QuerySupplierApplyProductParam param) {
        if(param == null) {
            return Result.errorInfo("请求参数错误");
        }
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if(supplierTokenInfo == null) {
            return Result.notLoginError();
        }
        if(param.getCategoryId() != null) {
            List<Long> categoryIdList = Lists.newArrayList();
            categoryIdList.add(param.getCategoryId());
            List<Category> categoryList = categoryService.getChildCategory(param.getCategoryId().intValue());
            if(CollectionUtils.isNotEmpty(categoryList)) {
                List<Long> childCategoryIdList = categoryList.stream().map(category -> Long.valueOf(category.getId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(childCategoryIdList)) {
                    categoryIdList.addAll(childCategoryIdList);
                }
            }
            param.setCategoryIdList(categoryIdList);
        }
        param.setSupplierId(supplierTokenInfo.getId());
        Page<QuerySupplierApplyProductListVo> supplierApplyProductListVoPage = baseMapper.querySupplierApplyProductList(new Page<>(param.getCurrentPage(), param.getSize()), param);
        if(supplierApplyProductListVoPage == null || CollectionUtils.isEmpty(supplierApplyProductListVoPage.getRecords())) {
            return Result.noData();
        }
        //设置状态
        supplierApplyProductListVoPage.getRecords().forEach(product -> product.setBindStatusName(BindStatusEnum.getStatusName(product.getBindStatus())));
        // 设置预估利润 (先取未税取不到那就是含税)
        supplierApplyProductListVoPage.getRecords().forEach(product -> product.setPredictProfit(product.getAdvicePrice().subtract(Optional.ofNullable(product.getBuyTaxPrice()).orElse(product.getBuyNoTaxPrice()))));
        // 设置物料
        supplierApplyProductListVoPage.getRecords().forEach(product -> {
            if(StringUtils.isNotBlank(product.getMaterial())) {
                List<String> materialNameList = Lists.newArrayList();
                Splitter.on(",").splitToList(product.getMaterial()).forEach(material -> materialNameList.add(MaterialEnum.getNameByValue(Integer.valueOf(material))));
                product.setMaterialName(Joiner.on(";").join(materialNameList));
            }
        });
        return Result.success(supplierApplyProductListVoPage);
    }

    @Override
    public Result<SupplierProductDetail> querySupplierProductDetail(QuerySupplierProductDetailParam param) {
        if(param == null) {
            return Result.errorInfo("请求参数错误");
        }
        // 判断用户是否登录
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if(supplierTokenInfo == null) {
            return Result.notLoginError();
        }
        SupplierProductDetail supplierProductDetail = this.getOne(new LambdaQueryWrapper<SupplierProductDetail>().eq(SupplierProductDetail::getPpid, param.getPpid()).eq(SupplierProductDetail::getSupplierId, supplierTokenInfo.getId()));
        if(supplierProductDetail == null) {
            return Result.noData();
        }
        return Result.success(supplierProductDetail);
    }

    @Override
    public List<SupplierProductDetail> noLogicList(Collection<Long> supplierProductIds) {
        if (CollectionUtils.isEmpty(supplierProductIds)) {
            return Collections.emptyList();
        }
        return baseMapper.noLogicList(supplierProductIds);
    }

    @Override
    public void syncStockAndSaleCount() {
        int pageSize = 200;
        int totalCount = pickProductService.count();
        if(totalCount == 0) {
            log.info("同步销量和库存，获取pickProduct结果为空");
            return;
        }
        List<Long> refreshPpidList = Lists.newArrayList();
        Map<Long, Integer> stockDataMap = Maps.newHashMap();
        // 处理销量
        int totalPage = PageUtil.totalPage(totalCount, pageSize);
        for (int i=1; i<= totalPage; i++) {
            IPage<PickProduct> pickProductPage = pickProductService.page(new Page<>(i, pageSize));
            if(pickProductPage == null || CollectionUtils.isEmpty(pickProductPage.getRecords())) {
                continue;
            }
            List<Long> ppidList = pickProductPage.getRecords().stream().map(PickProduct::getPpid).collect(Collectors.toList());
            OaStockVo oaStockVo = this.getStockAndSaleInfo(ppidList);
            if(oaStockVo == null || MapUtils.isEmpty(oaStockVo.getData())) {
                log.error("获取销量和库存为空，不处理数据，ppidList:{}", ppidList);
                continue;
            }
            pickProductPage.getRecords().forEach(pickProduct -> {
                Map<Long, OaStockVo.StockInfo> stockMap = oaStockVo.getData();
                OaStockVo.StockInfo stockInfo = stockMap.get(pickProduct.getPpid());
                if(stockInfo != null) {
                    pickProduct.setSaleCount(stockInfo.getSalesCount());
                    stockDataMap.put(pickProduct.getPpid(), stockInfo.getStock());
                }
            });
            pickProductService.saveOrUpdateBatch(pickProductPage.getRecords());
            refreshPpidList.addAll(ppidList);
        }
        // 处理库存
        int stockTotalCount = this.count(new LambdaQueryWrapper<SupplierProductDetail>().eq(SupplierProductDetail::getSupplierId, CommonConstant.SHENZHEN_JIUXUN_SUPPLIER_ID));
        if(stockTotalCount == 0) {
            log.info("同步销量和库存，获取九讯云供应商的product结果为空");
            return;
        }
        int stockTotalPage = PageUtil.totalPage(stockTotalCount, pageSize);
        for(int i=1; i<=stockTotalPage; i++) {
            IPage<SupplierProductDetail> supplierProductDetailIPage = this.page(new Page<>(i, pageSize), new LambdaQueryWrapper<SupplierProductDetail>().eq(SupplierProductDetail::getSupplierId, CommonConstant.SHENZHEN_JIUXUN_SUPPLIER_ID));
            if(supplierProductDetailIPage == null || CollectionUtils.isEmpty(supplierProductDetailIPage.getRecords())) {
                continue;
            }
            supplierProductDetailIPage.getRecords().forEach(supplierProductDetail -> {
                supplierProductDetail.setStockCount(stockDataMap.get(supplierProductDetail.getPpid()));
            });
            this.saveOrUpdateBatch(supplierProductDetailIPage.getRecords());
        }
        log.info("同步销量和库存数据完成。。。。。。。。");
        refreshCacheDataService.refreshEsData(refreshPpidList, null, CommonConstant.UPDATE_PRODUCT);
    }

    private OaStockVo getStockAndSaleInfo(List<Long> ppidList) {
        if(CollectionUtils.isEmpty(ppidList)) {
            return null;
        }
        String ciphertext = DigestUtils.md5Hex(DateUtil.stringParseLocalDate(LocalDateTime.now()));
        log.info("获取销量和库存Token:{}", ciphertext);
        String invokeResult = HttpRequest.post(systemConfig.getOaStockUrl())
                .header("token", ciphertext)
                .body(JSONUtil.toJsonStr(ppidList))
                .execute()
                .body();
        log.info("调用OA接口获取库存和销量，ppidList:{},token:{},result:{}", ppidList, ciphertext, invokeResult);
        if(StringUtils.isBlank(invokeResult)) {
            return null;
        }
        OaStockVo oaStockVo = JSONUtil.toBean(invokeResult, OaStockVo.class);
        if(oaStockVo == null || !Objects.equals(oaStockVo.getCode(), Result.SUCCESS)) {
            return null;
        }
        return oaStockVo;
    }

    private ProductBind buildProductBind(ProductBind productBind, SupplierAddOrUpdateProductParam param, SupplierTokenInfo supplierTokenInfo, SupplierProductDetail supplierProductDetail) {
        productBind.setProductId(param.getProductId());
        productBind.setSupplierId(supplierTokenInfo.getId());
        productBind.setPpid(param.getPpid());
        productBind.setSupplierProductId(supplierProductDetail.getId());
        productBind.setBindStatus(BindStatusEnum.WAIT.getCode());
        return productBind;
    }

    private SupplierProductDetail buildSupplierProductDetail(SupplierProductDetail supplierProductDetail, SupplierAddOrUpdateProductParam param, SupplierTokenInfo supplierTokenInfo) {
        supplierProductDetail.setProductId(param.getProductId());
        supplierProductDetail.setPpid(param.getPpid());
        supplierProductDetail.setSupplierId(supplierTokenInfo.getId());
        supplierProductDetail.setPaymentDay(param.getPaymentDay());
        supplierProductDetail.setMaterial(param.getMaterial());
        supplierProductDetail.setBoxRule(param.getBoxRule());
        supplierProductDetail.setQualityDate(param.getQualityDate());
        supplierProductDetail.setBuyNoTaxPrice(param.getBuyNoTaxPrice());
        supplierProductDetail.setBuyTaxPrice(param.getBuyTaxPrice());
        supplierProductDetail.setDeliveryDay(param.getDeliveryDay());
        supplierProductDetail.setNoReasonReturn(param.getNoReasonReturn());
        supplierProductDetail.setChangeDay(param.getChangeDay());
        supplierProductDetail.setBadPay(param.getBadPay());
        supplierProductDetail.setLackPay(param.getLackPay());
        supplierProductDetail.setAfterSalePolicy(param.getAfterSalePolicy());
        supplierProductDetail.setMinimumOrderQuantity(param.getMinimumOrderQuantity());
        supplierProductDetail.setOtherPolicy(param.getOtherPolicy());
        supplierProductDetail.setUpdateTime(LocalDateTime.now());
        Integer remoteDeliveryFee = Objects.isNull(param.getRemoteDeliveryFee()) ? MagicalValueConstant.INT_0 : param.getRemoteDeliveryFee();
        supplierProductDetail.setRemoteDeliveryFee(remoteDeliveryFee);
        return supplierProductDetail;
    }

    private Boolean saveSupplierProductPriceModify(SupplierProductDetail supplierProductDetail, SupplierAddOrUpdateProductParam param) {

        int noTax = Optional.ofNullable(supplierProductDetail.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO)
                .compareTo(Optional.ofNullable(param.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO));
        int tax = Optional.ofNullable(supplierProductDetail.getBuyTaxPrice()).orElse(BigDecimal.ZERO)
                .compareTo(Optional.ofNullable(param.getBuyTaxPrice()).orElse(BigDecimal.ZERO));
        // 判断是否有价格改动
        if (noTax != 0 || tax != 0) {
            // 查询出最新的一条改价记录
            SupplierProductPriceModify priceModify = priceModifyService.getSupplierProductPriceModify(supplierProductDetail.getId(), false, null);
            log.info("改价记录:{}", priceModify);
            // 该商品第一次进行改价
            if (ObjectUtil.isNull(priceModify)) {
                priceModify = new SupplierProductPriceModify();
                priceModify.setSupplierProductDetailId(supplierProductDetail.getId())
                        .setLastNoTaxPrice(supplierProductDetail.getBuyNoTaxPrice())
                        .setLastTaxPrice(supplierProductDetail.getBuyTaxPrice())
                        .setCurrentNoTaxPrice(param.getBuyNoTaxPrice())
                        .setCurrentTaxPrice(param.getBuyTaxPrice())
                        .setEnabled(false);
                priceModifyService.save(priceModify);
            } else {
                // 判断这条改价是否生效
                if (Boolean.TRUE.equals(priceModify.getEnabled())) {
                    SupplierProductPriceModify newPriceModify = new SupplierProductPriceModify();
                    newPriceModify.setSupplierProductDetailId(supplierProductDetail.getId())
                            .setLastNoTaxPrice(supplierProductDetail.getBuyNoTaxPrice())
                            .setLastTaxPrice(supplierProductDetail.getBuyTaxPrice())
                            .setCurrentNoTaxPrice(param.getBuyNoTaxPrice())
                            .setCurrentTaxPrice(param.getBuyTaxPrice())
                            .setEnabled(false);
                    priceModifyService.save(newPriceModify);
                } else {
                    // 不修改上次价格，只修改当前价格
                    priceModify.setCurrentNoTaxPrice(Optional.ofNullable(param.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO))
                            .setCurrentTaxPrice(Optional.ofNullable(param.getBuyTaxPrice()).orElse(BigDecimal.ZERO));
                    priceModify.setUpdateTime(LocalDateTime.now());
                    priceModifyService.updateById(priceModify);
                    log.info("供应商改价,审核未通过,不新增商品改价记录,只进行修改,供应商商品详情id:[{}],改价记录id:[{}]", supplierProductDetail.getId(), priceModify.getId());
                }
            }
            return true;
        }
        return false;
    }


}

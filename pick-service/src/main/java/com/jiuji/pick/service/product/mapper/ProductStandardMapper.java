package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.entity.ProductStandard;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductStandardMapper extends BaseMapper<ProductStandard> {

    /**
     * 指定唯一id进行插入，这个方法不要随便调用
     */
    @SqlParser(filter = true)
    int addProductStandard(ProductStandard productStandard);
}

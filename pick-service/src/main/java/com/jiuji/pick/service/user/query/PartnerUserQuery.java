package com.jiuji.pick.service.user.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.param.BasePageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 合作伙伴信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PartnerUserQuery extends BasePageParam {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    private Long id;

    /***
     * 搜索类型 1 名字 2 id 3 电话
     */
    private String searchType;

    /***
     * 关键字
     */
    private String keyWord;


    /**
     * 名称
     */
    private String name;


    /**
     * 联系电话
     */
    private String phone;

    /**
     * 严选负责人
     */
    private String headPerson;

    /**
     * 注册时间
     */
    private String registerTimeStart;
    /**
     * 注册时间
     */
    private String registerTimeEnd;


}

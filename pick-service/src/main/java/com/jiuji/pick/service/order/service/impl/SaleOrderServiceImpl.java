package com.jiuji.pick.service.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SaleAreaBo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.config.apollo.SysConfig;
import com.jiuji.pick.common.config.yml.SystemConfig;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.enums.LogisticsCompanyEnum;
import com.jiuji.pick.common.enums.OrderStatusEnum;
import com.jiuji.pick.common.utils.RegexUtils;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.NotifyLogInfo;
import com.jiuji.pick.service.common.service.NotifyLogInfoService;
import com.jiuji.pick.service.order.bo.SaleOrderParamBO;
import com.jiuji.pick.service.order.bo.SaleOrderResultBO;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.param.SalesOrderParam;
import com.jiuji.pick.service.order.service.OrderDetailInfoService;
import com.jiuji.pick.service.order.service.OrderInfoService;
import com.jiuji.pick.service.order.service.SaleOrderService;
import com.jiuji.pick.service.order.vo.CreateSaleOrderVo;
import com.jiuji.pick.service.product.entity.PickProduct;
import com.jiuji.pick.service.product.entity.ProductInfo;
import com.jiuji.pick.service.product.service.PickProductService;
import com.jiuji.pick.service.product.service.ProductInfoService;
import com.jiuji.pick.service.product.vo.BuildSaleOrderParam;
import com.jiuji.pick.service.rpc.cloud.LogisticsCloud;
import com.jiuji.pick.service.rpc.cloud.OaOfficeCloud;
import com.jiuji.pick.service.rpc.cloud.OaUserCloud;
import com.jiuji.pick.service.rpc.vo.CommonQueryRoutReq;
import com.jiuji.pick.service.rpc.vo.OaUserVo;
import com.jiuji.pick.service.rpc.vo.PickInfoVo;
import com.jiuji.pick.service.rpc.vo.QueryRoutTrackRes;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.service.PartnerUserService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.wcf.wcfclient.utils.MD5Util;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @function:
 * @description: SaleOrderServiceImpl.java
 * @date: 2021/07/01
 * @author: sunfayun
 * @version: 1.0
 */
@Service
@Slf4j
public class SaleOrderServiceImpl implements SaleOrderService {

    @Resource
    private SystemConfig systemConfig;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private OrderInfoService orderInfoService;
    @Resource
    private OrderDetailInfoService orderDetailInfoService;
    @Resource
    private OaUserCloud oaUserCloud;
    @Resource
    private PartnerUserService partnerUserService;
    @Resource
    private PickProductService pickProductService;
    @Resource
    private ProductInfoService productInfoService;
    @Resource
    private NotifyLogInfoService notifyLogInfoService;
    @Resource
    private LogisticsCloud logisticsCloud;
    @Resource
    private SysConfig sysConfig;

    @Resource
    private OaOfficeCloud oaOfficeCloud;

    @Override
    public R<QueryRoutTrackRes> queryRoute(CommonQueryRoutReq commonQueryRoutReq) {
        PartnerTokenInfo partnerTokenInfo = Optional.ofNullable(currentRequestComponent.getPartnerTokenInfoWithoutCheck())
                .orElse(new PartnerTokenInfo());
        String token = MD5Util.GetMD5Code(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        NotifyLogInfo notifyLogInfo = new NotifyLogInfo();
        commonQueryRoutReq.setMark("采货王：" + partnerTokenInfo.getXtenant());
        String param = JSONUtil.toJsonStr(commonQueryRoutReq);
        notifyLogInfo.setXTenant(partnerTokenInfo.getXtenant())
                .setNotifyParam(param+"Authorization:"+token)
                .setNotifyName("物流中台查询物流轨迹")
                .setCreateTime(LocalDateTime.now());
         R<QueryRoutTrackRes> queryRoutTrackResR = new R<>();
        long startTime = 0;
        //服务调用物流中台查询快递轨迹
        try {
            startTime=System.currentTimeMillis();
            queryRoutTrackResR = logisticsCloud.queryRoute(commonQueryRoutReq, token);
            int code = queryRoutTrackResR.getCode();
            if(code!=0){
                return R.error("物流中台异常:"+queryRoutTrackResR.getUserMsg());
            }
            QueryRoutTrackRes data = queryRoutTrackResR.getData();
            //把快递公司拼音转成中文
            data.setExpressName(LogisticsCompanyEnum.getChinese(data.getCom()));
        }catch (RetryableException r){
            long endTime=System.currentTimeMillis();
            log.error("物流中台查询物流轨迹超时{}",r.getMessage(),r);
            queryRoutTrackResR.setUserMsg("物流中台服务调用超时,请求时间为："+(endTime-startTime)+"ms");
            return R.error("物流中台查询物流轨迹超时");
        }catch (Exception e){
            log.error("物流中台查询物流轨迹异常{}",e.getMessage(),e);
            queryRoutTrackResR.setUserMsg("物流中台查询物流轨迹异常");
            return R.error("物流中台查询物流轨迹异常");
        }finally {
            notifyLogInfo.setNotifyResult(JSONUtil.toJsonStr(queryRoutTrackResR))
                    .setOrderNo(commonQueryRoutReq.getOrderNo())
                    .setUpdateTime(LocalDateTime.now());
            notifyLogInfoService.saveNotifyLogInfo(notifyLogInfo);
        }
        return queryRoutTrackResR;
    }

    /**
     * Apollo 初始花数据
     * @param createSaleOrderVo
     */
    private void initializedFromApollo(CreateSaleOrderVo createSaleOrderVo){
        createSaleOrderVo.setAreaCode(sysConfig.getSalesOrderAreaCode());
    }

    @Override
    public Result<String> createSaleOrder(CreateSaleOrderVo createSaleOrderVo) {
        //Apollo 初始花数据
        initializedFromApollo(createSaleOrderVo);
        String orderIds = createSaleOrderVo.getOrderIds();
        String areaCode = createSaleOrderVo.getAreaCode();
        if (StringUtils.isBlank(orderIds)) {
            return Result.errorInfo("订单号不能为空");
        }
        if (StringUtils.isBlank(areaCode)) {
            return Result.errorInfo("门店编号不能为空");
        }
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        //处理自动生成销售单没有登录信息的问题
        if(supplierTokenInfo==null && ObjectUtil.isNotNull(createSaleOrderVo.getSupplierId())){
            supplierTokenInfo = new SupplierTokenInfo();
            supplierTokenInfo.setId(createSaleOrderVo.getSupplierId());
        }
        if (supplierTokenInfo == null) {
            return Result.notLoginError();
        }
        // 判断是否是九讯供应商，只有九讯供应商需要该功能
        if (supplierTokenInfo.getId() != CommonConstant.SHENZHEN_JIUXUN_SUPPLIER_ID.longValue()) {
            return Result.errorInfo("生成销售单只针对九讯供应商开放");
        }
        List<Long> orderIdList = Splitter.on(",").splitToList(orderIds).stream().map(Long::new).collect(Collectors.toList());
        // 查询订单信息
        List<OrderInfo> orderInfoList = orderInfoService.list(new LambdaQueryWrapper<OrderInfo>().eq(OrderInfo::getSupplierId, supplierTokenInfo.getId()).in(OrderInfo::getId, orderIdList));
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return Result.errorInfo("订单不存在");
        }
        // 判断订单状态
        Optional<OrderInfo> orderInfoOptional = orderInfoList.stream().filter(orderInfo -> orderInfo.getOrderStatus() == OrderStatusEnum.CANCEL.getCode().intValue() || orderInfo.getOrderStatus() == OrderStatusEnum.DEFAULT.getCode().intValue()).findAny();
        if (orderInfoOptional.isPresent()) {
            return Result.errorInfo("采购单：" + orderInfoOptional.get().getOrderNo() + "已取消，不能生成销售单");
        }
        // 判断订单是否生成销售单
        Optional<OrderInfo> hasSaleOrderOptional = orderInfoList.stream().filter(orderInfo -> StringUtils.isNotBlank(orderInfo.getSaleOrderNo()) && StringUtils.isNumeric(orderInfo.getSaleOrderNo())).findAny();
        if (hasSaleOrderOptional.isPresent()) {
            return Result.errorInfo("采购单：" + hasSaleOrderOptional.get().getOrderNo() + "已生成销售单，不能重复生成");
        }
        // 查询订单详情
        List<OrderDetailInfo> orderDetailInfoList = orderDetailInfoService.list(new LambdaQueryWrapper<OrderDetailInfo>().eq(OrderDetailInfo::getSupplierId, supplierTokenInfo.getId()).in(OrderDetailInfo::getOrderId, orderIdList));
        if (CollectionUtils.isEmpty(orderDetailInfoList)) {
            return Result.errorInfo("订单明细不存在");
        }
        // 查询合作伙伴信息
        List<Long> partnerIdList = orderInfoList.stream().map(OrderInfo::getPartnerId).collect(Collectors.toList());
        List<PartnerUser> partnerUserList = partnerUserService.list(new LambdaQueryWrapper<PartnerUser>().in(PartnerUser::getId, partnerIdList));
        if (CollectionUtils.isEmpty(partnerUserList)) {
            return Result.errorInfo("合作伙伴信息不存在");
        }
        // 查询购买的商品信息
        List<Long> productIdList = orderDetailInfoList.stream().map(OrderDetailInfo::getProductId).collect(Collectors.toList());
        List<PickProduct> pickProductList = pickProductService.list(new LambdaQueryWrapper<PickProduct>().in(PickProduct::getId, productIdList));
        if (CollectionUtils.isEmpty(pickProductList)) {
            return Result.errorInfo("购买的商品信息不存在");
        }
        // 查询商品数据
        List<Long> ppidList = pickProductList.stream().map(PickProduct::getPpid).distinct().collect(Collectors.toList());
        List<ProductInfo> productInfoList = productInfoService.list(new LambdaQueryWrapper<ProductInfo>().in(ProductInfo::getPpriceid, ppidList));
        if (CollectionUtils.isEmpty(productInfoList)) {
            return Result.errorInfo("商品详细信息不存在");
        }
        Map<Long, ProductInfo> productInfoMap = productInfoList.stream().collect(Collectors.toMap(ProductInfo::getPpriceid, Function.identity()));
        Map<Long, PickProduct> pickProductMap = pickProductList.stream().collect(Collectors.toMap(PickProduct::getId, Function.identity()));
        Map<Long, PartnerUser> partnerUserMap = partnerUserList.stream().collect(Collectors.toMap(PartnerUser::getId, Function.identity()));
        Map<Long, List<OrderDetailInfo>> orderDetailInfoMap = orderDetailInfoList.stream().collect(Collectors.groupingBy(OrderDetailInfo::getOrderId));
        List<String> errorInfoList = Lists.newArrayList();
        List<OrderInfo> needUpdateOrderList = Lists.newArrayList();
        for (OrderInfo orderInfo : orderInfoList) {
            PartnerUser partnerUser = partnerUserMap.get(orderInfo.getPartnerId());
            if (partnerUser == null) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "对应的合作伙伴不存在");
                continue;
            }
            String businessPhone;

            Long xtenant = partnerUser.getXtenant();
//            if (xtenant == 50000) {
//                xtenant = 10050L;
//            }
            Result<List<PickInfoVo>> listResult = oaOfficeCloud.list9XunPick(Arrays.asList(xtenant));
            Long finalXtenant = xtenant;
            PickInfoVo pickInfoVo = listResult.getData().stream().filter(x -> finalXtenant.equals(x.getXtenant()))
                    .collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (Objects.nonNull(pickInfoVo) && StringUtils.isNotEmpty(pickInfoVo.getCustomerMobile())) {
                businessPhone = pickInfoVo.getCustomerMobile();
            } else {
                errorInfoList.add("合作档案：" + partnerUser.getName() + "对应的合作伙伴业务联系人查询不到");
                businessPhone = partnerUser.getBusinessPhone();
            }
            if (StringUtils.isBlank(businessPhone) || !RegexUtils.checkMobile(businessPhone)) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "对应的合作伙伴业务联系人手机号错误");
                continue;
            }
            Result<OaUserVo> oaUser = oaUserCloud.getUserByPhone(businessPhone, 0);
            log.info("生成销售单，调用OA接口获取用户ID,请求：{},返回：{}", businessPhone, JSONUtil.toJsonStr(oaUser));
            if (oaUser == null || oaUser.getData() == null || !oaUser.isSucceed()) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "对应的九机用户不存在");
                continue;
            }
            String saleName = Optional.ofNullable(partnerUser.getHeadPerson()).orElse("网络");
            Integer saleJobNumber = Optional.ofNullable(partnerUser.getHeadPersonId()).orElse(0);
            // 构建销售单数据
            BuildSaleOrderParam buildSaleOrderParam = new BuildSaleOrderParam();
            buildSaleOrderParam.setOrderInfo(orderInfo)
                    .setPickProductMap(pickProductMap)
                    .setOaUserVo(oaUser.getData())
                    .setProductInfoMap(productInfoMap)
                    .setOrderDetailInfoMap(orderDetailInfoMap)
                    .setSaleName(saleName)
                    .setAreaCode(areaCode);
            SaleOrderParamBO saleOrderParamBO = this.buildSaleOrderParamBO(buildSaleOrderParam);
            if (saleOrderParamBO == null) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "构建销售单数据失败");
                continue;
            }
            // 调用接口创建销售单
            String url = systemConfig.getWcfUrl() + "/oaApi.svc/rest/submitorder";
            String createSaleOrderResult = HttpRequest.post(url)
                    .header(Header.CONTENT_TYPE, ContentType.JSON.getValue())
                    .header(Header.ACCEPT, ContentType.JSON.getValue())
                    .body(JSON.toJSONString(saleOrderParamBO))
                    .execute()
                    .body();
            log.warn("创建销售单，请求参数：{}, 返回结果：{}", JSON.toJSONString(saleOrderParamBO), createSaleOrderResult);
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(orderInfo.getXtenantId()).orderNo(orderInfo.getOrderNo()).notifyName("生成销售单").notifyUrl(url).notifyParam(JSON.toJSONString(saleOrderParamBO)).notifyResult(createSaleOrderResult).build());
            if (StringUtils.isBlank(createSaleOrderResult) || !createSaleOrderResult.contains("stats")) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "调用OA生成销售单异常");
                continue;
            }
            SaleOrderResultBO saleOrderResultBO = JSONUtil.toBean(createSaleOrderResult, SaleOrderResultBO.class);
            if (saleOrderResultBO == null) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "调用OA生成销售单返回结果错误");
                continue;
            }
            if (saleOrderResultBO.getStats() != 1 && saleOrderResultBO.getStats() != 2) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "调用OA生成销售单返回状态错误,错误信息：" + saleOrderResultBO.getResult());
                continue;
            }
            // 生成成功更新销售单号
            orderInfo.setSaleOrderNo(saleOrderResultBO.getResult());
            orderInfo.setSaleJobNumber(saleJobNumber)
                    .setSaleName(saleName);
            needUpdateOrderList.add(orderInfo);
        }
        // 更新订单数据
        if (CollectionUtils.isNotEmpty(needUpdateOrderList)) {
            orderInfoService.updateBatchById(needUpdateOrderList);
        }
        if (CollectionUtils.isNotEmpty(errorInfoList)) {
            return Result.errorInfo(Joiner.on(";").join(errorInfoList));
        }
        return Result.successInfo("销售单生成成功");
    }

    @Override
    public Result<String> createSaleOrder(String orderIds, String areaCode) {
        if (StringUtils.isBlank(orderIds)) {
            return Result.errorInfo("订单号不能为空");
        }
        if (StringUtils.isBlank(areaCode)) {
            return Result.errorInfo("门店编号不能为空");
        }
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if (supplierTokenInfo == null) {
            return Result.notLoginError();
        }
        // 判断是否是九讯供应商，只有九讯供应商需要该功能
        if (supplierTokenInfo.getId() != CommonConstant.SHENZHEN_JIUXUN_SUPPLIER_ID.longValue()) {
            return Result.errorInfo("生成销售单只针对九讯供应商开放");
        }
        List<Long> orderIdList = Splitter.on(",").splitToList(orderIds).stream().map(Long::new).collect(Collectors.toList());
        // 查询订单信息
        List<OrderInfo> orderInfoList = orderInfoService.list(new LambdaQueryWrapper<OrderInfo>().eq(OrderInfo::getSupplierId, supplierTokenInfo.getId()).in(OrderInfo::getId, orderIdList));
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return Result.errorInfo("订单不存在");
        }
        // 判断订单状态
        Optional<OrderInfo> orderInfoOptional = orderInfoList.stream().filter(orderInfo -> orderInfo.getOrderStatus() == OrderStatusEnum.CANCEL.getCode().intValue() || orderInfo.getOrderStatus() == OrderStatusEnum.DEFAULT.getCode().intValue()).findAny();
        if (orderInfoOptional.isPresent()) {
            return Result.errorInfo("采购单：" + orderInfoOptional.get().getOrderNo() + "已取消，不能生成销售单");
        }
        // 判断订单是否生成销售单
        Optional<OrderInfo> hasSaleOrderOptional = orderInfoList.stream().filter(orderInfo -> StringUtils.isNotBlank(orderInfo.getSaleOrderNo()) && StringUtils.isNumeric(orderInfo.getSaleOrderNo())).findAny();
        if (hasSaleOrderOptional.isPresent()) {
            return Result.errorInfo("采购单：" + hasSaleOrderOptional.get().getOrderNo() + "已生成销售单，不能重复生成");
        }
        // 查询订单详情
        List<OrderDetailInfo> orderDetailInfoList = orderDetailInfoService.list(new LambdaQueryWrapper<OrderDetailInfo>().eq(OrderDetailInfo::getSupplierId, supplierTokenInfo.getId()).in(OrderDetailInfo::getOrderId, orderIdList));
        if (CollectionUtils.isEmpty(orderDetailInfoList)) {
            return Result.errorInfo("订单明细不存在");
        }
        // 查询合作伙伴信息
        List<Long> partnerIdList = orderInfoList.stream().map(OrderInfo::getPartnerId).collect(Collectors.toList());
        List<PartnerUser> partnerUserList = partnerUserService.list(new LambdaQueryWrapper<PartnerUser>().in(PartnerUser::getId, partnerIdList));
        if (CollectionUtils.isEmpty(partnerUserList)) {
            return Result.errorInfo("合作伙伴信息不存在");
        }
        // 查询购买的商品信息
        List<Long> productIdList = orderDetailInfoList.stream().map(OrderDetailInfo::getProductId).collect(Collectors.toList());
        List<PickProduct> pickProductList = pickProductService.list(new LambdaQueryWrapper<PickProduct>().in(PickProduct::getId, productIdList));
        if (CollectionUtils.isEmpty(pickProductList)) {
            return Result.errorInfo("购买的商品信息不存在");
        }
        // 查询商品数据
        List<Long> ppidList = pickProductList.stream().map(PickProduct::getPpid).distinct().collect(Collectors.toList());
        List<ProductInfo> productInfoList = productInfoService.list(new LambdaQueryWrapper<ProductInfo>().in(ProductInfo::getPpriceid, ppidList));
        if (CollectionUtils.isEmpty(productInfoList)) {
            return Result.errorInfo("商品详细信息不存在");
        }
        Map<Long, ProductInfo> productInfoMap = productInfoList.stream().collect(Collectors.toMap(ProductInfo::getPpriceid, Function.identity()));
        Map<Long, PickProduct> pickProductMap = pickProductList.stream().collect(Collectors.toMap(PickProduct::getId, Function.identity()));
        Map<Long, PartnerUser> partnerUserMap = partnerUserList.stream().collect(Collectors.toMap(PartnerUser::getId, Function.identity()));
        Map<Long, List<OrderDetailInfo>> orderDetailInfoMap = orderDetailInfoList.stream().collect(Collectors.groupingBy(OrderDetailInfo::getOrderId));
        List<String> errorInfoList = Lists.newArrayList();
        List<OrderInfo> needUpdateOrderList = Lists.newArrayList();
        for (OrderInfo orderInfo : orderInfoList) {
            PartnerUser partnerUser = partnerUserMap.get(orderInfo.getPartnerId());
            if (partnerUser == null) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "对应的合作伙伴不存在");
                continue;
            }
            String businessPhone;

            Long xtenant = partnerUser.getXtenant();
//            if (xtenant == 50000) {
//                xtenant = 10050L;
//            }
            Result<List<PickInfoVo>> listResult = oaOfficeCloud.list9XunPick(Arrays.asList(xtenant));
            Long finalXtenant = xtenant;
            PickInfoVo pickInfoVo = listResult.getData().stream().filter(x -> finalXtenant.equals(x.getXtenant()))
                    .collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (Objects.nonNull(pickInfoVo) && StringUtils.isNotEmpty(pickInfoVo.getCustomerMobile())) {
                businessPhone = pickInfoVo.getCustomerMobile();
            } else {
                errorInfoList.add("合作档案：" + partnerUser.getName() + "对应的合作伙伴业务联系人查询不到");
                businessPhone = partnerUser.getBusinessPhone();
            }

            if (StringUtils.isBlank(businessPhone) || !RegexUtils.checkMobile(businessPhone)) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "对应的合作伙伴业务联系人手机号错误");
                continue;
            }
            Result<OaUserVo> oaUser = oaUserCloud.getUserByPhone(businessPhone, 0);
            log.info("生成销售单，调用OA接口获取用户ID,请求：{},返回：{}", businessPhone, JSONUtil.toJsonStr(oaUser));
            if (oaUser == null || oaUser.getData() == null || !oaUser.isSucceed()) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "对应的九机用户不存在");
                continue;
            }
            // 构建销售单数据
            SaleOrderParamBO saleOrderParamBO = this.buildSaleOrderParamBO(orderInfo, pickProductMap, oaUser.getData(), productInfoMap, orderDetailInfoMap, areaCode);
            if (saleOrderParamBO == null) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "构建销售单数据失败");
                continue;
            }
            // 调用接口创建销售单
            String url = systemConfig.getWcfUrl() + "/oaApi.svc/rest/submitorder";
            String createSaleOrderResult = HttpRequest.post(url)
                    .header(Header.CONTENT_TYPE, ContentType.JSON.getValue())
                    .header(Header.ACCEPT, ContentType.JSON.getValue())
                    .body(JSON.toJSONString(saleOrderParamBO))
                    .execute()
                    .body();
            log.info("创建销售单，请求参数：{}, 返回结果：{}", JSON.toJSONString(saleOrderParamBO), createSaleOrderResult);
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(orderInfo.getXtenantId()).orderNo(orderInfo.getOrderNo()).notifyName("生成销售单").notifyUrl(url).notifyParam(JSON.toJSONString(saleOrderParamBO)).notifyResult(createSaleOrderResult).build());
            if (StringUtils.isBlank(createSaleOrderResult) || !createSaleOrderResult.contains("stats")) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "调用OA生成销售单异常");
                continue;
            }
            SaleOrderResultBO saleOrderResultBO = JSONUtil.toBean(createSaleOrderResult, SaleOrderResultBO.class);
            if (saleOrderResultBO == null) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "调用OA生成销售单返回结果错误");
                continue;
            }
            if (saleOrderResultBO.getStats() != 1 && saleOrderResultBO.getStats() != 2) {
                errorInfoList.add("订单：" + orderInfo.getOrderNo() + "调用OA生成销售单返回状态错误,错误信息：" + saleOrderResultBO.getResult());
                continue;
            }
            // 生成成功更新销售单号
            orderInfo.setSaleOrderNo(saleOrderResultBO.getResult());
            needUpdateOrderList.add(orderInfo);
        }
        // 更新订单数据
        if (CollectionUtils.isNotEmpty(needUpdateOrderList)) {
            orderInfoService.updateBatchById(needUpdateOrderList);
        }
        if (CollectionUtils.isNotEmpty(errorInfoList)) {
            return Result.errorInfo(Joiner.on(";").join(errorInfoList));
        }
        return Result.successInfo("销售单生成成功");
    }




    private SaleOrderParamBO buildSaleOrderParamBO(OrderInfo orderInfo, Map<Long, PickProduct> pickProductMap, OaUserVo oaUserVo,
                                                   Map<Long, ProductInfo> productInfoMap, Map<Long, List<OrderDetailInfo>> orderDetailInfoMap, String areaCode) {
        List<OrderDetailInfo> orderDetailInfoList = orderDetailInfoMap.get(orderInfo.getId());
        if (CollectionUtils.isEmpty(orderDetailInfoList)) {
            return null;
        }
        // 计算运费
        BigDecimal totalDeliveryFee = new BigDecimal(0);
        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            if (orderDetailInfo.getDeliveryFee() != null) {
                totalDeliveryFee = totalDeliveryFee.add(orderDetailInfo.getDeliveryFee());
            }
        }
        // 销售单数据
        SaleAreaBo saleAreaBo = CommonConstant.SALE_AREA_MAP.get(areaCode);
        if (saleAreaBo == null) {
            log.warn("构建销售单数据，获取门店相关配置结果为空,param:{}", areaCode);
            return null;
        }
        SaleOrderParamBO.SaleOrderParam saleOrderParam = new SaleOrderParamBO.SaleOrderParam();
        saleOrderParam.setUserId(oaUserVo.getId());
        saleOrderParam.setArea(saleAreaBo.getAreaCode());
        saleOrderParam.setAreaId(saleAreaBo.getAreaId());
        // 采货王订单类型20
        saleOrderParam.setSubType(20);
        saleOrderParam.setSubPay(1);
        saleOrderParam.setComment("九讯严选订单，订单号：" + orderInfo.getOrderNo());
        saleOrderParam.setSubTo(orderInfo.getContactPerson());
        saleOrderParam.setSubTel(orderInfo.getContactPhone());
        saleOrderParam.setSubAdds(orderInfo.getReceiveAddress());
        saleOrderParam.setSubMobile(orderInfo.getContactPhone());
        if (StringUtils.isNotBlank(orderInfo.getReceivePoiCityId()) && StringUtils.isNumeric(orderInfo.getReceivePoiCityId())) {
            saleOrderParam.setCityId(Integer.parseInt(orderInfo.getReceivePoiCityId()));
        }
        saleOrderParam.setDelivery(4);
        saleOrderParam.setTotalDeliveryFee(totalDeliveryFee.toString());
        saleOrderParam.setTotalPrice(orderInfo.getTotalPrice().toString());
        saleOrderParam.setInUser("付庭伟");
        saleOrderParam.setUserName(oaUserVo.getUserName());
        saleOrderParam.setUserClass(oaUserVo.getUserClass());

        // 商品数据
        List<SaleOrderParamBO.SaleProductParam> saleProductParamList = Lists.newArrayList();
        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            PickProduct pickProduct = pickProductMap.get(orderDetailInfo.getProductId());

            SaleOrderParamBO.SaleProductParam saleProductParam = new SaleOrderParamBO.SaleProductParam();
            if (pickProduct != null) {
                saleProductParam.setPpid(pickProduct.getPpid());
                ProductInfo productInfo = productInfoMap.get(pickProduct.getPpid());
                if (productInfo != null) {
                    saleProductParam.setProductTitle(productInfo.getProductName());
                    saleProductParam.setIsMobile(productInfo.getIsMobile() == null ? 0 : productInfo.getIsMobile() ? 1 : 0);
                }
            }
            saleProductParam.setProductCount(orderDetailInfo.getBuyAmount());
            saleProductParam.setPrice(orderDetailInfo.getProductPrice().toString());
            if (orderDetailInfo.getDeliveryFee() != null) {
                saleProductParam.setPrice(orderDetailInfo.getProductPrice().add(orderDetailInfo.getDeliveryFee().divide(new BigDecimal(orderDetailInfo.getBuyAmount()))).toString());
            }
            saleProductParam.setSalePrice(saleProductParam.getPrice());
            saleProductParam.setCostPrice(saleProductParam.getPrice());
            saleProductParam.setType(0);
            saleProductParam.setSeller("采货王");
            saleProductParamList.add(saleProductParam);
        }
        SaleOrderParamBO saleOrderParamBO = new SaleOrderParamBO();
        saleOrderParamBO.setSaleOrderParam(saleOrderParam);
        saleOrderParamBO.setSaleProductParamList(saleProductParamList);
        return saleOrderParamBO;
    }

    private SaleOrderParamBO buildSaleOrderParamBO(BuildSaleOrderParam buildSaleOrderParam) {
        OrderInfo orderInfo = buildSaleOrderParam.getOrderInfo();
        Map<Long, List<OrderDetailInfo>> orderDetailInfoMap = buildSaleOrderParam.getOrderDetailInfoMap();
        List<OrderDetailInfo> orderDetailInfoList = orderDetailInfoMap.get(orderInfo.getId());
        if (CollectionUtils.isEmpty(orderDetailInfoList)) {
            return null;
        }
        // 计算运费
        BigDecimal totalDeliveryFee = new BigDecimal(0);
        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            if (orderDetailInfo.getDeliveryFee() != null) {
                totalDeliveryFee = totalDeliveryFee.add(orderDetailInfo.getDeliveryFee());
            }
        }
        OaUserVo oaUserVo = buildSaleOrderParam.getOaUserVo();
        SaleOrderParamBO.SaleOrderParam saleOrderParam = new SaleOrderParamBO.SaleOrderParam();
        saleOrderParam.setUserId(oaUserVo.getId());
        saleOrderParam.setArea(sysConfig.getSalesOrderAreaCode());
        saleOrderParam.setAreaId(sysConfig.getSalesOrderAreaId());
        // 采货王订单类型20
        saleOrderParam.setSubType(20);
        saleOrderParam.setSubPay(1);
        saleOrderParam.setComment("九讯严选订单，订单号：" + orderInfo.getOrderNo());
        saleOrderParam.setSubTo(orderInfo.getContactPerson());
        saleOrderParam.setSubTel(orderInfo.getContactPhone());
        saleOrderParam.setSubAdds(orderInfo.getReceiveAddress());
        saleOrderParam.setSubMobile(orderInfo.getContactPhone());
        if (StringUtils.isNotBlank(orderInfo.getReceivePoiCityId()) && StringUtils.isNumeric(orderInfo.getReceivePoiCityId())) {
            saleOrderParam.setCityId(Integer.parseInt(orderInfo.getReceivePoiCityId()));
        }
        saleOrderParam.setDelivery(4);
        saleOrderParam.setTotalDeliveryFee(totalDeliveryFee.toString());
        saleOrderParam.setTotalPrice(orderInfo.getTotalPrice().toString());
        saleOrderParam.setInUser(sysConfig.getSalesOrderPersonnel());
        saleOrderParam.setUserName(oaUserVo.getUserName());
        saleOrderParam.setUserClass(oaUserVo.getUserClass());

        // 商品数据
        Map<Long, PickProduct> pickProductMap = buildSaleOrderParam.getPickProductMap();
        Map<Long, ProductInfo> productInfoMap = buildSaleOrderParam.getProductInfoMap();
        List<SaleOrderParamBO.SaleProductParam> saleProductParamList = Lists.newArrayList();
        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            PickProduct pickProduct = pickProductMap.get(orderDetailInfo.getProductId());

            SaleOrderParamBO.SaleProductParam saleProductParam = new SaleOrderParamBO.SaleProductParam();
            if (pickProduct != null) {
                saleProductParam.setPpid(pickProduct.getPpid());
                ProductInfo productInfo = productInfoMap.get(pickProduct.getPpid());
                if (productInfo != null) {
                    saleProductParam.setProductTitle(productInfo.getProductName());
                    saleProductParam.setIsMobile(productInfo.getIsMobile() == null ? 0 : productInfo.getIsMobile() ? 1 : 0);
                }
            }
            saleProductParam.setProductCount(orderDetailInfo.getBuyAmount());
            saleProductParam.setPrice(orderDetailInfo.getProductPrice().toString());
            if (orderDetailInfo.getDeliveryFee() != null) {
                saleProductParam.setPrice(orderDetailInfo.getProductPrice().add(orderDetailInfo.getDeliveryFee().divide(new BigDecimal(orderDetailInfo.getBuyAmount()))).toString());
            }
            saleProductParam.setSalePrice(saleProductParam.getPrice());
            saleProductParam.setCostPrice(saleProductParam.getPrice());
            saleProductParam.setType(0);
            saleProductParam.setSeller(buildSaleOrderParam.getSaleName());
            saleProductParamList.add(saleProductParam);
        }
        SaleOrderParamBO saleOrderParamBO = new SaleOrderParamBO();
        saleOrderParamBO.setSaleOrderParam(saleOrderParam);
        saleOrderParamBO.setSaleProductParamList(saleProductParamList);
        return saleOrderParamBO;
    }

}

package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.service.product.entity.Brand;
import com.jiuji.pick.service.product.vo.BrandDetailVO;

import java.util.List;

/**
 * <p>
 * 品牌 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface BrandService extends IService<Brand> {
    /**
     * 获取BrandCategory维护的排序后的品牌
     */
    List<Brand> listWithCategorySort(List<Integer> brandIds, int categoryId);

    /**
     * 同步品牌
     * @param brandDetailVO
     * @param isSynchronize
     * @return
     */
    Result<Boolean> saveBrand(BrandDetailVO brandDetailVO, boolean isSynchronize);
}

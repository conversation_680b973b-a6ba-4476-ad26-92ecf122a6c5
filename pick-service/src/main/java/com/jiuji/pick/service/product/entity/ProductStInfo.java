package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_product_st_info")
public class ProductStInfo extends Model<ProductStInfo> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * productStDetail ID
     */
    private Integer standardDetailId;

    /**
     * 商品规格ID
     */
    private Long ppriceid;


    public static final String STANDARDDETAILID = "standard_detail_id";

    public static final String PPRICEID = "ppriceid";

    public static final String ID = "id";

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

package com.jiuji.pick.service.product.param;

import com.ch999.common.util.tenant.Namespaces;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.utils.StringTemplateUtil;
import com.jiuji.pick.service.product.entity.CategoryDiyPcType;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.bytebuddy.implementation.InvokeDynamic;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@NoArgsConstructor
public class CategoryDiy4PcTypeSaveParam {
    private Integer id;
    @Valid
    private List<CategoryDiyPcTypeBo> types;
    @Valid
    private List<CategoryDiyPcTypeKeywords> keywords;
    @Data
    @NoArgsConstructor
    public static class CategoryDiyPcTypeBo{
        private Integer id;
        @NotBlank(message = "请填写分类链接")
        private String link=" ";
        @NotBlank(message = "请填写分类名称")
        private String name;

        private String img;
        //        @NotBlank(message = "请上传分类图片")
        private String fid;
        /**是否删除  0--不删除；1--删除*/
        private Integer isdel;

        public CategoryDiyPcTypeBo(CategoryDiyPcType type) {
            this.id=type.getId();
            this.fid=type.getFid();
            if(StringUtils.isNotBlank(type.getFid())){
                this.img= WebConstant.WEBURL.IMAGE_SERVER_NEWSTATIC+type.getFid();
            }
            this.link=type.getLink();
            this.name=type.getName();
        }
    }

    @Data
    @NoArgsConstructor
    public static class CategoryDiyPcTypeKeywords{
        private  Integer id;
        @NotBlank(message = "请填写分类关键词链接")
        private String link;
        @NotBlank(message = "请填写分类关键词名称")
        private String name;
        /**是否删除  0--不删除；1--删除*/
        private Integer isdel;
        public CategoryDiyPcTypeKeywords(CategoryDiyPcType type) {
            this.id=type.getId();
            this.link=type.getLink();
            this.name=type.getName();
            this.isdel = type.getDeleted() ? 1 : 0;
        }
    }

    public static CategoryDiyPcType map2CategoryDiyPcTypeByType(CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeBo type, int i, Integer id, Integer currentStaffId){
        String link = type.getLink();
        String links = StringTemplateUtil.roadKing(link);
        CategoryDiyPcType categoryDiyPcType = new CategoryDiyPcType();
        categoryDiyPcType.setCategoryDiyId(id);
        if(StringUtils.isNotEmpty(type.getName())){
            categoryDiyPcType.setName(type.getName());
        } else {
            categoryDiyPcType.setName("分类"+(i+1));
        }
        categoryDiyPcType.setLink(links);
        categoryDiyPcType.setFid(type.getFid());
        categoryDiyPcType.setXtenant(Integer.parseInt(Namespaces.get()+""));
        categoryDiyPcType.setUpdateUserId(currentStaffId);
        categoryDiyPcType.setKinds(1);
        categoryDiyPcType.setCreateTime(LocalDateTime.now());
        categoryDiyPcType.setId(type.getId());
        categoryDiyPcType.setUpdateTime(LocalDateTime.now());
        return categoryDiyPcType;
    }

    public static CategoryDiyPcType map2CategoryDiyPcTypeByKeyWord(CategoryDiy4PcTypeSaveParam.CategoryDiyPcTypeKeywords keyword, int i, int id, int currentStaffId){
        String link = keyword.getLink();
        String links = StringTemplateUtil.roadKing(link);
        CategoryDiyPcType categoryDiyPcType = new CategoryDiyPcType();
        categoryDiyPcType.setCategoryDiyId(id);
        if(StringUtils.isNotEmpty(keyword.getName())){
            categoryDiyPcType.setName(keyword.getName());
        } else {
            categoryDiyPcType.setName("关键词"+(i+1));
        }
        categoryDiyPcType.setLink(links);
        categoryDiyPcType.setXtenant(Integer.parseInt(Namespaces.get()+""));
        categoryDiyPcType.setUpdateTime(LocalDateTime.now());
        categoryDiyPcType.setKinds(2);
        categoryDiyPcType.setUpdateUserId(currentStaffId);
        categoryDiyPcType.setId(keyword.getId());
        return categoryDiyPcType;
    }

}

/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.bo;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ProductSearchInEsV2 {
    /**
     * ppid和供应商的绑定id
     */
    private Long bindId;
    /**
     * ppid和供应商的明细ID
     */
    private Long suppllierDetailId;

    private Long ppid;

    private Long productId;

    private String name;

    private String searchKey;

    private String keyword;

    private Integer defaultSort;//综合排序

    private Integer pickSort;

    private Integer sales;//销量

    private boolean isMobile;

    private Integer categoryId;

    private Integer brandId;

    private String cidFamily;

    //    扩展分类
    private String cidExtend;

    @JSONField(serialize = false)
    private Integer que;
    @JSONField(serialize = false)
    private Boolean display;
    //单选、多选参数
    private String params;
    @JSONField(serialize = false)
    private Double score;

    @JSONField(serialize = false)
    private String color;

    @JSONField(serialize = false)
    private String barCode;

    @JSONField(serialize = false)
    private Integer sinkRank = 0;//商品下沉排序，目的是为了缺货商品下沉展示 ，搜索有维修配件下沉到最下面

    @JSONField(serialize = false)
    private String paramSearchKey;//商品参数搜索关键词
    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商地址省
     */
    private String supplierProvinceName;

    /**
     * 供应商地址市
     */
    private String supplierCityName;

    /**
     * 采购未税单价
     */
    private BigDecimal buyNoTaxPrice;

    /**
     * 采购含税单价
     */
    private BigDecimal buyTaxPrice;

    /**
     * 参考价
     */
    private BigDecimal advicePrice;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 商品上架时间
     */
    @JSONField(serialize = false)
    private LocalDateTime productUpTime;

    /**
     * 售后省名称
     */
    private String afterSaleProvinceName;

    /**
     * 售后城市名称
     */
    private String afterSaleCityName;

    /**
     * 售后区名称
     */
    private String afterSaleDistrictName;

}

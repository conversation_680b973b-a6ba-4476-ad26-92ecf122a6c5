package com.jiuji.pick.service.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 购物车信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_cart_info")
public class CartInfo extends Model<CartInfo> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品详情ID
     */
    private Long productDetailId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品ppid
     */
    private Long ppid;

    /**
     * 合作伙伴表主键ID
     */
    private Long partnerId;

    /**
     * 添加人id
     */
    private Long userId;

    /**
     * 供应商id
     */
    private Long supplierUserId;

    /**
     * 商品数量
     */
    private Integer productCount;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 使用的价格类型,0:未税,1:含税
     */
    private Integer priceType;

    /**
     * 购物车类型
     */
    private Integer cartType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

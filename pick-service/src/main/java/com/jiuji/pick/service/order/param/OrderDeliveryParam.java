package com.jiuji.pick.service.order.param;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @function:
 * @description: OrderDeliveryParam.java
 * @date: 2021/06/29
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class OrderDeliveryParam {

    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空")
    private Long orderId;

    /**
     * 快递类型
     */
    @Length(max = 20, message = "物流公司可输入字段长度不允许超过20字符")
    private String deliveryType;

    /**
     * 快递单号
     */
    @Length(max = 50, message = "物流单号可输入长度不允许超过50字符")
    private String deliveryNo;

    /**
     * 物流日志备注
     */
    private String remarks;

    /**
     * 是否全部发货，0：否，1：是
     */
    @NotNull(message = "操作类型不能为空")
    private Integer operateType;

    /**
     * 发货明细
     */
    private List<DeliveryProductInfo> deliveryProductInfoList;

    /**
     * 订单类型
     */
    private Integer orderType;

    @Data
    public static class DeliveryProductInfo {
        private Long productId;
        private Integer count;
    }

}

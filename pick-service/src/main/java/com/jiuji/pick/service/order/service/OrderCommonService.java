package com.jiuji.pick.service.order.service;

import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.bo.DeliveryProductBO;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.param.CartOrderParam;
import com.jiuji.pick.service.order.param.OrderDeliveryParam;
import com.jiuji.pick.service.order.vo.CartInfoVO;
import com.jiuji.pick.service.order.vo.UpdateOrderPriceVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 订单通用接口
 * @function:
 * @description: OrderCommonService.java
 * @date: 2021/10/08
 * @author: sunfayun
 * @version: 1.0
 */
public interface OrderCommonService {

    /**
     * 修改价格
     * @param updateOrderPriceVoList
     */
    default void updateOrderPrice(UpdateOrderPriceVo updateOrderPriceVoList){}

    /**
     * 创建订单
     * @param cartInfoData 购物车订单数据
     * @param partnerTokenInfo 合作伙伴信息
     * @param cartOrderParam 下单请求参数
     * @return 返回失败信息
     */
    String createOrder(CartInfoVO.CartInfoData cartInfoData, PartnerTokenInfo partnerTokenInfo, CartOrderParam cartOrderParam);

    /**
     * 取消订单
     * @param orderNo 外部订单号
     * @param xTenant 租户ID
     * @return 取消结果
     */
    Result<String> cancelOrder(Long orderNo, Long xTenant);

    /**
     * 发货
     * @param deliveryProductBOList 发货产品列表
     * @param orderInfo 订单信息
     * @param param 发货请求参数
     * @param supplierTokenInfo 供应商信息
     * @return 发货结果
     */
    Result<String> deliveryProduct(List<DeliveryProductBO> deliveryProductBOList, OrderInfo orderInfo,
                                   OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo);

}

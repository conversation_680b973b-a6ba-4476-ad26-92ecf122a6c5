package com.jiuji.pick.service.product.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/11
 * @Time 14:13
 */
@Data
public class ProductWhiteConfigDetailVo {


    /**
     * 商品ID
     */
    private Long productId;

    /**
     * ppid 多个英文逗号分隔
     */
    private String ppid;

    /**
     * 用户id 多个英文逗号分隔
     */
    private String userIds;

    /**
     * 限制对象0：合作伙伴 1：供应商
     */
    private Integer restrictTarget;

    /**
     * 限制类型0：白名单 1：黑名单
     */
    private Integer restrictType;


}

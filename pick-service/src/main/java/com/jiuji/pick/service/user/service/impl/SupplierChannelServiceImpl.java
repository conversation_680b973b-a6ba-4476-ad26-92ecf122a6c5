package com.jiuji.pick.service.user.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.enums.AttachTypeEnum;
import com.jiuji.pick.common.enums.OrderTypeEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.utils.HostManageUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.service.AttachmentInfoService;
import com.jiuji.pick.service.order.dto.OaChannelCheckDTO;
import com.jiuji.pick.service.order.param.OaChannelCheckParam;
import com.jiuji.pick.service.order.service.NeoService;
import com.jiuji.pick.service.order.service.OaService;
import com.jiuji.pick.service.product.vo.SupplierChannelCountVO;
import com.jiuji.pick.service.user.dto.*;
import com.jiuji.pick.service.user.entity.*;
import com.jiuji.pick.service.user.mapper.SupplierChannelMapper;
import com.jiuji.pick.service.user.mapper.SupplierUserMapper;
import com.jiuji.pick.service.user.service.*;
import com.jiuji.pick.service.user.vo.SupplierChannelVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商渠道关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Slf4j
@Service
public class SupplierChannelServiceImpl extends ServiceImpl<SupplierChannelMapper, SupplierChannel> implements SupplierChannelService {

    @Resource
    private SupplierUserService supplierUserService;
    @Resource
    private SupplierUserMapper supplierUserMapper;
    @Resource
    private SupplierUserAccountService supplierUserAccountService;
    @Resource
    private SupplierUserContactService supplierUserContactService;
    @Resource
    private SupplierUserQualificationService supplierUserQualificationService;
    @Resource
    private OaService oaService;
    @Resource
    private NeoService neoService;
    @Resource
    private AttachmentInfoService attachmentInfoService;

    @Override
    public String getChannelIdTest(Long xTenant, Long supplierId, String addressId, Integer authId, Integer orderType) {

        // 构建数据
        OaSupplierDTO perfectSupplierDTO = new OaSupplierDTO();

        // 设置基本数据
        boolean supplierBaseInfo = setSupplierBaseInfo(perfectSupplierDTO, supplierId);

        // 设置审核信息
        boolean supplierQualificationInfo = setSupplierQualificationInfo(perfectSupplierDTO, supplierId);

        // 设置业务联系人
        boolean supplierUserContact = setSupplierUserContact(perfectSupplierDTO, supplierId);

        // 设置财务信息
        //setSupplierUserAccount(perfectSupplierDTO, supplierId, orderType);

        // 设置财务信息 start
        LambdaQueryWrapper<SupplierUserAccount> accountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountLambdaQueryWrapper.eq(SupplierUserAccount::getSupplierUserId, supplierId);
        List<SupplierUserAccount> supplierUserAccountList = supplierUserAccountService.list(accountLambdaQueryWrapper);

        List<OaFinanceDTO> financeDTOList = Lists.newArrayList();

        for(int i=0; i<=2; i++){

            OaFinanceDTO financeDTO = new OaFinanceDTO();
            OaFinanceDTO.KindLinkData kindLinkData = new OaFinanceDTO.KindLinkData();

            // Kind 赋值 0 小件 3 大件  1 维修配件
            if(i==0){
                kindLinkData.setKind(0);
            }else if(i == 1){
                kindLinkData.setKind(3);
            }else if(i == 2){
                kindLinkData.setKind(1);
            }

            kindLinkData.setInvoicingFlag(1);
            financeDTO.setKindLink(kindLinkData);

            if (CollectionUtils.isEmpty(supplierUserAccountList)) {
                financeDTO.setAccountsList(new ArrayList<>());
                // 财务信息
                //perfectSupplierDTO.setFinanceList(Collections.singletonList(financeDTO));
            }else {
                List<OaAccountsDTO> accountsDTOList = supplierUserAccountList.stream().map((SupplierUserAccount e) -> {
                    OaAccountsDTO accountsDTO = new OaAccountsDTO();
                    // 账号
                    accountsDTO.setAccountNumber(e.getAccountNum());
                    // 户名
                    accountsDTO.setUsername(e.getAccountName());
                    // 开户行
                    accountsDTO.setOpeningBank(e.getBankDeposit());
                    // 创建时间
                    accountsDTO.setDTime(e.getCreateTime());
                    // 对公对私  1-对公 2-对私
                    accountsDTO.setType(e.getType());
                    return accountsDTO;
                }).collect(Collectors.toList());

                financeDTO.setAccountsList(accountsDTOList);
            }
            financeDTOList.add(financeDTO);
        }

        // 财务信息
        perfectSupplierDTO.setFinanceList(financeDTOList);
        // 设置财务信息 end


        // 上面信息必须都有
        if (!supplierBaseInfo || !supplierQualificationInfo || !supplierUserContact) {
            log.warn("供应商的信息不全，无法获取渠道id，供应商id:{}", supplierId);
            return null;
        }

        // 设置合同信息 kinds 不维护
        setSupplierAttachmentInfo(perfectSupplierDTO, supplierId);

        // 渠道规模
        perfectSupplierDTO.setChannelScale(1);

        // 门店ID
        if(StringUtils.isNotBlank(addressId) && StringUtils.isNumeric(addressId)) {
            perfectSupplierDTO.setAreaId(Integer.parseInt(addressId));
        }
        // 授权隔离ID
        perfectSupplierDTO.setAuthId(authId);

        String paramStr = JSON.toJSONString(perfectSupplierDTO);
        // 调用oa接口获取渠道商id
        OaSupplierResultDTO oaSupplierResultDTO = oaService.createOaChannelId(perfectSupplierDTO, xTenant);
        if (oaSupplierResultDTO == null) {
            return null;
        }
        if (!Integer.valueOf(0).equals(oaSupplierResultDTO.getCode())
                || oaSupplierResultDTO.getExData() == null
                || oaSupplierResultDTO.getExData().getChannelId() == null) {
            return null;
        }

        String channelId = String.valueOf(oaSupplierResultDTO.getExData().getChannelId());

        log.warn("渠道id={}", channelId);

        String host = HostManageUtil.getHost(String.valueOf(0));
        if(StringUtils.isBlank(host)) {
            return null;
        }
        String url = host + "/cloudapi_nc/oa-stock/api/chw/check/v1?xservicename=oa-stock&channelId=%s&authorizeid=%s";
        url = String.format(url, channelId, authId);
        String ciphertext = DigestUtils.md5Hex(DateUtil.stringParseLocalDate(LocalDateTime.now()));
        String res = HttpRequest.get(url).header("token", ciphertext).execute().body();
        log.info("调用OA校验渠道， url:{}, res:{}", url, res);

        return channelId;
        //return null;
    }

    @Override
    public String getChannelId(Long xTenant, Long supplierId, String addressId, Integer authId, Integer orderType) {
        LambdaQueryWrapper<SupplierChannel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SupplierChannel::getSupplierId, supplierId);
        lambdaQueryWrapper.eq(SupplierChannel::getXtenantId, xTenant);
        lambdaQueryWrapper.eq(SupplierChannel::getAuthId, authId);
        lambdaQueryWrapper.eq(SupplierChannel::getChannelType, orderType);
        SupplierChannel supplierChannel = this.getOne(lambdaQueryWrapper);
        if (supplierChannel != null && StringUtils.isNotBlank(supplierChannel.getChannelId())) {
            return supplierChannel.getChannelId();
        }
        // 校验渠道
        supplierChannel = this.getOne(new LambdaQueryWrapper<SupplierChannel>().eq(SupplierChannel::getSupplierId, supplierId).eq(SupplierChannel::getXtenantId, xTenant).eq(SupplierChannel::getAuthId, MagicalValueConstant.INT_0));
        if (supplierChannel != null && StringUtils.isNotBlank(supplierChannel.getChannelId())) {
            OaChannelCheckParam channelCheckParam = OaChannelCheckParam.builder().xtenant(xTenant).channelId(supplierChannel.getChannelId()).authId(String.valueOf(authId))
                    .kind(OrderTypeEnum.BULKY.getCode() == orderType ? 3 : 0).build();
            OaChannelCheckDTO oaChannelCheckDTO = oaService.channelCheck(channelCheckParam);
            if(oaChannelCheckDTO == null || !Objects.equals(oaChannelCheckDTO.getCode(), MagicalValueConstant.INT_0) || oaChannelCheckDTO.getExData() == null) {
                log.error("调用OA接口校验渠道返回结果错误，xtenant:{},channelId:{},authId:{}", xTenant, supplierChannel.getChannelId(), authId);
                return null;
            }
            // 渠道已经创建
            if(oaChannelCheckDTO.getExData().isCheckResult()) {
                supplierChannel.setAuthId(authId);
                this.saveOrUpdate(supplierChannel);
                return supplierChannel.getChannelId();
            }
        }

        // 构建数据
        OaSupplierDTO perfectSupplierDTO = new OaSupplierDTO();

        // 设置基本数据
        boolean supplierBaseInfo = setSupplierBaseInfo(perfectSupplierDTO, supplierId);

        // 设置审核信息
        boolean supplierQualificationInfo = setSupplierQualificationInfo(perfectSupplierDTO, supplierId);

        // 设置业务联系人
        boolean supplierUserContact = setSupplierUserContact(perfectSupplierDTO, supplierId);

        // 设置财务信息
        setSupplierUserAccount(perfectSupplierDTO, supplierId, orderType);

        // 上面信息必须都有
        if (!supplierBaseInfo || !supplierQualificationInfo || !supplierUserContact) {
            log.warn("供应商的信息不全，无法获取渠道id，供应商id:{}", supplierId);
            return null;
        }

        // 设置合同信息
        setSupplierAttachmentInfo(perfectSupplierDTO, supplierId);

        if(OrderTypeEnum.SMALL.getCode() == orderType){
            perfectSupplierDTO.setKinds(Collections.singletonList(0));
        }else if(OrderTypeEnum.BULKY.getCode() == orderType){
            perfectSupplierDTO.setKinds(Collections.singletonList(3));
        }else {
            log.warn("获取渠道id失败，订单类型不合法，orderType:{} 供应商id:{}", orderType, supplierId);
            return null;
        }

        // 渠道规模
        perfectSupplierDTO.setChannelScale(1);

        // 门店ID
        if(StringUtils.isNotBlank(addressId) && StringUtils.isNumeric(addressId)) {
            perfectSupplierDTO.setAreaId(Integer.parseInt(addressId));
        }
        // 授权隔离ID
        perfectSupplierDTO.setAuthId(authId);

        // 调用oa接口获取渠道商id
        OaSupplierResultDTO oaSupplierResultDTO = oaService.createOaChannelId(perfectSupplierDTO, xTenant);
        if (oaSupplierResultDTO == null) {
            return null;
        }
        if (!Integer.valueOf(0).equals(oaSupplierResultDTO.getCode())
                || oaSupplierResultDTO.getExData() == null
                || oaSupplierResultDTO.getExData().getChannelId() == null) {
            return null;
        }

        String channelId = String.valueOf(oaSupplierResultDTO.getExData().getChannelId());
        if (supplierChannel == null) {
            supplierChannel = new SupplierChannel();
        }
        supplierChannel.setXtenantId(xTenant);
        supplierChannel.setAuthId(authId);
        supplierChannel.setSupplierId(supplierId);
        supplierChannel.setChannelId(channelId);
        // 渠道类型
        supplierChannel.setChannelType(Long.valueOf(String.valueOf(orderType)));
        supplierChannel.insertOrUpdate();

        return channelId;
    }

    @Override
    public String getChannelIdV2(Long xTenant, Long supplierId, String addressId, Integer authId) {
        LambdaQueryWrapper<SupplierChannel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SupplierChannel::getSupplierId, supplierId);
        lambdaQueryWrapper.eq(SupplierChannel::getXtenantId, xTenant);
        lambdaQueryWrapper.eq(SupplierChannel::getAuthId, authId);
        SupplierChannel supplierChannel = this.getOne(lambdaQueryWrapper);
        if (supplierChannel != null && StringUtils.isNotBlank(supplierChannel.getChannelId())) {
            return supplierChannel.getChannelId();
        }
        // 校验渠道 选取授权=0的校验
        supplierChannel = this.getOne(new LambdaQueryWrapper<SupplierChannel>().eq(SupplierChannel::getSupplierId, supplierId).eq(SupplierChannel::getXtenantId, xTenant).eq(SupplierChannel::getAuthId, MagicalValueConstant.INT_0));
        if (supplierChannel != null && StringUtils.isNotBlank(supplierChannel.getChannelId())) {
            OaChannelCheckParam channelCheckParam = OaChannelCheckParam.builder().xtenant(xTenant).channelId(supplierChannel.getChannelId()).authId(String.valueOf(authId)).build();
            OaChannelCheckDTO oaChannelCheckDTO = oaService.channelCheckV2(channelCheckParam);
            if(oaChannelCheckDTO == null || !Objects.equals(oaChannelCheckDTO.getCode(), MagicalValueConstant.INT_0) || oaChannelCheckDTO.getExData() == null) {
                log.error("调用OA接口校验渠道返回结果错误，xtenant:{},channelId:{},authId:{}", xTenant, supplierChannel.getChannelId(), authId);
                return null;
            }
            // 渠道已经创建
            if(oaChannelCheckDTO.getExData().isCheckResult()) {
                supplierChannel.setAuthId(authId);
                this.saveOrUpdate(supplierChannel);
                return supplierChannel.getChannelId();
            }
        }

        // 构建数据
        OaSupplierDTO perfectSupplierDTO = new OaSupplierDTO();

        // 设置基本数据
        boolean supplierBaseInfo = setSupplierBaseInfo(perfectSupplierDTO, supplierId);

        // 设置审核信息
        boolean supplierQualificationInfo = setSupplierQualificationInfo(perfectSupplierDTO, supplierId);

        // 设置业务联系人
        boolean supplierUserContact = setSupplierUserContact(perfectSupplierDTO, supplierId);

        // 上面信息必须都有
        if (!supplierBaseInfo || !supplierQualificationInfo || !supplierUserContact) {
            log.warn("供应商的信息不全，无法获取渠道id，供应商id:{}", supplierId);
            return null;
        }

        // 设置财务信息
        LambdaQueryWrapper<SupplierUserAccount> accountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountLambdaQueryWrapper.eq(SupplierUserAccount::getSupplierUserId, supplierId);
        List<SupplierUserAccount> supplierUserAccountList = supplierUserAccountService.list(accountLambdaQueryWrapper);

        // 财务信息列表
        List<OaFinanceDTO> financeDTOList = Lists.newArrayList();
        // 采货王财务信息是公用的，但是oa 需要每个渠道都有一份财务信息。所以，每一种渠道，就复制一份财务信息传递
        for(int i=0; i<=2; i++){
            OaFinanceDTO financeDTO = new OaFinanceDTO();
            OaFinanceDTO.KindLinkData kindLinkData = new OaFinanceDTO.KindLinkData();

            // Kind 赋值 0 小件 3 大件  1 维修配件
            if(i==0){
                kindLinkData.setKind(0);
            }else if(i == 1){
                kindLinkData.setKind(3);
            }else if(i == 2){
                kindLinkData.setKind(1);
            }

            kindLinkData.setInvoicingFlag(1);
            financeDTO.setKindLink(kindLinkData);

            if (CollectionUtils.isEmpty(supplierUserAccountList)) {
                financeDTO.setAccountsList(new ArrayList<>());
            }else {
                List<OaAccountsDTO> accountsDTOList = supplierUserAccountList.stream().map((SupplierUserAccount e) -> {
                    OaAccountsDTO accountsDTO = new OaAccountsDTO();
                    // 账号
                    accountsDTO.setAccountNumber(e.getAccountNum());
                    // 户名
                    accountsDTO.setUsername(e.getAccountName());
                    // 开户行
                    accountsDTO.setOpeningBank(e.getBankDeposit());
                    // 创建时间
                    accountsDTO.setDTime(e.getCreateTime());
                    // 对公对私  1-对公 2-对私
                    accountsDTO.setType(e.getType());
                    return accountsDTO;
                }).collect(Collectors.toList());

                financeDTO.setAccountsList(accountsDTOList);
            }
            financeDTOList.add(financeDTO);
        }

        // 财务信息 注意：perfectSupplierDTO 的 kinds 不维护
        perfectSupplierDTO.setFinanceList(financeDTOList);

        // 设置合同信息
        setSupplierAttachmentInfo(perfectSupplierDTO, supplierId);

        // 渠道规模
        perfectSupplierDTO.setChannelScale(1);

        // 门店ID
        if(StringUtils.isNotBlank(addressId) && StringUtils.isNumeric(addressId)) {
            perfectSupplierDTO.setAreaId(Integer.parseInt(addressId));
        }
        // 授权隔离ID
        perfectSupplierDTO.setAuthId(authId);

        // 调用oa接口获取渠道商id
        OaSupplierResultDTO oaSupplierResultDTO = oaService.createOaChannelId(perfectSupplierDTO, xTenant);
        if (oaSupplierResultDTO == null) {
            return null;
        }
        if (!Integer.valueOf(0).equals(oaSupplierResultDTO.getCode())
                || oaSupplierResultDTO.getExData() == null
                || oaSupplierResultDTO.getExData().getChannelId() == null) {
            return null;
        }

        String channelId = String.valueOf(oaSupplierResultDTO.getExData().getChannelId());

        log.warn("渠道id={}", channelId);

        if (supplierChannel == null) {
            supplierChannel = new SupplierChannel();
        }
        supplierChannel.setXtenantId(xTenant);
        supplierChannel.setAuthId(authId);
        supplierChannel.setSupplierId(supplierId);
        supplierChannel.setChannelId(channelId);
        supplierChannel.insertOrUpdate();

        return channelId;
    }

    @Override
    public String getNeoChannelId(Long xtenant, Long supplierId, String host, String token) {
        LambdaQueryWrapper<SupplierChannel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierChannel::getXtenantId, xtenant);
        queryWrapper.eq(SupplierChannel::getSupplierId, supplierId);
        queryWrapper.eq(SupplierChannel::getChannelSource, MagicalValueConstant.INT_2);
        SupplierChannel supplierChannel = this.getOne(queryWrapper);
        if (supplierChannel != null && StringUtils.isNotBlank(supplierChannel.getChannelId())) {
            return supplierChannel.getChannelId();
        }
        NeoSupplierDTO neoSupplierDTO = supplierUserMapper.getSupplierInfo4Neo(supplierId);

        Result<String> result = neoService.createNeoChannelId(neoSupplierDTO, xtenant, host, token);
        if(!result.isSucceed()){
            throw new BizException("NEO异常问题："+ Optional.ofNullable(result.getMsg()).orElse(result.getUserMsg()));
        }
        if (ObjectUtil.isNull(result) || !result.isSucceed()) {
            return null;
        }
        if (supplierChannel == null) {
            supplierChannel = new SupplierChannel();
        }
        supplierChannel.setChannelId(result.getData());
        supplierChannel.setSupplierId(supplierId);
        supplierChannel.setXtenantId(xtenant);
        supplierChannel.setChannelSource(2);
        supplierChannel.insertOrUpdate();

        return supplierChannel.getChannelId();
    }

    /**
     * 设置财务信息
     *
     * @param perfectSupplierDTO
     * @param supplierId
     * @return
     */
    private void setSupplierUserAccount(OaSupplierDTO perfectSupplierDTO, Long supplierId, Integer orderType) {
        LambdaQueryWrapper<SupplierUserAccount> accountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountLambdaQueryWrapper.eq(SupplierUserAccount::getSupplierUserId, supplierId);
        List<SupplierUserAccount> supplierUserAccountList = supplierUserAccountService.list(accountLambdaQueryWrapper);
        OaFinanceDTO financeDTO = new OaFinanceDTO();
        OaFinanceDTO.KindLinkData kindLinkData = new OaFinanceDTO.KindLinkData();

        if(OrderTypeEnum.SMALL.getCode() == orderType){
            kindLinkData.setKind(0);
        }else if(OrderTypeEnum.BULKY.getCode() == orderType){
            kindLinkData.setKind(3);
        }else {
            log.warn("setSupplierUserAccount方法 orderType匹配值， orderType={}", orderType);
            return;
        }

        kindLinkData.setInvoicingFlag(1);
        financeDTO.setKindLink(kindLinkData);

        if (CollectionUtils.isEmpty(supplierUserAccountList)) {
            financeDTO.setAccountsList(new ArrayList<>());
            // 财务信息
            perfectSupplierDTO.setFinanceList(Collections.singletonList(financeDTO));
        }
        List<OaAccountsDTO> accountsDTOList = supplierUserAccountList.stream().map((SupplierUserAccount e) -> {
            OaAccountsDTO accountsDTO = new OaAccountsDTO();
            // 账号
            accountsDTO.setAccountNumber(e.getAccountNum());
            // 户名
            accountsDTO.setUsername(e.getAccountName());
            // 开户行
            accountsDTO.setOpeningBank(e.getBankDeposit());
            // 创建时间
            accountsDTO.setDTime(e.getCreateTime());
            // 对公对私  1-对公 2-对私
            accountsDTO.setType(e.getType());
            return accountsDTO;
        }).collect(Collectors.toList());

        financeDTO.setAccountsList(accountsDTOList);
        // 财务信息
        perfectSupplierDTO.setFinanceList(Collections.singletonList(financeDTO));
    }

    /**
     * 设置业务联系人
     *
     * @param perfectSupplierDTO
     * @param supplierId
     * @return
     */
    private boolean setSupplierUserContact(OaSupplierDTO perfectSupplierDTO, Long supplierId) {
        LambdaQueryWrapper<SupplierUserContact> contactLambdaQueryWrapper = new LambdaQueryWrapper<>();
        contactLambdaQueryWrapper.eq(SupplierUserContact::getSupplierUserId, supplierId);
        List<SupplierUserContact> supplierUserContactList = supplierUserContactService.list(contactLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(supplierUserContactList)) {
            return false;
        }
        List<OaContactsDTO> contacts = supplierUserContactList.stream().map((SupplierUserContact e) -> {
            OaContactsDTO contactsDTO = new OaContactsDTO();
            contactsDTO.setUsername(e.getName());
            contactsDTO.setTel(e.getPhone());
            return contactsDTO;
        }).collect(Collectors.toList());
        // 业务联系人列表
        perfectSupplierDTO.setContactsList(contacts);
        return true;
    }

    /**
     * 设置合同信息
     *
     * @param perfectSupplierDTO
     * @param supplierId
     */
    private void setSupplierAttachmentInfo(OaSupplierDTO perfectSupplierDTO, Long supplierId) {
        LambdaQueryWrapper<AttachmentInfo> attachmentInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        attachmentInfoLambdaQueryWrapper.eq(AttachmentInfo::getRelateId, supplierId);
        attachmentInfoLambdaQueryWrapper.eq(AttachmentInfo::getType, AttachTypeEnum.SUPPLIER.getCode());
        List<AttachmentInfo> attachmentInfoList = attachmentInfoService.list(attachmentInfoLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(attachmentInfoList)) {
            List<OaFileBO> fileBOList = attachmentInfoList.stream()
                    .map(SupplierChannelServiceImpl::getFileBO).collect(Collectors.toList());
            perfectSupplierDTO.setAttachments(fileBOList);
        } else {
            perfectSupplierDTO.setAttachments(new ArrayList<>());
        }
    }

    /**
     * 获取文件信息
     *
     * @param attachmentInfo
     * @return
     */
    private static OaFileBO getFileBO(AttachmentInfo attachmentInfo) {
        OaFileBO fileBO = new OaFileBO();
        String fileName = attachmentInfo.getFileName();
        fileBO.setFileName(fileName);
        String filePath = attachmentInfo.getFilePath();
        fileBO.setFilePath(filePath);
        fileBO.setFileRelativePath(filePath);
        int i = fileName.lastIndexOf('.');
        if (i > 0) {
            fileBO.setFileSuffix(fileName.substring(i + 1));
        }
        return fileBO;
    }

    /**
     * 设置审核信息
     *
     * @param perfectSupplierDTO
     * @param supplierId
     * @return
     */
    private boolean setSupplierQualificationInfo(OaSupplierDTO perfectSupplierDTO, Long supplierId) {
        LambdaQueryWrapper<SupplierUserQualification> qualificationLambdaQueryWrapper = new LambdaQueryWrapper<>();
        qualificationLambdaQueryWrapper.eq(SupplierUserQualification::getSupplierUserId, supplierId);
        SupplierUserQualification supplierUserQualification =
                supplierUserQualificationService.getOne(qualificationLambdaQueryWrapper);
        if (supplierUserQualification == null) {
            return false;
        }
        // 注册资金
        perfectSupplierDTO.setRegisteredFund(new BigDecimal(supplierUserQualification.getRegisteredFund()));
        // 企业性质
        String nature = supplierUserQualification.getNature();
        perfectSupplierDTO.setCompanyNature(Integer.valueOf(nature));
        // 法人
        perfectSupplierDTO.setLegalPerson(supplierUserQualification.getLegalPerson());
        // 负责人联系手机
        perfectSupplierDTO.setMobile(supplierUserQualification.getLeaderPhone());
        // 商品分类
        perfectSupplierDTO.setClassification(supplierUserQualification.getCategoryId());
        // 售后联系人
        perfectSupplierDTO.setAfterContacts(supplierUserQualification.getAfterSaleName());
        // 售后联系电话
        perfectSupplierDTO.setAfterMobile(supplierUserQualification.getAfterSalePhone());
        // 售后地址
        perfectSupplierDTO.setAfterAddress(supplierUserQualification.getAfterSaleAddress());
        // 售后地址cityId
        if(supplierUserQualification.getAfterSaleDistrictId() != null) {
            perfectSupplierDTO.setAfterCityId(supplierUserQualification.getAfterSaleDistrictId());
        } else {
            perfectSupplierDTO.setAfterCityId(supplierUserQualification.getAfterSaleCityId());
        }

        // 售后省份id
        perfectSupplierDTO.setAfterProvinceId(supplierUserQualification.getAfterSaleProvinceId());
        // 售后区县id
        perfectSupplierDTO.setAfterCountyId(supplierUserQualification.getAfterSaleDistrictId());
        // 财务负责人
        perfectSupplierDTO.setFinanceName(supplierUserQualification.getFinanceName());
        // 财务负责人电话
        perfectSupplierDTO.setFinancePhone(supplierUserQualification.getFinancePhone());
        // 主营业务备注
        perfectSupplierDTO.setComment1(StringUtils.isBlank(supplierUserQualification.getRemark()) ? "暂无" : supplierUserQualification.getRemark());

        return true;
    }

    /**
     * 设置基本数据
     *
     * @param perfectSupplierDTO
     * @param supplierId
     * @return
     */
    private boolean setSupplierBaseInfo(OaSupplierDTO perfectSupplierDTO, Long supplierId) {
        SupplierUser supplierUser = supplierUserService.getById(supplierId);
        if (supplierUser == null) {
            return false;
        }
        // 公司名称
        perfectSupplierDTO.setCompany(supplierUser.getName());
        // 公司简称
        perfectSupplierDTO.setCompanyJc(supplierUser.getShortName());
        // 城市id(对应公司地址)
        perfectSupplierDTO.setCityId(supplierUser.getCityId());
        // 省份id(对应公司地址)
        perfectSupplierDTO.setPid(supplierUser.getProvinceId());
        // 市级id(对应公司地址)
        perfectSupplierDTO.setZid(supplierUser.getCityId());
        // 县级ID(对应公司地址)
        perfectSupplierDTO.setDid(supplierUser.getDistrictId());
        // 公司地址
        perfectSupplierDTO.setAddress(supplierUser.getAddress());
        return true;
    }

    @Override
    public List<SupplierChannelCountVO> supplierChannelCount(List<Long> supplierIdList) {
        if (CollectionUtils.isEmpty(supplierIdList)) {
            return Lists.newArrayList();
        }
        List<SupplierChannelCountVO> countVOList = baseMapper.supplierChannelCount(supplierIdList);
        List<SupplierChannelCountVO> list = countVOList.stream().filter(e -> null != e.getSupplierId()).collect(Collectors.toList());
        return list;
    }

    @Override
    public List<SupplierChannelVO> listBySupplierId(Long supplierId) {
        if (supplierId <= 0) {
            return Lists.newArrayList();
        }
        return baseMapper.listBySupplierId(supplierId);
    }
}

package com.jiuji.pick.service.product.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:zuofei
 * @Description
 * @Date 2021-04-13
 * 69码vo对象，这个对象是和九机同步兼容的一个对象，用于接收九机同步分发
 */
@Setter
@Getter
public class BarCodeVo implements Serializable {
    /**
     * ppid
     */
    private Long ppid;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 租户标识
     */
    private Long xtenant;
    /**
     * barCode集合
     */
    private List<Item> codeList;


    @Setter
    @Getter
    public static class Item {
        /**
         * 修改前barCode
         */
        private String beforeCode;
        /**
         * 修改后barCode
         */
        private String afterCode;
        /**
         * 是否是默认 ，1--是默认；0--不是
         */
        private Integer isDefault;
        /**
         * 是否删除 ，1--删除；0--否
         */
        private Integer isDel;
    }
}

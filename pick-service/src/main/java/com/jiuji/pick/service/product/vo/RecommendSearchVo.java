/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by 502 on 2017/7/25.
 */
@Setter
@Getter
public class RecommendSearchVo {

    private String name;

    private Integer count;

    private Long productId;

    @JsonIgnore
    private Boolean isMobile = false;
}

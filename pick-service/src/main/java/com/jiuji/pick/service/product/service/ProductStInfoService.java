package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.bo.ProductStDBO;
import com.jiuji.pick.service.product.entity.ProductStInfo;
import com.jiuji.pick.service.product.vo.ProductSpecUpdateVO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductStInfoService extends IService<ProductStInfo> {

    /**
     * 修改商品规格
     *
     * @param param 修改入参
     * @return 修改结果
     */
    boolean updateProductSpec(ProductSpecUpdateVO param);

    /**
     * 通过商品下的所有sku规格
     */
    List<ProductStDBO> getProductAllSpecByPid(Long productId);
}

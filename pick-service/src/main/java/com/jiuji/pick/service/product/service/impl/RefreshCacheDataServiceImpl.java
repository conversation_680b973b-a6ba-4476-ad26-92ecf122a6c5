package com.jiuji.pick.service.product.service.impl;

import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.service.product.service.ProductSearchService;
import com.jiuji.pick.service.product.service.RefreshCacheDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @function:
 * @description: RefreshCacheDataServiceImpl.java
 * @date: 2021/06/01
 * @author: sunfayun
 * @version: 1.0
 */
@Service
@Slf4j
public class RefreshCacheDataServiceImpl implements RefreshCacheDataService {

    @Resource
    private ProductSearchService productSearchService;

    @Override
    public void refreshEsData(List<Long> ppidList, List<Long> bindIdList, Integer type) {
        if(type == null) {
            return;
        }
        try {
            CompletableFuture.runAsync(() -> {
                if(type == CommonConstant.UPDATE_PRODUCT) {
                    productSearchService.addProductBatch(ppidList, Boolean.FALSE);
                } else if (type == CommonConstant.DELETE_PRODUCT) {
                    productSearchService.deleteProductBatch(bindIdList);
                }
                //联想词重建
                productSearchService.rebuildProductAssociationEs();
            });
        } catch (Exception e) {
            log.error("刷新ES数据发生异常，exception:", e);
        }
    }
}

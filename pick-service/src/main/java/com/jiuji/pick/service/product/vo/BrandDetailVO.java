package com.jiuji.pick.service.product.vo;

import com.jiuji.pick.service.product.entity.Brand;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 添加品牌VO对象
 * @since 2019-11-28 09:44
 */
@Data
public class BrandDetailVO implements Serializable {

    private static final long serialVersionUID = -6522107837138081554L;

    /**
     * id
     */
    private Integer id;
    /**
     * 品牌名称
     */
    private String name;
    /**
     * 品牌图片url
     */
    private String url;
    /**
     * 品牌图片访问路径path
     */
    private String path;
    /**
     * 排序\等级（可重复）
     */
    private Integer rank;
    /**
     * 品牌简介
     */
    private String info;
    /**
     * 搜索关键字
     */
    private String seoKey;
    /**
     * 图片（用于APP分类）
     */
    private String picForApp;
    /**
     * 品牌官方网站地址
     */
    private String webSite;
    /**
     * 品牌官方售后服务热线
     */
    private String tel;
    /**
     * 商品排序权重
     */
    private Integer productSortValue;
    /**
     * 品牌短名称
     */
    private String shortName;
    /**
     * 关联的分类
     */
    private List<Integer> brandAssociatedCategoryIdList;
    /**
     * 是否显示
     */
    private Boolean display;

    /**
     * 转换为父类
     */
    public static Brand toSuper(BrandDetailVO brandDetailVO) {
        Brand brand = new Brand();
        brand.setId(brandDetailVO.getId());
        brand.setName(brandDetailVO.getName());
        brand.setUrl(brandDetailVO.getUrl());
        brand.setRank(brandDetailVO.getRank());
        brand.setInfo(brandDetailVO.getInfo());
        brand.setSeoKey(brandDetailVO.getSeoKey());
        brand.setPicforapp(brandDetailVO.getPicForApp());
        brand.setWebSite(brandDetailVO.getWebSite());
        brand.setTel(brandDetailVO.getTel());
        brand.setProductSortValue(brandDetailVO.getProductSortValue());
        brand.setShortName(brandDetailVO.getShortName());
        brand.setDisplay(brandDetailVO.getDisplay() == null ? true : brandDetailVO.getDisplay());
        return brand;
    }
}

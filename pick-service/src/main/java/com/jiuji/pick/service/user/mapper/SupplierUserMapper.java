package com.jiuji.pick.service.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.user.dto.NeoSupplierDTO;
import com.jiuji.pick.service.user.entity.SupplierUser;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
public interface SupplierUserMapper extends BaseMapper<SupplierUser> {

    /**
     * 查询供应商基本信息和联系人，构建创建NEO渠道接口参数
     * @param supplierId 供应商Id
     * @return NeoSupplierDTO
     */
    NeoSupplierDTO getSupplierInfo4Neo(Long supplierId);

}

package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UpdateProductParamVO {

    private Long productId;

    private Long ppid;

    /**
     * 主分类参数集合
     */
    private List<ParamVo> param;
    /**
     * 扩展分类参数集合
     */
    private List<ParamVo> extendCateParam;

    @Data
    public static class ParamVo {

        /**
         * 是否展示字段
         */
        private Boolean displayFlag;

        /**
         * 表product_cs 的csid   表product_cs_basic的id
         */
        private Integer paramDetailId;

        /**
         * paramDetailId对应的参数名称
         */
        private String name;

        /**
         * 具体的参数（这里传入的是id或者文本）
         */
        private String value;

        /**
         * value对应的参数名称
         */
        private String text;
    }
}

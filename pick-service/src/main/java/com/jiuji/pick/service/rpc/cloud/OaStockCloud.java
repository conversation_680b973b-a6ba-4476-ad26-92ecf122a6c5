package com.jiuji.pick.service.rpc.cloud;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.vo.ProductInfoReq;
import com.jiuji.pick.service.rpc.fallback.OaStockCloudFallbackFactory;
import com.jiuji.pick.service.rpc.vo.DiyCostFlowLeftRes;
import com.jiuji.pick.service.rpc.vo.DiyCostFlowSearchReq;
import com.jiuji.pick.service.rpc.vo.DiyCostFlowSearchRes;
import com.jiuji.pick.service.rpc.vo.OaStockVo;
import com.jiuji.tc.common.vo.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * @function:
 * @description: OaStockCloud.java
 * @date: 2021/08/09
 * @author: sunfayun
 * @version: 1.0
 */
@FeignClient(value = "oa-stock-service", fallbackFactory = OaStockCloudFallbackFactory.class)
public interface OaStockCloud {

    @PostMapping("/oa-stock/api/chw/get_small_sales/v1")
    Result<OaStockVo> getSmallPartsSales(@RequestBody List<Long> ppidList,
                                         @RequestHeader("token") String token);

    @PostMapping("/oa-stock/api/product-info/isMobile")
    Result<Integer> queryIsMobile(ProductInfoReq req);


    /**
     * 根据流水信息
     * @return
     */
    @PostMapping("/oa-stock/api/diy/cost-flow/list/v1")
    Result<Page<DiyCostFlowSearchRes>> getDiyCostFlowPageList(@RequestBody DiyCostFlowSearchReq query);

    /**
     * 根据流水信息
     * @return
     */
    @PostMapping("/oa-stock/api/diy/cost-flow/list/v2")
    Result<Page<DiyCostFlowSearchRes>> getDiyCostFlowPageListV2(@RequestBody DiyCostFlowSearchReq query);



    /**
     * 根据流水信息
     * @return
     */
    @PostMapping("/oa-stock/api/diy/cost-flow/left/v1")
    Result<List<DiyCostFlowLeftRes>> getLeftDiyCostList(@RequestBody DiyCostFlowSearchReq query);

}

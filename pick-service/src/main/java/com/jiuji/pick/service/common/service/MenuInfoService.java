package com.jiuji.pick.service.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.MenuInfo;
import com.jiuji.pick.service.common.vo.MenuInfoVo;

import java.util.List;

/**
 * <p>
 * 菜单信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
public interface MenuInfoService extends IService<MenuInfo> {

    Result<List<MenuInfoVo>> getMenuInfo(Integer type);

     List<MenuInfoVo> buildMenuInfoVoList(String key);

}

package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.entity.ProductBind;
import com.jiuji.pick.service.product.vo.SupplierProductCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductBindMapper extends BaseMapper<ProductBind> {

    /***
     * @description: 统计供应商绑定的商品数量（不区分商品状态）
     * @Param: [supplierIdList]
     * @author: Lbj
     * @date: 2021/5/7 14:22
     */
    List<SupplierProductCountVO> supplierProductCount(@Param("supplierIdList") List<Long> supplierIdList);
}

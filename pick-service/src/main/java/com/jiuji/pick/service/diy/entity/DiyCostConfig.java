package com.jiuji.pick.service.diy.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DiyCostConfig implements Serializable {

    private static final long serialVersionUID = 1L;


    private LocalDateTime updateTime;

    private Long id;

    private BigDecimal pickwebCost;

    private BigDecimal saleCost;

    private Integer delFlag;

    private Integer ppriceid;

    private LocalDateTime createTime;


}

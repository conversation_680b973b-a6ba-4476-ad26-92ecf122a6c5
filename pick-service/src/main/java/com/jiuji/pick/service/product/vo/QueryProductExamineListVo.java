package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @function:
 * @description: QueryProductExamineListVo.java
 * @date: 2021/05/08
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class QueryProductExamineListVo {

    private Long productId;
    private Long ppid;
    private String productName;
    private Long categoryId;
    private String categoryName;
    private LocalDateTime productUpTime;
    private Integer bindStatus;
    private String bindStatusName;
    private Integer productStatus;
    private String productStatusName;
    /**
     * 物料
     */
    private String material;
    private String materialName;
    /**
     * 采购未税单价
     */
    private BigDecimal buyNoTaxPrice;
    /**
     * 账期
     */
    private Integer paymentDay;
    private BigDecimal advicePrice;
    private BigDecimal predictProfit;
    private Long supplierId;
    private String supplierName;
    private LocalDateTime bindTime;
    /**
     * 供应商商品详情id
     */
    private Long supplierProductId;

}

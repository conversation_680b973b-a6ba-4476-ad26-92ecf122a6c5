package com.jiuji.pick.service.product.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.param.BasePageParam;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.bo.ProductInfoSku;
import com.jiuji.pick.service.product.param.QueryAreaProductPageParam;
import com.jiuji.pick.service.product.param.QuerySortProductParam;
import com.jiuji.pick.service.product.param.UpdateAreaProductParam;
import com.jiuji.pick.service.product.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface IndexProductService {

    /**
     * 查询商城首页商品列表
     * @return
     */
    Result<IndexProductListVo> indexProductList();

    /**
     * 更多专题
     * @param querySortProductParam
     * @return
     */
    Result<Page<IndexProductVo>>  queryProductArea(QuerySortProductParam querySortProductParam);

    /**
     * 获取商品详情信息
     * @param supplierProductId
     * @return
     */
    Result<IndexProductDetailVo> getIndexProductDetailInfo(Long supplierProductId);

    /**
     * 专区商品分页查询
     * @param pageParam pageParam
     * @return Page
     */
    Page<AreaProductVO> pageAreaProductVO(QueryAreaProductPageParam pageParam);

    /**
     * 专区商品排序
     *
     * @param id id
     * @param sort sort
     * @return Boolean
     */
    Boolean areaProductSort(Long id, Integer sort);

    /**
     * 修改商品专区
     * @param param
     * @return
     */
    boolean updateAreaProduct(UpdateAreaProductParam param);
}

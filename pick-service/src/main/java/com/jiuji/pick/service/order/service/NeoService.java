package com.jiuji.pick.service.order.service;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.dto.NeoOrderDTO;
import com.jiuji.pick.service.order.dto.NeoOrderDeliveryDTO;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.vo.OrderAreaInfoVO;
import com.jiuji.pick.service.user.dto.NeoSupplierDTO;

import java.util.List;

/**
 * neo服务调用
 * @Description
 * <AUTHOR>
 * @Date 2021/11/4
 */
public interface NeoService {

    /**
     * 获取订单门店信息
     * @param xtenant 租户ID
     * @return 基础门店VO
     */
    List<OrderAreaInfoVO> getAreaInfo(Long xtenant);

    /**
     * 调用NEO，生成渠道
     * @param neoSupplierDTO NEO渠道DTO
     * @param host 租户域名
     * @param xtenant 租户ID
     * @param token token
     * @return 渠道ID
     */
    Result<String> createNeoChannelId(NeoSupplierDTO neoSupplierDTO, Long xtenant, String host, String token);

    /**
     * 创建NEO采购单
     * @param neoOrderDTO NEO创建订单DTO
     * @param xtenant 租户ID
     * @param host 租户域名
     * @param token token
     * @return 采购单ID
     */
    Result<String> createNeoOrder(NeoOrderDTO neoOrderDTO, Long xtenant, String host, String token);

    /**
     * 取消NEO订单，并同步
     * @param orderNo 订单号
     * @param xtenant 租户ID
     * @param host 租户域名
     * @param token token
     * @return boolean
     */
    Result<Boolean> cancelNeoOrder(Long orderNo, Long xtenant, String host, String token);

    /**
     * 订单发货，同步NEO
     * @param orderInfo 订单信息
     * @param neoOrderDeliveryDTO neoOrderDeliveryDTO
     * @return boolean
     */
    Result<Boolean> neoOrderDelivery(OrderInfo orderInfo, NeoOrderDeliveryDTO neoOrderDeliveryDTO);

}

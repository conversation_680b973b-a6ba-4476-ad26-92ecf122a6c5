package com.jiuji.pick.service.order.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单详情VO
 *
 * <AUTHOR>
 * @since 2021-5-20
 */
@Data
public class OrderDetailVO {

    /**
     * 合作伙伴id
     */
    private Integer partnerId;

    /**
     * 销售单号
     */
    private String saleOrderNo;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 订单来源
     */
    private Integer channelType;

    /**
     * 平台单号
     */
    private Long orderId;

    /**
     * 采货单号
     */
    private Long orderNo;
    /**
     * 联系人
     */
    private String contactPerson;
    /**
     * 收货地址
     */
    private String receiveAddress;
    /**
     * 联系方式
     */
    private String contactPhone;
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商联系电话
     */
    private String leaderPhone;
    /**
     * 供应商虚拟商品负责人联系电话
     */
    private String virtualPhone;
    /**
     * 供应商资产商品负责人联系电话
     */
    private String assetsPhone;
    /**
     * 门店id
     */
    private String receivePoiId;
    /**
     * 门店名称
     */
    private String receivePoiName;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 快递公司
     */
    private String deliveryType;
    /**
     * 快递单号
     */
    private String deliveryNo;
    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 总物流费
     */
    private BigDecimal totalDeliveryFee;


    /**
     * 是否全部发货
     */
    private Boolean allDistributedFlag;

    /**
     * 商品列表
     */
    private List<OrderProductVO> productList;

    /**
     * 操作日志
     */
    private List<OrderLogVO> orderLogList;
}

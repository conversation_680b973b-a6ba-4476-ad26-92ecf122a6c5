package com.jiuji.pick.service.order.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UpdateOrderPriceVo {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单号
     */
    private Long orderNo;



    /**
     * 采购单详情
     */
    private List<UpdateOrderPriceDetailVo> detailVoList;

    @Data
    public static class UpdateOrderPriceDetailVo{
        /**
         * 订单详情id
         */

        private Long orderDetailInfoId;

        /**
         * 商品新价格
         */

        private BigDecimal productNewPrice;


        /**
         * 商品老价格
         */

        private BigDecimal productOldPrice;


        /**
         * 购买数量
         */
        private Integer buyAmount;


        private Integer ppriceid;
        /**
         * 商品名称
         */
        private String productName;

    }


}

package com.jiuji.pick.service.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.query.PartnerUserQuery;

/**
 * <p>
 * 合作伙伴信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
public interface PartnerUserService extends IService<PartnerUser> {


    /**
     * 清除登录token
     * @param type
     */
     void clearLogout(Integer type);

    /***
     * @description: 更新
     * @author: Lbj
     * @date: 2021/4/29 17:24
     */
    boolean saveOrUpdateByXtenantAndCheck(PartnerUser partnerUserInfo);

    /***
     * @description: 添加或者更新 - 通过Xtenant
     * @author: Lbj
     * @date: 2021/4/29 17:24
     */
    boolean saveOrUpdateByXtenant(PartnerUser partnerUserInfo);

    /***
     * @description: 查询 - 通过Xtenant
     * @author: Lbj
     * @date: 2021/4/29 17:24
     */
    PartnerUser getByXtenant(Long xtenant);

    /***
     * @description: 验证是否有相同的登录名
     * @Param: [loginName]
     * @author: Lbj
     * @date: 2021/4/30 14:04
     */
    boolean hasSameName(Long xtenant, String name);

    /***
     * @description: 禁止
     * @author: Lbj
     * @date: 2021/4/29 17:24
     */
    boolean forbid(Long id);
    /***
     * @description: 启用
     * @author: Lbj
     * @date: 2021/4/29 17:24
     */
    boolean enable(Long id);

    /***
     * @description: 分页查询
     * @Param: [partnerUserQuery]
     * @author: Lbj
     * @date: 2021/4/30 11:35
     */
    IPage<PartnerUser> listPageQuery(PartnerUserQuery partnerUserQuery);

    /***
     * @description: 登陆并获取OA合作伙伴信息并存储
     * @Param: [token]
     * @author: Lbj
     * @date: 2021/5/6 9:31
     */
    Result<String> loginAndSaveOAPartner(String token, String xtenant);

    /**
     * 登录新接口
     * @param token pcOAToken
     * @param xtenant xtenant
     * @param loginUserId 登录用户id
     * @param loginUserName 登录用户Name
     * @return 跳转连接
     */
    Result<String> loginAndSavePartnerInfo(String token, String xtenant, Long loginUserId, String loginUserName);

    /**
     * 登录，并获取NEO合作伙伴信息
     * @param token token
     * @param xtenant 域名
     * @return 跳转url
     */
    Result<String> loginAndSaveNeoPartner(String token, String xtenant,String oaHost);

    /***
     * @description: 退出登陆
     * @Param: [token]
     * @author: Lbj
     * @date: 2021/5/21 11:36
     */
    Result<String> logout(String token);
}

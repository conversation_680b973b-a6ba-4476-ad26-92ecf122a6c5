package com.jiuji.pick.service.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.MenuInfo;
import com.jiuji.pick.service.common.mapper.MenuInfoMapper;
import com.jiuji.pick.service.common.service.MenuInfoService;
import com.jiuji.pick.service.common.vo.MenuInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
@Service
@Slf4j
public class MenuInfoServiceImpl extends ServiceImpl<MenuInfoMapper, MenuInfo> implements MenuInfoService {

    private static final String CACHE_KEY = "menu_cache_";
    private static final ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(1, 1,
            0L, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(1));

    private final LoadingCache<String, List<MenuInfoVo>> cache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .refreshAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<MenuInfoVo>>() {
                @Override
                public List<MenuInfoVo> load(String key) throws Exception {
                    log.info("调用load方法加载菜单数据。。。。");
                    return buildMenuInfoVoList(key);
                }

                @Override
                public ListenableFuture<List<MenuInfoVo>> reload(String key, List<MenuInfoVo> oldValue) throws Exception {
                    log.info("调用reload方法刷新菜单缓存数据。。。");
                    ListenableFutureTask<List<MenuInfoVo>> task = ListenableFutureTask.create(() -> buildMenuInfoVoList(key));
                    EXECUTOR_SERVICE.execute(task);
                    return task;
                }
            });

    @Override
    public Result<List<MenuInfoVo>> getMenuInfo(Integer type) {
        if(type == null) {
            return Result.errorInfo("请求参数错误");
        }
        try {
            List<MenuInfoVo> list = cache.get(CACHE_KEY + type);
            if(CollectionUtils.isEmpty(list)) {
                return Result.noData();
            }
            return Result.success(list);
        } catch (Exception e) {
            log.error("获取菜单信息发生异常，exception:",e);
            return Result.success(this.buildMenuInfoVoList(CACHE_KEY + type));
        }
    }

    @Override
    public List<MenuInfoVo> buildMenuInfoVoList(String key) {
        Integer type = Integer.valueOf(key.replace(CACHE_KEY,""));
        List<MenuInfo> menuInfoList = this.list(new LambdaQueryWrapper<MenuInfo>().eq(MenuInfo::getMenuType, type));
        if(CollectionUtils.isEmpty(menuInfoList)) {
            return Collections.emptyList();
        }
        return getMenuChildTree(menuInfoList, 0L);
    }

    private List<MenuInfoVo> getMenuChildTree(List<MenuInfo> menuInfoList, Long parentId) {
        List<MenuInfoVo> menuInfoVoList = Lists.newArrayList();
        List<MenuInfo> childMenuList = getChildCategory(menuInfoList, parentId);
        if (CollectionUtils.isNotEmpty(childMenuList)) {
            for (MenuInfo menuInfo : childMenuList) {
                MenuInfoVo menuInfoVo = new MenuInfoVo();
                menuInfoVo.setPermission(menuInfo.getName());
                menuInfoVo.setName(menuInfo.getTitle());
                menuInfoVo.setLink(menuInfo.getPath());
                List<MenuInfoVo> subList = getMenuChildTree(menuInfoList, menuInfo.getId());
                if(CollectionUtils.isNotEmpty(subList)) {
                    menuInfoVo.setChildren(subList);
                }
                menuInfoVoList.add(menuInfoVo);
            }
        }
        return menuInfoVoList;
    }

    /**
     * 过滤出父分类中的子分类
     */
    private List<MenuInfo> getChildCategory(List<MenuInfo> allCategoryList, long parentId) {
        if(CollectionUtils.isEmpty(allCategoryList)) {
            return Collections.emptyList();
        }
        List<MenuInfo> categoryList = allCategoryList.stream().filter(e -> e.getParentId() == parentId).collect(Collectors.toList());
        allCategoryList.removeAll(categoryList);
        return categoryList;
    }

}

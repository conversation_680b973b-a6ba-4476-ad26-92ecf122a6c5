package com.jiuji.pick.service.rpc.vo;

import com.jiuji.pick.service.diy.enums.DiySearchTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单列表查询参数
 *
 * <AUTHOR>
 * @since 2021-6-2
 */
@Data
public class DiyCostFlowSearchReq {


    private Integer current = 1;


    private Integer size = 10;
    /**
     * userId
     */
    private String userId;

    private String userName;

    private List<String> userIdList;


    /**
     * subId
     */
    private String subId;

    /**
     * kind
     */
    private List<String> kind;

    /**
     *
     */
    private String searchValue;

    /**
     * @see DiySearchTypeEnum
     *
     */
    private Integer searchType;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Boolean isMall;

}

package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.bo.ProductBO;
import com.jiuji.pick.service.product.entity.Product;

/**
 * <p>
 * 商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductService extends IService<Product> {
    /**
     * 新增商品
     */
    boolean addProduct(ProductBO productBO, Long staffId, boolean isSynchronize);

    /**
     * 修改商品
     */
    boolean updateProduct(ProductBO productBO, Long staffId);
}

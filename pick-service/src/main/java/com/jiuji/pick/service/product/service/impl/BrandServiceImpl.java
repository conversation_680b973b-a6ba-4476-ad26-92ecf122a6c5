package com.jiuji.pick.service.product.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.service.product.entity.Brand;
import com.jiuji.pick.service.product.mapper.BrandMapper;
import com.jiuji.pick.service.product.service.BrandCategoryService;
import com.jiuji.pick.service.product.service.BrandService;
import com.jiuji.pick.service.product.vo.BrandDetailVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 品牌 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Service
public class BrandServiceImpl extends ServiceImpl<BrandMapper, Brand> implements BrandService {

    @Autowired
    private BrandCategoryService brandCategoryService;


    @Override
    public List<Brand> listWithCategorySort(List<Integer> brandIds, int categoryId) {
        if (CollectionUtils.isEmpty(brandIds)) {
            return new ArrayList<>();
        }
        return baseMapper.listWithCategorySort(brandIds, categoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveBrand(BrandDetailVO brandDetailVO, boolean isSynchronize) {
        Brand brand = BrandDetailVO.toSuper(brandDetailVO);
        String url = "https://img2.ch999img.com";
        if (StringUtils.isNotBlank(brand.getUrl()) && brand.getUrl().contains(url)) {
            brand.setUrl(StringUtils.substringAfterLast(brand.getUrl(), url));
        }
        //保存更新品牌
        if (isSynchronize){
            Brand existBrand = baseMapper.selectById(brand.getId());
            if (existBrand == null){
                baseMapper.insertBrand(brand);
            } else {
                baseMapper.updateById(brand);
            }
        }else {
            this.saveOrUpdate(brand);
        }
        //设置id，给同步的消息试用
        brandDetailVO.setId(brand.getId());
        //保存品牌与商品之间的关系
        List<Integer> brandAssociatedCategoryIdList = brandDetailVO.getBrandAssociatedCategoryIdList();
        if (CollectionUtils.isNotEmpty(brandAssociatedCategoryIdList)){
            brandCategoryService.bindCategoryToBrand(brandAssociatedCategoryIdList,brand.getId());
        }
        //同步oa商品
        return Result.success(true);
    }
}

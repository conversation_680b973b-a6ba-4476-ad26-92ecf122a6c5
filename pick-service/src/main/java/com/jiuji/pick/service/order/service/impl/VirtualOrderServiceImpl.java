package com.jiuji.pick.service.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.constant.OrderTipConstant;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.GenerateNoUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.DeliveryFeeArea;
import com.jiuji.pick.service.common.entity.MessageInfo;
import com.jiuji.pick.service.common.entity.MessageInfoUser;
import com.jiuji.pick.service.common.service.DeliveryFeeAreaService;
import com.jiuji.pick.service.common.service.MessageInfoService;
import com.jiuji.pick.service.common.service.MessageInfoUserService;
import com.jiuji.pick.service.order.bo.DeliveryProductBO;
import com.jiuji.pick.service.order.bo.OrderReceiveBO;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.entity.OrderInfoLog;
import com.jiuji.pick.service.order.entity.ProductOrderVersion;
import com.jiuji.pick.service.order.param.CartOrderParam;
import com.jiuji.pick.service.order.param.OrderDeliveryParam;
import com.jiuji.pick.service.order.service.*;
import com.jiuji.pick.service.order.vo.CartInfoVO;
import com.jiuji.pick.service.order.vo.UpdateOrderPriceVo;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.mapper.PartnerUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 虚拟商品订单业务处理
 * @function:
 * @description: VirtualOrderServiceImpl.java
 * @date: 2021/10/09
 * @author: sunfayun
 * @version: 1.0
 */
@Service
@Slf4j
public class VirtualOrderServiceImpl implements VirtualOrderService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Resource
    private CartInfoService cartInfoService;

    @Resource
    private OrderInfoService orderInfoService;

    @Resource
    private OrderDetailInfoService orderDetailInfoService;

    @Resource
    private ProductOrderVersionService productOrderVersionService;

    @Resource
    private OrderInfoLogService orderInfoLogService;

    @Resource
    private MessageInfoService messageInfoService;

    @Resource
    private MessageInfoUserService messageInfoUserService;

    @Resource
    private PartnerUserMapper partnerUserMapper;


    @Resource
    private DeliveryFeeAreaService deliveryFeeAreaService;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 订单改价之前的校验
     *
     * @param updateOrderPriceVoList
     */
    private void checkOrder(UpdateOrderPriceVo updateOrderPriceVoList) {
        Long orderId = Optional.ofNullable(updateOrderPriceVoList.getOrderId()).orElseThrow(() -> new BizException("订单id不能为空"));
        OrderInfo orderInfo = Optional.ofNullable(orderInfoService.getById(orderId)).orElseThrow(() -> new BizException("订单" + orderId + "无效"));
        Integer channelType = orderInfo.getChannelType();
        if(channelType==1){
            throw new BizException("该采购单为小型的小件采购单，暂不持支改价功能");
        }
        Integer orderType = orderInfo.getOrderType();
        Long orderNo = orderInfo.getOrderNo();
        updateOrderPriceVoList.setOrderNo(orderNo);
        if (OrderTypeEnum.VIRTUAL.getCode() != orderType) {
            throw new BizException("订单" + orderNo + "不属于虚拟商品类型订单，暂不支持改价功能");
        }
        //判断订单类型是不是虚拟商品
        Integer orderStatus = orderInfo.getOrderStatus();
        List<Integer> status = Arrays.asList(OrderStatusEnum.DEFAULT.getCode(), OrderStatusEnum.AUDITED.getCode());
        if (!status.contains(orderStatus)) {
            throw new BizException("订单" + orderNo + "状态不支持改价功能，（取消，已发货，完成状态下不持支该改价）");
        }
        List<UpdateOrderPriceVo.UpdateOrderPriceDetailVo> detailVoList = updateOrderPriceVoList.getDetailVoList();
        if (CollectionUtils.isEmpty(detailVoList)) {
            throw new BizException("改价详情不能为空");
        }
        //通过快照校验价格
        productOrderVersionService.checkOrderUpdatePrice(updateOrderPriceVoList);

    }


    /**
     * 订单改价
     *
     * @param updateOrderPriceVoList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPrice(UpdateOrderPriceVo updateOrderPriceVoList) {
        //订单校验
        checkOrder(updateOrderPriceVoList);
        StringBuilder comment = new StringBuilder();
        OrderInfo orderInfo = orderInfoService.getById(updateOrderPriceVoList.getOrderId());
        BigDecimal totalPriceOld = orderInfo.getTotalPrice();
        BigDecimal totalPriceNew = new BigDecimal("0.00");
        List<UpdateOrderPriceVo.UpdateOrderPriceDetailVo> detailVoList = updateOrderPriceVoList.getDetailVoList();
        for (UpdateOrderPriceVo.UpdateOrderPriceDetailVo item : detailVoList) {
            BigDecimal productNewPrice = item.getProductNewPrice();
            BigDecimal productOldPrice = item.getProductOldPrice();
            BigDecimal multiply = productNewPrice.multiply(new BigDecimal(item.getBuyAmount() + ""));
            totalPriceNew=totalPriceNew.add(multiply);
            //OrderDetailInfo表修改
            orderDetailInfoService.lambdaUpdate().eq(OrderDetailInfo::getId, item.getOrderDetailInfoId())
                    .set(OrderDetailInfo::getProductPrice, productNewPrice).update();

            //拼接修改详情日志
            if(productNewPrice.compareTo(productOldPrice)!=0){
                comment.append(item.getProductName()).append("价格由【").append(productOldPrice).append("】改为【").append(productNewPrice).append("】。");
            }

        }
        //OrderInfo表修改
        orderInfoService.lambdaUpdate().eq(OrderInfo::getId, orderInfo.getId())
                .eq(OrderInfo::getTotalPrice, totalPriceOld)
                .set(OrderInfo::getTotalPrice, totalPriceNew)
                .update();
        //拼接修改总价日志
        if(totalPriceOld.compareTo(totalPriceNew)!=0){
            comment.append("订单总价由【").append(totalPriceOld).append("】修改为【").append(totalPriceNew).append("】。");
        }
        // 保存日志
        SupplierTokenInfo tokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck())
                .orElseThrow(() -> new BizException("登录信息失效，请重新登录"));
        orderInfoLogService.saveOrderLog(comment.toString(), OrderLogTypeEnum.OTHER, orderInfo.getId(),
                tokenInfo.getId(), tokenInfo.getLoginName());
    }

    @Override
    public String createOrder(CartInfoVO.CartInfoData cartInfoData, PartnerTokenInfo partnerTokenInfo, CartOrderParam cartOrderParam) {
        // 参数预设
        List<OrderDetailInfo> orderDetailInfoList = Lists.newArrayList();
        List<ProductOrderVersion> productOrderVersionList = Lists.newArrayList();
        List<Long> cartIds = Lists.newArrayList();
        Long supplierId = cartInfoData.getSupplierId();
        BigDecimal totalPrice = BigDecimal.ZERO;
        DeliveryFeeArea deliveryFeeArea = deliveryFeeAreaService.getFeeArea(cartOrderParam.getCityId());

        // 处理购物车商品
        List<CartInfoVO.CartProduct> productList = cartInfoData.getProductList();
        for (CartInfoVO.CartProduct cartProduct : productList) {
            // 购物车id
            cartIds.add(cartProduct.getId());
            // 价格
            Integer priceType = cartProduct.getPriceType();
            BigDecimal price = Integer.valueOf(1).equals(priceType) ? Optional.ofNullable(cartProduct.getBuyTaxPrice()).orElse(BigDecimal.ZERO) : Optional.ofNullable(cartProduct.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO);
            // 采购单详情
            OrderDetailInfo orderDetailInfo = orderInfoService.
                    createSaveInfo(cartProduct, partnerTokenInfo, supplierId, price, null, OrderDetailInfo.class);
            // 商品快照
            ProductOrderVersion productOrderVersion = orderInfoService
                    .createSaveInfo(cartProduct, partnerTokenInfo, supplierId, null, priceType, ProductOrderVersion.class);
            // 物流费
            if (cartProduct.getRemoteDeliveryFee() == 1 && deliveryFeeArea != null && Objects.nonNull(orderDetailInfo)) {
                BigDecimal deliveryFee = deliveryFeeArea.getDeliveryFee().multiply(BigDecimal.valueOf(cartProduct.getProductCount()));
                orderDetailInfo.setDeliveryFee(deliveryFee);
                totalPrice = totalPrice.add(deliveryFee);
            }
            // 总价
            totalPrice = totalPrice.add(BigDecimal.valueOf(cartProduct.getProductCount()).multiply(price));
            orderDetailInfoList.add(orderDetailInfo);
            productOrderVersionList.add(productOrderVersion);
        }

        // 保存采购单相关信息
        OrderInfo orderInfo = createOrderInfo(partnerTokenInfo, cartOrderParam, supplierId, cartInfoData.getSupplierName(), totalPrice);
        boolean saveResult = saveOrderInfo(orderInfo, orderDetailInfoList, productOrderVersionList, cartIds);
        if (!Boolean.TRUE.equals(saveResult)) {
            return OrderTipConstant.SAVE_ERROR;
        }
        orderInfoLogService.saveOrderLog("虚拟商品采购单已生成，采购单号：" + orderInfo.getOrderNo(), OrderLogTypeEnum.GENERATE_NO,
                orderInfo.getId(), partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());
        return null;
    }

    @Override
    public Result<String> cancelOrder(Long orderNo, Long xTenant) {
        return Result.success();
    }

    @Override
    public Result<String> deliveryProduct(List<DeliveryProductBO> deliveryProductBOList, OrderInfo orderInfo, OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo) {
        return Result.success();
    }

    @Override
    public List<String> orderReceive(List<OrderInfo> orderInfoList) {
        // 参数预设
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        List<String> errorInfoList = Lists.newArrayList();
        List<OrderInfo> orderInfos = Lists.newArrayList();
        List<OrderInfoLog> orderInfoLogList  = Lists.newArrayList();
        List<MessageInfoUser> messageInfoUserList = Lists.newArrayList();
        OrderReceiveBO orderReceiveBO = new OrderReceiveBO();
        // 设置订单id与合作伙伴名称对应关系
        Map<Long, Long> idsMap = orderInfoList.stream().collect(Collectors.toMap(OrderInfo::getId, OrderInfo::getPartnerId));
        Map<Long, String> nameMap = partnerUserMapper.selectBatchIds(idsMap.values()).stream().collect(Collectors.toMap(PartnerUser::getId, PartnerUser::getName));
        Map<Long, String> partnerNameMap = idsMap.keySet().stream()
                .collect(Collectors.toMap(orderInfoId -> orderInfoId, orderInfoId -> nameMap.get(idsMap.get(orderInfoId))));

        for (OrderInfo orderInfo : orderInfoList) {
            orderInfo.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
            OrderInfoLog orderInfoLog;
            // 记录订单日志
            if (Objects.nonNull(partnerTokenInfo)) {
                orderInfoLog = new OrderInfoLog().setOrderId(orderInfo.getId()).setContent("订单已完成").setType(OrderLogTypeEnum.COMPLETED.getCode())
                        .setOperationUserId(partnerTokenInfo.getLoginOAUserId()).setOperationUserName(partnerTokenInfo.getLoginOAUserName());
            } else {
                orderInfoLog = new OrderInfoLog().setOrderId(orderInfo.getId()).setContent("订单已完成")
                        .setType(OrderLogTypeEnum.COMPLETED.getCode()).setOperationUserName("系统");
            }
            // 站内信息推送，只在自动收货时有推送
            if (Objects.isNull(partnerTokenInfo) && StringUtils.isNotBlank(partnerNameMap.get(orderInfo.getId()))) {
                MessageInfo messageInfo = new MessageInfo().setContent("您的订单：" + orderInfo.getOrderNo() + "已自动确认收货，订单已完成").setTitle("订单自动收货通知")
                        .setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode()).setUserType(MessagePushUserTypeEnum.PARTNER.getCode());
                messageInfoService.saveOrUpdate(messageInfo);
                MessageInfoUser messageInfoUser = new MessageInfoUser().setMessageId(messageInfo.getId()).setUserId(orderInfo.getPartnerId())
                        .setHasRead(1).setUserName(partnerNameMap.get(orderInfo.getId()));
                messageInfoUserList.add(messageInfoUser);
            }
            orderInfos.add(orderInfo);
            orderInfoLogList.add(orderInfoLog);
        }
        // 保存订单收货确认信息
        orderReceiveBO.setOrderInfoList(orderInfos);
        orderReceiveBO.setOrderInfoLogList(orderInfoLogList);
        orderReceiveBO.setMessageInfoUserList(messageInfoUserList);
        boolean saveResult = this.saveOrderReceiveInfo(orderReceiveBO);
        if (!saveResult) {
            List<Long> orderNo = orderInfoList.stream().map(OrderInfo::getOrderNo).collect(Collectors.toList());
            errorInfoList.add("虚拟商品订单确认收货信息保存失败, 订单号：" + JSON.toJSON(orderNo));
        }
        return errorInfoList;
    }

    /**
     * 创建虚拟商品采购单
     *
     * @param partnerTokenInfo partnerTokenInfo
     * @param cartOrderParam cartOrderParam
     * @param supplierId supplierId
     * @param supplierName supplierName
     * @param totalPrice totalPrice
     * @return 采购单
     */
    private static OrderInfo createOrderInfo(PartnerTokenInfo partnerTokenInfo, CartOrderParam cartOrderParam, Long supplierId, String supplierName, BigDecimal totalPrice) {
        // 内部流程，不记录渠道信息，且直接生成订单号和采购单
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo(GenerateNoUtil.generateOrderNo());
        orderInfo.setOrderType(OrderTypeEnum.VIRTUAL.getCode());
        orderInfo.setOrderStatus(OrderStatusEnum.AUDITED.getCode());
        orderInfo.setPartnerId(partnerTokenInfo.getId());
        orderInfo.setChannelId(CommonConstant.DEFAULT_CHANNEL_ID);
        orderInfo.setReceivePoiId(cartOrderParam.getAddressId());
        orderInfo.setReceivePoiName(cartOrderParam.getAddressName());
        orderInfo.setReceivePoiCityId(cartOrderParam.getCityId());
        orderInfo.setSupplierId(supplierId);
        orderInfo.setXtenantId(partnerTokenInfo.getXtenant());
        orderInfo.setOrderTitle(supplierName + "采购单");
        orderInfo.setTotalPrice(totalPrice);
        orderInfo.setContactPerson(cartOrderParam.getContactPerson());
        orderInfo.setContactPhone(cartOrderParam.getContactPhone());
        orderInfo.setReceiveAddress(cartOrderParam.getReceiveAddress());
        return orderInfo;
    }

    /**
     * 保存订单信息
     *
     * @param orderInfo 采购单
     * @param orderDetailInfoList 采购单详情
     * @param productOrderVersionList 商品快照
     * @return 错误信息
     */
    private Boolean saveOrderInfo(OrderInfo orderInfo, List<OrderDetailInfo> orderDetailInfoList, List<ProductOrderVersion> productOrderVersionList, List<Long> cartIds) {
        // 保存信息
        boolean saveResult = true;
        boolean insert = orderInfo.insert();
        orderDetailInfoList.forEach(orderDetailInfo -> orderDetailInfo.setOrderNo(orderInfo.getOrderNo()).setOrderId(orderInfo.getId()));
        productOrderVersionList.forEach(productOrderVersion -> productOrderVersion.setOrderNo(orderInfo.getOrderNo()).setOrderId(orderInfo.getId()));
        if (!insert) {
            saveResult = false;
        }
        boolean save1 = orderDetailInfoService.saveBatch(orderDetailInfoList);
        if (!save1) {
            orderInfo.deleteById();
            saveResult = false;
        }
        boolean save2 = productOrderVersionService.saveBatch(productOrderVersionList);
        if (!save2) {
            orderInfo.deleteById();
            saveResult = false;
        }
        boolean remove = cartInfoService.removeByIds(cartIds);
        orderInfoLogService.errorLog(insert, save2, save1, remove, orderInfo, orderInfo.getOrderNo(), cartIds);
        return saveResult;
    }

    /**
     * 保存订单确认收货信息
     */
    private Boolean saveOrderReceiveInfo(OrderReceiveBO orderReceiveBO) {
        return transactionTemplate.execute(transactionStatus -> {
            try {
                orderInfoService.updateBatchById(orderReceiveBO.getOrderInfoList());
                if (CollectionUtils.isNotEmpty(orderReceiveBO.getOrderInfoLogList())) {
                    orderInfoLogService.saveOrUpdateBatch(orderReceiveBO.getOrderInfoLogList());
                }
                if (CollectionUtils.isNotEmpty(orderReceiveBO.getMessageInfoList())) {
                    messageInfoService.saveOrUpdateBatch(orderReceiveBO.getMessageInfoList());
                }
                if (CollectionUtils.isNotEmpty(orderReceiveBO.getMessageInfoUserList())) {
                    messageInfoUserService.saveOrUpdateBatch(orderReceiveBO.getMessageInfoUserList());
                }
                return true;
            } catch (Exception e) {
                transactionStatus.setRollbackOnly();
                log.error("虚拟商品订单确认收货信息保存失败,orderReceiveBO:{},exception:", JSON.toJSON(orderReceiveBO), e);
                return false;
            }
        });
    }
}

package com.jiuji.pick.service.product.bo;

import com.jiuji.pick.service.common.param.FileUploadParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @function: 添加商品请求参数
 * @description: AddOrUpdateProductParam.java
 * @date: 2021/04/29
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class AddOrUpdateProductParam {

    @NotNull(message = "主站产品ID不能为空")
    private Long jiuJiProductId;
    @NotNull(message = "ppid不能为空")
    private Long ppid;
    @NotNull(message = "建议售价不能为空")
    private BigDecimal advicePrice;
    @NotBlank(message = "商品卖点不能为空")
    private String productFuture;
    @NotBlank(message = "默认配置不能为空")
    private String productConfig;
    @NotNull(message = "商品类型不能为空")
    private Integer productType;
    private Integer defaultArea;
    private Integer hotArea;
    private Integer happyArea;
    /**
     * 1-表示同步
     * 0/null-表示不同步
     * 同步其他商品
     */
    private Integer synOtherPpid;
    private Integer recommendArea;
    private List<FileUploadParam> fileList;
    private List<FileUploadParam> imageList;
    private AddOrUpdateProductParamOld paramOld;

}

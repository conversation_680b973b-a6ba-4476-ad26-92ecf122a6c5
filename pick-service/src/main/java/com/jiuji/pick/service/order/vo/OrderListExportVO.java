package com.jiuji.pick.service.order.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单列表VO
 *
 * <AUTHOR>
 * @since 2021-05-20
 */
@Data
public class OrderListExportVO {

    /**
     * 采购单号
     */
    private Long orderNo;
    /**
     * 销售单号
     */
    private String saleOrderNo;
    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 采购方
     */
    private String partnerName;

    /**
     * ppid
     */
    private Integer ppid;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 购买数量
     */
    private Integer buyAmount;

    /**
     * 单价
     */
    private BigDecimal productPrice;
    /**
     * 合计金额
     */
    private BigDecimal totalAmount;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 含税 未税
     */
    private Integer priceType;


}

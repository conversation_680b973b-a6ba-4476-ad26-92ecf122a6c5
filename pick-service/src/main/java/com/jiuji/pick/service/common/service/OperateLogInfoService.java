package com.jiuji.pick.service.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.OperateLogInfo;
import com.jiuji.pick.service.common.param.ConditionalQueryLogInfoParam;
import com.jiuji.pick.service.common.param.QueryOperateLogInfoParam;
import com.jiuji.pick.service.common.vo.QueryOperateLogVo;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
public interface OperateLogInfoService extends IService<OperateLogInfo> {

    /***
     * @description: 添加日志
     * @Param: [relateId, type, userId, userName, content, showType]
     * @author: Lbj
     * @date: 2021/5/6 15:16
     */
    boolean addLog(String relateId, Integer type, Long optUserId, String optUserName, Long userId, String content, Integer showType);

    /**
     * 管理员日志列表查询
     * @param param
     * @return
     */
    Result<Page<QueryOperateLogVo>> queryAdmLogInfo(ConditionalQueryLogInfoParam param);

    /**
     * 查询供应商操作日志
     * @param param
     * @return
     */
    Result<Page<QueryOperateLogVo>> querySupplierLogInfo(ConditionalQueryLogInfoParam param);
}

package com.jiuji.pick.service.rpc.cloud;

import com.jiuji.pick.service.rpc.config.BigDataClientFallbackConfig;
import com.jiuji.pick.service.rpc.fallback.LogisticsCloudFallbackFactory;
import com.jiuji.pick.service.rpc.vo.CommonQueryRoutReq;
import com.jiuji.pick.service.rpc.vo.QueryRoutTrackRes;
import com.jiuji.tc.common.vo.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@FeignClient(value = "logistics-service", configuration = BigDataClientFallbackConfig.class,fallbackFactory = LogisticsCloudFallbackFactory.class)
public interface LogisticsCloud {
    /**
     * 物流轨迹中台查询
     * @param commonQueryRoutReq
     * @return
     */
    @PostMapping("/logistics/api/logistics-center-route/query-route/v2")
    R<QueryRoutTrackRes> queryRoute(@RequestBody CommonQueryRoutReq commonQueryRoutReq, @RequestHeader("Authorization") String token);

    /**
     * OA异常监控
     * @param comment
     * @return
     */
    @GetMapping("/logistics/api/ChwNoticeController/sendMessage/v2")
    void sendNewsTwo(@RequestParam("comment") String comment);
}

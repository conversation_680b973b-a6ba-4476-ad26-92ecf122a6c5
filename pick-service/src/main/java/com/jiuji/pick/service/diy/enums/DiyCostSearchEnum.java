package com.jiuji.pick.service.diy.enums;

import com.jiuji.pick.common.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> <PERSON><PERSON> ming
 * @date 2021/6/4 18:56
 */
@AllArgsConstructor
@Getter
public enum DiyCostSearchEnum {
    PRODUCTID(1, "商品id"),
    PPID(2, "ppid"),
    /**
     * 查询选项枚举
     **/
    PRODUCTNAME(3, "商品名称");


    private final Integer code;

    private final String message;

  
    /**
     * 搜索内容仅为熟悉类型
     * @param searchType  搜索类型
     * @param searchValue 搜索内容
     * @throws IllegalArgumentException 错误的参数
     */
    public static void onlyNumber(Integer searchType, String searchValue) {
        if (StringUtils.isEmpty(searchValue)) {
            return;
        }
        DiyCostSearchEnum[] onlyNumberTypes = {PPID,PRODUCTID};
        for (DiyCostSearchEnum searchEnum : onlyNumberTypes) {
            if (searchEnum.getCode().equals(searchType)) {
                try {
                    Integer.valueOf(searchValue);
                } catch (NumberFormatException e) {
                    throw new BizException("请输入正确的" + searchEnum.getMessage());
                }
            }
        }
    }
}

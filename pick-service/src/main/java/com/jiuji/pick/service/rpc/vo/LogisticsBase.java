package com.jiuji.pick.service.rpc.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 说明： 物流订单创建公共 Res （支持：美团）
 *
 * <AUTHOR>
 */
@Data
@Valid
public class LogisticsBase {
    /**
     * 快递类型
     **/

    @NotNull(message = "快递类型不能为空！")
    @JsonProperty(value = "expressType")
    private Integer expressType;

    /**
     * 租户Id
     **/

    @NotNull(message = "租户Id不能为空!")
    @JsonProperty(value = "xTenantId")
    private Long xTenantId;


    /**
     * 寄件门店id
     **/
    @JsonProperty(value = "sendShopId")
    private Integer sendShopId;

    /**
     * 寄件门店名称
     **/
    @JsonProperty(value = "sendShopName")
    private String sendShopName;

    /**
     * 收货门店id
     **/
    @JsonProperty(value = "receiveShopId")
    private Integer receiveShopId;

    /**
     * 收货门店名称
     **/
    @JsonProperty(value = "receiveShopName")
    private String receiveShopName;


}

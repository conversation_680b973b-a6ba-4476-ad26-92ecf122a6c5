package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.vo.CategoryTreeVo;

import java.util.List;

/**
 * <p>
 * 分类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface CategoryService extends IService<Category> {

    /**
     * 通过id查找
     */
    Category getOneById(Integer id);

    /**
     * 获取分类树
     * @param categoryType，类型：0全部，1小件
     * @return
     */
    List<CategoryTreeVo> getCategoryTree(Integer categoryType);

    /**
     * 获取子分类数据,不是树形结构
     * @param cid
     * @return
     */
    List<Category> getChildCategory(Integer cid);


    /**
     * 生成分类关键列表
     */
    Boolean modifyCategorySearchKeyword(Integer cid, String keywordStr);


    /**
     * 更新，新增分类
     */
    Result<Boolean> checkAndSave(Category category, boolean isSynchronize) throws Exception;

}

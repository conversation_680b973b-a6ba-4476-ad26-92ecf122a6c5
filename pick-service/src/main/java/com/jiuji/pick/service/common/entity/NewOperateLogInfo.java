package com.jiuji.pick.service.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 新操作日志记录表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_new_operate_log_info")
public class NewOperateLogInfo extends Model<NewOperateLogInfo> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联业务单号
     */
    private String relateId;

    /**
     *     SUPPLY_MANAGEMENT(1, "可供应商品管理"),
     *     SUPPLIER_INFORMATION(2, "供应商信息"),
     *     ORDER_MANAGEMENT(3, "订单管理"),
     *     GOODS_ON_AND_OFF_SHELVES(4, "商品库管理上下架"),
     *     COMMODITY_WAREHOUSE_MANAGEMENT(5, "商品库管理"),
     *     SUPPLIER_BOUND_GOODS_QUERY(6, "供应商绑定商品查询"),
     *     BLACK_AND_WHITE_LIST_MANAGEMENT(7, "黑白名单管理"),
     *     SUPPLIER_LIST(8, "供应商详情"),
     *     PARTNER_LIST_ORDER(9, "合作伙伴列表下单"),
     *     PARTNER_LIST(10, "合作伙伴列表"),
     *     PURCHASE_ORDER_DATA_QUERY(11, "采购单数据查询");
     */
    private Integer type;

    /**
     * 操作人ID
     */
    private Long optUserId;

    /**
     * 操作人姓名
     */
    private String optUserName;


    /**
     * 日志内容
     */
    private String content;

    /**
     * 展示类型，0不展示，1所有用户，2供应商用户，3合作伙伴
     */
    private Integer showType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 删除标识
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

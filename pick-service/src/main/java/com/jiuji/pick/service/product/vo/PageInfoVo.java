package com.jiuji.pick.service.product.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageInfoVo {
    private String searchKey;

    /**
     * 每页数量
     */
    @NotNull(message = "每页数量不能为空")
    private Long size;

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

}

package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jiuji.pick.service.product.entity.CommodityAssociation;
import com.jiuji.pick.service.product.vo.AssociationTerms;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CommodityAssociationServiceMapper extends BaseMapper<CommodityAssociation> {

    /**
     * 获取最新的关联标识
     * @return
     */
    @Select("SELECT ifnull(association_id, 0)+1 as associationId FROM t_commodity_association order by association_id desc LIMIT 1")
    Integer getNewestAssociationId();


    @Select(" select DISTINCT info.productid as productId  ,info.product_name as productName FROM t_pick_product pick  " +
            " left join t_product_info info on info.productid =pick .jiu_ji_product_id " +
            " ${ew.customSqlSegment} ")
    List<AssociationTerms> selectAssociationTerms(@Param(Constants.WRAPPER) QueryWrapper wrapper);
}

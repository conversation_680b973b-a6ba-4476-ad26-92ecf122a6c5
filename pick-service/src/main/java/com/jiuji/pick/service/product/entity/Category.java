package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 分类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_category")
public class Category extends Model<Category> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer parentId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类级别
     */
    private Integer level;

    private Integer child;

    /**
     * 排序
     */
    @TableField("`rank`")
    private Integer rank;

    /**
     * 路径，id以逗号连接，前后加逗号
     */
    private String path;

    private String pic;

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 描述
     */
    private String description;

    private Boolean display;

    /**
     * 价格区间：多个价格区间，逗号间隔
     */
    private String prices;

    @JsonProperty("isMobile")
    private Boolean isMobile;

    private Integer newsid;

    private Integer newsid2;

    private String ppids;

    /**
     * 分类首页标题
     */
    private String pageTitle;

    @JsonProperty("isService")
    private Boolean isService;

    /**
     * 主站是否展示
     */
    private Boolean isShow;

    private Integer adid;

    @JsonProperty("isStatistics")
    private Boolean isStatistics;

    private String serviceIds;

    /**
     * 商品排序值
     */
    private Integer productSortValue;

    private String notSupportDelivery;

    /**
     * 系统排序
     */
    private Boolean sysSort;

    /**
     * 输出分类 1是 0否
     */
    @JsonProperty("isOutputCategory")
    private Boolean isOutputCategory;

    /**
     * 是否虚拟商品 1是 0否
     */
    @JsonProperty("isVirtualGoods")
    private Boolean isVirtualGoods;

    /**
     * 搜索关键字
     */
    @TableField(exist = false)
    private String searchKeywords;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    public static final String ID = "id";

    public static final String PARENTID = "parent_id";

    public static final String NAME = "name";

    public static final String LEVEL = "level";

    public static final String CHILD = "child";

    public static final String RANK = "`rank`";

    public static final String PATH = "path";

    public static final String PIC = "pic";

    public static final String KEYWORDS = "keywords";

    public static final String DESCRIPTION = "description";

    public static final String DISPLAY = "display";

    public static final String PRICES = "prices";

    public static final String ISMOBILE = "is_mobile";

    public static final String NEWSID = "newsid";

    public static final String NEWSID2 = "newsid2";

    public static final String PPIDS = "ppids";

    public static final String PAGETITLE = "page_title";

    public static final String ISSERVICE = "is_service";

    public static final String ISSHOW = "is_show";

    public static final String ADID = "adid";

    public static final String ISSTATISTICS = "is_statistics";

    public static final String SERVICEIDS = "service_ids";

    public static final String PRODUCTSORTVALUE = "product_sort_value";

    public static final String NOTSUPPORTDELIVERY = "not_support_delivery";

    public static final String IS_OUTPUT_CATEGORY = "is_output_category";

    public static final String IS_VIRTUAL_GOODS = "is_virtual_goods";

    public static final String SYS_SORT = "sys_sort";

}

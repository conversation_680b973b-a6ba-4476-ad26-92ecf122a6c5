package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.entity.CategorySearchWord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 分类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface CategoryMapper extends BaseMapper<Category> {

    /**
     * 获取子节点数
     */
    Integer getChildCount(@Param("parentId") Integer parentId);

    /**
     * 获取子节点id列表
     */
    List<Integer> getChildIdList(@Param("id") Integer id);

    /**
     * 获取需要重设的path
     */
    String getNeedResetPath(@Param("id") Integer id);

    /**
     * 重新设置子目录
     */
    void resetSubCategory(@Param("path") String path, @Param("level") Integer level,
                          @Param("id") Integer id);

    /**
     * 重新刷新分类的Child的数量
     */
    @SqlParser(filter = true)
    void resetCategoryChild();

    /**
     * 更新商品分类树Id
     */
    void resetProductCatgs(@Param("id") Integer id);

    /**
     * 更新父亲节点的子节点数
     */
    void updateParentChild(@Param("parentId") Integer parentId, @Param("child") Integer child);

    /**
     * 删除分类的搜索关键字
     */
    void deleteCategorySearchKeyword(@Param("cid") Integer cid);

    /**
     * 插入分类的搜索关键字
     */
    Boolean insertCategorySearchWord(
            @Param("searchWordList") List<CategorySearchWord> searchWordList);

    /**
     * 删除所有子节点
     */
    void deleteAllChild(@Param("cid") Integer cid);

    /**
     * 减少父节点的child值
     */
    void decreaseParent(@Param("parentId") Integer parentId, @Param("child") Integer child);

    /**
     * 获取所有子节点
     */
    List<Long> getAllChild(@Param("id") Long id);

    /***
     * 根据父分类id的列表，获取所有子分类id的列表
     * @param idList
     * @return
     */
    List<Long> getAllChildByIdList(@Param("idList") List<Long> idList);

    /**
     * 获取多个节点的所有子节点
     */
    List<Long> getMultipleParentAllChild(@Param("ids")List<Long> ids);

    /**
     * 设置所有子节点不可见
     */
    Integer displayForAllChild(@Param("id") Integer id);

    int insertCategory(Category category);
}

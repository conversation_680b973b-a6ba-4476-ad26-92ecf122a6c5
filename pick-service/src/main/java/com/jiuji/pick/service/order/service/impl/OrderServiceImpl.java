package com.jiuji.pick.service.order.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.jiuji.cloud.stock.service.ChwQueryCloud;
import com.jiuji.cloud.stock.vo.response.SubInfoDTO;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.component.minifile.MiniFileConfig;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.constant.OrderTipConstant;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.*;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.param.MessageInfoSaveParam;
import com.jiuji.pick.service.common.service.MessageInfoService;
import com.jiuji.pick.service.order.bo.DeliveryProductBO;
import com.jiuji.pick.service.order.bo.SupplierProductInfoBO;
import com.jiuji.pick.service.order.entity.*;
import com.jiuji.pick.service.order.mapper.OrderInfoMapper;
import com.jiuji.pick.service.order.param.*;
import com.jiuji.pick.service.order.service.*;
import com.jiuji.pick.service.order.vo.*;
import com.jiuji.pick.service.product.entity.PickProduct;
import com.jiuji.pick.service.product.entity.SupplierProductDetail;
import com.jiuji.pick.service.product.service.PickProductService;
import com.jiuji.pick.service.product.service.SupplierProductDetailService;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.service.PartnerUserService;
import com.jiuji.pick.service.user.service.SupplierUserService;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    private static final Long NOT_EXIST_TYPE = -1L;


    /**
     * 员工维度 （正式 3469，测试 4647），明细 （正式 4667，测试 4658）
     */
    private final List<Integer> excludeDimension = Arrays.asList(3469, 4647, 4667, 4658);

    /**
     * excel标题
     */
    private static final String[] EXCEL_TITLE = {"订单号", "订单状态", "采购方", "供应商名称", "ppid", "商品名称",
            "数量", "单价", "合计金额"};

    private static final String NO_ORDER_DETAIL = "订单明细不存在";

    private static final String iframeUrl = "https://oa.9ji.com/NewDataAnalysisPlatform/embed/question/%s#bordered=true&titled=false";


    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private ChwQueryCloud chwQueryCloud;
    @Resource
    private CartInfoService cartInfoService;
    @Resource
    private SupplierUserService supplierUserService;
    @Resource
    private MiniFileConfig miniFileConfig;
    @Resource
    private OrderInfoService orderInfoService;
    @Resource
    private OrderDetailInfoService orderDetailInfoService;
    @Resource
    private MessageInfoService messageInfoService;
    @Resource
    private PartnerUserService partnerUserService;
    @Resource
    private OaService oaService;
    @Resource
    private NeoService neoService;
    @Resource
    private DeliveryOrderInfoService deliveryOrderInfoService;
    @Resource
    private PickProductService pickProductService;
    @Resource
    private OrderInfoLogService orderInfoLogService;
    @Resource
    private OrderServiceFactory orderServiceFactory;
    @Resource
    private OrderFactoryProducer orderFactoryProducer;
    @Resource
    private SupplierProductDetailService supplierProductDetailService;

    @Resource
    private OrderInfoMapper infoMapper;


    /**
     * 已全部发货（不含部分发货订单）的订单，从发货时间开始计算，超过20天订单未完成的，每天定时10:30系统自动变为已完成，订单记录日志：订单发货后超过20天未完成，订单自动完成交易
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String automaticPickup() {
        //查询要处理单据
        List<AutomaticPickupOrder> automaticPickupOrders = orderInfoService.selectAutomaticPickupOrder();
        if(CollectionUtils.isEmpty(automaticPickupOrders)){
            return "没有需要自动完成的订单";
        }
        List<Long> orderIds = automaticPickupOrders.stream().map(AutomaticPickupOrder::getId).collect(Collectors.toList());
        String orderIdStr = orderIds.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(","));
        log.warn("需要处理自动完成的订单：{}", orderIdStr);
        LocalDateTime now = LocalDateTime.now();
        boolean update = orderInfoService.lambdaUpdate().in(OrderInfo::getId, orderIds)
                .set(OrderInfo::getOrderStatus, OrderStatusEnum.COMPLETED.getCode())
                .set(OrderInfo::getUpdateTime, now)
                .set(OrderInfo::getFinishTime, now)
                .update();
        String result = "";
        if(update){
            result = String.format("需要处理自动完成的finishTime：%s订单：%s", now,orderIdStr);
            log.warn(result);
            //进行订单日志记录
            List<OrderInfoLog> orderInfoLogs = orderIds.stream().map(item -> {
                OrderInfoLog orderInfoLog = new OrderInfoLog();
                orderInfoLog.setContent("订单发货后超过20天未完成，订单自动完成交易");
                orderInfoLog.setType(OrderLogTypeEnum.COMPLETED.getCode());
                orderInfoLog.setOrderId(item);
                orderInfoLog.setCreateTime(now);
                orderInfoLog.setUpdateTime(now);
                orderInfoLog.setOperationUserId(0L);
                orderInfoLog.setOperationUserName("系统");
                return orderInfoLog;
            }).collect(Collectors.toList());
            boolean b = orderInfoLogService.saveBatch(orderInfoLogs);
            if(!b){
                throw new BizException("订单日志保存失败");
            }
        } else {
            throw new BizException("自动完成失败");
        }
        return result;
    }

    @Override
    public Result updateOrderPrice(UpdateOrderPriceVo updateOrderPriceVoList) {
        Long orderId = Optional.ofNullable(updateOrderPriceVoList.getOrderId())
                .orElseThrow(() -> new BizException("订单id不能为空"));
        OrderInfo orderInfo = Optional.ofNullable(orderInfoService.getById(orderId))
                .orElseThrow(()->new BizException("订单id"+orderId+"查询为空"));
        Integer channelType = Optional.ofNullable(orderInfo.getChannelType())
                .orElseThrow(() -> new BizException("订单" + orderId + "渠道类型查询为空"));
        Integer orderType = Optional.ofNullable(orderInfo.getOrderType())
                .orElseThrow(() -> new BizException("订单" + orderId + "订单类型型查询为空"));
        //获取到oa工厂
        AbstractOrderFactory orderFactory = orderFactoryProducer.getOrderFactory(channelType+1);
        //获取到件业务
        OrderCommonService orderService = orderFactory.getOrderService(orderType);
        orderService.updateOrderPrice(updateOrderPriceVoList);
        return Result.success();
    }

    @Override
    public Result<String> getAllDomainsByOrder(Long orderNumber) {
        PartnerTokenInfo tokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (tokenInfo == null) {
            return Result.error("登录失效，请重新登录");
        }
        String jumpUrl = orderFactoryProducer.getOrderFactory(tokenInfo.getSource()).getJumpUrl(tokenInfo, orderNumber);
        return Result.success(jumpUrl);
    }

    @Override
    public Result<String> submitOrderByCart(CartOrderParam cartOrderParam) {
        // 获取用户信息
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        Result<String> checkPartnerInfo = checkPartnerInfo(partnerTokenInfo);
        if (!checkPartnerInfo.isSucceed()) {
            return checkPartnerInfo;
        }

        // 供应商商品集合
        List<CartInfoVO.CartInfoData> cartInfoDataList = new ArrayList<>();
        // 处理购物车并添加订单商品信息
        String dealCartAndAddProduct = dealCartAndAddProduct(cartOrderParam, cartInfoDataList, partnerTokenInfo);
        if (StringUtils.isNotBlank(dealCartAndAddProduct)) {
            return Result.error(dealCartAndAddProduct);
        }

        // 提交订单
        return submitOrder(cartInfoDataList, partnerTokenInfo, cartOrderParam);
    }

    /**
     * 处理购物车并添加订单商品信息
     *
     * @param cartOrderParam
     * @param cartInfoDataList
     * @param partnerTokenInfo
     * @return
     */
    private String dealCartAndAddProduct(CartOrderParam cartOrderParam, List<CartInfoVO.CartInfoData> cartInfoDataList,
                                         PartnerTokenInfo partnerTokenInfo) {
        // 获取购物车
        List<CartOrderParam.CartOrderData> cartOrderDataList = cartOrderParam.getCartOrderDataList();
        // 获取要下单的购物车集合
        List<CartInfo> cartInfoList = getCartInfoList(cartOrderDataList, partnerTokenInfo.getId());
        if (CollectionUtils.isEmpty(cartInfoList)) {
            return "没有要下单的购物车";
        }

        // 供应商购物车分组
        Map<Long, List<CartInfo>> supplierMap = cartInfoList.stream().collect(Collectors.groupingBy(CartInfo::getSupplierUserId));

        // 获取分组供应商信息
        Map<Long, SupplierUser> supplierUserMap = getSupplierUserMap(supplierMap);

        // 查询购物车商品对应信息
        Map<Long, SupplierProductInfoBO> supplierProductInfoMap = getSupplierProductInfoMap(cartInfoList);

        // 对应购物车的数量和价格类型
        Map<Long, CartOrderParam.CartOrderData> cartOrderDataMap = Maps.newHashMapWithExpectedSize(cartOrderDataList.size());
        for (CartOrderParam.CartOrderData cartOrderData : cartOrderDataList) {
            cartOrderDataMap.put(cartOrderData.getCartId(), cartOrderData);
        }

        // 处理购物车里面的数据
        String res = dealCart(supplierMap, supplierUserMap, supplierProductInfoMap, cartInfoDataList, cartOrderDataMap);
        if (StringUtils.isNotBlank(res)) {
            return res;
        }
        return null;
    }


    /**
     * 把含税和为含税的商品拆开
     * @param cartInfoData
     * @return
     */
    private List<CartInfoVO.CartInfoData> splitOrder(CartInfoVO.CartInfoData cartInfoData){
        CartInfoVO.CartInfoData cartInfoDataTaxIncluded = new CartInfoVO.CartInfoData();
        CartInfoVO.CartInfoData cartInfoDataNoTaxIncluded = new CartInfoVO.CartInfoData();

        List<CartInfoVO.CartProduct> productList = cartInfoData.getProductList();
        if(CollectionUtils.isEmpty(productList)){
            throw new BizException("采购商品不能为空");
        }
        List<CartInfoVO.CartProduct> productListTaxIncluded = new ArrayList<>();
        List<CartInfoVO.CartProduct> productListNoTaxIncluded = new ArrayList<>();
        productList.forEach((CartInfoVO.CartProduct item)->{
            Integer priceType = Optional.ofNullable(item.getPriceType()).orElse(Integer.MAX_VALUE);
            //含税
            if(priceType.equals(OrderPriceTypeEnum.TAX_INCLUDED.getCode())){
                CartInfoVO.CartProduct cartProductTaxIncluded = new CartInfoVO.CartProduct();
                BeanUtils.copyProperties(item,cartProductTaxIncluded);
                productListTaxIncluded.add(cartProductTaxIncluded);
            }
            //非含税
            if(priceType.equals(OrderPriceTypeEnum.UNTAXED.getCode())){
                CartInfoVO.CartProduct cartProductNotTaxIncluded = new CartInfoVO.CartProduct();
                BeanUtils.copyProperties(item,cartProductNotTaxIncluded);
                productListNoTaxIncluded.add(cartProductNotTaxIncluded);
            }
        });
        List<CartInfoVO.CartInfoData> result= new ArrayList<>();
        if(CollectionUtils.isNotEmpty(productListTaxIncluded)){
            cartInfoDataTaxIncluded.setProductType(cartInfoData.getProductType());
            cartInfoDataTaxIncluded.setSupplierId(cartInfoData.getSupplierId());
            cartInfoDataTaxIncluded.setProductTypeStr(cartInfoData.getProductTypeStr());
            cartInfoDataTaxIncluded.setSupplierName(cartInfoData.getSupplierName());
            cartInfoDataTaxIncluded.setProductList(productListTaxIncluded);
            result.add(cartInfoDataTaxIncluded);
        }
        if(CollectionUtils.isNotEmpty(productListNoTaxIncluded)){
            cartInfoDataNoTaxIncluded.setProductType(cartInfoData.getProductType());
            cartInfoDataNoTaxIncluded.setSupplierId(cartInfoData.getSupplierId());
            cartInfoDataNoTaxIncluded.setProductTypeStr(cartInfoData.getProductTypeStr());
            cartInfoDataNoTaxIncluded.setSupplierName(cartInfoData.getSupplierName());
            cartInfoDataNoTaxIncluded.setProductList(productListNoTaxIncluded);
            result.add(cartInfoDataNoTaxIncluded);
        }
        return result;
    }

    /**
     * 提交订单
     *
     * @param cartInfoDataList
     * @param partnerTokenInfo
     * @param cartOrderParam
     * @return
     */
    private Result<String> submitOrder(List<CartInfoVO.CartInfoData> cartInfoDataList,
                                       PartnerTokenInfo partnerTokenInfo,
                                       CartOrderParam cartOrderParam) {
        PartnerUser partnerUser = partnerUserService.getById(partnerTokenInfo.getId());
        if(partnerUser == null) {
            return Result.errorInfo("合作伙伴信息不存在");
        }
        // 返回结果消息
        List<String> errorMessage = new ArrayList<>();
        List<String> successMessage = new ArrayList<>();
        // 循环供应商下订单
        for (CartInfoVO.CartInfoData cartInfoData : cartInfoDataList) {
            // 创建订单
            OrderCommonService orderService = orderFactoryProducer.getOrderFactory(partnerUser.getSource()).getOrderService(OrderTypeEnum.getOrderTypeByProductType(cartInfoData.getProductType()));
            String order;
            List<Integer> typeList =Arrays.asList(OrderTypeEnum.SMALL.getCode(), OrderTypeEnum.REPAIR_PART.getCode(),OrderTypeEnum.FIX_ASSETS.getCode(),OrderTypeEnum.COMMON_ASSETS.getCode());
            if(typeList.contains(cartInfoData.getProductType())){
                List<CartInfoVO.CartInfoData> cartInfoDataSplit= splitOrder(cartInfoData);
                StringJoiner joiner = new StringJoiner(",");
                cartInfoDataSplit.forEach((CartInfoVO.CartInfoData item)->{
                    String serviceOrder = orderService.createOrder(item, partnerTokenInfo, cartOrderParam);
                    if(StringUtils.isNotEmpty(serviceOrder)){
                        joiner.add(serviceOrder);
                    }
                });
                order=joiner.toString();
            } else {
                order = orderService.createOrder(cartInfoData, partnerTokenInfo, cartOrderParam);

            }
            if (StringUtils.isNotBlank(order)) {
                errorMessage.add(order);
            } else {
                successMessage.add(cartInfoData.getSupplierName() + " 的供货单生成功；");
            }
        }
         // 有错误信息
        if (CollectionUtils.isNotEmpty(errorMessage)) {
            successMessage.addAll(errorMessage);
            return Result.error(StringUtils.join(successMessage, " | "));
        }
        return Result.success(StringUtils.join(successMessage, " | "));
    }

    /**
     * 检查合作伙伴状态
     *
     * @param partnerTokenInfo
     * @return
     */
    private Result<String> checkPartnerInfo(PartnerTokenInfo partnerTokenInfo) {
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null) {
            return Result.notLoginError();
        }
        // 查询用户状态
        PartnerUser partnerUser = partnerUserService.getById(partnerTokenInfo.getId());
        if (Boolean.TRUE.equals(partnerUser.getStatus())) {
            return Result.error(OrderTipConstant.ADD_ERROR, OrderTipConstant.SUBMIT_ORDER_LIMIT_ERROR);
        }
        return Result.success();
    }

    /**
     * 获取购物车集合
     *
     * @param cartOrderDataList
     * @param partnerId
     * @return
     */
    private List<CartInfo> getCartInfoList(List<CartOrderParam.CartOrderData> cartOrderDataList, Long partnerId) {
        if (partnerId == null || CollectionUtils.isEmpty(cartOrderDataList)) {
            return new ArrayList<>();
        }
        // 购物车id集合
        List<Long> idList = cartOrderDataList.stream()
                .filter(e -> e.getCartId() != null)
                .map(CartOrderParam.CartOrderData::getCartId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        // 查询购物车信息
        LambdaQueryWrapper<CartInfo> cartInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cartInfoLambdaQueryWrapper.eq(CartInfo::getPartnerId, partnerId);
        cartInfoLambdaQueryWrapper.in(CartInfo::getId, idList);
        return cartInfoService.list(cartInfoLambdaQueryWrapper);
    }

    /**
     * 获取供应商分组信息
     *
     * @param supplierMap
     * @return
     */
    private Map<Long, SupplierUser> getSupplierUserMap(Map<Long, List<CartInfo>> supplierMap) {
        // 查询供应商信息
        Set<Long> supplierUserIds = supplierMap.keySet();
        LambdaQueryWrapper<SupplierUser> supplierUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        supplierUserLambdaQueryWrapper.in(SupplierUser::getId, supplierUserIds);
        List<SupplierUser> supplierUserList = supplierUserService.list(supplierUserLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(supplierUserList)) {
            // 供应商分组map
            Map<Long, SupplierUser> supplierUserMap = Maps.newHashMapWithExpectedSize(supplierUserList.size());
            for (SupplierUser supplierUser : supplierUserList) {
                supplierUserMap.put(supplierUser.getId(), supplierUser);
            }
            return supplierUserMap;
        }
        return Collections.emptyMap();
    }

    /**
     * 购物车对应商品信息
     *
     * @param cartInfoList
     * @return
     */
    private Map<Long, SupplierProductInfoBO> getSupplierProductInfoMap(List<CartInfo> cartInfoList) {
        // 商品详情id集合
        List<Long> productDetailIdList = cartInfoList.stream().map(CartInfo::getProductDetailId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        // 商品集合
        List<SupplierProductInfoBO> supplierProductInfoByIdList =
                cartInfoService.getSupplierProductInfoByIdList(productDetailIdList);
        if (CollectionUtils.isNotEmpty(supplierProductInfoByIdList)) {
            Map<Long, SupplierProductInfoBO> supplierProductInfoMap = Maps.newHashMapWithExpectedSize(supplierProductInfoByIdList.size());
            for (SupplierProductInfoBO supplierProductInfoBO : supplierProductInfoByIdList) {
                // 偏远地区物流费为空，默认为false
                if (Objects.isNull(supplierProductInfoBO.getRemoteDeliveryFee())) {
                    supplierProductInfoBO.setRemoteDeliveryFee(MagicalValueConstant.INT_0);
                }
                supplierProductInfoMap.put(supplierProductInfoBO.getProductDetailId(), supplierProductInfoBO);
            }
            return supplierProductInfoMap;
        }
        return Collections.emptyMap();
    }

    /**
     * 处理购物车数据，判断是否可以下单
     *
     * @param supplierMap
     * @param supplierUserMap
     * @param supplierProductInfoMap
     * @param cartInfoDataList
     * @param cartOrderDataMap
     * @return
     */
    private String dealCart(Map<Long, List<CartInfo>> supplierMap,
                            Map<Long, SupplierUser> supplierUserMap,
                            Map<Long, SupplierProductInfoBO> supplierProductInfoMap,
                            List<CartInfoVO.CartInfoData> cartInfoDataList,
                            Map<Long, CartOrderParam.CartOrderData> cartOrderDataMap) {
        String errorMsg = null;
        for (Map.Entry<Long, List<CartInfo>> entry : supplierMap.entrySet()) {
            Long supplierId = entry.getKey();
            // 获取供应商信息
            SupplierUser supplierUser = supplierUserMap.get(supplierId);
            // 是否失效
            if (supplierUser == null) {
                return "供应商无效!";
            }
            if (supplierUser.getStatus() == null || !supplierUser.getStatus().equals(SupplierUserStatusEnum.PASS.getCode())) {
                return supplierUser.getName() + OrderTipConstant.SUPPLIER_NO_PRODUCT;
            }

            // 按商品类型分单
            Map<Integer, List<CartInfo>> productTypeCartInfoMap = this.splitCartInfoByProductType(entry.getValue(), supplierProductInfoMap);
            if(MapUtils.isEmpty(productTypeCartInfoMap)) {
                continue;
            }
            for (Map.Entry<Integer, List<CartInfo>> productTypeEntry : productTypeCartInfoMap.entrySet()) {
                // 供应商商品
                CartInfoVO.CartInfoData cartInfoData = new CartInfoVO.CartInfoData();
                cartInfoData.setSupplierId(supplierId);
                cartInfoData.setSupplierName(supplierUser.getName());
                // 商品类型
                cartInfoData.setProductType(productTypeEntry.getKey());
                cartInfoData.setProductTypeStr(ProductTypeEnum.getProductTypeDesc(productTypeEntry.getKey()));

                // 获取商品信息
                List<CartInfo> value = productTypeEntry.getValue();
                // 商品集合
                List<CartInfoVO.CartProduct> cartProductList = new ArrayList<>();
                // 处理商品列表
                String res = dealCartProductList(cartProductList, value, supplierProductInfoMap, supplierUser, supplierId, cartOrderDataMap);
                if (StringUtils.isNotBlank(res)) {
                    errorMsg = res;
                } else if (cartProductList.isEmpty()) {
                    errorMsg = supplierUser.getName() + " 供应商无商品!";
                } else {
                    cartInfoData.setProductList(cartProductList);
                    cartInfoDataList.add(cartInfoData);
                }
                // 有错误信息
                if (StringUtils.isNotBlank(errorMsg)) {
                    break;
                }
            }
        }
        return errorMsg;
    }

    private Map<Integer, List<CartInfo>> splitCartInfoByProductType(List<CartInfo> cartInfoList, Map<Long, SupplierProductInfoBO> supplierProductInfoMap) {
        if(CollectionUtils.isEmpty(cartInfoList) || MapUtils.isEmpty(supplierProductInfoMap)) {
            return Collections.emptyMap();
        }
        Map<Integer, List<CartInfo>> map = Maps.newHashMap();
        for (CartInfo cartInfo : cartInfoList) {
            SupplierProductInfoBO supplierProductInfoBO = supplierProductInfoMap.get(cartInfo.getProductDetailId());
            if(supplierProductInfoBO == null) {
                continue;
            }
            if(CollectionUtils.isNotEmpty(map.get(supplierProductInfoBO.getProductType()))) {
                map.get(supplierProductInfoBO.getProductType()).add(cartInfo);
            } else {
                List<CartInfo> list = Lists.newArrayList();
                list.add(cartInfo);
                map.put(supplierProductInfoBO.getProductType(), list);
            }
        }
        return map;
    }

    /**
     * 起订数量
     * @param minimumOrderQuantity
     * @param boxRule
     * @param productCount
     * @return
     */
    private String initialCheck(Integer minimumOrderQuantity,String boxRule,Integer productCount){
        String result=null;
        //判断起订量是否为空
        if(ObjectUtil.isNotEmpty(minimumOrderQuantity)){
            if(productCount<minimumOrderQuantity){
                result = "采购数量必须大于起订量";
            }
        }
        //判断是否空
        if(ObjectUtil.isNotEmpty(boxRule)){
            Integer boxRuleValue = Integer.parseInt(boxRule);
            if(productCount%boxRuleValue!=0){
                result = "采购数量必须是箱规的整数倍";
            }
        }
        return result;
    }

    /**
     * 处理商品列表
     *
     * @param cartProductList
     * @param value
     * @param supplierProductInfoMap
     * @param supplierUser
     * @param supplierId
     * @param cartOrderDataMap
     * @return
     */
    private String dealCartProductList(List<CartInfoVO.CartProduct> cartProductList, List<CartInfo> value,
                                       Map<Long, SupplierProductInfoBO> supplierProductInfoMap, SupplierUser supplierUser,
                                       Long supplierId, Map<Long, CartOrderParam.CartOrderData> cartOrderDataMap) {
        for (CartInfo cartInfo : value) {
            Long productDetailId = cartInfo.getProductDetailId();
            SupplierProductInfoBO supplierProductInfoBO = supplierProductInfoMap.get(productDetailId);
            if (supplierProductInfoBO == null) {
                return supplierUser.getName() + OrderTipConstant.SUPPLIER_NO_PRODUCT;
            }
            // 黑名单
            List<Long> blackUserList = CommonUtil.covertIdStr2Long(supplierProductInfoBO.getBlackUsers());
            // 绑定状态
            boolean bindStatus = Integer.valueOf(BindStatusEnum.BIND.getCode()).equals(supplierProductInfoBO.getBindStatus());
            // 商品状态
            boolean productStatus = Integer.valueOf(ProductStatusEnum.UP.getCode()).equals(supplierProductInfoBO.getProductStatus());
            // 失效的、未绑定的、未上架的、黑名单上的
            if (!bindStatus || !productStatus || blackUserList.contains(supplierId)) {
                return supplierUser.getName() + OrderTipConstant.SUPPLIER_NO_PRODUCT;
            }
            Long id = cartInfo.getId();
            CartOrderParam.CartOrderData cartOrderData = cartOrderDataMap.get(id);
            //获取起订量
            Integer minimumOrderQuantity = supplierProductInfoBO.getMinimumOrderQuantity();
            //获取箱规
            String boxRule = supplierProductInfoBO.getBoxRule();
            //获取购物车数量
            Integer productCount = cartOrderData.getProductCount();
            String res = initialCheck(minimumOrderQuantity, boxRule, productCount);
            if(StringUtils.isNotEmpty(res)){
                return supplierProductInfoBO.getProductName()+":"+res;
            }

            CartInfoVO.CartProduct cartProduct = new CartInfoVO.CartProduct();
            cartProduct.setId(id);
            cartProduct.setMinimumOrderQuantity(minimumOrderQuantity);
            cartProduct.setBoxRule(boxRule);
            // 数量和类型
            cartProduct.setProductCount(productCount);
            cartProduct.setPriceType(cartOrderData.getPriceType());

            cartProduct.setProductDetailId(productDetailId);
            cartProduct.setProductId(supplierProductInfoBO.getProductId());
            cartProduct.setPpid(supplierProductInfoBO.getPpid());

            cartProduct.setBuyNoTaxPrice(supplierProductInfoBO.getBuyNoTaxPrice());
            cartProduct.setBuyTaxPrice(supplierProductInfoBO.getBuyTaxPrice());

            cartProduct.setProductName(supplierProductInfoBO.getProductName());
            cartProduct.setProductColor(supplierProductInfoBO.getProductColor());

            cartProduct.setProductImage(ImageUtil.getProductImageUrl(miniFileConfig.getPathbase(),
                    supplierProductInfoBO.getBPic(), WebConstant.PICTURE_SIZE.PIC_440x440));
            cartProduct.setProductCid(supplierProductInfoBO.getProductCid());
            cartProduct.setProductCidName(supplierProductInfoBO.getProductCidName());
            cartProduct.setProductBarCode(supplierProductInfoBO.getProductBarCode());
            cartProduct.setIsMobile(supplierProductInfoBO.getIsMobile());
            cartProduct.setRemoteDeliveryFee(supplierProductInfoBO.getRemoteDeliveryFee());
            // 加入商品集合
            cartProductList.add(cartProduct);
        }

        return null;
    }

    @Override
    public Result cancelOrder(Long orderId) {
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null) {
            return Result.notLoginError();
        }

        LambdaQueryWrapper<OrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderInfo::getId, orderId);
        lambdaQueryWrapper.eq(OrderInfo::getPartnerId, partnerTokenInfo.getId());
        lambdaQueryWrapper.eq(OrderInfo::getXtenantId, partnerTokenInfo.getXtenant());
        OrderInfo one = orderInfoService.getOne(lambdaQueryWrapper);
        if (one == null) {
            return Result.error(OrderTipConstant.NOT_FIND_ORDER);
        }
        String saleOrderNo = one.getSaleOrderNo();
        if(StringUtils.isNotEmpty(saleOrderNo)){
            R<SubInfoDTO> result = chwQueryCloud.selectSubInfo(Integer.parseInt(saleOrderNo));
            log.warn("调用stock接口查询订单信息，传入参数：{}，返回结果：{}",saleOrderNo,JSONUtil.toJsonStr(result));
            if(result.getCode()!=0){
                throw new BizException(Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
            SubInfoDTO data = result.getData();
            Integer subCheck = data.getSubCheck();
            List<Integer> subCheckList = Arrays.asList(SubCheckEnum.ALREADY_DELETE.getCode(), SubCheckEnum.CANCEL.getCode());
            if(!subCheckList.contains(subCheck)){
                return Result.error("订单己标记发货，如需取消，请联系严选工作人员处理");
            }
        }

        // 不是待审核或审核状态不能取消
        if (!(OrderStatusEnum.AUDITED.getCode().equals(one.getOrderStatus())
                || OrderStatusEnum.DEFAULT.getCode().equals(one.getOrderStatus()))) {
            return Result.error("订单当前状态无法取消");
        }

        // 查询合作伙伴信息
        PartnerUser partnerUser = partnerUserService.getById(partnerTokenInfo.getId());
        if(partnerUser == null) {
            return Result.errorInfo("合作伙伴信息不存在");
        }

        // 采购单不为空
        if (one.getOrderNo() != null && one.getOrderNo() > 0) {
            Result<String> cancelResult = orderFactoryProducer.getOrderFactory(partnerUser.getSource()).getOrderService(one.getOrderType()).cancelOrder(one.getOrderNo(), partnerTokenInfo.getXtenant());
            if (cancelResult == null) {
                return Result.error("取消订单同步失败,返回消息为空");
            }
            if (!cancelResult.isSucceed()) {
                return Result.error("取消订单同步失败,返回消息:" + cancelResult.getUserMsg());
            }
        }

        LambdaUpdateWrapper<OrderInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(OrderInfo::getOrderStatus, OrderStatusEnum.CANCEL.getCode());
        lambdaUpdateWrapper.eq(OrderInfo::getId, orderId);
        lambdaUpdateWrapper.eq(OrderInfo::getPartnerId, partnerTokenInfo.getId());
        lambdaUpdateWrapper.eq(OrderInfo::getXtenantId, partnerTokenInfo.getXtenant());
        lambdaQueryWrapper.and(wrapper -> wrapper.eq(OrderInfo::getOrderStatus, OrderStatusEnum.DEFAULT.getCode())
                .or().eq(OrderInfo::getOrderStatus, OrderStatusEnum.DEFAULT.getCode()));
        boolean update = orderInfoService.update(lambdaUpdateWrapper);
        if (update) {
            // 保存日志
           orderInfoLogService.saveOrderLog("订单已取消。", OrderLogTypeEnum.CANCEL, orderId,
                    partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());
            return Result.success();
        }
        return Result.error();
    }

    @Override
    public Result vendorCancel(VendorCancelReq param) {
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if (supplierTokenInfo == null || supplierTokenInfo.getId() == null) {
            return Result.notLoginError();
        }
        Long orderId = param.getOrderId();
        LambdaQueryWrapper<OrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderInfo::getId, orderId);
        OrderInfo one = orderInfoService.getOne(lambdaQueryWrapper);
        if (one == null) {
            return Result.error(OrderTipConstant.NOT_FIND_ORDER);
        }
        String saleOrderNo = one.getSaleOrderNo();
        if(!(StringUtils.isEmpty(saleOrderNo) && OrderStatusEnum.AUDITED.getCode().equals(one.getOrderStatus()))){
            throw new BizException("订单为待发货状态且无OA销售单关联时才能取消");
        }
        Long partnerId = one.getPartnerId();
        // 查询合作伙伴信息
        PartnerUser partnerUser = partnerUserService.getById(partnerId);
        if(partnerUser == null) {
            return Result.errorInfo("（供应商）订单合作伙伴信息不存在");
        }
        LambdaUpdateWrapper<OrderInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(OrderInfo::getOrderStatus, OrderStatusEnum.CANCEL.getCode());
        lambdaUpdateWrapper.eq(OrderInfo::getId, orderId);
        lambdaQueryWrapper.and(wrapper -> wrapper.eq(OrderInfo::getOrderStatus, OrderStatusEnum.DEFAULT.getCode()));
        boolean update = orderInfoService.update(lambdaUpdateWrapper);
        if (update) {
            // 保存日志
            String message = String.format("取消操作，原因：%s，订单状态由【待发货】变为【已取消】", param.getVendorCancelReason());
            orderInfoLogService.saveOrderLog(message, OrderLogTypeEnum.CANCEL, orderId, supplierTokenInfo.getId(), supplierTokenInfo.getLoginName());
            return Result.success();
        }
        return Result.error();
    }

    @Override
    public Result deliveryChange(Long orderId, LocalDateTime deliveryTime) {
        // 查询供应商信息
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if (supplierTokenInfo == null || supplierTokenInfo.getId() == null) {
            return Result.notLoginError();
        }

        LambdaQueryWrapper<OrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderInfo::getId, orderId);
        lambdaQueryWrapper.eq(OrderInfo::getSupplierId, supplierTokenInfo.getId());
        OrderInfo one = orderInfoService.getOne(lambdaQueryWrapper);
        if (one == null) {
            return Result.error(OrderTipConstant.NOT_FIND_ORDER);
        }

        // 不是审核状态无法修改发货时间
        if (!OrderStatusEnum.AUDITED.getCode().equals(one.getOrderStatus())) {
            return Result.error("订单当前状态无法修改发货时间");
        }

        LambdaUpdateWrapper<OrderInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(OrderInfo::getDeliveryTime, deliveryTime);
        lambdaUpdateWrapper.eq(OrderInfo::getId, orderId);
        lambdaUpdateWrapper.eq(OrderInfo::getSupplierId, supplierTokenInfo.getId());
        lambdaUpdateWrapper.eq(OrderInfo::getOrderStatus, OrderStatusEnum.AUDITED.getCode());
        boolean update = orderInfoService.update(lambdaUpdateWrapper);
        if (!update) {
            return Result.error();
        }
        // 保存日志
        orderInfoLogService.saveOrderLog(String.format("订单发货日期变更为:%s", deliveryTime),
                OrderLogTypeEnum.DELIVERY, orderId, supplierTokenInfo.getId(), supplierTokenInfo.getLoginName());

        // 发送站内信
        MessageInfoSaveParam param = new MessageInfoSaveParam();
        param.setContent(String.format("采购单号:%s的发货时间变更为:%s", one.getOrderNo(), deliveryTime));
        param.setTitle("采购单发货时间变更");
        param.setUserIdStr(one.getPartnerId() + "");
        param.setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode());
        param.setUserType(MessagePushUserTypeEnum.PARTNER.getCode());
        messageInfoService.saveMessageInfo(param);

        return Result.success();
    }

    @Override
    public Result<Boolean> oaCancelOrder(Long orderNo, Long xtenant) {
        LambdaQueryWrapper<OrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderInfo::getOrderNo, orderNo);
        lambdaQueryWrapper.eq(OrderInfo::getXtenantId, xtenant);
        lambdaQueryWrapper.eq(OrderInfo::getChannelType, MagicalValueConstant.INT_0);
        OrderInfo orderInfo = orderInfoService.getOne(lambdaQueryWrapper);
        Result<Boolean> checkOaCancelOrder = checkOrderStatus(orderInfo, MagicalValueConstant.INT_0);
        if (!checkOaCancelOrder.isSucceed()) {
            return checkOaCancelOrder;
        }

        LambdaUpdateWrapper<OrderInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(OrderInfo::getOrderStatus, OrderStatusEnum.CANCEL.getCode());
        lambdaUpdateWrapper.eq(OrderInfo::getOrderNo, orderNo);
        lambdaUpdateWrapper.eq(OrderInfo::getXtenantId, xtenant);
        lambdaQueryWrapper.eq(OrderInfo::getChannelType, MagicalValueConstant.INT_0);
        lambdaUpdateWrapper.eq(OrderInfo::getOrderStatus, OrderStatusEnum.AUDITED.getCode());
        boolean update = orderInfoService.update(lambdaUpdateWrapper);
        if (update) {
            // 保存日志
            orderInfoLogService.saveOrderLog("oa采购单取消,采购单号:" + orderNo,
                    OrderLogTypeEnum.CANCEL, orderInfo.getId(), null, "系统");

            return Result.success(true);
        }
        return Result.error("订单当取消失败");
    }

    @Override
    public Result<Boolean> neoCancelOrder(Long orderNo, Long xtenant) {
        LambdaQueryWrapper<OrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderInfo::getOrderNo, orderNo);
        lambdaQueryWrapper.eq(OrderInfo::getXtenantId, xtenant);
        lambdaQueryWrapper.eq(OrderInfo::getChannelType, MagicalValueConstant.INT_1);
        OrderInfo orderInfo = orderInfoService.getOne(lambdaQueryWrapper);
        Result<Boolean> cancelResult = checkOrderStatus(orderInfo, MagicalValueConstant.INT_0);
        if (!cancelResult.isSucceed()) {
            return cancelResult;
        }
        orderInfo.setOrderStatus(OrderStatusEnum.CANCEL.getCode());
        boolean update = orderInfo.updateById();
        if (update) {
            // 保存日志
            orderInfoLogService.saveOrderLog("neo采购单取消,采购单号:" + orderNo, OrderLogTypeEnum.CANCEL, orderInfo.getId(), null, "NEO");
            return Result.success(true);
        }
        return Result.error("订单取消失败");
    }

    /**
     * 校验订单状态是否满足取消或入库条件
     * @param orderInfo 订单
     * @param checkType 校验类型 0，取消；1，入库
     * @return 校验结果
     */
    private static Result<Boolean> checkOrderStatus(OrderInfo orderInfo, Integer checkType) {
        if (ObjectUtil.isNull(orderInfo)) {
            return Result.errorInfo(OrderTipConstant.NOT_FIND_ORDER);
        }
        // 校验订单取消，不是审核状态无法取消
        if (checkType.equals(MagicalValueConstant.INT_0) && !OrderStatusEnum.AUDITED.getCode().equals(orderInfo.getOrderStatus())) {
            return Result.errorInfo("订单当前状态无法取消");
        }
        // 校验订单入库，不是已发货（在途）状态无法入库
        if (checkType.equals(MagicalValueConstant.INT_1) && !OrderStatusEnum.DELIVERY.getCode().equals(orderInfo.getOrderStatus())) {
            return Result.errorInfo("订单当前状态无法入库");
        }
        return Result.success();
    }

    @Override
    public Result<Boolean> finish(Long orderNo, Long xtenant, Integer count, Integer orderType) {
        LambdaQueryWrapper<OrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderInfo::getOrderNo, orderNo);
        lambdaQueryWrapper.eq(OrderInfo::getXtenantId, xtenant);
        //lambdaQueryWrapper.eq(OrderInfo::getOrderType, orderType);
        lambdaQueryWrapper.eq(OrderInfo::getChannelType, MagicalValueConstant.INT_0);
        OrderInfo one = orderInfoService.getOne(lambdaQueryWrapper);
        if (one == null) {
            return Result.error(OrderTipConstant.NOT_FIND_ORDER);
        }

        // 大件判断商品入库数量是否等于购买数量
        if(orderType == OrderTypeEnum.BULKY.getCode()) {
            List<OrderDetailInfo> orderDetailInfoList = orderDetailInfoService.list(new LambdaQueryWrapper<OrderDetailInfo>().eq(OrderDetailInfo::getOrderId, one.getId()));
            if(CollectionUtils.isEmpty(orderDetailInfoList)) {
                return Result.errorInfo(NO_ORDER_DETAIL);
            }
            int buyCount = orderDetailInfoList.stream().mapToInt(OrderDetailInfo::getBuyAmount).sum();
            if(count != buyCount) {
                return Result.errorInfo("商品入库数量不等于购买数量");
            }
        }

        LambdaUpdateWrapper<OrderInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(OrderInfo::getOrderStatus, OrderStatusEnum.COMPLETED.getCode());
        lambdaUpdateWrapper.set(OrderInfo::getFinishTime, LocalDateTime.now());
        lambdaUpdateWrapper.eq(OrderInfo::getOrderNo, orderNo);
        lambdaUpdateWrapper.eq(OrderInfo::getXtenantId, xtenant);
        lambdaQueryWrapper.eq(OrderInfo::getChannelType, MagicalValueConstant.INT_0);
        boolean update = orderInfoService.update(lambdaUpdateWrapper);
        if (update) {
            // 保存日志
            orderInfoLogService.saveOrderLog("订单已完成", OrderLogTypeEnum.COMPLETED, one.getId(), null, "系统");

            return Result.success(true);
        }
        return Result.error("订单完成失败");
    }

    @Override
    public Result<Boolean> neoOrderFinish(Long orderNo, Long xtenant, Integer inStockCount) {
        LambdaQueryWrapper<OrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderInfo::getOrderNo, orderNo);
        lambdaQueryWrapper.eq(OrderInfo::getXtenantId, xtenant);
        lambdaQueryWrapper.eq(OrderInfo::getChannelType, MagicalValueConstant.INT_1);
        OrderInfo orderInfo = orderInfoService.getOne(lambdaQueryWrapper);
        Result<Boolean> finishResult = checkOrderStatus(orderInfo, MagicalValueConstant.INT_1);
        if (!finishResult.isSucceed()) {
            return finishResult;
        }
        // 已入库数量和购买数量判断
        List<OrderDetailInfo> orderDetailInfoList = orderDetailInfoService.list(Wrappers.<OrderDetailInfo>lambdaQuery().eq(OrderDetailInfo::getOrderId, orderInfo.getId()));
        if (CollectionUtils.isEmpty(orderDetailInfoList)) {
            return Result.error(NO_ORDER_DETAIL);
        }
        int buyAmount = orderDetailInfoList.stream().mapToInt(OrderDetailInfo::getBuyAmount).sum();
        if (!inStockCount.equals(buyAmount)) {
            return Result.error("商品已入库数量不等于购买数量，无法完成订单");
        }

        orderInfo.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        orderInfo.setFinishTime(LocalDateTime.now());
        boolean update = orderInfo.updateById();
        if (update) {
            // 保存日志
            orderInfoLogService.saveOrderLog("订单已完成", OrderLogTypeEnum.COMPLETED, orderInfo.getId(), null, "NEO");
            return Result.success(true);
        }
        return Result.errorInfo("订单完成失败");
    }

    @Override
    public Result exportPlatformOrderList(OrderSearchParam param, HttpServletResponse response) {
        if (StringUtils.isNotBlank(param.getStartTimeStr())) {
            LocalDateTime startTime = DateUtil.stringParseLocalDateTime(param.getStartTimeStr());
            param.setStartTime(startTime);
        }
        if (StringUtils.isNotBlank(param.getEndTimeStr())) {
            LocalDateTime endTime = DateUtil.stringParseLocalDateTime(param.getEndTimeStr());
            param.setEndTime(endTime);
        }

        int count = orderInfoService.countPlatformOrder(param);
        List<OrderListExportVO> platformOrderList = new ArrayList<>();
        if (count > 0) {
            platformOrderList = orderInfoService.getExportPlatformOrderList(param);
        }
        if (CollectionUtils.isEmpty(platformOrderList)) {
            return Result.noData();
        }

        try {
            //excel文件名
            String fileName = "订单列表" + System.currentTimeMillis() + ".xls";
            //sheet名
            String sheetName = "订单列表";
            int row = platformOrderList.size();
            int column = EXCEL_TITLE.length;
            String[][] content = new String[row][column];
            for (int i = 0; i < platformOrderList.size(); i++) {
                OrderListExportVO platformOrderListVO = platformOrderList.get(i);
                content[i][MagicalValueConstant.INT_0] = platformOrderListVO.getOrderNo() + "";
                content[i][MagicalValueConstant.INT_1] = OrderStatusEnum.getDescByCode(platformOrderListVO.getOrderStatus());
                content[i][MagicalValueConstant.INT_2] = platformOrderListVO.getPartnerName();
                content[i][MagicalValueConstant.INT_3] = platformOrderListVO.getSupplierName();
                content[i][MagicalValueConstant.INT_4] = platformOrderListVO.getPpid() + "";
                content[i][MagicalValueConstant.INT_5] = platformOrderListVO.getProductName();
                content[i][MagicalValueConstant.INT_6] = platformOrderListVO.getBuyAmount() + "";
                content[i][MagicalValueConstant.INT_7] = platformOrderListVO.getProductPrice()+"";
                content[i][MagicalValueConstant.INT_8] = platformOrderListVO.getTotalAmount() + "";

            }
            // 创建HSSFWorkbook
            HSSFWorkbook wb = ExcelExport.getWorkbook(sheetName, EXCEL_TITLE, content, null);
            // 响应到客户端
            OutputStream os = response.getOutputStream();
            // 设置响应头
            ExcelExport.setResponseHeader(response, fileName);
            wb.write(os);
            // 关闭流
            os.close();
        } catch (Exception e) {
            log.error("export error ", e);
            return Result.error("导出异常");
        }
        return null;
    }

    @Override
    public Result<String> reBuy(Long orderId) {
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null) {
            return Result.notLoginError();
        }

        OrderInfo orderInfo = orderInfoService.getById(orderId);
        if (orderInfo == null) {
            return Result.error(OrderTipConstant.QUERY_ORDER_ERROR);
        }

        PartnerUser partnerUser = partnerUserService.getById(partnerTokenInfo.getId());
        if (Boolean.TRUE.equals(partnerUser.getStatus())) {
            return Result.error("添加失败", "您已被平台禁止下单，如有疑问请咨询平台人员");
        }

        List<OrderDetailInfo> orderDetailInfoList = orderDetailInfoService.getOrderDetailByOrderId(orderId);

        if (CollectionUtils.isEmpty(orderDetailInfoList)) {
            return Result.error(OrderTipConstant.NO_CAN_RE_BUY_PRODUCT);
        }

        // 查询原有的购物车
        List<Long> cartIdList = orderDetailInfoList.stream().map(OrderDetailInfo::getCartId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<CartInfo> list = cartInfoService.listByCartIds(cartIdList);
        if (CollectionUtils.isEmpty(list)) {
            return Result.error(OrderTipConstant.NO_CAN_RE_BUY_PRODUCT);
        }

        // 查询是否有现成购物车
        List<Long> collect = orderDetailInfoList.stream().map(OrderDetailInfo::getProductDetailId).collect(Collectors.toList());
        LambdaQueryWrapper<CartInfo> queryWrapper = new LambdaQueryWrapper<CartInfo>()
                .eq(CartInfo::getSupplierUserId, orderInfo.getSupplierId())
                .eq(CartInfo::getPartnerId, partnerTokenInfo.getId())
                .in(CartInfo::getProductDetailId, collect);
        List<CartInfo> cartInfos = cartInfoService.list(queryWrapper);
        List<Long> cartInfoIds = cartInfos.stream().map(CartInfo::getProductDetailId).collect(Collectors.toList());
        Map<Long, Integer> map = list.stream().collect(Collectors.toMap(CartInfo::getProductDetailId, CartInfo::getProductCount));
        // 过滤出需要新增的购物车
        List<CartInfo> insertList = list.stream().filter(cartInfo -> !cartInfoIds.contains(cartInfo.getProductDetailId())).collect(Collectors.toList());
        // 过滤出需要更新的购物车
        List<Long> updateIds = list.stream().filter(cartInfo -> cartInfoIds.contains(cartInfo.getProductDetailId())).map(CartInfo::getProductDetailId).collect(Collectors.toList());
        List<CartInfo> updateList = cartInfos.stream().filter(cartInfo -> updateIds.contains(cartInfo.getProductDetailId())).collect(Collectors.toList());
        // 重新加入购物车
        boolean success = false;
        if (CollectionUtils.isNotEmpty(insertList)) {
            insertList.forEach((CartInfo e) -> {
                e.setId(null);
                e.setCartType(CartTypeEnum.NORMAL.getCode());
                e.setDelFlag(false);
            });
            success = cartInfoService.saveBatch(insertList);
        }

        // 更新购物车
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.forEach(cartInfo -> cartInfo.setProductCount(cartInfo.getProductCount() + map.get(cartInfo.getProductDetailId())));
            success = cartInfoService.updateBatchById(updateList);
        }

        if (success) {
            List<Long> idList = list.stream().map(CartInfo::getId).collect(Collectors.toList());
            return Result.success(CommonUtil.convertCollection2String(idList));
        }

        return Result.error(OrderTipConstant.NO_CAN_RE_BUY_PRODUCT);
    }

    @Override
    public Result<List<OrderAreaInfoVO>> getAreaInfo() {
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null || partnerTokenInfo.getXtenant() == null) {
            return Result.success(Collections.emptyList());
        }
        List<OrderAreaInfoVO> areaInfo = Integer.valueOf(MagicalValueConstant.INT_2).equals(partnerTokenInfo.getSource())
                ? neoService.getAreaInfo(partnerTokenInfo.getXtenant()) : oaService.getAreaInfo(partnerTokenInfo.getXtenant());
        return Result.success(areaInfo);
    }

    @Override
    public Result<Integer> getToBeDeliveredOrderCount() {
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if (supplierTokenInfo == null) {
            return Result.notLoginError();
        }

        LambdaQueryWrapper<OrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderInfo::getSupplierId, supplierTokenInfo.getId());
        lambdaQueryWrapper.eq(OrderInfo::getOrderStatus, OrderStatusEnum.AUDITED.getCode());
        int count = orderInfoService.count(lambdaQueryWrapper);
        return Result.success(count);
    }
    @Override
    public Result<String> deliveryFromOa(OrderDeliveryParam param) {
        //进行单号的转换
        Long orderId = param.getOrderId();
        List<OrderInfo> list = orderInfoService.lambdaQuery().eq(OrderInfo::getSaleOrderNo, orderId).list();
        if(CollectionUtils.isNotEmpty(list)){
            OrderInfo orderInfo = list.get(0);
            Optional.ofNullable(orderInfo.getId()).ifPresent(param::setOrderId);
        }
        String logisticsCompany;
        String deliveryType = Optional.ofNullable(param.getDeliveryType()).orElse("");
        if(LogisticsCompanyEnum.OTHER.getSpell().equals(deliveryType)){
            logisticsCompany=param.getRemarks();
        }else {
            logisticsCompany=LogisticsCompanyEnum.getChinese(param.getDeliveryType());
        }
        StringBuilder sb = new StringBuilder(String.format("OA订单已发货，物流公司：%s，物流单号：%s", logisticsCompany,"<a>"+ param.getDeliveryNo()+"</a>"));
        List<OrderDeliveryParam.DeliveryProductInfo> deliveryProductInfoList = param.getDeliveryProductInfoList();
        SupplierTokenInfo supplierTokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck()).orElseThrow(()->new BizException("供应商登录信息超时，请重新登录"));
        if(CollectionUtils.isNotEmpty(deliveryProductInfoList)){
            deliveryProductInfoList.forEach(item-> Optional.ofNullable(item.getProductId()).ifPresent(obj->{
                //进行商品ppid转换为严选系统的productId
                List<SupplierProductDetail> detailList = supplierProductDetailService.lambdaQuery()
                        .eq(SupplierProductDetail::getPpid, item.getProductId())
                        .eq(SupplierProductDetail::getSupplierId, supplierTokenInfo.getId())
                        .list();
                if(CollectionUtils.isNotEmpty(detailList)){
                    SupplierProductDetail supplierProductDetail = detailList.get(0);
                    item.setProductId(supplierProductDetail.getProductId());
                    sb.append(",商品ppid：").append(supplierProductDetail.getPpid()).append(",发货数量：").append(item.getCount()).append(";");
                }

            }));
        }
        //聚合发货商品
        polymerizationProduct(param);
        Result<String> result = deliveryForProduct(param);
        //操作成功才进行日志记录
        if(Result.SUCCESS==result.getCode()){
            orderInfoLogService.saveOrderLog(sb.toString(), OrderLogTypeEnum.DELIVERY, param.getOrderId(), supplierTokenInfo.getId(), supplierTokenInfo.getLoginName());
        }
        return result ;
    }

    /**
     * 进行商品聚合
     * @param param
     */
    private void polymerizationProduct(OrderDeliveryParam param) {
        List<OrderDeliveryParam.DeliveryProductInfo> deliveryProductInfoList = param.getDeliveryProductInfoList();
        if (CollectionUtils.isEmpty(deliveryProductInfoList)) {
            return;
        }

        Map<Long, Integer> productCounts = new HashMap<>();
        for (OrderDeliveryParam.DeliveryProductInfo deliveryProductInfo : deliveryProductInfoList) {
            long productId = deliveryProductInfo.getProductId();
            int count = productCounts.getOrDefault(productId, 0) + deliveryProductInfo.getCount();
            productCounts.put(productId, count);
        }

        List<OrderDeliveryParam.DeliveryProductInfo> deliveryProductInfoNewList = productCounts.entrySet().stream()
                .map(entry -> {
                    OrderDeliveryParam.DeliveryProductInfo deliveryProductInfo = new OrderDeliveryParam.DeliveryProductInfo();
                    deliveryProductInfo.setProductId(entry.getKey());
                    deliveryProductInfo.setCount(entry.getValue());
                    return deliveryProductInfo;
                })
                .collect(Collectors.toList());

        param.setDeliveryProductInfoList(deliveryProductInfoNewList);
    }

    @Override
    public Result<String> deliveryForProduct(OrderDeliveryParam param) {
        // 查询供应商信息
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();

        if (supplierTokenInfo == null || supplierTokenInfo.getId() == null) {
            return Result.notLoginError();
        }
        //虚拟商品不参与校验
        if(OrderTypeEnum.VIRTUAL.getCode()!=param.getOrderType()){
            if(CommonUtil.isContainChinese(param.getDeliveryNo())){
                return Result.error("快递单号不能包含中文并且只能填写一个");
            }
        }
        //虚拟商品不参与校验
        if(OrderTypeEnum.VIRTUAL.getCode()!=param.getOrderType()){
            if(CommonUtil.isContainPunctuation(param.getDeliveryNo())){
                return Result.error("快递单号不能包含符号并且只能填写一个");
            }
        }
        // 判断参数
        if(param.getOperateType() != CommonConstant.DELIVERY_ALL_OPT_TYPE.intValue() && CollectionUtils.isEmpty(param.getDeliveryProductInfoList())) {
            return Result.error("部分发货，发货的商品明细不能为空");
        }
        OrderInfo orderInfo = orderInfoService.getOne(new LambdaQueryWrapper<OrderInfo>().eq(OrderInfo::getId, param.getOrderId()).eq(OrderInfo::getSupplierId, supplierTokenInfo.getId()));
        if (orderInfo == null) {
            return Result.error(OrderTipConstant.NOT_FIND_ORDER);
        }
        // 小件订单物流信息不能为空
        if(Objects.equals(orderInfo.getOrderType(), OrderTypeEnum.SMALL.getCode())) {
            if(StringUtils.isBlank(param.getDeliveryType()) || StringUtils.isBlank(param.getDeliveryNo())) {
                return Result.errorInfo("小件订单物流信息不能为空");
            }
        }
        // 其他订单物流信息为空，默认为其他
        if(StringUtils.isBlank(param.getDeliveryType())) {
            param.setDeliveryType(LogisticsCompanyEnum.OTHER.getSpell());
        }
        if(StringUtils.isBlank(param.getDeliveryNo())) {
            param.setDeliveryNo(LogisticsCompanyEnum.OTHER.getChinese());
        }
        // 不是审核状态或者发货状态不能发货
        if (!OrderStatusEnum.AUDITED.getCode().equals(orderInfo.getOrderStatus()) && !OrderStatusEnum.DELIVERY.getCode().equals(orderInfo.getOrderStatus())) {
            return Result.error("订单当前状态无法发货");
        }
        // 查询订单明细
        List<OrderDetailInfo> orderDetailInfoList = orderDetailInfoService.getOrderDetailByOrderId(orderInfo.getId());
        if(CollectionUtils.isEmpty(orderDetailInfoList)) {
            return Result.error(NO_ORDER_DETAIL);
        }
        // 物流公司拼音中文转换匹配查询
        if ("".equals(LogisticsCompanyEnum.getChinese(param.getDeliveryType()))) {
            return Result.error("物流公司信息错误");
        }
        // 构建物流单数据
        List<DeliveryOrderInfo> deliveryOrderInfoList = this.buildDeliveryOrderInfoList(orderInfo, orderDetailInfoList, param, supplierTokenInfo);
        if(CollectionUtils.isEmpty(deliveryOrderInfoList)) {
            return Result.error("构建物流单数据失败,请确认订单是否已全部发货");
        }
        boolean deliverySaveResult = deliveryOrderInfoService.saveBatch(deliveryOrderInfoList);
        if (!deliverySaveResult) {
            return Result.errorInfo("物流单生成失败");
        }
        // 构建ppid和发货数量的关系
        List<DeliveryProductBO> deliveryProductBOList = this.buildDeliveryProductBOList(deliveryOrderInfoList);
        // 查询合作伙伴信息
        PartnerUser partnerUser = partnerUserService.getById(orderInfo.getPartnerId());
        if (partnerUser == null) {
            return Result.errorInfo("合作伙伴信息不存在");
        }
        if (orderInfo.getOrderNo() != null && orderInfo.getOrderNo() > 0) {
            // 调用OA/NEO生成物流单
            OrderCommonService orderService = orderFactoryProducer.getOrderFactory(partnerUser.getSource()).getOrderService(orderInfo.getOrderType());
            Result<String> deliveryResult = orderService.deliveryProduct(deliveryProductBOList, orderInfo, param, supplierTokenInfo);
            if (deliveryResult == null) {
                // 同步失败删除物流单
                this.deleteDeliveryOrderInfo(deliveryOrderInfoList);
                return Result.error("分批发货同步失败,请重试或联系管理员");
            }
            if (!deliveryResult.isSucceed()) {
                // 同步失败删除物流单
                this.deleteDeliveryOrderInfo(deliveryOrderInfoList);
                return Result.error("分批发货同步失败,错误消息:" + deliveryResult.getUserMsg());
            }
        }

        // 更新物流单状态
        for (DeliveryOrderInfo deliveryOrderInfo : deliveryOrderInfoList) {
            deliveryOrderInfo.setDeliveryStatus(DeliveryStatusEnum.SEND.getCode());
            deliveryOrderInfo.setDeliveryTime(LocalDateTime.now());
        }
        deliveryOrderInfoService.updateBatchById(deliveryOrderInfoList);
        // 更新订单状态为在途
        if (orderInfo.getOrderStatus() == OrderStatusEnum.AUDITED.getCode().intValue()) {
            orderInfo.setOrderStatus(OrderStatusEnum.DELIVERY.getCode());
            orderInfo.setDeliveryTime(LocalDateTime.now());
            orderInfoService.updateById(orderInfo);
        }
        // 更新订单明细中产品的发货数量
        List<OrderDetailInfo> needUpdateOrderDetailInfoList = this.dealOrderDetailProductSendCount(orderInfo, orderDetailInfoList, supplierTokenInfo);
        if(CollectionUtils.isNotEmpty(needUpdateOrderDetailInfoList)) {
            orderDetailInfoService.updateBatchById(needUpdateOrderDetailInfoList);
        }
        // 记录进程
        //日志获取快递公司名称
        String logisticsCompany;
        String deliveryType = Optional.ofNullable(param.getDeliveryType()).orElse("");
        if(LogisticsCompanyEnum.OTHER.getSpell().equals(deliveryType)){
            logisticsCompany=param.getRemarks();
        }else {
            logisticsCompany=LogisticsCompanyEnum.getChinese(param.getDeliveryType());
        }
        StringBuilder sb = new StringBuilder(String.format("订单已发货，物流公司：%s，物流单号：%s", logisticsCompany,"<a>"+ param.getDeliveryNo()+"</a>"));
        for (DeliveryProductBO deliveryProductBO : deliveryProductBOList) {
            sb.append(",商品ppid：").append(deliveryProductBO.getPpid()).append(",发货数量：").append(deliveryProductBO.getCount()).append(";");
        }
        orderInfoLogService.saveOrderLog(sb.toString(), OrderLogTypeEnum.DELIVERY, orderInfo.getId(), supplierTokenInfo.getId(), supplierTokenInfo.getLoginName());
        // 发送站内信
        MessageInfoSaveParam messageInfoSaveParam = new MessageInfoSaveParam();
        messageInfoSaveParam.setContent(sb.toString());
        messageInfoSaveParam.setTitle("采购单发货通知");
        messageInfoSaveParam.setUserIdStr(orderInfo.getPartnerId() + "");
        messageInfoSaveParam.setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode());
        messageInfoSaveParam.setUserType(MessagePushUserTypeEnum.PARTNER.getCode());
        messageInfoService.saveMessageInfo(messageInfoSaveParam);
        return Result.success();
    }

    @Override
    public Result<String> orderReceive4User(List<Long> orderIdList) {
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if(partnerTokenInfo == null) {
            return Result.errorInfo("请登录后在进行操作!");
        }
        List<OrderInfo> orderInfoList = orderInfoService.list(new LambdaQueryWrapper<OrderInfo>().eq(OrderInfo::getPartnerId, partnerTokenInfo.getId()).in(OrderInfo::getId, orderIdList));
        if(CollectionUtils.isEmpty(orderInfoList)) {
            return Result.errorInfo("订单不存在");
        }
        log.info("用户订单收货，收货订单号orderId：{}", JSONUtil.toJsonStr(orderIdList));
        return this.orderReceive(orderInfoList);
    }

    @Override
    public void orderReceive4System() {
        List<Long> receiveOrderIdList = orderInfoService.getReceiveOrderList();
        if(CollectionUtils.isEmpty(receiveOrderIdList)) {
            log.info("系统自动收货，待收货订单为空");
            return;
        }
        // 查询发货记录
        List<DeliveryOrderInfo> deliveryOrderInfoList = deliveryOrderInfoService.list(new LambdaQueryWrapper<DeliveryOrderInfo>()
                .eq(DeliveryOrderInfo::getDeliveryStatus, DeliveryStatusEnum.SEND.getCode()).isNotNull(DeliveryOrderInfo::getDeliveryTime)
                .in(DeliveryOrderInfo::getOrderId, receiveOrderIdList));
        if(CollectionUtils.isEmpty(deliveryOrderInfoList)) {
            log.info("系统自动收货，查询发货记录为空");
            return;
        }
        Map<Long, List<DeliveryOrderInfo>> deliveryOrderInfoMap = deliveryOrderInfoList.stream().collect(Collectors.groupingBy(DeliveryOrderInfo::getOrderId));
        if(MapUtils.isEmpty(deliveryOrderInfoMap)) {
            return;
        }
        List<Long> needReceiveOrderIdList = Lists.newArrayList();
        for (Long orderId : receiveOrderIdList) {
            List<DeliveryOrderInfo> orderIdDeliveryInfoList = deliveryOrderInfoMap.get(orderId);
            if(CollectionUtils.isEmpty(orderIdDeliveryInfoList)) {
                continue;
            }
            // 按时间排序
            Optional<DeliveryOrderInfo> deliveryOrderInfoOptional = orderIdDeliveryInfoList.stream().sorted(Comparator.comparing(DeliveryOrderInfo::getDeliveryTime).reversed()).findFirst();
            if(!deliveryOrderInfoOptional.isPresent()) {
                continue;
            }
            DeliveryOrderInfo deliveryOrderInfo = deliveryOrderInfoOptional.get();
            if(deliveryOrderInfo.getDeliveryTime().plusHours(CommonConstant.RECEIVE_END_TIME).isBefore(LocalDateTime.now())) {
                needReceiveOrderIdList.add(orderId);
            }
        }
        if(CollectionUtils.isEmpty(needReceiveOrderIdList)) {
            log.info("系统自动收货，满足发货时间超过10天的数据为空");
            return;
        }
        // 查询订单信息
        List<OrderInfo> orderInfoList = orderInfoService.list(new LambdaQueryWrapper<OrderInfo>().in(OrderInfo::getId, needReceiveOrderIdList));
        if(CollectionUtils.isEmpty(orderInfoList)) {
            return;
        }
        log.info("系统自动收货，收货订单号orderId：{}", JSONUtil.toJsonStr(needReceiveOrderIdList));
        this.orderReceive(orderInfoList);
    }



    @Override
    public Result<Long> getOrderIdBySaleNo(Integer saleNo) {
        return Result.success(infoMapper.getOrderIdBySaleNo(saleNo));
    }


    private Result<String> orderReceive(List<OrderInfo> orderInfoList) {
        // 大小件订单不存在收货
//        Optional<OrderInfo> smallOrderOptional = orderInfoList.stream().filter(orderInfo -> Objects.equals(orderInfo.getOrderType(), OrderTypeEnum.SMALL.getCode())).findAny();
//        if(smallOrderOptional.isPresent()) {
//            OrderInfo orderInfo = smallOrderOptional.get();
//            if (orderInfo.getOrderNo() != null && orderInfo.getOrderNo() > 0) {
//                return Result.errorInfo("订单包含小件订单，小件订单不能收货");
//            }
//        }
        Optional<OrderInfo> bulkyOrderOptional = orderInfoList.stream().filter(orderInfo -> Objects.equals(orderInfo.getOrderType(), OrderTypeEnum.BULKY.getCode())).findAny();
        if(bulkyOrderOptional.isPresent()) {
            return Result.errorInfo("订单包含大件订单，大件订单不能收货");
        }
        // 判断订单状态
        Optional<OrderInfo> orderStatusOptional = orderInfoList.stream().filter(orderInfo -> !Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.DELIVERY.getCode())).findAny();
        if(orderStatusOptional.isPresent()) {
            return Result.errorInfo(orderStatusOptional.get().getOrderNo() + ":订单未发货，不能操作收货");
        }
        List<String> errorInfoList = Lists.newArrayList();
        // 小件收货
        List<OrderInfo> smallOrderList = orderInfoList.stream().filter(orderInfo -> Objects.equals(orderInfo.getOrderType(), OrderTypeEnum.SMALL.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(smallOrderList)) {
            List<String> smallNotOaReceiveRes = orderServiceFactory.getSmallOrderService().orderReceive(smallOrderList);
            if(CollectionUtils.isNotEmpty(smallNotOaReceiveRes)) {
                errorInfoList.addAll(smallNotOaReceiveRes);
            }
        }

        // 虚拟商品收货
        List<OrderInfo> virtualOrderList = orderInfoList.stream().filter(orderInfo -> Objects.equals(orderInfo.getOrderType(), OrderTypeEnum.VIRTUAL.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(virtualOrderList)) {
            List<String> virtualReceiveRes = orderServiceFactory.getVirtualOrderService().orderReceive(virtualOrderList);
            if(CollectionUtils.isNotEmpty(virtualReceiveRes)) {
                errorInfoList.addAll(virtualReceiveRes);
            }
        }
        // 资产收货
        List<OrderInfo> assetsOrderList = orderInfoList.stream().filter(orderInfo -> Objects.equals(orderInfo.getOrderType(), OrderTypeEnum.FIX_ASSETS.getCode()) || Objects.equals(orderInfo.getOrderType(), OrderTypeEnum.COMMON_ASSETS.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(assetsOrderList)) {
            List<String> assetsReceiveRes = orderServiceFactory.getAssetsOrderService().orderReceive(assetsOrderList);
            if(CollectionUtils.isNotEmpty(assetsReceiveRes)) {
                errorInfoList.addAll(assetsReceiveRes);
            }
        }
        if(CollectionUtils.isNotEmpty(errorInfoList)) {
            log.warn("订单收货，收货失败信息：{}", JSONUtil.toJsonStr(errorInfoList));
            return Result.errorInfo(Joiner.on(";").join(errorInfoList));
        }
        log.info("-----订单收货完成-----");
        return Result.successInfo("收货成功");
    }

    private void deleteDeliveryOrderInfo(List<DeliveryOrderInfo> deliveryOrderInfoList) {
        List<Long> deliveryIdList = deliveryOrderInfoList.stream().map(DeliveryOrderInfo::getId).collect(Collectors.toList());
        deliveryOrderInfoService.removeByIds(deliveryIdList);
    }

    private List<DeliveryProductBO> buildDeliveryProductBOList(List<DeliveryOrderInfo> deliveryOrderInfoList) {
        List<Long> productIdList = deliveryOrderInfoList.stream().map(DeliveryOrderInfo::getProductId).collect(Collectors.toList());
        List<PickProduct> pickProductList = pickProductService.list(new LambdaQueryWrapper<PickProduct>().in(PickProduct::getId, productIdList));
        if(CollectionUtils.isEmpty(pickProductList)) {
            return null;
        }
        Map<Long, PickProduct> pickProductMap = pickProductList.stream().collect(Collectors.toMap(PickProduct::getId, Function.identity()));
        List<DeliveryProductBO> deliveryProductBOList = Lists.newArrayList();
        for (DeliveryOrderInfo deliveryOrderInfo : deliveryOrderInfoList) {
            PickProduct pickProduct = pickProductMap.get(deliveryOrderInfo.getProductId());
            if(pickProduct == null) {
                continue;
            }
            DeliveryProductBO deliveryProductBO = new DeliveryProductBO();
            deliveryProductBO.setPpid(pickProduct.getPpid());
            deliveryProductBO.setCount(deliveryOrderInfo.getProductCount());
            deliveryProductBOList.add(deliveryProductBO);
        }
        return deliveryProductBOList;
    }

    private List<DeliveryOrderInfo> buildDeliveryOrderInfoList(OrderInfo orderInfo, List<OrderDetailInfo> orderDetailInfoList, OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo) {
        List<Long> productIdList = orderDetailInfoList.stream().map(OrderDetailInfo::getProductId).collect(Collectors.toList());
        Map<Long, OrderDetailInfo> orderDetailInfoMap = orderDetailInfoList.stream().collect(Collectors.toMap(OrderDetailInfo::getProductId, Function.identity(),(n1,n2)->n2));
        List<DeliveryOrderInfo> hasSendDeliveryOrderList = deliveryOrderInfoService.list(new LambdaQueryWrapper<DeliveryOrderInfo>().eq(DeliveryOrderInfo::getOrderId, orderInfo.getId()).eq(DeliveryOrderInfo::getSupplierId, supplierTokenInfo.getId()).eq(DeliveryOrderInfo::getDeliveryStatus, DeliveryStatusEnum.SEND.getCode()).in(DeliveryOrderInfo::getProductId, productIdList));
        List<Long> hasAllSendProductIdList = Lists.newArrayList();
        Map<Long, List<DeliveryOrderInfo>> hasSendDeliveryOrderMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(hasSendDeliveryOrderList)) {
            hasSendDeliveryOrderMap = hasSendDeliveryOrderList.stream().collect(Collectors.groupingBy(DeliveryOrderInfo::getProductId));
            for (Map.Entry<Long, List<DeliveryOrderInfo>> entry : hasSendDeliveryOrderMap.entrySet()) {
                int productHasDeliveryTotalCount = entry.getValue().stream().collect(Collectors.summingInt(DeliveryOrderInfo::getProductCount));
                OrderDetailInfo orderDetailInfo = orderDetailInfoMap.get(entry.getKey());
                if(productHasDeliveryTotalCount >= orderDetailInfo.getBuyAmount()) {
                    hasAllSendProductIdList.add(entry.getKey());
                }
            }
        }
        List<DeliveryOrderInfo> deliveryOrderInfoList = Lists.newArrayList();
        if(param.getOperateType() == CommonConstant.DELIVERY_ALL_OPT_TYPE.intValue()) {
            // 订单购买的商品全部发货
            for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
                // 排除已经全部发货的商品
                if(hasAllSendProductIdList.contains(orderDetailInfo.getProductId())) {
                    continue;
                }
                // 处理未全部发货的商品
                int needDeliveryCount = orderDetailInfo.getBuyAmount();
                List<DeliveryOrderInfo> partDeliveryProductInfoList = hasSendDeliveryOrderMap.get(orderDetailInfo.getProductId());
                if(CollectionUtils.isNotEmpty(partDeliveryProductInfoList)) {
                    int productHasDeliveryTotalCount = partDeliveryProductInfoList.stream().collect(Collectors.summingInt(DeliveryOrderInfo::getProductCount));
                    needDeliveryCount = needDeliveryCount - productHasDeliveryTotalCount;
                }
                DeliveryOrderInfo deliveryOrderInfo = new DeliveryOrderInfo();
                deliveryOrderInfo.setSupplierId(supplierTokenInfo.getId());
                deliveryOrderInfo.setOrderNo(orderInfo.getOrderNo());
                deliveryOrderInfo.setOrderId(orderInfo.getId());
                deliveryOrderInfo.setOrderDetailId(orderDetailInfo.getId());
                deliveryOrderInfo.setProductId(orderDetailInfo.getProductId());
                deliveryOrderInfo.setProductDetailId(orderDetailInfo.getProductDetailId());
                deliveryOrderInfo.setProductCount(needDeliveryCount);
                deliveryOrderInfo.setDeliveryStatus(DeliveryStatusEnum.WAIT.getCode());
                deliveryOrderInfo.setDeliveryName(param.getDeliveryType());
                deliveryOrderInfo.setDeliveryNo(param.getDeliveryNo());
                deliveryOrderInfoList.add(deliveryOrderInfo);
            }
            return deliveryOrderInfoList;
        }
        // 部分发货
        if(CollectionUtils.isEmpty(param.getDeliveryProductInfoList())) {
            return deliveryOrderInfoList;
        }
        for (OrderDeliveryParam.DeliveryProductInfo deliveryProductInfo : param.getDeliveryProductInfoList()) {
            // 排除已全部发货的商品
            if(hasAllSendProductIdList.contains(deliveryProductInfo.getProductId())) {
                continue;
            }
            OrderDetailInfo orderDetailInfo = orderDetailInfoMap.get(deliveryProductInfo.getProductId());
            if(orderDetailInfo == null) {
                continue;
            }
            // 处理未全部发货的商品
            int needDeliveryCount = deliveryProductInfo.getCount();
            List<DeliveryOrderInfo> partDeliveryProductInfoList = hasSendDeliveryOrderMap.get(orderDetailInfo.getProductId());
            if(CollectionUtils.isNotEmpty(partDeliveryProductInfoList)) {
                int productHasDeliveryTotalCount = partDeliveryProductInfoList.stream().collect(Collectors.summingInt(DeliveryOrderInfo::getProductCount));
                if(needDeliveryCount > (orderDetailInfo.getBuyAmount() - productHasDeliveryTotalCount)) {
                    needDeliveryCount = orderDetailInfo.getBuyAmount() - productHasDeliveryTotalCount;
                }
            }
            if(needDeliveryCount > orderDetailInfo.getBuyAmount()) {
                needDeliveryCount = orderDetailInfo.getBuyAmount();
            }
            DeliveryOrderInfo deliveryOrderInfo = new DeliveryOrderInfo();
            deliveryOrderInfo.setSupplierId(supplierTokenInfo.getId());
            deliveryOrderInfo.setOrderNo(orderInfo.getOrderNo());
            deliveryOrderInfo.setOrderId(orderInfo.getId());
            deliveryOrderInfo.setOrderDetailId(orderDetailInfo.getId());
            deliveryOrderInfo.setProductId(deliveryProductInfo.getProductId());
            deliveryOrderInfo.setProductDetailId(orderDetailInfo.getProductDetailId());
            deliveryOrderInfo.setProductCount(needDeliveryCount);
            deliveryOrderInfo.setDeliveryStatus(DeliveryStatusEnum.WAIT.getCode());
            deliveryOrderInfo.setDeliveryName(param.getDeliveryType());
            deliveryOrderInfo.setDeliveryNo(param.getDeliveryNo());
            deliveryOrderInfoList.add(deliveryOrderInfo);
        }
        return deliveryOrderInfoList;
    }

    /**
     * 计算商品已发货数量
     * @param orderDetailInfoList
     * @return
     */
    private List<OrderDetailInfo> dealOrderDetailProductSendCount(OrderInfo orderInfo, List<OrderDetailInfo> orderDetailInfoList, SupplierTokenInfo supplierTokenInfo) {
        List<Long> productIdList = orderDetailInfoList.stream().map(OrderDetailInfo::getProductId).collect(Collectors.toList());
        List<DeliveryOrderInfo> hasSendDeliveryOrderList = deliveryOrderInfoService.list(new LambdaQueryWrapper<DeliveryOrderInfo>().eq(DeliveryOrderInfo::getOrderId, orderInfo.getId()).eq(DeliveryOrderInfo::getSupplierId, supplierTokenInfo.getId()).eq(DeliveryOrderInfo::getDeliveryStatus, DeliveryStatusEnum.SEND.getCode()).in(DeliveryOrderInfo::getProductId, productIdList));
        if(CollectionUtils.isEmpty(hasSendDeliveryOrderList)) {
            return Collections.emptyList();
        }
        Map<Long, List<DeliveryOrderInfo>> hasSendDeliveryOrderMap = hasSendDeliveryOrderList.stream().collect(Collectors.groupingBy(DeliveryOrderInfo::getProductId));
        List<OrderDetailInfo> needUpdateOrderDetailInfoList = Lists.newArrayList();
        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            List<DeliveryOrderInfo> deliveryOrderInfoList = hasSendDeliveryOrderMap.get(orderDetailInfo.getProductId());
            if (CollectionUtils.isEmpty(deliveryOrderInfoList)) {
                continue;
            }
            int hasSendCount = deliveryOrderInfoList.stream().collect(Collectors.summingInt(DeliveryOrderInfo::getProductCount));
            orderDetailInfo.setDeliveryCount(hasSendCount);
            needUpdateOrderDetailInfoList.add(orderDetailInfo);
        }
        return needUpdateOrderDetailInfoList;
    }

    @Override
    public String getSalesReportFormPageList(SalesReportFormSearchReq req) {
        return String.format(iframeUrl, commonHandle(req.getType(), req));
    }

    private <T> String commonHandle(Integer type, T req){
        Map<String, Object> resource = new HashMap<>(1);
        // metabase指定的question的值
        resource.put("question", type);

        // 转map
        Map<String, Object> params = JsonUtil.beanToMap(req);

        // 无关的字段传到matebase那边会识别不了报错
        params.remove("type");
        if (!excludeDimension.contains(type)) {
            params.remove("ch999_id");
        }
        return JwtTokenUtils.createToken(resource, params);
    }
}

package com.jiuji.pick.service.order.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description NEO创建采购单DTO
 * <AUTHOR>
 * @Date 2021/11/4
 */
@Data
public class NeoOrderDTO {

    /**
     * 采购单标题
     */
    private String title;

    /**
     * NEO渠道ID
     */
    private Long inSourceId;

    /**
     * 是否含税
     */
    private Boolean invoiceFlag;

    /**
     * 入库门店
     */
    private Long areaId;

    /**
     * 	采购单商品组
     */
    private List<PurchaseProduct> product;

    @Data
    public static class PurchaseProduct {

        // 商品规格编号，如果是大件则不能为空
        private Long ppid;

        // 商品名称
        private String name;

        // 采购数量
        private Long counts;

        // 采购单价
        private BigDecimal unitPrice;

        // 默认0，0：销售机 1：样机
        private Integer sampleProduct;

        // 是否为大件
        private Boolean isMobile;

        // 物流费
        private BigDecimal deliveryFee;
    }
}

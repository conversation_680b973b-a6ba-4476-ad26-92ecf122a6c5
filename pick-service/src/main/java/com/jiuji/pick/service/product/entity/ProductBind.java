package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_product_bind")
public class ProductBind extends Model<ProductBind> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 商品ppid
     */
    private Long ppid;

    /**
     * 供应商产品ID
     */
    private Long supplierProductId;

    /**
     * 商品绑定状态,0待审核，1绑定，2解绑
     */
    private Integer bindStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 绑定时间
     */
    private LocalDateTime bindTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;


    public static final String ID = "id";

    public static final String PRODUCT_ID = "product_id";

    public static final String SUPPLIER_ID = "supplier_id";

    public static final String PPID = "ppid";

    public static final String BIND_STATUS = "bind_status";

    public static final String REMARK = "remark";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String DEL_FLAG = "del_flag";

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

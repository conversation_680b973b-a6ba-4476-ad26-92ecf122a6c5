package com.jiuji.pick.service.user.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.secure.MD5Util;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.common.enums.PartnerUserSourceEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.common.utils.HostManageUtil;
import com.jiuji.pick.common.utils.HttpClientUtils;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.common.vo.SaveLogVo;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.mapper.PartnerUserMapper;
import com.jiuji.pick.service.user.query.PartnerUserQuery;
import com.jiuji.pick.service.user.service.PartnerUserService;
import com.jiuji.pick.service.user.vo.OAPartnerInfo;
import com.jiuji.pick.service.user.vo.PartnerInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 合作伙伴信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
@Slf4j
@Service
public class PartnerUserServiceImpl extends ServiceImpl<PartnerUserMapper, PartnerUser> implements PartnerUserService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Value("${skip.pick.url}")
    private String skipPickUrl;
    private static final String APP_NAME ="pick-web:";
    @Resource
    private NewOperateLogInfoService logInfoService;



    @Override
    public void clearLogout(Integer type) {
        if(type==null){
            throw new BizException("清除登录用户类型不能为空 2-NEO，1-大中型  ");
        }
        Set<String> keys = stringRedisTemplate.keys(WebConstant.PARTNER_KEY_PREFIX_LOGIN_TOKEN + "*");
        if (CollectionUtils.isNotEmpty(keys)) {
            for (String token : keys) {
                String key = token.substring(APP_NAME.length());
                String tokenInfoStr = stringRedisTemplate.opsForValue().get(key);
                if (ObjectUtil.isNotEmpty(tokenInfoStr)) {
                    PartnerTokenInfo partnerTokenInfo = Optional.ofNullable(JSON.parseObject(tokenInfoStr, PartnerTokenInfo.class))
                            .orElse(new PartnerTokenInfo());
                    Optional.ofNullable(partnerTokenInfo.getSource()).ifPresent((Integer item) -> {
                        if (type.equals(item)) {
                            stringRedisTemplate.delete(key);
                        }
                    });
                }
            }
        }
    }
    @Override
    public boolean saveOrUpdateByXtenantAndCheck(PartnerUser partnerUserInfo) {
        if (hasSameName(partnerUserInfo.getXtenant(), partnerUserInfo.getName())) {
            return Boolean.FALSE;
        }
        // 严选负责人不能为空
        String headPerson = partnerUserInfo.getHeadPerson();
        Integer headPersonId = partnerUserInfo.getHeadPersonId();
        if(StringUtils.isEmpty(headPerson)){
            throw new BizException("严选负责人不能为空");
        }
        if(ObjectUtil.isNull(headPersonId)){
            throw new BizException("严选负责人填写不正确，请填写九机在职员工");
        }
        //查询原来合作伙伴的数据
        Optional.ofNullable(partnerUserInfo.getId()).ifPresent(item->{
            PartnerUser partnerUser = Optional.ofNullable(this.getById(item)).orElse(new PartnerUser());
            String headPersonOld = Optional.ofNullable(partnerUser.getHeadPerson()).orElse("空");
            String headPersonNew = Optional.ofNullable(headPerson).orElse("空");
            if(!headPersonOld.equals(headPersonNew)){
                SaveLogVo saveLogVo = new SaveLogVo();
                String comment=String.format("严选负责人由：【%s】修改为【%s】",headPersonOld,headPersonNew);
                saveLogVo.setContent(comment);
                saveLogVo.setRelateId(item.toString());
                saveLogVo.setShowType(LogShowTypeEnum.ADMIN.getCode());
                saveLogVo.setType(NewOperateLogInfoTypeEnum.PARTNER_LIST.getCode());
                //日志记录
                logInfoService.saveLog(saveLogVo);
            }
        });


        return saveOrUpdateByXtenant(partnerUserInfo);
    }

    @Override
    public boolean saveOrUpdateByXtenant(PartnerUser partnerUserInfo) {
        PartnerUser partnerUserDB = getByXtenant(partnerUserInfo.getXtenant());
        if (Objects.isNull(partnerUserDB)) {
            return partnerUserInfo.insert();
        }
        LambdaQueryWrapper<PartnerUser> query = new LambdaQueryWrapper<>();
        query.eq(PartnerUser::getXtenant, partnerUserInfo.getXtenant());
        return baseMapper.update(partnerUserInfo, query) > 0;
    }

    @Override
    public PartnerUser getByXtenant(Long xtenant) {
        LambdaQueryWrapper<PartnerUser> query = new LambdaQueryWrapper<>();
        query.eq(PartnerUser::getXtenant, xtenant);
        return baseMapper.selectList(query).stream().findFirst().orElse(null);
    }

    @Override
    public boolean hasSameName(Long xtenant, String name) {
        LambdaQueryWrapper<PartnerUser> query = new LambdaQueryWrapper<>();
        query.eq(PartnerUser::getName, name);
        List<PartnerUser> partnerUserList = baseMapper.selectList(query);
        if (CollectionUtils.isEmpty(partnerUserList)) {
            return Boolean.FALSE;
        }
        if (partnerUserList.size() > 1) {
            return Boolean.TRUE;
        }
        // 排除更新的情况
        PartnerUser user = partnerUserList.get(0);
        if (String.valueOf(user.getXtenant()).equals(String.valueOf(xtenant))) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean forbid(Long id) {
        PartnerUser entity = new PartnerUser();
        entity.setId(id);
        entity.setStatus(Boolean.TRUE);
        return baseMapper.updateById(entity) > 0;
    }

    @Override
    public boolean enable(Long id) {
        PartnerUser entity = new PartnerUser();
        entity.setId(id);
        entity.setStatus(Boolean.FALSE);
        return baseMapper.updateById(entity) > 0;
    }

    @Override
    public IPage<PartnerUser> listPageQuery(PartnerUserQuery partnerUserQuery) {
        Page<PartnerUser> page = new Page<>(partnerUserQuery.getCurrent(), partnerUserQuery.getSize());

        LambdaQueryWrapper<PartnerUser> query = new LambdaQueryWrapper<>();
        if (null != partnerUserQuery.getId()) {
            query.eq(PartnerUser::getId, partnerUserQuery.getId());
        }
        if (StringUtils.isNotEmpty(partnerUserQuery.getName())) {
            query.like(PartnerUser::getName, partnerUserQuery.getName());
        }
        if (StringUtils.isNotEmpty(partnerUserQuery.getPhone())) {
            query.eq(PartnerUser::getPhone, partnerUserQuery.getPhone());
        }
        if (StringUtils.isNotEmpty(partnerUserQuery.getHeadPerson())) {
            query.eq(PartnerUser::getHeadPerson, partnerUserQuery.getHeadPerson());
        }
        if (null != partnerUserQuery.getRegisterTimeStart()) {
            query.ge(PartnerUser::getRegisterTime, partnerUserQuery.getRegisterTimeStart());
        }
        if (null != partnerUserQuery.getRegisterTimeEnd()) {
            query.le(PartnerUser::getRegisterTime, partnerUserQuery.getRegisterTimeEnd());
        }
        return baseMapper.selectPage(page, query);
    }

    @Override
    public Result<String> loginAndSaveOAPartner(String token, String xtenant) {
        String uuid = MD5Util.GetMD5Code(token);
        // 之前登陆过就不请求接口
        String tokenInfo = stringRedisTemplate.opsForValue().get(WebConstant.PARTNER_KEY_PREFIX_LOGIN_TOKEN + uuid);
        if (StringUtils.isNotEmpty(tokenInfo)) {
            return Result.success(skipPickUrl + uuid);
        }

        // 从OA获取合作伙伴信息
        String host = HostManageUtil.getHost(xtenant);
        if (StringUtils.isEmpty(host)) {
            return Result.error("未获取到域名信息");
        }
        Result<OAPartnerInfo> getPartnerResult = getPartnerFromOA(token, host);
        if (!getPartnerResult.isSucceed() || null == getPartnerResult.getData()) {
            return Result.error(getPartnerResult.getMsg(), getPartnerResult.getUserMsg(), null);
        }
        OAPartnerInfo oaPartnerInfo = getPartnerResult.getData();

        PartnerUser partnerUserDB = getByXtenant(oaPartnerInfo.getXtenant());
        if (Objects.isNull(partnerUserDB)) {
            PartnerUser partnerUser = new PartnerUser();
            partnerUser.setName(oaPartnerInfo.getName());
            partnerUser.setShortName(oaPartnerInfo.getShortName());
            partnerUser.setXtenant(oaPartnerInfo.getXtenant());
            this.save(partnerUser);
        } else {
            partnerUserDB.setName(oaPartnerInfo.getName());
            partnerUserDB.setShortName(oaPartnerInfo.getShortName());
            this.updateById(partnerUserDB);
        }
        PartnerUser partnerUserInfo = getByXtenant(oaPartnerInfo.getXtenant());
        // 存储redis
        PartnerTokenInfo partnerTokenInfo = new PartnerTokenInfo();
        partnerTokenInfo.setXtenant(partnerUserInfo.getXtenant());
        partnerTokenInfo.setId(partnerUserInfo.getId());
        partnerTokenInfo.setName(partnerUserInfo.getName());
        partnerTokenInfo.setPhone(partnerUserInfo.getPhone());
        partnerTokenInfo.setShortName(partnerUserInfo.getShortName());
        partnerTokenInfo.setSource(PartnerUserSourceEnum.OA.getCode());
        partnerTokenInfo.setLoginOAUserId(oaPartnerInfo.getLoginUserId());
        partnerTokenInfo.setLoginOAUserName(oaPartnerInfo.getLoginUserName());
        Date nowDate = new Date();
        partnerTokenInfo.setLoginTime(nowDate);
        partnerTokenInfo.setExpireDate(DateUtil.addDays(nowDate, 7));
        // 存储redis 7天有效期
        String tokenStr = JSONObject.toJSONString(partnerTokenInfo);
        stringRedisTemplate.opsForValue().set(WebConstant.PARTNER_KEY_PREFIX_LOGIN_TOKEN + uuid, tokenStr, 3, TimeUnit.DAYS);
        // 返回跳转地址信息
        return Result.success(skipPickUrl + uuid);
    }

    @Override
    public Result<String> loginAndSavePartnerInfo(String token, String xtenant, Long loginUserId, String loginUserName) {
        String uuid = MD5Util.GetMD5Code(token+xtenant);
        // 之前登陆过就不请求接口
        String tokenInfo = stringRedisTemplate.opsForValue().get(WebConstant.PARTNER_KEY_PREFIX_LOGIN_TOKEN + uuid);
        if (StringUtils.isNotEmpty(tokenInfo)) {
            return Result.success(skipPickUrl + uuid);
        }

        Result<OAPartnerInfo> getPartnerResult = getPartnerInfo(token, xtenant, loginUserId, loginUserName);
        if (!getPartnerResult.isSucceed() || null == getPartnerResult.getData()) {
            return Result.error(getPartnerResult.getMsg(), getPartnerResult.getUserMsg(), null);
        }
        OAPartnerInfo oaPartnerInfo = getPartnerResult.getData();

        PartnerUser partnerUser = getByXtenant(oaPartnerInfo.getXtenant());
        if (Objects.isNull(partnerUser)) {
            PartnerUser newPartnerUser = new PartnerUser();
            newPartnerUser.setName(oaPartnerInfo.getName());
            newPartnerUser.setShortName(oaPartnerInfo.getShortName());
            newPartnerUser.setXtenant(oaPartnerInfo.getXtenant());
            this.save(newPartnerUser);
            partnerUser = newPartnerUser;
        } else {
            partnerUser.setName(oaPartnerInfo.getName());
            partnerUser.setShortName(oaPartnerInfo.getShortName());
            this.updateById(partnerUser);
        }
        // 存储redis
        PartnerTokenInfo partnerTokenInfo = new PartnerTokenInfo();
        partnerTokenInfo.setXtenant(partnerUser.getXtenant());
        partnerTokenInfo.setId(partnerUser.getId());
        partnerTokenInfo.setName(partnerUser.getName());
        partnerTokenInfo.setPhone(partnerUser.getPhone());
        partnerTokenInfo.setShortName(partnerUser.getShortName());
        partnerTokenInfo.setSource(PartnerUserSourceEnum.OA.getCode());
        partnerTokenInfo.setLoginOAUserId(oaPartnerInfo.getLoginUserId());
        partnerTokenInfo.setLoginOAUserName(oaPartnerInfo.getLoginUserName());
        Date nowDate = new Date();
        partnerTokenInfo.setLoginTime(nowDate);
        partnerTokenInfo.setExpireDate(DateUtil.addDays(nowDate, 7));
        // 存储redis 7天有效期
        String tokenStr = JSON.toJSONString(partnerTokenInfo);
        stringRedisTemplate.opsForValue().set(WebConstant.PARTNER_KEY_PREFIX_LOGIN_TOKEN + uuid, tokenStr, 3, TimeUnit.DAYS);
        log.info("OA跳转采货王平台返回信息：{},xtenant:{}", skipPickUrl + uuid, xtenant);
        // 返回跳转地址信息
        return Result.success(skipPickUrl + uuid);
    }

    @Override
    public Result<String> loginAndSaveNeoPartner(String token, String xtenant, String oaHost) {
        String uuid = MD5Util.GetMD5Code(token);
        String skipLink = skipPickUrl + uuid;
        // 之前登陆过就不请求接口
        String tokenInfo = stringRedisTemplate.opsForValue().get(WebConstant.PARTNER_KEY_PREFIX_LOGIN_TOKEN + uuid);
        if (StringUtils.isNotEmpty(tokenInfo)) {
            return Result.success(skipLink);
        }
        // neo跳转携带过来的xtenant就是域名，直接传入
        Result<OAPartnerInfo> neoPartnerInfo = getPartnerFromNeo(token, xtenant);
        if (!neoPartnerInfo.isSucceed() || null == neoPartnerInfo.getData()) {
            return Result.error(neoPartnerInfo.getMsg(), neoPartnerInfo.getUserMsg());
        }
        PartnerUser partnerUser = getByXtenant(neoPartnerInfo.getData().getXtenant());
        if (ObjectUtil.isNull(partnerUser)) {
            PartnerUser newPartnerUser = new PartnerUser();
            newPartnerUser.setName(neoPartnerInfo.getData().getName());
            newPartnerUser.setShortName(neoPartnerInfo.getData().getShortName());
            newPartnerUser.setXtenant(neoPartnerInfo.getData().getXtenant());
            newPartnerUser.setHost(xtenant);
            newPartnerUser.setSource(PartnerUserSourceEnum.SMALL_SASS.getCode());
            this.save(newPartnerUser);
            partnerUser = newPartnerUser;
        } else {
            partnerUser.setName(neoPartnerInfo.getData().getName());
            partnerUser.setShortName(neoPartnerInfo.getData().getShortName());
            partnerUser.setHost(xtenant);
            this.updateById(partnerUser);
        }
        // 存储redis
        PartnerTokenInfo partnerTokenInfo = new PartnerTokenInfo();
        partnerTokenInfo.setXtenant(partnerUser.getXtenant());
        partnerTokenInfo.setId(partnerUser.getId());
        partnerTokenInfo.setOaHost(oaHost);
        partnerTokenInfo.setName(partnerUser.getName());
        partnerTokenInfo.setPhone(partnerUser.getPhone());
        partnerTokenInfo.setShortName(partnerUser.getShortName());
        partnerTokenInfo.setSource(PartnerUserSourceEnum.SMALL_SASS.getCode());
        partnerTokenInfo.setLoginOAUserId(neoPartnerInfo.getData().getLoginUserId());
        partnerTokenInfo.setLoginOAUserName(neoPartnerInfo.getData().getLoginUserName());
        partnerTokenInfo.setToken(token);
        partnerTokenInfo.setHost(xtenant);
        Date nowDate = new Date();
        partnerTokenInfo.setLoginTime(nowDate);
        partnerTokenInfo.setExpireDate(DateUtil.addDays(nowDate, 7));
        // 存储redis 7天有效期
        String tokenStr = JSON.toJSONString(partnerTokenInfo);
        stringRedisTemplate.opsForValue().set(WebConstant.PARTNER_KEY_PREFIX_LOGIN_TOKEN + uuid, tokenStr, 3, TimeUnit.DAYS);
        // 返回跳转地址信息
        return Result.success(skipLink);
    }

    @Override
    public Result<String> logout(String token) {
        if (StringUtils.isEmpty(token)) {
            return Result.error("token参数为空");
        }
        stringRedisTemplate.delete(WebConstant.PARTNER_KEY_PREFIX_LOGIN_TOKEN + token);
        return Result.success();
    }

    /***
     * @description: 请求OA 接口获取合作伙伴数据
     * @Param: [token]
     * @author: Lbj
     * @date: 2021/5/10 16:58
     */
    private Result<OAPartnerInfo> getPartnerFromOA(String token, String host) {
        String resultStr = null;
        try {
            String url = host + "/cloudapi_nc/oa-stock/api/chw/getXtenant/v1?xservicename=oa-stock";
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Authorization", token);
            resultStr = HttpClientUtils.get(url, headMap);
            log.info("合作伙伴跳转，调用OA获取合作伙伴信息，url:{},Token:{},result:{}", url, token, resultStr);
            Result<JSONObject> result = JSONObject.parseObject(resultStr, Result.class);
            if (Objects.isNull(result) || !result.isSucceed() || Objects.isNull(result.getData())) {
                log.error("获取OA合作伙伴信息失败 返回结果:{} 参数:{}", resultStr, token);
                return Result.error(resultStr, "调用OA接口失败");
            }
            log.info("获取合作伙伴OA登录信息 token {} 结果{} ", token, resultStr);
            JSONObject data = result.getData();
            JSONObject userData = result.getData().getJSONObject("oaUserBO");
            if (Objects.isNull(userData)) {
                return Result.error("获取OA合作伙伴用户登录数据为空！");
            }
            OAPartnerInfo oaPartnerInfo = new OAPartnerInfo();
            oaPartnerInfo.setName((String) data.get("name"));
            oaPartnerInfo.setShortName((String) data.get("shortName"));
            oaPartnerInfo.setXtenant(Long.parseLong(userData.getString("xtenant")));
            oaPartnerInfo.setLoginUserId(Long.parseLong(userData.getString("UserID")));
            oaPartnerInfo.setLoginUserName(userData.getString("UserName"));
            return Result.success(oaPartnerInfo);
        } catch (NumberFormatException e) {
            log.error("合作伙伴跳转，调用OA获取合作伙伴信息发生异常，Exception:", e);
            return Result.errorInfo("从OA获取用户信息失败:" + resultStr);
        }
    }

    /**
     * 获取租户信息，研发组接口
     */
    private Result<OAPartnerInfo> getPartnerInfo(String token, String xtenant, Long loginUserId, String loginUserName) {
        String resultStr = null;
        try {
            String paramToken = MD5Util.GetMD5Code(LocalDate.now().toString());
            String url = "https://manager.saas.ch999.cn/saasManager/api/configProvider/tenantList/v3";
            resultStr = HttpRequest.get(url).header("token", paramToken.toLowerCase()).form("pageSize", 100).execute().body();
            log.info("合作伙伴跳转，调用OA获取合作伙伴信息，url:{},Token:{},result:{}", url, token, resultStr);
            Result<JSONObject> result = JSON.parseObject(resultStr, new TypeReference<Result<JSONObject>>() {
            });
            if (Objects.isNull(result) || !result.isSucceed() || Objects.isNull(result.getData())) {
                log.error("获取OA合作伙伴信息失败 返回结果:{} 参数:[{},{}]", resultStr, token, xtenant);
                return Result.error(resultStr, "调用OA接口失败");
            }
            Object data = result.getData().get("records");
            List<PartnerInfoVO> partnerInfoList = JSON.parseObject(data.toString(), new TypeReference<List<PartnerInfoVO>>() {
            });
            PartnerInfoVO partnerInfoVO = null;
            for (PartnerInfoVO vo : partnerInfoList) {
                for (PartnerInfoVO.BusinessInfos businessInfo : vo.getBusinessInfos()) {
                    int xTenant = Optional.ofNullable(businessInfo.getXtenant()).orElse(Integer.MAX_VALUE);
                    Integer newXtenant;
                    // 易腾租户的特殊处理，研发组提供的接口中的xtenant与C#传过来的值不对应
                    if (2000 <= xTenant && xTenant < 5000) {
                        newXtenant = xTenant / 1000 * 1000;
                    } else {
                        newXtenant = xTenant;
                    }
                    if (newXtenant.equals(Integer.valueOf(xtenant))) {
                        partnerInfoVO = vo;
                        partnerInfoVO.setBusinessInfos(Collections.singletonList(businessInfo));
                    }
                }
            }
            if (Objects.isNull(partnerInfoVO)) {
                return Result.error("获取OA合作伙伴用户登录数据为空！");
            }
            OAPartnerInfo oaPartnerInfo = new OAPartnerInfo();
            oaPartnerInfo.setName(partnerInfoVO.getBusinessInfos().get(0).getBusinessName());
            oaPartnerInfo.setShortName(partnerInfoVO.getTenantName());
            oaPartnerInfo.setXtenant(Long.parseLong(xtenant));
            oaPartnerInfo.setLoginUserId(loginUserId);
            oaPartnerInfo.setLoginUserName(loginUserName);
            return Result.success(oaPartnerInfo);
        } catch (Exception e) {
            log.error("合作伙伴跳转，调用OA获取合作伙伴信息发生异常，Exception:", e);
            return Result.errorInfo("从OA获取用户信息失败:" + resultStr);
        }
    }

    /**
     * 调用NEO获取租户数据
     *
     * @param token token
     * @param host  域名
     * @return 租户数据
     */
    private Result<OAPartnerInfo> getPartnerFromNeo(String token, String host) {
        String resultStr = null;
        try {
            String url = CommonConstant.HTTPS + host + "/small-oa/api/purchaseKing/getUserInfo/v1";
            Map<String, String> headMap = new HashMap<>(2);
            headMap.put("Authorization", token);
            headMap.put(CommonConstant.XTENANT, host);
            resultStr = HttpClientUtils.get(url, headMap);
            log.info("合作伙伴跳转，调用NEO获取合作伙伴信息，url:{},Token:{},result:{}", url, token, resultStr);
            if (StringUtils.isEmpty(resultStr)) {
                return Result.error("获取NEO合作伙伴信息失败，返回结果为空");
            }
            Result<JSONObject> result = JSON.parseObject(resultStr, new TypeReference<Result<JSONObject>>() {
            });
            if (!result.isSucceed() || ObjectUtil.isNull(result.getData())) {
                log.error("获取NEO合作伙伴信息失败，返回结果:{} 参数:{}", resultStr, token);
                return Result.error(resultStr, "调用NEO接口失败");
            }
            JSONObject data = result.getData();
            OAPartnerInfo partnerInfo = new OAPartnerInfo();
            partnerInfo.setName((String) data.get("name"));
            partnerInfo.setShortName((String) data.get("shortName"));
            partnerInfo.setXtenant(Long.parseLong(data.getString(CommonConstant.XTENANT)));
            partnerInfo.setLoginUserId(Long.parseLong(data.getString("loginUserId")));
            partnerInfo.setLoginUserName(data.getString("loginUserName"));
            return Result.success(partnerInfo);
        } catch (Exception e) {
            log.error("合作伙伴跳转，调用NEO获取合作伙伴信息发生异常，Exception:", e);
            return Result.errorInfo("从NEO获取用户信息失败:" + resultStr);
        }
    }
}

package com.jiuji.pick.service.common.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.DeliveryFeeArea;

/**
 * <p>
 *  物流费区域表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
public interface DeliveryFeeAreaService extends IService<DeliveryFeeArea> {


    /**
     * 编辑接口
     * @param feeArea DeliveryFeeArea
     * @return Boolean
     */
    Result<Boolean> edit(DeliveryFeeArea feeArea);

    /**
     * 删除
     * @param id 主键
     * @return boolean
     */
    Result<Boolean> delete(Integer id);

    /**
     * 分页列表
     * @param province 省
     * @param provinceCode 省代码
     * @param currentPage 当前页
     * @param size 页条数
     * @return IPage
     */
    Result<IPage<DeliveryFeeArea>> getPage(String province, String provinceCode, Integer currentPage, Integer size);

    /**
     * 根据cityId，获取区域物流费
     * @param cityId cityId
     * @return DeliveryFeeArea
     */
    DeliveryFeeArea getFeeArea(String cityId);
}

package com.jiuji.pick.service.rpc.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 查询物流轨迹请求参数
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/12 19:03
 */
@Data
public class CommonQueryRoutReq {

    /**
     * 运单号
     */
    @NotNull(message = "运单号不能为空")
    private String waybillNo;

    /**
     * 收件人或寄件人的手机号或固话
     */
    private String phone;

    /**
     * 快递编码
     */
    private String mark;
    /**
     * 订单号
     */
    private Long orderNo;

}

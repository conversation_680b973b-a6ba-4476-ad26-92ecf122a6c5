package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_product_st_detail")
public class ProductStDetail extends Model<ProductStDetail> {

    private static final long serialVersionUID=1L;

    /**
     * 明细id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 规格id
     */
    private Integer standId;

    /**
     * 规格内容（每种规格的明细值，比如颜色：蓝色、红色....）
     */
    private String value;

    private Integer orderId;

    /**
     * 是否删除
     */
    private Boolean isDel;

    private Integer newsId;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    public static final String IS_DEL = "is_del";
    public static final String ORDER_ID = "order_id";
    public static final String STAND_ID = "stand_id";
    public static final String VALUE = "value";

}

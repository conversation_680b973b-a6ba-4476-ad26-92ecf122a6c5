package com.jiuji.pick.service.product.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProductImageVO {

    /**
     * 图片id
     */
    private Integer id;

    /**
     * 图片相对路径
     */
    private String url;

    /**
     * 图片名字
     */
    private String name;

    /**
     * 图片排序
     */
    private Integer rank = 100;

    private Boolean selectFlag;

    /**
     * 图片type参考 WebConstants.PRODUCTIMAGETYPE
     */
    private Integer type;

    /**
     * 关联的商品id（当为商品图和开箱图时候为productId其余均为ppid）
     */
    private Long productId;

    /**
     * 商品主图区域code
     */
    private String areaCode;
}

package com.jiuji.pick.service.order.service.impl;

import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.constant.OrderTipConstant;
import com.jiuji.pick.common.enums.LogisticsCompanyEnum;
import com.jiuji.pick.common.enums.OrderLogTypeEnum;
import com.jiuji.pick.common.enums.OrderStatusEnum;
import com.jiuji.pick.common.enums.OrderTypeEnum;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.DeliveryFeeArea;
import com.jiuji.pick.service.common.service.DeliveryFeeAreaService;
import com.jiuji.pick.service.order.bo.DeliveryProductBO;
import com.jiuji.pick.service.order.bo.OrderDeliveryBO;
import com.jiuji.pick.service.order.dto.OaOrderDTO;
import com.jiuji.pick.service.order.dto.OaOrderResultDTO;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.entity.ProductOrderVersion;
import com.jiuji.pick.service.order.param.CartOrderParam;
import com.jiuji.pick.service.order.param.OrderDeliveryParam;
import com.jiuji.pick.service.order.service.*;
import com.jiuji.pick.service.order.vo.CartInfoVO;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.service.CategoryService;
import com.jiuji.pick.service.user.service.SupplierChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 大件订单业务处理
 * @function:
 * @description: BulkyOrderServiceImpl.java
 * @date: 2021/10/09
 * @author: sunfayun
 * @version: 1.0
 */
@Service
@Slf4j
public class BulkyOrderServiceImpl implements BulkyOrderService {

    /**
     * 默认发货时间
     */
    private static final int DEFAULT_DELIVERY_DAY = 7;

    /**
     * 商品备注
     */
    private static final String PRODUCT_COMMENT = "采货王商品";

    @Resource
    private CartInfoService cartInfoService;
    @Resource
    private SupplierChannelService supplierChannelService;
    @Resource
    private ProductOrderVersionService productOrderVersionService;
    @Resource
    private OrderDetailInfoService orderDetailInfoService;
    @Resource
    private OaService oaService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private DeliveryFeeAreaService deliveryFeeAreaService;
    @Resource
    private OrderInfoLogService orderInfoLogService;

    @Override
    public String createOrder(CartInfoVO.CartInfoData cartInfoData, PartnerTokenInfo partnerTokenInfo, CartOrderParam cartOrderParam) {
        Long supplierId = cartInfoData.getSupplierId();
        // 获取渠道商id
        String channelId = supplierChannelService.getChannelIdV2(partnerTokenInfo.getXtenant(), supplierId, cartOrderParam.getAddressId(), cartOrderParam.getAuthId());
        if (StringUtils.isBlank(channelId)) {
            return cartInfoData.getSupplierName() + " 渠道id获取失败!";
        }

        // oa采购单
        OaOrderDTO oaOrderDTO = createOaOrderDTO(channelId, cartOrderParam, partnerTokenInfo, cartInfoData);


        // 采购单商品信息
        List<OaOrderDTO.BulkyPurchaseGoods> bulkyPurchaseGoodsList = new ArrayList<>();
        // 交易快照
        List<ProductOrderVersion> productOrderVersionList = new ArrayList<>();
        // 订单详情
        List<OrderDetailInfo> orderDetailInfoList = new ArrayList<>();
        // 购物车id集合
        List<Long> cartIdList = new ArrayList<>();
        // 总价
        BigDecimal totalPrice = BigDecimal.ZERO;

        DeliveryFeeArea deliveryFeeArea = deliveryFeeAreaService.getFeeArea(cartOrderParam.getCityId());

        List<CartInfoVO.CartProduct> productList = cartInfoData.getProductList();
        for (CartInfoVO.CartProduct cartProduct : productList) {
            // 使用的价格
            Integer priceType = cartProduct.getPriceType();
            BigDecimal price = Integer.valueOf(1).equals(priceType) ? Optional.ofNullable(cartProduct.getBuyTaxPrice()).orElse(BigDecimal.ZERO) : Optional.ofNullable(cartProduct.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO);

            // 交易快照
            ProductOrderVersion productOrderVersion = createProductOrderVersion(cartProduct, priceType, supplierId, partnerTokenInfo);
            // add
            productOrderVersionList.add(productOrderVersion);

            // 购物车id
            cartIdList.add(cartProduct.getId());

            // 订单详情
            OrderDetailInfo orderDetailInfo = createOrderDetailInfo(cartProduct, partnerTokenInfo, price, supplierId);
            // 物流费计算
            if (cartProduct.getRemoteDeliveryFee() == 1 && deliveryFeeArea != null) {
                BigDecimal deliveryFee = deliveryFeeArea.getDeliveryFee().multiply(BigDecimal.valueOf(cartProduct.getProductCount()));
                orderDetailInfo.setDeliveryFee(deliveryFee);
                totalPrice = totalPrice.add(deliveryFee);
            }
            // add
            orderDetailInfoList.add(orderDetailInfo);
            // 总价
            totalPrice = totalPrice.add(price.multiply(BigDecimal.valueOf(cartProduct.getProductCount())));


            // 处理商品个数，大件商品数量只能是 1
            for(int i = 0; i < cartProduct.getProductCount(); i++){
                BigDecimal productTotalPrice = BigDecimal.ZERO;
                // 物流费计算
                if (cartProduct.getRemoteDeliveryFee() == 1 && deliveryFeeArea != null) {
                    // 物流费
                    productTotalPrice = productTotalPrice.add(deliveryFeeArea.getDeliveryFee());
                }
                productTotalPrice = productTotalPrice.add(price);
                // 采购单商品
                OaOrderDTO.BulkyPurchaseGoods bulkyPurchaseGoods = createPurchaseGoods(cartProduct, price, productTotalPrice);
                bulkyPurchaseGoods.setInbeihuoprice(price);
                // add
                bulkyPurchaseGoodsList.add(bulkyPurchaseGoods);
            }
        }

        // 加入oa采购单商品列表
        oaOrderDTO.setMkcIdList(bulkyPurchaseGoodsList);

        // 订单信息
        OrderInfo orderInfo = createOrderInfo(partnerTokenInfo, channelId, cartOrderParam, supplierId, oaOrderDTO, totalPrice);

        // 保存订单信息
        boolean saveOrderInfo = saveOrderInfo(orderInfo, orderDetailInfoList, productOrderVersionList);
        if (!saveOrderInfo) {
            return OrderTipConstant.SAVE_ERROR;
        }
        // 保存日志
        orderInfoLogService.saveOrderLog("订单已创建，等待生成采购单。", OrderLogTypeEnum.CREATE, orderInfo.getId(),
                partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());

        // 保存并生成oa采购单
        return saveOaOrder(oaOrderDTO, partnerTokenInfo, orderInfo, cartInfoData,
                productOrderVersionList, orderDetailInfoList, cartIdList);
    }

    @Override
    public Result<String> cancelOrder(Long orderNo, Long xTenant) {
        OaOrderResultDTO oaOrderResultDTO = oaService.cancelOrder(orderNo, xTenant, OrderTypeEnum.BULKY.getCode());
        if (oaOrderResultDTO == null) {
            return Result.error("oa取消大件订单失败,返回消息为空");
        }
        if (!Integer.valueOf(0).equals(oaOrderResultDTO.getCode())) {
            return Result.error("oa取消大件订单失败,返回消息:" + oaOrderResultDTO.getUserMsg());
        }
        return Result.success();
    }

    @Override
    public Result<String> deliveryProduct(List<DeliveryProductBO> deliveryProductBOList, OrderInfo orderInfo, OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo) {
        OrderDeliveryBO orderDeliveryBO = this.buildOrderDeliveryBO(deliveryProductBOList, orderInfo, param, supplierTokenInfo);
        OaOrderResultDTO oaOrderResultDTO = oaService.orderDeliveryForProduct(orderInfo, orderDeliveryBO, OrderTypeEnum.BULKY.getCode());
        if (oaOrderResultDTO == null) {

            return Result.error("分批发货同步OA失败,请重试或联系管理员");
        }
        if (!Integer.valueOf(0).equals(oaOrderResultDTO.getCode())) {
            return Result.error("分批发货同步OA失败,错误消息:" + oaOrderResultDTO.getUserMsg());
        }
        return Result.success();
    }

    /**
     * 创建oa采购单
     *
     * @param channelId
     * @param cartOrderParam
     * @param partnerTokenInfo
     * @param cartInfoData
     * @return
     */
    private static OaOrderDTO createOaOrderDTO(String channelId, CartOrderParam cartOrderParam, PartnerTokenInfo partnerTokenInfo, CartInfoVO.CartInfoData cartInfoData) {
        OaOrderDTO oaOrderDTO = new OaOrderDTO();
        oaOrderDTO.setInSourceId4Bulky(channelId);
        oaOrderDTO.setAreaid(Integer.valueOf(cartOrderParam.getAddressId()));
        //oaOrderDTO.setRemark("采货王订单");
        oaOrderDTO.setInuser(partnerTokenInfo.getLoginOAUserName());
        //oaOrderDTO.setSubKind(0);
        oaOrderDTO.setTitle(cartInfoData.getSupplierName() + "采购单");
        return oaOrderDTO;
    }

    /**
     * 创建采购单商品
     *
     * @param cartProduct
     * @param price
     * @param totalPrice  采购真实价格 = 商品价格+物流费用
     * @return
     */
    private static OaOrderDTO.BulkyPurchaseGoods createPurchaseGoods(CartInfoVO.CartProduct cartProduct, BigDecimal price, BigDecimal totalPrice) {
        OaOrderDTO.BulkyPurchaseGoods purchaseGoods = new OaOrderDTO.BulkyPurchaseGoods();
        purchaseGoods.setPpid(Integer.valueOf(cartProduct.getPpid() + ""));
        purchaseGoods.setPrice(price);
        return purchaseGoods;
    }

    /**
     * 创建交易快照
     *
     * @param cartProduct
     * @param priceType
     * @param supplierId
     * @param partnerTokenInfo
     * @return
     */
    private ProductOrderVersion createProductOrderVersion(CartInfoVO.CartProduct cartProduct, Integer priceType, Long supplierId,
                                                          PartnerTokenInfo partnerTokenInfo) {
        ProductOrderVersion productOrderVersion = new ProductOrderVersion();
        productOrderVersion.setBuyNoTaxPrice(cartProduct.getBuyNoTaxPrice());
        productOrderVersion.setBuyTaxPrice(cartProduct.getBuyTaxPrice());
        productOrderVersion.setPartnerId(partnerTokenInfo.getId());
        productOrderVersion.setPpid(cartProduct.getPpid());
        productOrderVersion.setPriceType(priceType);
        productOrderVersion.setProductColor(cartProduct.getProductColor());
        productOrderVersion.setProductName(cartProduct.getProductName());
        productOrderVersion.setProductDetailId(cartProduct.getProductDetailId());
        productOrderVersion.setProductId(cartProduct.getProductId());
        productOrderVersion.setProductImg(cartProduct.getProductImage());
        productOrderVersion.setProductCid(cartProduct.getProductCid());
        productOrderVersion.setProductBarCode(cartProduct.getProductBarCode());
        productOrderVersion.setSupplierUserId(supplierId);
        // 查询分类名称
        if(cartProduct.getProductCid() != null) {
            Category category = categoryService.getOneById(cartProduct.getProductCid().intValue());
            if(category != null) {
                productOrderVersion.setProductCidName(category.getName());
            }
        }

        return productOrderVersion;
    }

    /**
     * 创建采购单详情
     *
     * @param cartProduct
     * @param partnerTokenInfo
     * @param price
     * @param supplierId
     * @return
     */
    private static OrderDetailInfo createOrderDetailInfo(CartInfoVO.CartProduct cartProduct, PartnerTokenInfo partnerTokenInfo,
                                                         BigDecimal price, Long supplierId) {
        OrderDetailInfo orderDetailInfo = new OrderDetailInfo();
        orderDetailInfo.setBuyAmount(cartProduct.getProductCount());
        orderDetailInfo.setPartnerId(partnerTokenInfo.getId());
        orderDetailInfo.setProductId(cartProduct.getProductId());
        orderDetailInfo.setProductDetailId(cartProduct.getProductDetailId());
        orderDetailInfo.setProductPrice(price);
        orderDetailInfo.setSupplierId(supplierId);
        orderDetailInfo.setCartId(cartProduct.getId());
        return orderDetailInfo;
    }

    /**
     * 创建采货王订单信息
     *
     * @param partnerTokenInfo
     * @param channelId
     * @param cartOrderParam
     * @param supplierId
     * @param oaOrderDTO
     * @param totalPrice
     * @return
     */
    private static OrderInfo createOrderInfo(PartnerTokenInfo partnerTokenInfo, String channelId, CartOrderParam cartOrderParam, Long supplierId, OaOrderDTO oaOrderDTO, BigDecimal totalPrice) {
        OrderInfo orderInfo = new OrderInfo();
        // 默认采货王
        orderInfo.setChannelType(0);
        orderInfo.setOrderType(OrderTypeEnum.BULKY.getCode());
        orderInfo.setOrderStatus(OrderStatusEnum.DEFAULT.getCode());
        orderInfo.setPartnerId(partnerTokenInfo.getId());
        orderInfo.setChannelId(channelId);
        orderInfo.setReceivePoiId(cartOrderParam.getAddressId());
        orderInfo.setReceivePoiName(cartOrderParam.getAddressName());
        orderInfo.setReceivePoiCityId(cartOrderParam.getCityId());
        orderInfo.setSupplierId(supplierId);
        orderInfo.setXtenantId(partnerTokenInfo.getXtenant());
        orderInfo.setOrderTitle(oaOrderDTO.getTitle());
        orderInfo.setTotalPrice(totalPrice);
        orderInfo.setContactPerson(cartOrderParam.getContactPerson());
        orderInfo.setContactPhone(cartOrderParam.getContactPhone());
        orderInfo.setReceiveAddress(cartOrderParam.getReceiveAddress());
        orderInfo.setDeliveryTime(LocalDateTime.now().plusDays(DEFAULT_DELIVERY_DAY));
        return orderInfo;
    }

    /**
     * 保存订单信息
     *
     * @param orderInfo
     * @param orderDetailInfoList
     * @param productOrderVersionList
     * @return
     */
    private boolean saveOrderInfo(OrderInfo orderInfo, List<OrderDetailInfo> orderDetailInfoList, List<ProductOrderVersion> productOrderVersionList) {
        // 保存信息
        boolean insert = orderInfo.insert();
        if (!insert) {
            return false;
        }
        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            orderDetailInfo.setOrderId(orderInfo.getId());
        }
        boolean insert1 = orderDetailInfoService.saveBatch(orderDetailInfoList);
        if (!insert1) {
            orderInfo.deleteById();
            return false;
        }
        for (ProductOrderVersion productOrderVersion : productOrderVersionList) {
            productOrderVersion.setOrderId(orderInfo.getId());
        }
        boolean insert2 = productOrderVersionService.saveBatch(productOrderVersionList);
        if (!insert2) {
            orderInfo.deleteById();
        }
        return insert2;
    }

    /**
     * 生成oa采购单，并更新订单信息
     *
     * @param oaOrderDTO
     * @param partnerTokenInfo
     * @param orderInfo
     * @param cartInfoData
     * @param productOrderVersionList
     * @param orderDetailInfoList
     * @param cartIdList
     * @return
     */
    private String saveOaOrder(OaOrderDTO oaOrderDTO, PartnerTokenInfo partnerTokenInfo, OrderInfo orderInfo,
                               CartInfoVO.CartInfoData cartInfoData,
                               List<ProductOrderVersion> productOrderVersionList,
                               List<OrderDetailInfo> orderDetailInfoList,
                               List<Long> cartIdList) {
        OaOrderResultDTO<OaOrderResultDTO.OaOrderData> oaOrderResultDTO =
                oaService.createOaOrder(oaOrderDTO, partnerTokenInfo.getXtenant(), OrderTypeEnum.BULKY.getCode());
        if (oaOrderResultDTO == null) {
            // 保存日志
            orderInfoLogService.saveOrderLog("生成采购单失败，返回结果为空", OrderLogTypeEnum.GENERATE_NO, orderInfo.getId(),
                    partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());
            // 删除订单
            orderInfo.deleteById();
            return cartInfoData.getSupplierName() + " 的采购单生成失败! 原因：" + oaOrderResultDTO.getUserMsg();
        }
        if (!Integer.valueOf(0).equals(oaOrderResultDTO.getCode()) || oaOrderResultDTO.getData() == null) {
            // 保存日志
            orderInfoLogService.saveOrderLog("生成大件采购单失败，返回结果：" + oaOrderResultDTO.getMsg(), OrderLogTypeEnum.GENERATE_NO,
                    orderInfo.getId(), partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());

            // 删除订单
            orderInfo.deleteById();

            return cartInfoData.getSupplierName() + " 的采购单生成失败!,错误信息：" + oaOrderResultDTO.getMsg();
        }
        // 获取订单号
        Integer orderNoInt = (Integer) oaOrderResultDTO.getData();
        Long orderNo = Long.valueOf(String.valueOf(orderNoInt));

        // 保存日志
        orderInfoLogService.saveOrderLog("采购单已生成，采购单号：" + orderNo, OrderLogTypeEnum.GENERATE_NO, orderInfo.getId(),
                partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());

        orderInfo.setOrderNo(orderNo);
        orderInfo.setOrderStatus(OrderStatusEnum.AUDITED.getCode());
        // 保存信息
        boolean update = orderInfo.updateById();

        for (ProductOrderVersion productOrderVersion : productOrderVersionList) {
            productOrderVersion.setOrderNo(orderNo);
        }
        boolean update1 = productOrderVersionService.updateBatchById(productOrderVersionList);

        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            orderDetailInfo.setOrderNo(orderNo);
        }
        boolean update2 = orderDetailInfoService.updateBatchById(orderDetailInfoList);

        // 删除购物车
        boolean remove = cartInfoService.removeByIds(cartIdList);

        // 错误日志统计
        errorLog(update, update1, update2, remove, orderInfo, orderNo, cartIdList);
        return null;
    }

    /**
     * 错误日志统计
     *
     * @param update
     * @param update1
     * @param update2
     * @param remove
     * @param orderInfo
     * @param orderNo
     * @param cartIdList
     */
    private void errorLog(boolean update, boolean update1, boolean update2, boolean remove,
                          OrderInfo orderInfo, Long orderNo, List<Long> cartIdList) {
        if (!update) {
            log.error("保存更新采购单失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!update1) {
            log.error("保存更新商品快照失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!update2) {
            log.error("保存更新订单详情失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!remove) {
            log.error("删除购物车失败，cartIdList:{}", cartIdList);
        }
    }

    private OrderDeliveryBO buildOrderDeliveryBO(List<DeliveryProductBO> deliveryProductBOList, OrderInfo orderInfo,
                                                 OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo) {
        OrderDeliveryBO orderDeliveryBO = new OrderDeliveryBO();
        orderDeliveryBO.setExpressCode(param.getDeliveryType());
        orderDeliveryBO.setChannelName(supplierTokenInfo.getName());
        //如果过快递选择其他的情况,就把备注的快递传过去
        if(LogisticsCompanyEnum.OTHER.getSpell().equals(param.getDeliveryType())){
            String expressName = Optional.ofNullable(param.getRemarks())
                    .orElse(LogisticsCompanyEnum.getChinese(param.getDeliveryType()));
            orderDeliveryBO.setExpressName(expressName);
        } else {
            orderDeliveryBO.setExpressName(LogisticsCompanyEnum.getChinese(param.getDeliveryType()));
        }
       // orderDeliveryBO.setExpressName(LogisticsCompanyEnum.getChinese(param.getDeliveryType()));
        orderDeliveryBO.setExpressNum(param.getDeliveryNo());
        orderDeliveryBO.setSubId4Bulk(String.valueOf(orderInfo.getOrderNo()));
        orderDeliveryBO.setTxtDay(String.valueOf(MagicalValueConstant.INT_7));
        orderDeliveryBO.setInUser(supplierTokenInfo.getLoginName());
        orderDeliveryBO.setPhone(orderInfo.getContactPhone());
        List<OrderDeliveryBO.CaigouDetailVo> caigouDetailVoList = Lists.newArrayList();
        for (DeliveryProductBO deliveryProductBO : deliveryProductBOList) {
            OrderDeliveryBO.CaigouDetailVo caigouDetailVo = new OrderDeliveryBO.CaigouDetailVo();
            caigouDetailVo.setPpriceid(deliveryProductBO.getPpid());
            caigouDetailVo.setNumber(deliveryProductBO.getCount());
            caigouDetailVoList.add(caigouDetailVo);
        }
        orderDeliveryBO.setCaigouDetailVoList(caigouDetailVoList);
        return orderDeliveryBO;
    }


}

package com.jiuji.pick.service.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.jiuji.cloud.stock.service.ChwQueryCloud;
import com.jiuji.cloud.stock.vo.request.ProductStockReq;
import com.jiuji.cloud.stock.vo.response.ShowProductStockVO;
import com.jiuji.cloud.stock.vo.response.StockChwInfo;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.constant.OrderTipConstant;
import com.jiuji.pick.common.enums.OrderPriceTypeEnum;
import com.jiuji.pick.common.enums.OrderStatusEnum;
import com.jiuji.pick.common.enums.OrderTypeEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.utils.ExcelExport;
import com.jiuji.pick.common.vo.BasePageVO;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.service.AttachmentInfoService;
import com.jiuji.pick.service.order.dto.PreviousOrderPriceReq;
import com.jiuji.pick.service.order.dto.PreviousProductDTO;
import com.jiuji.pick.service.order.entity.OrderInfoLog;
import com.jiuji.pick.service.order.mapper.OrderDetailInfoMapper;
import com.jiuji.pick.service.order.param.OrderSearchParam;
import com.jiuji.pick.service.order.service.OrderDetailInfoService;
import com.jiuji.pick.service.order.service.OrderGetInfoService;
import com.jiuji.pick.service.order.service.OrderInfoLogService;
import com.jiuji.pick.service.order.service.OrderInfoService;
import com.jiuji.pick.service.order.vo.*;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单获取信息服务  实现类
 *
 * <AUTHOR>
 * @date 2021/6/10
 */
@Slf4j
@Service
public class OrderGetInfoServiceImpl implements OrderGetInfoService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private OrderInfoService orderInfoService;
    @Resource
    private OrderDetailInfoService orderDetailInfoService;
    @Resource
    private AttachmentInfoService attachmentInfoService;
    @Resource
    private OrderInfoLogService orderInfoLogService;
    @Value("${getDomainsBy.isDev}")
    private Boolean isDev;
    /**
     * excel标题
     */
    private static final String[] EXCEL_TITLE_JIUXUN = {"采购单号", "OA销售单","订单状态","采购方", "是否含税", "ppid", "商品名称", "数量", "单价",
            "合计金额"};
    private static final String[] EXCEL_TITLE = {"采购单号","订单状态","采购方",  "ppid", "商品名称", "数量", "单价",
            "合计金额"};

    private static final List<Long> jiuXunListProd = Collections.singletonList(300016L);
    private static final List<Long> jiuXunListDev = Arrays.asList(180015L, 300016L);

    @Resource
    private ChwQueryCloud chwQueryCloud;
    @Resource
    private OrderDetailInfoMapper orderDetailInfoMapper;


    /**
     * 导出非深圳九讯供应链管理有限公司数据
     *
     * @param records
     * @param response
     * @return
     */
    private Result export(List<OrderListExportVO> records, HttpServletResponse response) {
        try {
            //excel文件名
            String fileName = "订单列表" + System.currentTimeMillis() + ".xls";
            //sheet名
            String sheetName = "订单列表";
            int row = records.size();
            int column = EXCEL_TITLE.length;
            String[][] content = new String[row][column];
            for (int i = 0; i < records.size(); i++) {
                OrderListExportVO orderListVO = records.get(i);
                content[i][MagicalValueConstant.INT_0] = orderListVO.getOrderNo() + "";
                content[i][MagicalValueConstant.INT_1] = OrderStatusEnum.getDescByCode(orderListVO.getOrderStatus());
                content[i][MagicalValueConstant.INT_2] = orderListVO.getPartnerName();
                content[i][MagicalValueConstant.INT_3] = orderListVO.getPpid()+"";
                content[i][MagicalValueConstant.INT_4] = orderListVO.getProductName();
                content[i][MagicalValueConstant.INT_5] = orderListVO.getBuyAmount() + "";
                content[i][MagicalValueConstant.INT_6] = orderListVO.getProductPrice()+"";
                content[i][MagicalValueConstant.INT_7] = orderListVO.getTotalAmount()+"";

            }
            // 创建HSSFWorkbook
            HSSFWorkbook wb = ExcelExport.getWorkbook(sheetName, EXCEL_TITLE, content, null);
            // 响应到客户端
            OutputStream os = response.getOutputStream();
            // 设置响应头
            ExcelExport.setResponseHeader(response, fileName);
            wb.write(os);
            // 关闭流
            os.close();
        } catch (Exception e) {
            log.error("export error ", e);
            return Result.error("导出异常");
        }
        return null;
    }


    /**
     * 导出深圳九讯供应链管理有限公司数据
     *
     * @param records
     * @param response
     * @return
     */
    private Result exportJiuXun(List<OrderListExportVO> records, HttpServletResponse response) {
        try {
            //excel文件名
            String fileName = "订单列表" + System.currentTimeMillis() + ".xls";
            //sheet名
            String sheetName = "订单列表";
            int row = records.size();
            int column = EXCEL_TITLE_JIUXUN.length;
            String[][] content = new String[row][column];
            for (int i = 0; i < records.size(); i++) {
                OrderListExportVO orderListVO = records.get(i);
                content[i][MagicalValueConstant.INT_0] = orderListVO.getOrderNo() + "";
                content[i][MagicalValueConstant.INT_1] = orderListVO.getSaleOrderNo();
                content[i][MagicalValueConstant.INT_2] = OrderStatusEnum.getDescByCode(orderListVO.getOrderStatus());
                content[i][MagicalValueConstant.INT_3] = orderListVO.getPartnerName();
                content[i][MagicalValueConstant.INT_4] = OrderPriceTypeEnum.UNTAXED.getCode().equals(orderListVO.getPriceType())?OrderPriceTypeEnum.UNTAXED.getDesc():OrderPriceTypeEnum.TAX_INCLUDED.getDesc();
                content[i][MagicalValueConstant.INT_5] = orderListVO.getPpid()+"";
                content[i][MagicalValueConstant.INT_6] = orderListVO.getProductName();
                content[i][MagicalValueConstant.INT_7] = orderListVO.getBuyAmount() + "";
                content[i][MagicalValueConstant.INT_8] = orderListVO.getProductPrice()+"";
                content[i][MagicalValueConstant.INT_9] = orderListVO.getTotalAmount()+"";

            }
            // 创建HSSFWorkbook
            HSSFWorkbook wb = ExcelExport.getWorkbook(sheetName, EXCEL_TITLE_JIUXUN, content, null);
            // 响应到客户端
            OutputStream os = response.getOutputStream();
            // 设置响应头
            ExcelExport.setResponseHeader(response, fileName);
            wb.write(os);
            // 关闭流
            os.close();
        } catch (Exception e) {
            log.error("export error ", e);
            return Result.error("导出异常");
        }
        return null;
    }

    @Override
    public Result listExport(List<OrderListExportVO> records, HttpServletResponse response) {
        //判断是否为 深圳九讯供应链管理有限公司
        SupplierTokenInfo supplierTokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck())
                .orElseThrow(() -> new BizException("登录信息失效，请重新登录"));
        Long id = supplierTokenInfo.getId();
        //判断正式环境和测试环境
        Result result;
        if (isDev) {
            //判断是否是深圳九讯供应链管理有限公司
            if (jiuXunListDev.contains(id)) {
                result = exportJiuXun(records, response);
            } else {
                result = export(records, response);
            }

        } else {
            //判断是否是深圳九讯供应链管理有限公司
            if (jiuXunListProd.contains(id)) {
                result = exportJiuXun(records, response);
            } else {
                result = export(records, response);
            }
        }
        return result;
    }

    @Override
    public Result<Page<OrderListVO>> getPartnerOrderList(OrderSearchParam param) {
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null) {
            return Result.notLoginError();
        }
        param.setPartnerId(partnerTokenInfo.getId());
        Page<OrderListVO> partnerOrderListPage = orderInfoService.getPartnerOrderList(param);
        if (partnerOrderListPage == null) {
            return Result.noData();
        }
        if (CollectionUtils.isEmpty(partnerOrderListPage.getRecords())) {
            return Result.noData();
        }

        for (OrderListVO record : partnerOrderListPage.getRecords()) {
            if (Objects.nonNull(record.getOrderNo()) && record.getOrderNo() < 0) {
                record.setOrderNo(null);
            }
        }
        return Result.success(partnerOrderListPage);
    }

    @Override
    public Result<OrderDetailVO> getPartnerOrderDetail(Long orderId) {
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null) {
            return Result.notLoginError();
        }

        // 查询订单信息
        OrderDetailVO partnerOrderDetail = orderInfoService.getPartnerOrderDetail(orderId, partnerTokenInfo.getId());
        if (partnerOrderDetail == null) {
            return Result.error(OrderTipConstant.QUERY_ORDER_ERROR);
        }
        // 查询订单商品信息
        List<OrderProductVO> partnerOrderProduct = orderDetailInfoService.getOrderProduct(orderId);
        Boolean allDistributedFlag = false;
        Integer deliveryCountSum = 0;
        Integer buyAmountSum = 0;
        if (CollectionUtils.isNotEmpty(partnerOrderProduct)) {
            List<Long> attachmentIdList = partnerOrderProduct.stream().flatMap((OrderProductVO e) -> {
                String attachmentIds = e.getAttachmentIds();
                return CommonUtil.covertIdStr2Long(attachmentIds).stream();
            }).distinct().collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(attachmentIdList)) {
                joinAttachment(attachmentIdList, partnerOrderProduct, partnerOrderDetail);
            }

            for (OrderProductVO x : partnerOrderProduct) {
                Integer deliveryCount = x.getDeliveryCount();
                deliveryCountSum += deliveryCount;
                Integer buyAmount = x.getBuyAmount();
                buyAmountSum += buyAmount;
            }

        }
        if (deliveryCountSum.equals(buyAmountSum)) {
            allDistributedFlag = true;
        }
        partnerOrderDetail.setAllDistributedFlag(allDistributedFlag);

        // 设置虚拟商品和资产订单的对应联系人
        this.setOrderLeaderPhone(partnerOrderDetail);
        // 查询订单日志
        List<OrderLogVO> orderLogVOList = getOrderDetailLogList(orderId);
        partnerOrderDetail.setOrderLogList(orderLogVOList);
        // 设置总物流费
        partnerOrderDetail.setTotalDeliveryFee(orderDetailInfoService.calculateTotalDeliveryFee(partnerOrderDetail.getOrderId()));
        // 放入商品
        partnerOrderDetail.setProductList(partnerOrderProduct);
        if (Objects.nonNull(partnerOrderDetail.getOrderNo()) && partnerOrderDetail.getOrderNo() < 0) {
            partnerOrderDetail.setOrderNo(null);
        }
        return Result.success(partnerOrderDetail);
    }

    /**
     * 放入商品资料
     *
     * @param attachmentIdList
     * @param partnerOrderProduct
     */
    private void joinAttachment(List<Long> attachmentIdList, List<OrderProductVO> partnerOrderProduct, OrderDetailVO partnerOrderDetail) {
        // 获取商品资料
        List<AttachmentInfo> attachmentInfos = attachmentInfoService.getBaseMapper().selectBatchIds(attachmentIdList);
        if (CollectionUtils.isNotEmpty(attachmentInfos)) {
            Map<Long, AttachmentInfo> attachmentInfoMap = Maps.newHashMapWithExpectedSize(attachmentInfos.size());
            for (AttachmentInfo attachmentInfo : attachmentInfos) {
                attachmentInfoMap.put(attachmentInfo.getId(), attachmentInfo);
            }
            // 循环设置商品资料
            for (OrderProductVO partnerOrderProductVO : partnerOrderProduct) {
                setAttachment(partnerOrderProductVO, attachmentInfoMap, partnerOrderDetail);
            }
        }
    }

    /**
     * 循环放入资料
     *
     * @param partnerOrderProductVO partnerOrderProductVO
     * @param attachmentInfoMap     attachmentInfoMap
     */
    private static void setAttachment(OrderProductVO partnerOrderProductVO, Map<Long, AttachmentInfo> attachmentInfoMap, OrderDetailVO partnerOrderDetail) {
        List<Long> idList = CommonUtil.covertIdStr2Long(partnerOrderProductVO.getAttachmentIds());
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<OrderProductVO.AttachmentInfoData> attachmentInfoDataList = new ArrayList<>();
        for (Long id : idList) {
            AttachmentInfo attachmentInfo = attachmentInfoMap.get(id);
            if (attachmentInfo != null) {
                OrderProductVO.AttachmentInfoData attachmentInfoData =
                        new OrderProductVO.AttachmentInfoData();
                attachmentInfoData.setFileName(attachmentInfo.getFileName());
                // 只有订单完成了才能下载资料
                if (partnerOrderDetail.getOrderStatus() != null &&
                        partnerOrderDetail.getOrderStatus() == OrderStatusEnum.COMPLETED.getCode().intValue()) {
                    attachmentInfoData.setFilePath(attachmentInfo.getFilePath());
                }
                attachmentInfoDataList.add(attachmentInfoData);
            }
        }
        // 放入商品资料
        partnerOrderProductVO.setAttachmentInfoList(attachmentInfoDataList);
    }

    @Override
    public Result<Page<OrderListVO>> getSupplierOrderList(OrderSearchParam param) {
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if (supplierTokenInfo == null || supplierTokenInfo.getId() == null) {
            return Result.notLoginError();
        }
        param.setSupplierId(supplierTokenInfo.getId());
        Page<OrderListVO> supplierOrderListPage = orderInfoService.getSupplierOrderList(param);
        if (supplierOrderListPage == null) {
            return Result.noData();
        }
        for (OrderListVO record : supplierOrderListPage.getRecords()) {
            if (Objects.nonNull(record.getOrderNo()) && record.getOrderNo() < 0) {
                record.setOrderNo(null);
            }
        }
        return Result.success(supplierOrderListPage);
    }


    @Override
    public Result<Page<OrderListExportVO>> getSupplierOrderListV2(OrderSearchParam param) {
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if (supplierTokenInfo == null || supplierTokenInfo.getId() == null) {
            return Result.notLoginError();
        }
        param.setSupplierId(supplierTokenInfo.getId());
        Page<OrderListExportVO> supplierOrderListPage = orderInfoService.getSupplierOrderListV2(param);
        if (supplierOrderListPage == null) {
            return Result.noData();
        }
        return Result.success(supplierOrderListPage);
    }

    @Override
    public Result<OrderDetailVO> getSupplierOrderDetail(Long orderId) {
        SupplierTokenInfo supplierTokenInfo = currentRequestComponent.getSupplierTokenInfoWithoutCheck();
        if (supplierTokenInfo == null || supplierTokenInfo.getId() == null) {
            return Result.notLoginError();
        }

        // 查询订单信息
        OrderDetailVO supplierOrderDetail = orderInfoService.getSupplierOrderDetail(orderId, supplierTokenInfo.getId());
        if (supplierOrderDetail == null) {
            return Result.noData();
        }
        // 查询订单商品信息
        List<OrderProductVO> partnerOrderProduct = orderDetailInfoService.getOrderProduct(orderId);
        //查询订单商品对应的上一单的价格
        selectPreviousOrderPrice(partnerOrderProduct,supplierOrderDetail.getPartnerId(),orderId);
        //查询九机对应商品的库存情况
        setStockInfoByJiuJi(partnerOrderProduct);
        // 放入商品
        supplierOrderDetail.setProductList(partnerOrderProduct);

        // 设置虚拟商品和资产订单的对应联系人
        this.setOrderLeaderPhone(supplierOrderDetail);

        // 获取订单详情日志
        List<OrderLogVO> orderLogVOList = getOrderDetailLogList(orderId);
        // 放入日志
        supplierOrderDetail.setOrderLogList(orderLogVOList);
        // 设置总物流费
        supplierOrderDetail.setTotalDeliveryFee(orderDetailInfoService.calculateTotalDeliveryFee(supplierOrderDetail.getOrderId()));
        if (Objects.nonNull(supplierOrderDetail.getOrderNo()) && supplierOrderDetail.getOrderNo() < 0) {
            supplierOrderDetail.setOrderNo(null);
        }
        return Result.success(supplierOrderDetail);
    }

    /**
     * 查询订单商品上一次家的价格
     * @param partnerOrderProduct
     * @param partnerId
     */
    private void selectPreviousOrderPrice(List<OrderProductVO> partnerOrderProduct,Integer partnerId,Long orderId){
        if(CollectionUtils.isEmpty(partnerOrderProduct)){
            return;
        }
        List<Long> productIds = partnerOrderProduct.stream().map(OrderProductVO::getProductId).collect(Collectors.toList());
        PreviousOrderPriceReq priceReq = new PreviousOrderPriceReq();
        priceReq.setOrderId(orderId);
        priceReq.setProductIdList(productIds);
        priceReq.setPartnerId(partnerId);
        List<PreviousProductDTO> orderDetailInfos = orderDetailInfoMapper.selectPreviousOrderPrice(priceReq);
        if(CollectionUtils.isEmpty(orderDetailInfos)){
            return;
        }
        partnerOrderProduct.forEach(item -> {
            Integer priceType = Optional.ofNullable(item.getPriceType()).orElse(Integer.MAX_VALUE);
            Long productId = Optional.ofNullable(item.getProductId()).orElse(Long.MAX_VALUE);
            PreviousProductDTO previousProductDTO = orderDetailInfos.stream()
                    .filter(obj -> priceType.equals(obj.getPriceType()) && productId.equals(obj.getProductId()))
                    .findFirst()
                    .orElse(new PreviousProductDTO());
            Optional.ofNullable(previousProductDTO.getProductPrice()).ifPresent(item::setLastOrderPrice);
        });
    }

    /**
     * 九机商品库存查询
     * @param partnerOrderProduct
     */
    private void setStockInfoByJiuJi(List<OrderProductVO> partnerOrderProduct){
        if(CollectionUtils.isEmpty(partnerOrderProduct)){
            return;
        }
        List<Integer> ppidList = partnerOrderProduct.stream()
                .map(item -> Optional.ofNullable(item.getPpid()).orElse(0L).intValue())
                .collect(Collectors.toList());
        ProductStockReq productStockReq = new ProductStockReq();
        productStockReq.setPpidList(ppidList);
        R<ShowProductStockVO> result = chwQueryCloud.findProductStock(productStockReq);
        if(0!=result.getCode()){
            throw new BizException("九机商品库存查询异常:"+Optional.ofNullable(result.getMsg()).orElse(result.getUserMsg()));
        }
        Optional.ofNullable(result.getData()).ifPresent(item->{
            Map<Integer, String> ppidStockMap = item.getPpidStockMap();
            if(ObjectUtil.isNotNull(ppidStockMap)){
                partnerOrderProduct.forEach(obj-> obj.setStockInfo(ppidStockMap.get( Optional.ofNullable(obj.getPpid()).orElse(0L).intValue())));
            }
            Map<Integer, List<StockChwInfo>> productStockMap = item.getProductStockMap();
            if(ObjectUtil.isNotNull(productStockMap)){
                partnerOrderProduct.forEach(obj->{
                    List<StockChwInfo> stockChwInfos = productStockMap.get(Optional.ofNullable(obj.getPpid()).orElse(0L).intValue());
                    if(CollectionUtils.isNotEmpty(stockChwInfos)){
                        List<StockChwInfo> collect = stockChwInfos.stream().sorted(Comparator.comparing(StockChwInfo::getLeftCount).reversed()).collect(Collectors.toList());
                        obj.setStockChwInfos(collect);
                    }
                });
            }
        });
    }

    @Override
    public Result<BasePageVO<PlatformOrderListVO>> getPlatformOrderList(OrderSearchParam param) {
        BasePageVO<PlatformOrderListVO> basePageVO = new BasePageVO<>(param.getCurrent(), param.getSize());

        int count = orderInfoService.countPlatformOrder(param);
        basePageVO.setTotal(count);

        if (count == 0) {
            return Result.success(basePageVO);
        }

        List<PlatformOrderListVO> platformOrderList = orderInfoService.getPlatformOrderList(param);
        if (CollectionUtils.isEmpty(platformOrderList)) {
            return Result.success(basePageVO);
        }

        List<Long> orderIdList = platformOrderList.stream().map(PlatformOrderListVO::getOrderId).collect(Collectors.toList());
        // 设置物流费
        Map<Long, BigDecimal> deliveryFeeMap = orderDetailInfoService.calculateTotalDeliveryFee(orderIdList);
        if (MapUtils.isNotEmpty(deliveryFeeMap)) {
            platformOrderList.forEach(orderListVO -> orderListVO.setTotalDeliveryFee(deliveryFeeMap.get(orderListVO.getOrderId())));
        }
        // 订单日志查询
        LambdaQueryWrapper<OrderInfoLog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(OrderInfoLog::getOrderId, orderIdList);
        List<OrderInfoLog> allOrderLogList = orderInfoLogService.list(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(allOrderLogList)) {
            Map<Long, List<OrderInfoLog>> orderLogMap = allOrderLogList.stream()
                    .collect(Collectors.groupingBy(OrderInfoLog::getOrderId));
            for (PlatformOrderListVO platformOrderListVO : platformOrderList) {
                // 获取日志信息
                List<OrderInfoLog> orderInfoLogs = orderLogMap.get(platformOrderListVO.getOrderId());
                if (CollectionUtils.isNotEmpty(orderInfoLogs)) {
                    List<OrderLogVO> orderLogVOList = getOrderLogVOList(orderInfoLogs);
                    platformOrderListVO.setLogs(orderLogVOList);
                }
            }
        }
        for (PlatformOrderListVO platformOrderListVO : platformOrderList) {
            if (Objects.nonNull(platformOrderListVO.getOrderNo()) && platformOrderListVO.getOrderNo() < 0) {
                platformOrderListVO.setOrderNo(null);
            }
        }

        basePageVO.setRecords(platformOrderList);
        return Result.success(basePageVO);
    }

    /**
     * 获取订单日志列表
     *
     * @param orderInfoLogs
     * @return
     */
    private static List<OrderLogVO> getOrderLogVOList(List<OrderInfoLog> orderInfoLogs) {
        return orderInfoLogs.stream().map((OrderInfoLog e) -> {
            OrderLogVO orderLogVO = new OrderLogVO();
            orderLogVO.setId(e.getId());
            orderLogVO.setContent(e.getContent());
            orderLogVO.setCreateTime(e.getCreateTime());
            orderLogVO.setOperationUserName(e.getOperationUserName());
            return orderLogVO;
        }).sorted(Comparator.comparing(OrderLogVO::getCreateTime).thenComparing(Comparator.comparing(OrderLogVO::getId).reversed()))
                .collect(Collectors.toList());
    }

    @Override
    public Result<OrderDetailVO> getPlatformOrderDetail(Long orderId) {
        // 查询订单信息
        OrderDetailVO platformOrderDetail = orderInfoService.getPlatformOrderDetail(orderId);
        if (platformOrderDetail == null) {
            return Result.error(OrderTipConstant.QUERY_ORDER_ERROR);
        }
        // 查询订单商品信息
        List<OrderProductVO> platformOrderProduct = orderDetailInfoService.getOrderProduct(orderId);
        if (CollectionUtils.isNotEmpty(platformOrderProduct)) {
            Integer countProduct = platformOrderProduct.stream()
                    .map(OrderProductVO::getBuyAmount).reduce(Integer::sum).orElse(0);
            platformOrderDetail.setTotalCount(countProduct);
            platformOrderDetail.setProductList(platformOrderProduct);
        }

        // 设置虚拟商品和资产订单的对应联系人
        this.setOrderLeaderPhone(platformOrderDetail);

        // 获取订单详情日志
        List<OrderLogVO> orderLogVOList = getOrderDetailLogList(orderId);
        // 放入日志
        platformOrderDetail.setOrderLogList(orderLogVOList);
        // 设置总物流费
        platformOrderDetail.setTotalDeliveryFee(orderDetailInfoService.calculateTotalDeliveryFee(platformOrderDetail.getOrderId()));
        if (Objects.nonNull(platformOrderDetail.getOrderNo()) && platformOrderDetail.getOrderNo() < 0) {
            platformOrderDetail.setOrderNo(null);
        }

        return Result.success(platformOrderDetail);
    }

    /**
     * 获取订单详情日志
     *
     * @param orderId
     * @return
     */
    private List<OrderLogVO> getOrderDetailLogList(Long orderId) {
        // 查询订单日志
        LambdaQueryWrapper<OrderInfoLog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(OrderInfoLog::getOrderId, orderId);
        List<OrderInfoLog> orderLogList = orderInfoLogService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(orderLogList)) {
            return Collections.emptyList();
        }
        return getOrderLogVOList(orderLogList);
    }

    /**
     * 设置订单详情页中对应联系人电话
     *
     * @param detailVO 订单详情VO
     */
    private void setOrderLeaderPhone(OrderDetailVO detailVO) {
        Integer orderType = detailVO.getOrderType();
        if (orderType == null) {
            return;
        }
        if (orderType.equals(OrderTypeEnum.VIRTUAL.getCode()) && StringUtils.isNotBlank(detailVO.getVirtualPhone())) {
            detailVO.setLeaderPhone(detailVO.getVirtualPhone());
            return;
        }
        boolean assetsType;
        assetsType = orderType.equals(OrderTypeEnum.FIX_ASSETS.getCode()) || orderType.equals(OrderTypeEnum.COMMON_ASSETS.getCode());
        if (assetsType && StringUtils.isNotBlank(detailVO.getAssetsPhone())) {
            detailVO.setLeaderPhone(detailVO.getAssetsPhone());
        }
    }


}

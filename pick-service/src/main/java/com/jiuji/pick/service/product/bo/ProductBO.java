package com.jiuji.pick.service.product.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jiuji.pick.service.product.vo.StandardsVo;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;

/**
 * 商品提交的表单对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ProductBO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 分类
     */
    @NotNull(message = "请选择商品分类")
    private Integer cid;
    /**
     * 扩展分类
     */
    private Integer cidExtend;
    /**
     * 分类树
     */
    @JsonIgnore
    private String cidFamily;
    /**
     * 品牌
     */
    @NotNull(message = "请选择商品品牌")
    private Integer brandId;
    /**
     * 名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String name;
    /**
     * 简称
     */
    private String shotName;
    /**
     * 大小件
     */
    private Boolean isMobile;

    /**
     * 新品
     */
    private Boolean isNew;
    /**
     * 推荐
     */
    private Boolean isRecommend;
    /**
     * 热门
     */
    private Boolean isPopular;
    /**
     * 特价（抢购）
     */
    private Boolean isSpecial;
    /**
     * 商品卖点
     */
    private String sellingPoint;
    /**
     * 分类特性
     */
    private String proFeature;
    /**
     * 规格明细id拼接串
     */
    private String standardId;
    /**
     * 视频
     */
    private String video;
    /**
     * 视频预览图
     */
    private String videoPic;
    /**
     * 视频预览图显示路径
     */
    private String videoPicDisplay;
    /**
     * 备注
     */
    private String remark;
    /**
     * 商品关键字
     */
    private String proKey;
    /**
     * 搜索关键字
     */
    private String searchKey;
    /**
     * 描述
     */
    private String description;
    /**
     * 商品简介
     */
    private String proBrief;
    /**
     * m版以及APP版简介链接名称
     */
    private String desLinkName;
    /**
     * m版以及APP版简介链接
     */
    private String desLinkUrl;
    /**
     * PC版简介链接名称
     */
    private String pcDesLinkName;
    /**
     * PC版简介链接
     */
    private String pcDesLinkUrl;
    /**
     * 去看看链接名称
     */
    private String lookDesLinkName;
    /**
     * 去看看链接
     */
    private String lookDesLinkUrl;
    /**
     * 使用技巧Url
     */
    private String useSkillLink;
    /**
     * 服务介绍跳转链接
     */
    private String serviceIntroduceLink;
    /**
     * 适用机型
     */
    private String adpProduct;
    /**
     * 绑定故障
     */
    private String troubleType;
    /**
     * 绑定故障ppid
     */
    private String troubleTypePPID;
    /**
     * 商品政策
     */
    private List<Policy> polices;
    /**
     * 详细介绍
     */
    private String detail;
    /**
     * 地区选择
     */
    private String cityId;
    /**
     * 是否显示
     */
    private Boolean isDisplay;
    /**
     * 是否首页掌上专享推荐
     */
    private Boolean isRecommendByLike;
    /**
     * 是否支持三九服务
     */
    private Boolean isServices;
    /**
     * 是否是旗舰机型
     */
    private Boolean isUltimate;
    /**
     * 7天无理由
     */
    private Boolean sevenDays;
    /**
     * 7天无理由（激活除外）
     */
    private Boolean sevenDaysUnAct;
    /**
     * 不支持7天理由
     */
    private Boolean noSevenDays;
    /**
     * 支持服务
     */
    private String supportService;
    /**
     * 支持上门服务
     */
    private String supportToHomeService;
    /**
     * OA是否启用(OA用来控制该商品是否可用)
     */
    private Boolean oaEnable;

    /*********************详情回显时用*********************/
    /**
     * 操作人
     */
    private String inUser;
    /**
     * 添加时间
     */
    private Date addTime;
    /**
     * 最后更新时间
     */
    private Date lastModifyTime;
    /**
     * 规格明细拼接串 规格 紫色|裸机|
     */
    private String standard;
    /**
     * 七天无理由退货
     */
    private Integer sevenDayReturn;

    /*********************扩展字段 用于保存商品*********************/
    /**
     * 是否维修商品
     */
    @JsonIgnore
    private Boolean isRepair;
    /**
     * 商品投放地区
     */
    @JsonIgnore
    private List<Integer> cityIds = new ArrayList<>();
    /**
     * 适用商品ID   源:adpProduct
     */
    @JsonIgnore
    private List<Long> adpProductIds;
    /**
     * 故障ID       源:troubleType
     */
    @JsonIgnore
    private List<Integer> troubleIds;

    private List<AdmSkuBO> admSkuBOList;
    /*********************透传使用字段*********************/
    /**
     * 原始数据json
     */
    private String original;

    /**
     * 是否开启SN码
     */
    private Boolean isSn;

    /**
     * 小件预留列表排除限制 1-是否尽心预留限制    OA需要的字段（可以找oa咨询）
     */
    private Integer otherLimit;

    /**
     * 是否数量盘点   OA需要的字段（可以找oa咨询） 1--是 0--否
     */
    private Boolean numberPanDian;

    /********************* 商品同步缺少的字段 用于映射九机过来的数据 *********************/
    /**
     * ppid
     */
    private Long ppid;
    /**
     * 会员价
     */
    private BigDecimal memberPrice;
    /**
     * 成本价
     */
    private BigDecimal costPrice;
    /**
     * 默认图片
     */
    private String image;
    /**
     * 商品状态
     */
    private Integer que;
    /**
     * 默认配置
     */
    private String baseConfig;
    /**
     * 规格-明细
     */
    private Map<Integer, List<Integer>> standardMap;
    /********************* 以上字段不要随意删除 否则影响商品同步   *********************/


    /**
     * 商品政策对象
     */
    @Data
    public static class Policy {

        /**
         * 表productAreaInfo 的id
         */
        private Integer id;

        /**
         * 储存的是H5代码
         */
        @NotBlank(message = "商品政策为空")
        private String h5Str;

        /**
         * 适用的城市
         */
        @NotEmpty(message = "cityId为空")
        private List<Integer> cityIds;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            Policy policy = (Policy) o;
            return Objects.equals(id, policy.id) && Objects.equals(h5Str, policy.h5Str) && Objects
                    .equals(cityIds, policy.cityIds);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id, h5Str, cityIds);
        }
    }
}

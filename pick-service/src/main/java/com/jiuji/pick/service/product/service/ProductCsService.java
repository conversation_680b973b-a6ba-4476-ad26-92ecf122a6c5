/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.service.product.entity.ProductCs;
import com.jiuji.pick.service.product.vo.UpdateProductParamVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
public interface ProductCsService extends IService<ProductCs> {

    /**
     * 修改商品参数
     *
     * @param productId 商品id
     * @param param 商品参数
     * @param cateType 0--主分类参数 1--拓展分类参数
     * @param staffId 员工工号
     * @return 修改结果
     */
    boolean updateProductParam(Long productId, List<UpdateProductParamVO.ParamVo> param,
                               Integer cateType, Integer staffId);

    /**
     * 修改sku的参数
     *
     * @param ppid ppid
     * @param param 商品参数
     * @return 修改结果
     */
    boolean updateSkuParam(Long ppid, List<UpdateProductParamVO.ParamVo> param, Integer staffId);

    /**
     * 整合修改商品或者sku参数
     * @return 修改结果
     */
    Result<Boolean> updateProductOrSkuParam(UpdateProductParamVO param, boolean isSync);

    /**
     * 获取商品的选项参数（单选，多选）
     *
     * @param productId 商品id
     * @return 修改结果
     */
    List<String> getProductSelectParam(Long productId);

    /**
     * 查询所有inpputtype=2,3 多选参数的值
     * @return
     */
    Map<Long,String> getProductParamValue(List<Long> ppid);
}

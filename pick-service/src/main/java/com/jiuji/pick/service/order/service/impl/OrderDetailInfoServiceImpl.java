package com.jiuji.pick.service.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.mapper.OrderDetailInfoMapper;
import com.jiuji.pick.service.order.service.OrderDetailInfoService;
import com.jiuji.pick.service.order.vo.OrderProductVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购单详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Service
public class OrderDetailInfoServiceImpl extends ServiceImpl<OrderDetailInfoMapper, OrderDetailInfo> implements OrderDetailInfoService {

    @Override
    public List<OrderProductVO> getOrderProduct(Long orderId) {
        if (orderId == null) {
            return Collections.emptyList();
        }
        return baseMapper.getOrderProduct(orderId);
    }

    @Override
    public List<OrderDetailInfo> getOrderDetailByOrderId(Long orderId) {
        if(orderId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderDetailInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderDetailInfo::getOrderId, orderId);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public Map<Long, BigDecimal> calculateTotalDeliveryFee(List<Long> orderIdList) {
        if(CollectionUtils.isEmpty(orderIdList)) {
            return Maps.newHashMap();
        }
        List<OrderDetailInfo> orderDetailInfoList = this.list(new LambdaQueryWrapper<OrderDetailInfo>().in(OrderDetailInfo::getOrderId, orderIdList));
        if(CollectionUtils.isEmpty(orderDetailInfoList)) {
            return Maps.newHashMap();
        }
        Map<Long, List<OrderDetailInfo>> orderDetailInfoMap = orderDetailInfoList.stream().collect(Collectors.groupingBy(OrderDetailInfo::getOrderId));
        Map<Long, BigDecimal> deliveryFeeMap = Maps.newHashMap();
        for (Map.Entry<Long, List<OrderDetailInfo>> entry : orderDetailInfoMap.entrySet()) {
            BigDecimal totalDeliveryFee = new BigDecimal(0);
            for (OrderDetailInfo orderDetailInfo : entry.getValue()) {
                totalDeliveryFee = totalDeliveryFee.add(orderDetailInfo.getDeliveryFee() == null ? new BigDecimal(0) : orderDetailInfo.getDeliveryFee());
            }
            deliveryFeeMap.put(entry.getKey(), totalDeliveryFee);
        }
        return deliveryFeeMap;
    }

    @Override
    public BigDecimal calculateTotalDeliveryFee(Long orderId) {
        if(orderId == null) {
            return new BigDecimal(0);
        }
        List<OrderDetailInfo> orderDetailInfoList = this.list(new LambdaQueryWrapper<OrderDetailInfo>().eq(OrderDetailInfo::getOrderId, orderId));
        if(CollectionUtils.isEmpty(orderDetailInfoList)) {
            return new BigDecimal(0);
        }
        BigDecimal totalDeliveryFee = new BigDecimal(0);
        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            totalDeliveryFee = totalDeliveryFee.add(orderDetailInfo.getDeliveryFee() == null ? new BigDecimal(0) : orderDetailInfo.getDeliveryFee());
        }
        return totalDeliveryFee;
    }
}

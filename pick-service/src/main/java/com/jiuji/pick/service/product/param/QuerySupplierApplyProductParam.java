package com.jiuji.pick.service.product.param;

import com.jiuji.pick.common.param.BasePageParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @function:
 * @description: QuerySupplierApplyProductParam.java
 * @date: 2021/05/07
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class QuerySupplierApplyProductParam extends BasePageParam {

    private Long categoryId;
    private List<Long> categoryIdList;
    private Integer bindStatus;
    // 搜索类型，1ppid,2产品名称
    private String searchType;
    private String keyWord;
    private Long supplierId;

}

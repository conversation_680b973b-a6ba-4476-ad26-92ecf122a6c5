package com.jiuji.pick.service.product.vo;

import com.jiuji.pick.service.product.entity.ParamGroup;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2019-12-11
 */

@Data
@NoArgsConstructor
public class CategoryParamGroupVO {

    private Integer id;
    private Integer cateId;
    private String groupName;
    private Integer rank;
    private Boolean del;

    public CategoryParamGroupVO(ParamGroup paramGroup) {
        this.id = paramGroup.getId();
        this.cateId = paramGroup.getCateId();
        this.groupName = paramGroup.getGroupName();
        this.rank = paramGroup.getRank();
        this.del = paramGroup.getIsDel() == 1;
    }
}

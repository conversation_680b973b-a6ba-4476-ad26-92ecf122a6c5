package com.jiuji.pick.service.product.bo;

import com.jiuji.pick.service.product.entity.ProductImage;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author:zuofei
 * @Description
 * @Date 2019-10-21 商品后台管理sku(productprice)业务对象  这个对象返回列表和修改sku信息公用了这个对象
 */
@Data
@NoArgsConstructor
public class AdmSkuBO implements Serializable {

    private static final long serialVersionUID = 6887420244096741267L;
    /**
     * 商品id
     */
    private Long productid;
    /**
     * skuid
     */
    private Long ppriceid;
    /**
     * 商品名称
     */
    private String name;
    /**
     * 商品规格
     */
    private String productColor;

    /**
     * 以前是vip会员价，现在没有vip会员价格，,目前代表虚拟小店价
     */
    private BigDecimal vipPrice;
    /**
     * 默认图
     */
    private String bpic;

    /**
     * 显示图片
     */
    private String displayImg;
    /**
     * 默认图
     */
    private List<ProductImage> picList;
    /**
     * 全网价、普通会员价
     */
    private BigDecimal memberprice;
    /**
     * 品牌id
     */
    private Integer brandID;
    /**
     * 简单介绍
     */
    private String decription;
    /**
     * 成本价
     */
    private BigDecimal costprice;
    /**
     * 大盘价
     */
    private BigDecimal pricedp;
    /**
     * 商品的周访问量
     */
    private Integer viewsWeek;
    /**
     * 绑定出库的商品ID
     */
    private Integer ppriceid1;
    /**
     * 大小件 0--小件 1--大件
     */
    private Boolean ismobile = false;
    /**
     * 商品评论数量
     */
    private Integer commentCount;
    /**
     * 购买次数
     */
    private Integer buys;
    /**
     * 添加时间
     */
    private Date adddate;
    /**
     * sku 的周访问量
     */
    private Integer viewsWeekr;
    /**
     * 分类树
     */
    private String cidFamily;
    /**
     * 规格id字符串
     */
    private String stID;
    /**
     * 分类id
     */
    private Integer cid;
    /**
     * 0--无 1--热销 2--新品 3--特价 4--推荐 5--直降 6--爆款 7--热销标签 8--新品标签 9--限量标签 10--推荐标签 11--特惠标签 12--爆款标签
     * 13--上门 14--到货不定 15--活动机型
     */
    private Integer cues;
    /**
     * 市场价
     */
    private BigDecimal marketPrice;
    /**
     * 价格幅度
     */
    private BigDecimal pricefd;
    /**
     * 商品状态
     */
    private Integer que;
    /**
     * 限购数量
     */
    private Integer buyLimit;

    /**
     * 区域价
     */
    //private BigDecimal areaPrice;
    /**
     * 是否是锁定价
     */
    //private Boolean isLock;
    /**
     * 锁定价
     */
    //private BigDecimal lockPrice;
    /**
     * 上下架
     */
    private Boolean display;
    /**
     * 商品排序
     */
    private Integer rank1;
    /**
     * 商品短名称
     */
    private String shotName;

    /**
     * 实体小店价
     */
    private BigDecimal OEMPrice;

    /**
     * 排序字段 productIndex表里面的
     */
    private Integer sortValue;

    /**
     * 主推规格 productprice表里面的
     */
    private Boolean mainColor;

    /**
     * 卖点
     */
    private String sellingPoint;

    /**
     * 是否支持七天无理由退换
     */
    private Integer sevenDayReturn;

    /**
     * 是否是服务支持九机服务
     */
    private Boolean isServices;

    /**
     * 支持的服务类型，目前用于鉴别是否支持礼盒之类的
     */
    private Integer supportService;

    /**
     * 钻级价
     */
    private BigDecimal vip2Price;
    /**
     * sku排序值
     */
    private Integer skuRank;
    /**
     * &1>0 代表溢价商品规格  &2>0 代表不支持回购的规格
     */
    private Integer attributes;
    /**
     * 配置
     */
    private String config;
    /**
     * 特价   1-特价；0-非特价
     */
    private Boolean noPromotion;

    /**
     * 后台展示的排序值  计算出来的值   这个排序值的展示方式经常会改动，有时候不一定是计算出来的值
     */
    private Integer admShowSort;

    /**
     * 是否支持回收 逻辑处理赋值
     */
    private Boolean supportRecover;
    /**
     * 规格id
     */
    private List<Integer> specIds;
    /**
     * 开启改价排序 改价排序值
     */
    private Integer bgSortId;
    /**
     * 开启改价排序 改价排序值
     */
    private Integer bgSort;
    /**
     * 是否删除
     */
    private Boolean isdel;
    /**
     * 是否有库存状态设置
     */
    private Boolean isHaveKcState;
    /**
     * 更新会员价前的会员价--前端给定义的一个字段
     */
    private BigDecimal memberpriceCache;
    /**
     * 更新幅度前的幅度--前端给定义的一个字段
     */
    private BigDecimal pricefdCache;
    /**
     * 更新成本价前的成本价--前端给定义的一个字段
     */
    private BigDecimal costpriceCache;
    /**
     * 更新成本价前的大盘价--前端给定义的一个字段
     */
    private BigDecimal pricedpCache;
    /**
     * 更新默认图前的默认图--前端给定义的一个字段
     */
    private String bpicCache;
    /**
     * 清库
     */
    private Integer destock;
    /**
     * 小店价格手动干预
     */
    private Integer interveneShopPrice;

    /**
     * 后台配置的区域价
     */
    private BigDecimal areaPrice;
    /**
     * 区域价表的id
     */
    private Integer areaPriceId;

    /**
     * 开售时间
     */
    private String saleStartTime;
}

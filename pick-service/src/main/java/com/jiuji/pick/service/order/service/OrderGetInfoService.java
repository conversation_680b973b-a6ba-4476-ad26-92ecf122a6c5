package com.jiuji.pick.service.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.vo.BasePageVO;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.param.OrderSearchParam;
import com.jiuji.pick.service.order.vo.OrderDetailVO;
import com.jiuji.pick.service.order.vo.OrderListExportVO;
import com.jiuji.pick.service.order.vo.OrderListVO;
import com.jiuji.pick.service.order.vo.PlatformOrderListVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订单获取信息服务
 *
 * <AUTHOR>
 * @date 2021/6/10
 */
public interface OrderGetInfoService {


    /**
     * 订单导出
     * @param records
     * @param response
     * @return
     */
    Result listExport(List<OrderListExportVO> records, HttpServletResponse response);

    /**
     * 查询合作伙伴订单列表
     *
     * @param param param
     * @return
     */
    Result<Page<OrderListVO>> getPartnerOrderList(OrderSearchParam param);

    /**
     * 查询合作伙伴订单详情
     *
     * @param orderId 订单id
     * @return
     */
    Result<OrderDetailVO> getPartnerOrderDetail(Long orderId);

    /**
     * 查询供应商订单列表
     *
     * @param param param
     * @return
     */
    Result<Page<OrderListVO>> getSupplierOrderList(OrderSearchParam param);

    /**
     * 查询供应商订单列表
     *
     * @param param param
     * @return
     */
    Result<Page<OrderListExportVO>> getSupplierOrderListV2(OrderSearchParam param);

    /**
     * 查询供应商订单详情
     *
     * @param orderId orderId
     * @return
     */
    Result<OrderDetailVO> getSupplierOrderDetail(Long orderId);

    /**
     * 查询平台订单列表
     *
     * @param param param
     * @return
     */
    Result<BasePageVO<PlatformOrderListVO>> getPlatformOrderList(OrderSearchParam param);

    /**
     * 查询平台订单详情
     *
     * @param orderId orderId
     * @return
     */
    Result<OrderDetailVO> getPlatformOrderDetail(Long orderId);

}

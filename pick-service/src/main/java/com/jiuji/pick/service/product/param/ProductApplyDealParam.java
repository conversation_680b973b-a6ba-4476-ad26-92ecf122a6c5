package com.jiuji.pick.service.product.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @function:
 * @description: ProductApplyDealParam.java
 * @date: 2021/05/11
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class ProductApplyDealParam {

    /**
     * 1同意绑定，2拒绝绑定，3解绑
     */
    @NotNull(message = "操作类型不能为空")
    private Integer operateType;
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;
    @NotNull(message = "ppid不能为空")
    private Long ppid;
    private String remark;

}

package com.jiuji.pick.service.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.user.entity.Favorite;
import com.jiuji.pick.service.user.vo.FavoriteVO;

/**
 * <p>
 * 收藏夹 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
public interface FavoriteService extends IService<Favorite> {

    /**
     * 加入收藏
     * @param supplierProductId 供应商商品id
     * @return boolean
     */
    Result<Boolean> add(Integer supplierProductId);

    /**
     * 删除收藏
     * @param ids
     * @return boolean
     */
    Result<Boolean> del(String ids);

    /**
     * 我的收藏
     * @return FavoriteVO
     */
    Result<FavoriteVO> getList();

    /**
     * 合作伙伴是否收藏商品
     * @param supplierProductId supplierProductId
     * @param partnerId partnerId
     * @return boolean
     */
    Boolean isFavorite(Long supplierProductId, Long partnerId);
}

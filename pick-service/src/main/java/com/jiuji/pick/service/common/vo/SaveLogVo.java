package com.jiuji.pick.service.common.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class SaveLogVo {

   /**
     * 关联业务单号
     */
    @NotNull(message = "关联业务单号不能为空")
    private String relateId;

    /**
     * 日志类型
     */
    @NotNull(message = "日志类型不能为空")
    private Integer type;

    /**
     * 日志内容
     */
    @NotNull(message = "日志内容不能为空")
    private String content;

    /**
     * 展示类型，0不展示，1所有用户，2供应商用户，3合作伙伴
     */
    @NotNull(message = "展示类型不能为空")
    private Integer showType;


}

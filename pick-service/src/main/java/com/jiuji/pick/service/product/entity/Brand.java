package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;

/**
 * <p>
 * 品牌
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_brand")
public class Brand extends Model<Brand> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 品牌图片url
     */
    private String url;

    /**
     * 排序\等级（可重复）
     */
    @TableField("`rank`")
    private Integer rank;

    /**
     * 品牌简介
     */
    private String info;

    /**
     * 是否显示
     */
    private Boolean display;

    /**
     * 搜索关键字
     */
    private String seoKey;

    /**
     * 图片（用于APP分类）
     */
    private String picforapp;

    /**
     * 品牌官方网站地址
     */
    private String webSite;

    /**
     * 品牌官方售后服务热线
     */
    private String tel;

    /**
     * 商品排序权重
     */
    private Integer productSortValue;

    /**
     * 品牌短名称
     */
    private String shortName;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Brand brand = (Brand) o;

        return new EqualsBuilder()
                .append(id, brand.id)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(id)
                .toHashCode();
    }
}

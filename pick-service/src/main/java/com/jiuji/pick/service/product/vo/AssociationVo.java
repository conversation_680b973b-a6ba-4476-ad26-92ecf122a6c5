package com.jiuji.pick.service.product.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AssociationVo {

    private Long id;

    /**
     * 关联标识
     */
    private Integer associationId;

    @NotNull(message = "商品id不能为空")
    private Integer productId;
    /**
     * 排序值
     */
    @NotNull(message = "排序值不能为空")
    private Integer showSort;

    /**
     * 商品名称
     */
    @NotNull(message = "商品名称不能为空")
    @Length(max = 200,message = "商品名称不能超100字")
    private String productName;

    /**
     * 版本名称
     */
    @NotNull(message = "版本名称不能为空")
    @Length(max = 200,message = "版本名称不能超100字")
    private String edition;


}

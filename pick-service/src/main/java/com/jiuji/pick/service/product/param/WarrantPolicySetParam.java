package com.jiuji.pick.service.product.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description 质保政策设置请求参数
 * <AUTHOR>
 * @Date 2021/11/10
 */
@Data
public class WarrantPolicySetParam {

    /**
     * 质保政策标题
     */
    @NotNull(message = "质保政策标题不能为空")
    private String title;

    /**
     * 质保政策内容
     */
    @NotNull(message = "质保政策内容不能为空")
    private String content;

    /**
     * 质保政策类型 ProductTypeEnum
     */
    @NotNull(message = "质保政策类型不能为空")
    private Integer type;

}

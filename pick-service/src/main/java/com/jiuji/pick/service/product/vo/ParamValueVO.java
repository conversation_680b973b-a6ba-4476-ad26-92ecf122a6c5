package com.jiuji.pick.service.product.vo;

import com.jiuji.pick.service.product.entity.ParamValue;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @since 2019-12-12 15:35
 */
@Data
@NoArgsConstructor
public class ParamValueVO implements Serializable {

    private static final long serialVersionUID = 1630749382127565886L;
    private Integer id;
    private Integer paramId;
    private String value;
    private String valDes;
    private Integer rank;

    public ParamValueVO(ParamValue paramValue) {
        this.id = paramValue.getId();
        this.rank = paramValue.getRank();
        this.valDes = paramValue.getValdes();
        this.paramId = paramValue.getParamId();
        this.value = paramValue.getValue();
    }

    public static ParamValue toSuper(ParamValueVO paramValueVO) {
        ParamValue paramValue = new ParamValue();
        paramValue.setId(paramValueVO.id);
        paramValue.setValue(paramValueVO.value);
        paramValue.setValdes(paramValueVO.getValDes());
        paramValue.setRank(paramValueVO.getRank());
        paramValue.setParamId(paramValueVO.getParamId());
        return paramValue;
    }
}

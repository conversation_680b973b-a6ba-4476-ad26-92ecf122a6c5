package com.jiuji.pick.service.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.enums.OrderPriceTypeEnum;
import com.jiuji.pick.common.enums.OrderStatusEnum;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.entity.ProductOrderVersion;
import com.jiuji.pick.service.order.mapper.OrderInfoMapper;
import com.jiuji.pick.service.order.param.AutomaticPickupOrder;
import com.jiuji.pick.service.order.param.OrderSearchParam;
import com.jiuji.pick.service.order.service.OrderDetailInfoService;
import com.jiuji.pick.service.order.service.OrderInfoService;
import com.jiuji.pick.service.order.vo.*;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.service.CategoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购定单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Service
public class OrderInfoServiceImpl extends ServiceImpl<OrderInfoMapper, OrderInfo> implements OrderInfoService {

    /**
     * 合作伙伴订单
     */
    private static final int PARTNER_ORDER_TYPE = 1;
    /**
     * 供应商订单
     */
    private static final int SUPPLIER_ORDER_TYPE = 2;

    @Resource
    private OrderDetailInfoService orderDetailInfoService;
    @Resource
    private CategoryService categoryService;

    @Override
    public List<AutomaticPickupOrder> selectAutomaticPickupOrder() {
        return  this.baseMapper.selectAutomaticPickupOrder();
    }

    @Override
    public Page<OrderListVO> getPartnerOrderList(OrderSearchParam param) {
        if (param.getPartnerId() == null) {
            return null;
        }
        if(StringUtils.isBlank(param.getKeyValue())) {
            param.setKeyType(null);
        }
        if (param.getCurrent() == null || param.getCurrent() < 1) {
            param.setCurrent(1);
        }
        if (param.getSize() == null || param.getSize() > MagicalValueConstant.INT_50) {
            param.setSize(MagicalValueConstant.INT_50);
        }
        Page<OrderListVO> orderListVOListPage = baseMapper.getOrderList(new Page<>(param.getCurrent(), param.getSize()),PARTNER_ORDER_TYPE, param.getPartnerId(), null, param);
        if(orderListVOListPage == null || CollectionUtils.isEmpty(orderListVOListPage.getRecords())) {
            return null;
        }
        // 计算总的物流费
        List<Long> orderIdList = orderListVOListPage.getRecords().stream().map(OrderListVO::getOrderId).collect(Collectors.toList());
        Map<Long, BigDecimal> deliveryFeeMap = orderDetailInfoService.calculateTotalDeliveryFee(orderIdList);
        if(MapUtils.isEmpty(deliveryFeeMap)) {
            return orderListVOListPage;
        }
        orderListVOListPage.getRecords().forEach(orderListVO -> orderListVO.setTotalDeliveryFee(deliveryFeeMap.get(orderListVO.getOrderId())));
        return orderListVOListPage;
    }

    @Override
    public OrderDetailVO getPartnerOrderDetail(Long orderId, Long partnerId) {
        if (orderId == null || partnerId == null) {
            return null;
        }
        return baseMapper.getOrderDetail(PARTNER_ORDER_TYPE, partnerId, null, orderId);
    }

    @Override
    public Page<OrderListVO> getSupplierOrderList(OrderSearchParam param) {
        if (param.getSupplierId() == null) {
            return null;
        }
        if(StringUtils.isBlank(param.getKeyValue())) {
            param.setKeyType(null);
        }
        if (param.getCurrent() == null || param.getCurrent() < 1) {
            param.setCurrent(1);
        }
        if (param.getSize() == null || param.getSize() > MagicalValueConstant.INT_50) {
            param.setSize(MagicalValueConstant.INT_50);
        }
        Page<OrderListVO> orderListVOListPage = baseMapper.getOrderList(new Page<>(param.getCurrent(), param.getSize()), SUPPLIER_ORDER_TYPE, null, param.getSupplierId(), param);
        if(orderListVOListPage == null || CollectionUtils.isEmpty(orderListVOListPage.getRecords())) {
            return null;
        }
        // 计算总的物流费
        List<Long> orderIdList = orderListVOListPage.getRecords().stream().map(OrderListVO::getOrderId).collect(Collectors.toList());
        Map<Long, BigDecimal> deliveryFeeMap = orderDetailInfoService.calculateTotalDeliveryFee(orderIdList);
        if(MapUtils.isEmpty(deliveryFeeMap)) {
            return orderListVOListPage;
        }
        orderListVOListPage.getRecords().forEach(orderListVO ->{
            orderListVO.setTotalDeliveryFee(deliveryFeeMap.get(orderListVO.getOrderId()));
            orderListVO.setPriceTypeValue(OrderPriceTypeEnum.getDescByCode(orderListVO.getPriceType()));
        });
        return orderListVOListPage;
    }

    @Override
    public Page<OrderListExportVO> getSupplierOrderListV2(OrderSearchParam param) {
        if (param.getSupplierId() == null) {
            return null;
        }
        if(StringUtils.isBlank(param.getKeyValue())) {
            param.setKeyType(null);
        }
        if (param.getCurrent() == null || param.getCurrent() < 1) {
            param.setCurrent(1);
        }
        Page<OrderListExportVO> orderListVOListPage = baseMapper.getOrderListV2(new Page<>(param.getCurrent(), param.getSize()), SUPPLIER_ORDER_TYPE, null, param.getSupplierId(), param);
        if(orderListVOListPage == null || CollectionUtils.isEmpty(orderListVOListPage.getRecords())) {
            return null;
        }
        return orderListVOListPage;
    }

    @Override
    public OrderDetailVO getSupplierOrderDetail(Long orderId, Long supplierId) {
        if (orderId == null || supplierId == null) {
            return null;
        }
        return baseMapper.getOrderDetail(SUPPLIER_ORDER_TYPE, null, supplierId, orderId);
    }

    @Override
    public int countPlatformOrder(OrderSearchParam param) {
        if (StringUtils.isBlank(param.getKeyValue())) {
            param.setKeyType(null);
        }
        return baseMapper.countPlatformOrderList(param.getOrderStatus(), param.getKeyType(), param.getKeyValue(),
                param.getStartTime(), param.getEndTime(), param.getOrderType());
    }

    @Override
    public List<PlatformOrderListVO> getPlatformOrderList(OrderSearchParam param) {
        if (param.getCurrent() == null || param.getCurrent() < 1) {
            param.setCurrent(1);
        }
        if (param.getSize() == null || param.getSize() > MagicalValueConstant.INT_50) {
            param.setSize(MagicalValueConstant.INT_50);
        }
        if (StringUtils.isBlank(param.getKeyValue())) {
            param.setKeyType(null);
        }
        return baseMapper.getPlatformOrderList(param.getOrderStatus(), param.getKeyType(), param.getKeyValue(),
                param.getStartTime(), param.getEndTime(), param.getOrderType(),
                (param.getCurrent() - 1) * param.getSize(), param.getSize(), true);
    }

    @Override
    public OrderDetailVO getPlatformOrderDetail(Long orderId) {
        if (orderId == null) {
            return null;
        }
        return baseMapper.getOrderDetail(0, null, null, orderId);
    }

    @Override
    public List<OrderListExportVO> getExportPlatformOrderList(OrderSearchParam param) {
        return baseMapper.getPlatformOrderExportList(param.getOrderStatus(), param.getKeyType(), param.getKeyValue(),
                param.getStartTime(), param.getEndTime(), param.getOrderType(),
                null, null, false);
    }

    @Override
    public Boolean hasCompleteOrder4Product(Long partnerId, Long productId) {
        if(partnerId == null || productId == null) {
            return Boolean.FALSE;
        }
        List<OrderDetailInfo> orderDetailInfoList = orderDetailInfoService.list(new LambdaQueryWrapper<OrderDetailInfo>().eq(OrderDetailInfo::getPartnerId, partnerId).eq(OrderDetailInfo::getProductId, productId));
        if(CollectionUtils.isEmpty(orderDetailInfoList)) {
            return Boolean.FALSE;
        }
        List<Long> orderNoList= orderDetailInfoList.stream().map(OrderDetailInfo::getOrderNo).distinct().collect(Collectors.toList());
        List<OrderInfo> orderInfoList = this.list(new LambdaQueryWrapper<OrderInfo>().in(OrderInfo::getOrderNo, orderNoList));
        if(CollectionUtils.isEmpty(orderInfoList)) {
            return Boolean.FALSE;
        }
        Optional<OrderInfo> completeOrderOptional = orderInfoList.stream().filter(orderInfo -> orderInfo.getOrderStatus() != null && orderInfo.getOrderStatus().intValue() == OrderStatusEnum.COMPLETED.getCode()).findAny();
        if(completeOrderOptional.isPresent()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public List<Long> getReceiveOrderList() {
        return baseMapper.getReceiveOrderList();
    }

    @Override
    public <T> T createSaveInfo(CartInfoVO.CartProduct cartProduct, PartnerTokenInfo partnerTokenInfo, Long supplierId, BigDecimal price, Integer priceType, Class<T> tClass) {
        // 创建采购单详情
        if (price != null) {
            OrderDetailInfo orderDetailInfo = new OrderDetailInfo();
            orderDetailInfo.setBuyAmount(cartProduct.getProductCount());
            orderDetailInfo.setPartnerId(partnerTokenInfo.getId());
            orderDetailInfo.setProductId(cartProduct.getProductId());
            orderDetailInfo.setProductDetailId(cartProduct.getProductDetailId());
            orderDetailInfo.setProductPrice(price);
            orderDetailInfo.setSupplierId(supplierId);
            orderDetailInfo.setCartId(cartProduct.getId());
            return tClass.cast(orderDetailInfo);
        }
        // 创建商品快照
        if (priceType != null) {
            ProductOrderVersion productOrderVersion = new ProductOrderVersion();
            productOrderVersion.setBuyNoTaxPrice(cartProduct.getBuyNoTaxPrice());
            productOrderVersion.setBuyTaxPrice(cartProduct.getBuyTaxPrice());
            productOrderVersion.setPartnerId(partnerTokenInfo.getId());
            productOrderVersion.setPpid(cartProduct.getPpid());
            productOrderVersion.setPriceType(priceType);
            productOrderVersion.setProductColor(cartProduct.getProductColor());
            productOrderVersion.setProductName(cartProduct.getProductName());
            productOrderVersion.setProductDetailId(cartProduct.getProductDetailId());
            productOrderVersion.setProductId(cartProduct.getProductId());
            productOrderVersion.setProductImg(cartProduct.getProductImage());
            productOrderVersion.setProductCid(cartProduct.getProductCid());
            productOrderVersion.setProductBarCode(cartProduct.getProductBarCode());
            productOrderVersion.setSupplierUserId(supplierId);
            // 查询分类名称
            if(cartProduct.getProductCid() != null) {
                Category category = categoryService.getOneById(cartProduct.getProductCid().intValue());
                if(category != null) {
                    productOrderVersion.setProductCidName(category.getName());
                }
            }
            return tClass.cast(productOrderVersion);
        }
        return null;
    }
}

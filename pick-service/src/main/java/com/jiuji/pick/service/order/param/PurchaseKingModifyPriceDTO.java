package com.jiuji.pick.service.order.param;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PurchaseKingModifyPriceDTO {


    /**
     * 采购单id
     */
    private Long purchaseId;

    /**
     * 商品调价信息
     */
    private List<GoodsModifyPriceInfo> goodsModifyPriceInfoList;

    @Data
    public static class GoodsModifyPriceInfo {

        /**
         * 规格id
         */
        private Long ppId;

        /**
         * 调整后的价格
         */
        private BigDecimal price;
    }
}

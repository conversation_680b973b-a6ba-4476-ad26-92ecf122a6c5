package com.jiuji.pick.service.order.service.impl;

import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.enums.OrderTypeEnum;
import com.jiuji.pick.service.order.service.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @function:
 * @description: NeoOrderServiceFactory.java
 * @date: 2021/10/13
 * @author: sunfayun
 * @version: 1.0
 */
@Component
public class NeoOrderServiceFactory extends AbstractOrderFactory {

    @Resource(name = "NeoOrderServiceImpl")
    private NeoOrderService neoOrderService;
    @Resource
    private SmallService smallService;
    @Resource(name = "NeoAssetsOrderServiceImpl")
    private NeoAssetsOrderService neoAssetsOrderService;
    @Resource
    private NeoVirtualOrderService neoVirtualOrderService;

    @Override
    public OrderCommonService getOrderService(Integer orderType) {
        List<Integer> assetsList = Arrays.asList(OrderTypeEnum.COMMON_ASSETS.getCode(), OrderTypeEnum.FIX_ASSETS.getCode());
        if(assetsList.contains(orderType)){
            return neoAssetsOrderService;
        }
        if(OrderTypeEnum.VIRTUAL.getCode()==orderType){
            return neoVirtualOrderService;
        }
        return neoOrderService;
    }

    /**
     * 小型的采购单跳转连接的拼接
     * @param tokenInfo
     * @return
     */
    @Override
    public String getJumpUrl(PartnerTokenInfo tokenInfo,Long orderNumber) {
        return smallService.getJumpUrl(tokenInfo,orderNumber);
    }

}

package com.jiuji.pick.service.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.utils.DateUtil;
import com.jiuji.pick.service.product.entity.SupplierProductPriceModify;
import com.jiuji.pick.service.product.mapper.SupplierProductPriceModifyMapper;
import com.jiuji.pick.service.product.service.SupplierProductPriceModifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 *
 * <AUTHOR>
 * @Date 2021/8/23
 */
@Slf4j
@Service
public class SupplierProductPriceModifyServiceImpl extends ServiceImpl<SupplierProductPriceModifyMapper, SupplierProductPriceModify> implements SupplierProductPriceModifyService {

    @Override
    public SupplierProductPriceModify getSupplierProductPriceModify(Long supplierProductDetailId, Boolean queryTime, Boolean enable) {
        LambdaQueryWrapper<SupplierProductPriceModify> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SupplierProductPriceModify::getSupplierProductDetailId, supplierProductDetailId);
        if (Boolean.TRUE.equals(queryTime)) {
            lambdaQueryWrapper.between(SupplierProductPriceModify::getUpdateTime,
                    LocalDateTime.now().minusDays(30L).format(DateTimeFormatter.ofPattern(DateUtil.ZH_CN_DATETIME_PATTERN)),
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.ZH_CN_DATETIME_PATTERN)));
        }
        if (Boolean.TRUE.equals(enable)) {
            lambdaQueryWrapper.eq(SupplierProductPriceModify::getEnabled, true);
        } else {
            lambdaQueryWrapper.eq(SupplierProductPriceModify::getEnabled, false);
        }
        lambdaQueryWrapper.orderByDesc(SupplierProductPriceModify::getUpdateTime).last("limit 1");
        return this.getOne(lambdaQueryWrapper);
    }
}

package com.jiuji.pick.service.common.service.impl;

import cn.hutool.json.JSONUtil;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.service.LogService;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.product.param.ProductUpOrDownParam;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Service("GoodsOnAndOffShelvesServiceImpl")
public class GoodsOnAndOffShelvesServiceImpl implements LogService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private NewOperateLogInfoService logInfoService;

    /**
     * 该日志记录的时候 前端要过滤上架的物品然后修改成上架的商品
     * @param param
     */
    @Override
    public void systemSaveLog(Object param) {
        OATokenInfo oaTokenInfo = Optional.ofNullable(currentRequestComponent.getOATokenInfoBoWithoutCheck())
                .orElse(new OATokenInfo());
        CompletableFuture.runAsync(() -> {
            ProductUpOrDownParam productUpOrDownParam = JSONUtil.toBean(JSONUtil.toJsonStr(param), ProductUpOrDownParam.class);
            String newState = productUpOrDownParam.getType() == 1 ? "上架" : "下架";
            String oldState = productUpOrDownParam.getType() == 1 ? "下架" : "上架";
            String connent = "商品由【" + oldState + "】修改为【" + newState + "】。";
            List<Long> ppidList = productUpOrDownParam.getPpidList();
            ArrayList<NewOperateLogInfo> newOperateLogInfos = new ArrayList<>();
            Integer userId = oaTokenInfo.getUserId();
            String name = oaTokenInfo.getName();
            ppidList.forEach((Long item) -> {
                NewOperateLogInfo newOperateLogInfo = new NewOperateLogInfo();
                newOperateLogInfo.setContent(connent)
                        .setRelateId(item+"")
                        .setOptUserId(userId.longValue())
                        .setOptUserName(name)
                        .setType(NewOperateLogInfoTypeEnum.GOODS_ON_AND_OFF_SHELVES.getCode())
                        .setCreateTime(LocalDateTime.now())
                        .setShowType(LogShowTypeEnum.ADMIN.getCode());
                newOperateLogInfos.add(newOperateLogInfo);
            });
            logInfoService.saveBatch(newOperateLogInfos);

        });
    }
}

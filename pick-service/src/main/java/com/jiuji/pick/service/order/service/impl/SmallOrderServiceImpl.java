package com.jiuji.pick.service.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.constant.OrderTipConstant;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.HostManageUtil;
import com.jiuji.pick.common.utils.HttpClientUtils;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.DeliveryFeeArea;
import com.jiuji.pick.service.common.entity.MessageInfo;
import com.jiuji.pick.service.common.entity.MessageInfoUser;
import com.jiuji.pick.service.common.service.DeliveryFeeAreaService;
import com.jiuji.pick.service.common.service.MessageInfoService;
import com.jiuji.pick.service.common.service.MessageInfoUserService;
import com.jiuji.pick.service.order.bo.DeliveryProductBO;
import com.jiuji.pick.service.order.bo.OrderDeliveryBO;
import com.jiuji.pick.service.order.bo.OrderReceiveBO;
import com.jiuji.pick.service.order.dto.OaOrderDTO;
import com.jiuji.pick.service.order.dto.OaOrderResultDTO;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.entity.OrderInfoLog;
import com.jiuji.pick.service.order.entity.ProductOrderVersion;
import com.jiuji.pick.service.order.param.CartOrderParam;
import com.jiuji.pick.service.order.param.OrderDeliveryParam;
import com.jiuji.pick.service.order.service.*;
import com.jiuji.pick.service.order.vo.CartInfoVO;
import com.jiuji.pick.service.order.vo.ProductInfoReq;
import com.jiuji.pick.service.order.vo.UpdateOrderPriceVo;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.service.CategoryService;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.mapper.PartnerUserMapper;
import com.jiuji.pick.service.user.service.SupplierChannelService;
import com.jiuji.tc.common.vo.R;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 小件订单业务处理
 *
 * @function:
 * @description: SmallOrderServiceImpl.java
 * @date: 2021/10/09
 * @author: sunfayun
 * @version: 1.0
 */
@Service
@Slf4j
public class SmallOrderServiceImpl implements SmallOrderService {

    /**
     * 默认发货时间
     */
    private static final int DEFAULT_DELIVERY_DAY = 7;

    private static final Long NOT_EXIST_TYPE = -1L;


    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 商品备注
     */
    private static final String PRODUCT_COMMENT = "采货王商品";

    private static final String TOKEN = "token";

    @Resource
    private PartnerUserMapper partnerUserMapper;
    @Resource
    private CartInfoService cartInfoService;
    @Resource
    private SupplierChannelService supplierChannelService;
    @Resource
    private ProductOrderVersionService productOrderVersionService;
    @Resource
    private OrderDetailInfoService orderDetailInfoService;
    @Resource
    private OaService oaService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private DeliveryFeeAreaService deliveryFeeAreaService;
    @Resource
    private OrderInfoLogService orderInfoLogService;
    @Resource
    private OrderInfoService orderInfoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private MessageInfoService messageInfoService;
    @Resource
    private MessageInfoUserService messageInfoUserService;

    /**
     * 订单改价之前的校验
     *
     * @param updateOrderPriceVoList
     */
    private void checkOrder(UpdateOrderPriceVo updateOrderPriceVoList) {
        Long orderId = Optional.ofNullable(updateOrderPriceVoList.getOrderId()).orElseThrow(() -> new BizException("订单id不能为空"));
        OrderInfo orderInfo = Optional.ofNullable(orderInfoService.getById(orderId)).orElseThrow(() -> new BizException("订单" + orderId + "无效"));
        Integer channelType = orderInfo.getChannelType();
        if(channelType==1){
            throw new BizException("该采购单为小型的小件采购单，暂不持支改价功能");
        }
        Integer orderType = orderInfo.getOrderType();
        Long orderNo = orderInfo.getOrderNo();
        updateOrderPriceVoList.setOrderNo(orderNo);
        if (OrderTypeEnum.SMALL.getCode() != orderType) {
            throw new BizException("订单" + orderNo + "不属于小件类型订单，暂不支持改价功能");
        }
        //判断订单类型是不是小件
        Integer orderStatus = orderInfo.getOrderStatus();
        List<Integer> status = Arrays.asList(OrderStatusEnum.DEFAULT.getCode(), OrderStatusEnum.AUDITED.getCode());
        if (!status.contains(orderStatus)) {
            throw new BizException("订单" + orderNo + "状态不支持改价功能，（取消，已发货，完成状态下不持支该改价）");
        }
        List<UpdateOrderPriceVo.UpdateOrderPriceDetailVo> detailVoList = updateOrderPriceVoList.getDetailVoList();
        if (CollectionUtils.isEmpty(detailVoList)) {
            throw new BizException("改价详情不能为空");
        }

        productOrderVersionService.checkOrderUpdatePrice(updateOrderPriceVoList);

    }


    /**
     * 小件订单改价
     *
     * @param updateOrderPriceVoList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPrice(UpdateOrderPriceVo updateOrderPriceVoList) {
        //订单校验
        checkOrder(updateOrderPriceVoList);
        StringBuilder comment = new StringBuilder();
        OrderInfo orderInfo = orderInfoService.getById(updateOrderPriceVoList.getOrderId());
        BigDecimal totalPriceOld = orderInfo.getTotalPrice();
        BigDecimal totalPriceNew = new BigDecimal("0.00");
        List<UpdateOrderPriceVo.UpdateOrderPriceDetailVo> detailVoList = updateOrderPriceVoList.getDetailVoList();

        for (UpdateOrderPriceVo.UpdateOrderPriceDetailVo item : detailVoList) {
            BigDecimal productNewPrice = item.getProductNewPrice();
            BigDecimal productOldPrice = item.getProductOldPrice();
            BigDecimal multiply = productNewPrice.multiply(new BigDecimal(item.getBuyAmount() + ""));
            totalPriceNew=totalPriceNew.add(multiply);
            //OrderDetailInfo表修改
            orderDetailInfoService.lambdaUpdate().eq(OrderDetailInfo::getId, item.getOrderDetailInfoId())
                    .set(OrderDetailInfo::getProductPrice, productNewPrice).update();

            //拼接修改详情日志
            if(productOldPrice.compareTo(productNewPrice)!=0){
                comment.append(item.getProductName()).append("价格由【").append(productOldPrice).append("】改为【").append(productNewPrice).append("】。");
            }

        }
        //OrderInfo表修改
        orderInfoService.lambdaUpdate().eq(OrderInfo::getId, orderInfo.getId())
                .eq(OrderInfo::getTotalPrice, totalPriceOld)
                .set(OrderInfo::getTotalPrice, totalPriceNew)
                .update();
        //拼接修改总价日志
        if(totalPriceOld.compareTo(totalPriceNew)!=0){
            comment.append("订单总价由【").append(totalPriceOld).append("】修改为【").append(totalPriceNew).append("】。");
        }
        //改价同步
        Long xtenantId = orderInfo.getXtenantId();
        String host = HostManageUtil.getHost(xtenantId + "");
        String url = host + "/cloudapi_nc/oa-stock/api/chw/updatePrice/v1?xservicename=oa-stock";
        //        //接口调用加密处理
        LocalDate localDate = LocalDateTime.now().toLocalDate();
        String ciphertext = DigestUtils.md5Hex(localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        HashMap<String, String> map = new HashMap<>(1);
        map.put(TOKEN, ciphertext);
        String res;
        try {
            res = HttpClientUtils.postJson(url, JSON.toJSONString(updateOrderPriceVoList), map);
        } catch (Exception e) {
            log.error("查询租户大小件异常{}",e.getMessage(),e);
            throw new BizException("查询租户大小件异常");
        }
        if(StringUtils.isEmpty(res)){
            throw new BizException("查询租户大小件数据为空");
        }
        R r = JSONUtil.toBean(JSONUtil.toJsonStr(res), R.class);
        int code = r.getCode();
        if(code!=0){
            throw new BizException("查询租户大小件异常"+r.getUserMsg());
        }
        // 保存日志
        SupplierTokenInfo tokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck())
                .orElseThrow(() -> new BizException("登录信息失效，请重新登录"));
        orderInfoLogService.saveOrderLog(comment.toString(), OrderLogTypeEnum.OTHER, orderInfo.getId(),
                tokenInfo.getId(), tokenInfo.getLoginName());
    }
    @Override
    public String createOrder(CartInfoVO.CartInfoData cartInfoData, PartnerTokenInfo partnerTokenInfo, CartOrderParam cartOrderParam) {
        List<CartInfoVO.CartInfoData> cartInfoDatas = new ArrayList<>();
        CartInfoVO.CartInfoData smallCartInfo = new CartInfoVO.CartInfoData();
        smallCartInfo.setOrderFlag(Convert.toInt(Boolean.FALSE));
        CartInfoVO.CartInfoData bulkyCartInfo = new CartInfoVO.CartInfoData();
        bulkyCartInfo.setOrderFlag(Convert.toInt(Boolean.TRUE));
        BeanUtil.copyProperties(cartInfoData,smallCartInfo);
        BeanUtil.copyProperties(cartInfoData,bulkyCartInfo);

        //根据租户大小件拆分 cartInfoData
        List<CartInfoVO.CartProduct> smallCartInfoDatas = new ArrayList<>(Collections.emptyList());
        List<CartInfoVO.CartProduct> bulkyCartInfoDatas = new ArrayList<>(Collections.emptyList());

        List<CartInfoVO.CartProduct> productList = cartInfoData.getProductList();
        for (CartInfoVO.CartProduct x : productList) {
            ProductInfoReq req = new ProductInfoReq();
            req.setPpid(x.getPpid());
            Integer result = this.queryIsMobile(req,partnerTokenInfo.getXtenant());
            if (ProductTypeEnum.SMALL.getCode() == result){
                smallCartInfoDatas.add(x);
            }else if (ProductTypeEnum.BULKY.getCode() == result){
                bulkyCartInfoDatas.add(x);
            }
        }
        smallCartInfo.setProductList(smallCartInfoDatas);
        bulkyCartInfo.setProductList(bulkyCartInfoDatas);
        if (CollectionUtils.isNotEmpty(smallCartInfoDatas)){
            cartInfoDatas.add(smallCartInfo);
        }
        if (CollectionUtils.isNotEmpty(bulkyCartInfoDatas)) {
            cartInfoDatas.add(bulkyCartInfo);
        }
        StringBuilder result = new StringBuilder(Strings.EMPTY);
        for (CartInfoVO.CartInfoData ci : cartInfoDatas) {
            String singleOrder = createSingleOrder(ci, partnerTokenInfo, cartOrderParam);
            if (StringUtils.isNotEmpty(singleOrder)){
                result.append(singleOrder).append(";");
            }
        }
        return result.toString();
    }

    /**
     * 查询租户大小件
     * @param req
     * @param xtenantId
     * @return
     */
    private Integer queryIsMobile(ProductInfoReq req,Long xtenantId) {
        //查询租户大小件
        String host = HostManageUtil.getHost(xtenantId + "");
        String url = host + "/cloudapi_nc/oa-stock/api/product-info/isMobile?xservicename=oa-stock";
        //接口调用加密处理
        LocalDate localDate = LocalDateTime.now().toLocalDate();
        String ciphertext = DigestUtils.md5Hex(localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        HashMap<String, String> map = new HashMap<>(1);
        map.put(TOKEN, ciphertext);
        String res;
        try {
            res = HttpClientUtils.postJson(url, JSON.toJSONString(req), map);
        } catch (Exception e) {
            log.error("查询租户大小件异常{}",e.getMessage(),e);
            throw new BizException("查询租户大小件异常");
        }
        if(StringUtils.isEmpty(res)){
            throw new BizException("查询租户大小件数据为空");
        }
        R<Integer> r = JSONUtil.toBean(JSONUtil.toJsonStr(res), R.class);
        int code = r.getCode();
        if(code!=0){
            throw new BizException("查询租户大小件异常"+r.getUserMsg());
        }
        return r.getData();
    }


    public String createSingleOrder(CartInfoVO.CartInfoData cartInfoData, PartnerTokenInfo partnerTokenInfo, CartOrderParam cartOrderParam) {
        Long supplierId = cartInfoData.getSupplierId();
        // 获取渠道商id
        String channelId = supplierChannelService.getChannelIdV2(partnerTokenInfo.getXtenant(), supplierId, cartOrderParam.getAddressId(), cartOrderParam.getAuthId());
        if (StringUtils.isBlank(channelId)) {
            return cartInfoData.getSupplierName() + " 渠道id获取失败!";
        }

        // oa采购单
        OaOrderDTO oaOrderDTO = createOaOrderDTO(channelId, cartOrderParam, partnerTokenInfo, cartInfoData);

        // 采购单商品信息
        List<OaOrderDTO.PurchaseGoods> purchaseGoodsList = new ArrayList<>();
        // 交易快照
        List<ProductOrderVersion> productOrderVersionList = new ArrayList<>();
        // 订单详情
        List<OrderDetailInfo> orderDetailInfoList = new ArrayList<>();
        // 购物车id集合
        List<Long> cartIdList = new ArrayList<>();
        // 总价
        BigDecimal totalPrice = BigDecimal.ZERO;

        DeliveryFeeArea deliveryFeeArea = deliveryFeeAreaService.getFeeArea(cartOrderParam.getCityId());

        // 大小件
        boolean isBulky = false;

        List<CartInfoVO.CartProduct> productList = cartInfoData.getProductList();
        for (CartInfoVO.CartProduct cartProduct : productList) {
            Boolean isMobile = cartProduct.getIsMobile();
            if (Boolean.TRUE.equals(isMobile)) {
                isBulky = true;
            }
            // 使用的价格
            Integer priceType = cartProduct.getPriceType();
            BigDecimal price = Integer.valueOf(1).equals(priceType) ? Optional.ofNullable(cartProduct.getBuyTaxPrice()).orElse(BigDecimal.ZERO) : Optional.ofNullable(cartProduct.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO);

            // 采购单商品
            OaOrderDTO.PurchaseGoods purchaseGoods = createPurchaseGoods(cartProduct, price);
            // add
            purchaseGoodsList.add(purchaseGoods);

            // 交易快照
            ProductOrderVersion productOrderVersion =
                    createProductOrderVersion(cartProduct, priceType, supplierId, partnerTokenInfo);
            // add
            productOrderVersionList.add(productOrderVersion);

            // 订单详情
            OrderDetailInfo orderDetailInfo = createOrderDetailInfo(cartProduct, partnerTokenInfo, price, supplierId);
            // 物流费计算
            if (cartProduct.getRemoteDeliveryFee() == 1 && deliveryFeeArea != null) {
                BigDecimal deliveryFee = deliveryFeeArea.getDeliveryFee().multiply(BigDecimal.valueOf(cartProduct.getProductCount()));
                orderDetailInfo.setDeliveryFee(deliveryFee);
                totalPrice = totalPrice.add(deliveryFee);
            }

            // add
            orderDetailInfoList.add(orderDetailInfo);

            // 购物车id
            cartIdList.add(cartProduct.getId());

            // 总价
            totalPrice = totalPrice.add(price.multiply(BigDecimal.valueOf(cartProduct.getProductCount())));
        }

        // 大小件
        oaOrderDTO.setIsBulky(isBulky);

        // 加入oa采购单商品列表
        oaOrderDTO.setList(purchaseGoodsList);

        // 订单信息
        OrderInfo orderInfo = createOrderInfo(cartInfoData.getProductType(), partnerTokenInfo, channelId, cartOrderParam, supplierId, oaOrderDTO, totalPrice);

        // 保存订单信息
        boolean saveOrderInfo = saveOrderInfo(orderInfo, orderDetailInfoList, productOrderVersionList, oaOrderDTO);
        if (!saveOrderInfo) {
            return OrderTipConstant.SAVE_ERROR;
        }
        // 保存日志
        orderInfoLogService.saveOrderLog("订单已创建，等待生成采购单。", OrderLogTypeEnum.CREATE, orderInfo.getId(),
                partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());

        // 保存并生成oa采购单
        return saveOaOrder(oaOrderDTO, partnerTokenInfo, orderInfo, cartInfoData,
                productOrderVersionList, orderDetailInfoList, cartIdList);
    }

    @Override
    public Result<String> cancelOrder(Long orderNo, Long xTenant) {
        OaOrderResultDTO oaOrderResultDTO = oaService.cancelOrder(orderNo, xTenant, OrderTypeEnum.SMALL.getCode());
        if (oaOrderResultDTO == null) {
            return Result.error("oa取消订单失败,返回消息为空");
        }
        if (!Integer.valueOf(0).equals(oaOrderResultDTO.getCode())) {
            return Result.error("oa取消订单失败,返回消息:" + oaOrderResultDTO.getUserMsg());
        }
        return Result.success();
    }

    @Override
    public Result<String> deliveryProduct(List<DeliveryProductBO> deliveryProductBOList, OrderInfo orderInfo, OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo) {
        OrderDeliveryBO orderDeliveryBO = this.buildOrderDeliveryBO(deliveryProductBOList, orderInfo, param, supplierTokenInfo);
        OaOrderResultDTO oaOrderResultDTO = oaService.orderDeliveryForProduct(orderInfo, orderDeliveryBO, OrderTypeEnum.SMALL.getCode());
        if (oaOrderResultDTO == null) {

            return Result.error("分批发货同步OA失败,请重试或联系管理员");
        }
        if (!Integer.valueOf(0).equals(oaOrderResultDTO.getCode())) {
            return Result.error("分批发货同步OA失败,错误消息:" + oaOrderResultDTO.getUserMsg());
        }
        return Result.success();
    }

    /**
     * 创建oa采购单
     *
     * @param channelId
     * @param cartOrderParam
     * @param partnerTokenInfo
     * @param cartInfoData
     * @return
     */
    private static OaOrderDTO createOaOrderDTO(String channelId, CartOrderParam cartOrderParam, PartnerTokenInfo partnerTokenInfo, CartInfoVO.CartInfoData cartInfoData) {
        String kinds = "pj";
        Integer productType = cartInfoData.getProductType();
        if (Integer.valueOf(ProductTypeEnum.REPAIR_PART.getCode()).equals(productType)) {
            kinds = "wx";
        }
        OaOrderDTO oaOrderDTO = new OaOrderDTO();
        oaOrderDTO.setKinds(kinds);
        oaOrderDTO.setInSourceId(channelId);
        oaOrderDTO.setCompanyAreaId(Integer.valueOf(cartOrderParam.getAddressId()));
        oaOrderDTO.setRemark("九讯严选订单");
        oaOrderDTO.setUserName(partnerTokenInfo.getLoginOAUserName());
        oaOrderDTO.setSubKind(0);
        oaOrderDTO.setTitle(cartInfoData.getSupplierName() + "采购单");
        return oaOrderDTO;
    }

    /**
     * 创建采购单商品
     *
     * @param cartProduct
     * @param price
     * @return
     */
    private static OaOrderDTO.PurchaseGoods createPurchaseGoods(CartInfoVO.CartProduct cartProduct, BigDecimal price) {
        OaOrderDTO.PurchaseGoods purchaseGoods = new OaOrderDTO.PurchaseGoods();
        purchaseGoods.setCount(cartProduct.getProductCount());
        purchaseGoods.setPpid(Integer.valueOf(cartProduct.getPpid() + ""));
        purchaseGoods.setProductName(cartProduct.getProductName());
        purchaseGoods.setPrice(price);
        String comment = PRODUCT_COMMENT;
        if (StringUtils.isNotBlank(cartProduct.getProductColor())) {
            comment = comment.concat(",规格:" + cartProduct.getProductColor());
        }
        purchaseGoods.setComment(comment);
        return purchaseGoods;
    }

    /**
     * 创建交易快照
     *
     * @param cartProduct
     * @param priceType
     * @param supplierId
     * @param partnerTokenInfo
     * @return
     */
    private ProductOrderVersion createProductOrderVersion(CartInfoVO.CartProduct cartProduct, Integer priceType, Long supplierId,
                                                          PartnerTokenInfo partnerTokenInfo) {
        ProductOrderVersion productOrderVersion = new ProductOrderVersion();
        productOrderVersion.setBuyNoTaxPrice(cartProduct.getBuyNoTaxPrice());
        productOrderVersion.setBuyTaxPrice(cartProduct.getBuyTaxPrice());
        productOrderVersion.setPartnerId(partnerTokenInfo.getId());
        productOrderVersion.setPpid(cartProduct.getPpid());
        productOrderVersion.setPriceType(priceType);
        productOrderVersion.setProductColor(cartProduct.getProductColor());
        productOrderVersion.setProductName(cartProduct.getProductName());
        productOrderVersion.setProductDetailId(cartProduct.getProductDetailId());
        productOrderVersion.setProductId(cartProduct.getProductId());
        productOrderVersion.setProductImg(cartProduct.getProductImage());
        productOrderVersion.setProductCid(cartProduct.getProductCid());
        productOrderVersion.setProductBarCode(cartProduct.getProductBarCode());
        productOrderVersion.setSupplierUserId(supplierId);
        // 查询分类名称
        if (cartProduct.getProductCid() != null) {
            Category category = categoryService.getOneById(cartProduct.getProductCid().intValue());
            if (category != null) {
                productOrderVersion.setProductCidName(category.getName());
            }
        }

        return productOrderVersion;
    }

    /**
     * 创建采购单详情
     *
     * @param cartProduct
     * @param partnerTokenInfo
     * @param price
     * @param supplierId
     * @return
     */
    private static OrderDetailInfo createOrderDetailInfo(CartInfoVO.CartProduct cartProduct, PartnerTokenInfo partnerTokenInfo,
                                                         BigDecimal price, Long supplierId) {
        OrderDetailInfo orderDetailInfo = new OrderDetailInfo();
        orderDetailInfo.setBuyAmount(cartProduct.getProductCount());
        orderDetailInfo.setPartnerId(partnerTokenInfo.getId());
        orderDetailInfo.setProductId(cartProduct.getProductId());
        orderDetailInfo.setProductDetailId(cartProduct.getProductDetailId());
        orderDetailInfo.setProductPrice(price);
        orderDetailInfo.setSupplierId(supplierId);
        orderDetailInfo.setCartId(cartProduct.getId());
        return orderDetailInfo;
    }

    /**
     * 创建采货王订单信息
     *
     * @param partnerTokenInfo
     * @param channelId
     * @param cartOrderParam
     * @param supplierId
     * @param oaOrderDTO
     * @param totalPrice
     * @return
     */
    private static OrderInfo createOrderInfo(Integer productType, PartnerTokenInfo partnerTokenInfo, String channelId, CartOrderParam cartOrderParam, Long supplierId, OaOrderDTO oaOrderDTO, BigDecimal totalPrice) {
        OrderInfo orderInfo = new OrderInfo();
        // 默认采货王
        orderInfo.setChannelType(0);
        // 订单类型 和商品类型 值 一致
        orderInfo.setOrderType(productType);
        orderInfo.setOrderStatus(OrderStatusEnum.DEFAULT.getCode());
        orderInfo.setPartnerId(partnerTokenInfo.getId());
        orderInfo.setChannelId(channelId);
        orderInfo.setReceivePoiId(cartOrderParam.getAddressId());
        orderInfo.setReceivePoiName(cartOrderParam.getAddressName());
        orderInfo.setReceivePoiCityId(cartOrderParam.getCityId());
        orderInfo.setSupplierId(supplierId);
        orderInfo.setXtenantId(partnerTokenInfo.getXtenant());
        orderInfo.setOrderTitle(oaOrderDTO.getTitle());
        orderInfo.setTotalPrice(totalPrice);
        orderInfo.setContactPerson(cartOrderParam.getContactPerson());
        orderInfo.setContactPhone(cartOrderParam.getContactPhone());
        orderInfo.setReceiveAddress(cartOrderParam.getReceiveAddress());
        orderInfo.setDeliveryTime(LocalDateTime.now().plusDays(DEFAULT_DELIVERY_DAY));
        return orderInfo;
    }

    /**
     * 保存订单信息
     *
     * @param orderInfo
     * @param orderDetailInfoList
     * @param productOrderVersionList
     * @return
     */
    private boolean saveOrderInfo(OrderInfo orderInfo, List<OrderDetailInfo> orderDetailInfoList, List<ProductOrderVersion> productOrderVersionList, OaOrderDTO oaOrderDTO) {
        // 保存信息
        boolean insert = orderInfo.insert();
        if (!insert) {
            return false;
        }
        //因为oa跳转采货王详情页面的时候需要采购单订单的id
        oaOrderDTO.setPurchaseOrderId(orderInfo.getId());
        //自动生成销售单所需要数据
        oaOrderDTO.setSupplierId(orderInfo.getSupplierId());
        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            orderDetailInfo.setOrderId(orderInfo.getId());
        }
        boolean insert1 = orderDetailInfoService.saveBatch(orderDetailInfoList);
        if (!insert1) {
            orderInfo.deleteById();
            return false;
        }
        for (ProductOrderVersion productOrderVersion : productOrderVersionList) {
            productOrderVersion.setOrderId(orderInfo.getId());
        }
        boolean insert2 = productOrderVersionService.saveBatch(productOrderVersionList);
        if (!insert2) {
            orderInfo.deleteById();
        }
        return insert2;
    }

    /**
     * 生成oa采购单，并更新订单信息
     *
     * @param oaOrderDTO
     * @param partnerTokenInfo
     * @param orderInfo
     * @param cartInfoData
     * @param productOrderVersionList
     * @param orderDetailInfoList
     * @param cartIdList
     * @return
     */
    private String saveOaOrder(OaOrderDTO oaOrderDTO, PartnerTokenInfo partnerTokenInfo, OrderInfo orderInfo,
                               CartInfoVO.CartInfoData cartInfoData,
                               List<ProductOrderVersion> productOrderVersionList,
                               List<OrderDetailInfo> orderDetailInfoList,
                               List<Long> cartIdList) {
        // 生成采购单
        OaOrderResultDTO<OaOrderResultDTO.OaOrderData> oaOrderResultDTO =
                oaService.createOaOrder(oaOrderDTO, partnerTokenInfo.getXtenant(), OrderTypeEnum.SMALL.getCode());
        if (oaOrderResultDTO == null) {
            // 保存日志
            orderInfoLogService.saveOrderLog("生成采购单失败，返回结果为空", OrderLogTypeEnum.GENERATE_NO, orderInfo.getId(),
                    partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());
            // 删除订单
            orderInfo.deleteById();
            return cartInfoData.getSupplierName() + " 的采购单生成失败! 原因：" + oaOrderResultDTO.getUserMsg();
        }
        if (!Integer.valueOf(0).equals(oaOrderResultDTO.getCode())
                || oaOrderResultDTO.getExData() == null
                || oaOrderResultDTO.getExData().getSubId() == null) {
            // 保存日志
            orderInfoLogService.saveOrderLog("生成采购单失败，返回结果：" + oaOrderResultDTO.getMsg(), OrderLogTypeEnum.GENERATE_NO,
                    orderInfo.getId(), partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());

            // 删除订单
            orderInfo.deleteById();

            return cartInfoData.getSupplierName() + " 的采购单生成失败! 原因：" + oaOrderResultDTO.getUserMsg();
        }
        // 获取订单号
        Long orderNo = oaOrderResultDTO.getExData().getSubId();

        if (Convert.toLong(NOT_EXIST_TYPE).equals(orderNo)) {
            orderNo = -orderInfo.getId();
            orderInfo.setOrderFlag(Convert.toInt(Boolean.TRUE));
            orderInfoLogService.saveOrderLog("大小件属性不一致，不生成OA采购单", OrderLogTypeEnum.GENERATE_NO, orderInfo.getId(),
                    partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());

        }else {
            // 保存日志
            orderInfoLogService.saveOrderLog("采购单已生成，采购单号：" + orderNo, OrderLogTypeEnum.GENERATE_NO, orderInfo.getId(),
                    partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());
        }
        orderInfo.setOrderNo(orderNo);
        orderInfo.setOrderStatus(OrderStatusEnum.AUDITED.getCode());
        // 保存信息
        boolean update = orderInfo.updateById();

        for (ProductOrderVersion productOrderVersion : productOrderVersionList) {
            productOrderVersion.setOrderNo(orderNo);
        }
        boolean update1 = productOrderVersionService.updateBatchById(productOrderVersionList);

        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            orderDetailInfo.setOrderNo(orderNo);
        }
        boolean update2 = orderDetailInfoService.updateBatchById(orderDetailInfoList);

        // 删除购物车
        boolean remove = cartInfoService.removeByIds(cartIdList);

        // 错误日志统计
        errorLog(update, update1, update2, remove, orderInfo, orderNo, cartIdList);
        return null;
    }

    /**
     * 错误日志统计
     *
     * @param update
     * @param update1
     * @param update2
     * @param remove
     * @param orderInfo
     * @param orderNo
     * @param cartIdList
     */
    private void errorLog(boolean update, boolean update1, boolean update2, boolean remove,
                          OrderInfo orderInfo, Long orderNo, List<Long> cartIdList) {
        if (!update) {
            log.error("保存更新采购单失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!update1) {
            log.error("保存更新商品快照失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!update2) {
            log.error("保存更新订单详情失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!remove) {
            log.error("删除购物车失败，cartIdList:{}", cartIdList);
        }
    }

    private OrderDeliveryBO buildOrderDeliveryBO(List<DeliveryProductBO> deliveryProductBOList, OrderInfo orderInfo,
                                                 OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo) {
        OrderDeliveryBO orderDeliveryBO = new OrderDeliveryBO();
        orderDeliveryBO.setExpressCode(param.getDeliveryType());
        //如果过快递选择其他的情况,就把备注的快递传过去
        if(LogisticsCompanyEnum.OTHER.getSpell().equals(param.getDeliveryType())){
            String expressName = Optional.ofNullable(param.getRemarks())
                    .orElse(LogisticsCompanyEnum.getChinese(param.getDeliveryType()));
            orderDeliveryBO.setExpressName(expressName);
        } else {
            orderDeliveryBO.setExpressName(LogisticsCompanyEnum.getChinese(param.getDeliveryType()));
        }
        orderDeliveryBO.setExpressNum(param.getDeliveryNo());
        orderDeliveryBO.setSubId(String.valueOf(orderInfo.getOrderNo()));
        orderDeliveryBO.setTxtDay(String.valueOf(MagicalValueConstant.INT_7));
        orderDeliveryBO.setInUser(supplierTokenInfo.getLoginName());
        orderDeliveryBO.setPhone(orderInfo.getContactPhone());
        List<OrderDeliveryBO.CaigouDetailVo> caigouDetailVoList = Lists.newArrayList();
        for (DeliveryProductBO deliveryProductBO : deliveryProductBOList) {
            OrderDeliveryBO.CaigouDetailVo caigouDetailVo = new OrderDeliveryBO.CaigouDetailVo();
            caigouDetailVo.setPpriceid(deliveryProductBO.getPpid());
            caigouDetailVo.setNumber(deliveryProductBO.getCount());
            caigouDetailVoList.add(caigouDetailVo);
        }
        orderDeliveryBO.setCaigouDetailVoList(caigouDetailVoList);
        return orderDeliveryBO;
    }

    @Override
    public List<String> orderReceive(List<OrderInfo> orderInfoList) {
        // 参数预设
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        List<String> errorInfoList = Lists.newArrayList();
        List<OrderInfo> orderInfos = Lists.newArrayList();
        List<OrderInfoLog> orderInfoLogList  = Lists.newArrayList();
        List<MessageInfoUser> messageInfoUserList = Lists.newArrayList();
        OrderReceiveBO orderReceiveBO = new OrderReceiveBO();
        // 设置订单id与合作伙伴名称对应关系
        Map<Long, Long> idsMap = orderInfoList.stream().collect(Collectors.toMap(OrderInfo::getId, OrderInfo::getPartnerId));
        Map<Long, String> nameMap = partnerUserMapper.selectBatchIds(idsMap.values()).stream().collect(Collectors.toMap(PartnerUser::getId, PartnerUser::getName));
        Map<Long, String> partnerNameMap = idsMap.keySet().stream()
                .collect(Collectors.toMap(orderInfoId -> orderInfoId, orderInfoId -> nameMap.get(idsMap.get(orderInfoId))));

        for (OrderInfo orderInfo : orderInfoList) {
            orderInfo.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
            OrderInfoLog orderInfoLog;
            // 记录订单日志
            if (Objects.nonNull(partnerTokenInfo)) {
                orderInfoLog = new OrderInfoLog().setOrderId(orderInfo.getId()).setContent("确认收货操作,订单已完成").setType(OrderLogTypeEnum.COMPLETED.getCode())
                        .setOperationUserId(partnerTokenInfo.getLoginOAUserId()).setOperationUserName(partnerTokenInfo.getLoginOAUserName());
            } else {
                orderInfoLog = new OrderInfoLog().setOrderId(orderInfo.getId()).setContent("订单已完成")
                        .setType(OrderLogTypeEnum.COMPLETED.getCode()).setOperationUserName("系统");
            }
            // 站内信息推送，只在自动收货时有推送
            if (Objects.isNull(partnerTokenInfo) && StringUtils.isNotBlank(partnerNameMap.get(orderInfo.getId()))) {
                MessageInfo messageInfo = new MessageInfo().setContent("您的订单：" + orderInfo.getOrderNo() + "已自动确认收货，订单已完成").setTitle("订单自动收货通知")
                        .setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode()).setUserType(MessagePushUserTypeEnum.PARTNER.getCode());
                messageInfoService.saveOrUpdate(messageInfo);
                MessageInfoUser messageInfoUser = new MessageInfoUser().setMessageId(messageInfo.getId()).setUserId(orderInfo.getPartnerId())
                        .setHasRead(1).setUserName(partnerNameMap.get(orderInfo.getId()));
                messageInfoUserList.add(messageInfoUser);
            }
            orderInfos.add(orderInfo);
            orderInfoLogList.add(orderInfoLog);
        }
        // 保存订单收货确认信息
        orderReceiveBO.setOrderInfoList(orderInfos);
        orderReceiveBO.setOrderInfoLogList(orderInfoLogList);
        orderReceiveBO.setMessageInfoUserList(messageInfoUserList);
        boolean saveResult = this.saveOrderReceiveInfo(orderReceiveBO);
        if (!saveResult) {
            List<Long> orderNo = orderInfoList.stream().map(OrderInfo::getOrderNo).collect(Collectors.toList());
            errorInfoList.add("不生成OA小件确认收货信息保存失败, 订单号：" + JSON.toJSON(orderNo));
        }
        return errorInfoList;
    }


    /**
     * 保存订单确认收货信息
     */
    private Boolean saveOrderReceiveInfo(OrderReceiveBO orderReceiveBO) {
        return transactionTemplate.execute(transactionStatus -> {
            try {
                orderInfoService.updateBatchById(orderReceiveBO.getOrderInfoList());
                if (CollectionUtils.isNotEmpty(orderReceiveBO.getOrderInfoLogList())) {
                    orderInfoLogService.saveOrUpdateBatch(orderReceiveBO.getOrderInfoLogList());
                }
                if (CollectionUtils.isNotEmpty(orderReceiveBO.getMessageInfoList())) {
                    messageInfoService.saveOrUpdateBatch(orderReceiveBO.getMessageInfoList());
                }
                if (CollectionUtils.isNotEmpty(orderReceiveBO.getMessageInfoUserList())) {
                    messageInfoUserService.saveOrUpdateBatch(orderReceiveBO.getMessageInfoUserList());
                }
                return true;
            } catch (Exception e) {
                transactionStatus.setRollbackOnly();
                log.error("不生成OA小件确认收货信息保存失败,orderReceiveBO:{},exception:", JSON.toJSON(orderReceiveBO), e);
                return false;
            }
        });
    }
}

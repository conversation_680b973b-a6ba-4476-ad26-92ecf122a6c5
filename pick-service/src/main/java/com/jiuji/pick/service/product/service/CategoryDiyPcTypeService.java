package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.entity.CategoryDiyPcType;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
public interface CategoryDiyPcTypeService extends IService<CategoryDiyPcType> {

    /**
     * 批量保存
     * @param list
     * @return
     */
    int batchInsert(List<CategoryDiyPcType> list);

    /**
     * 批量保存
     * @param list
     * @return
     */
    int batchUpdate(List<CategoryDiyPcType> list);
    /**
     * 批量逻辑删除
     * @param ids
     * @return
     */
    int batchDelete(List<Integer> ids);
}

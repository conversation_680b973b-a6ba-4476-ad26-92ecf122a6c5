package com.jiuji.pick.service.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.param.AutomaticPickupOrder;
import com.jiuji.pick.service.order.param.OrderSearchParam;
import com.jiuji.pick.service.order.vo.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 采购定单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
public interface OrderInfoService extends IService<OrderInfo> {

    List<AutomaticPickupOrder> selectAutomaticPickupOrder();
    /**
     * 查询合作伙伴订单数据
     *
     * @param param param
     * @return
     */
    Page<OrderListVO> getPartnerOrderList(OrderSearchParam param);

    /**
     * 查询合作伙伴订单详情
     *
     * @param orderId   订单id
     * @param partnerId 合作伙伴id
     * @return
     */
    OrderDetailVO getPartnerOrderDetail(Long orderId, Long partnerId);

    /**
     * 查询供应商订单数据
     *
     * @param param param
     * @return
     */
    Page<OrderListVO> getSupplierOrderList(OrderSearchParam param);

    /**
     * 查询供应商订单数据
     *
     * @param param param
     * @return
     */
    Page<OrderListExportVO> getSupplierOrderListV2(OrderSearchParam param);

    /**
     * 查询供应商订单详情
     *
     * @param orderId    订单id
     * @param supplierId 供应商id
     * @return
     */
    OrderDetailVO getSupplierOrderDetail(Long orderId, Long supplierId);

    /**
     * 查询平台订单数量
     *
     * @param param param
     * @return
     */
    int countPlatformOrder(OrderSearchParam param);

    /**
     * 查询平台订单数据
     *
     * @param param param
     * @return
     */
    List<PlatformOrderListVO> getPlatformOrderList(OrderSearchParam param);

    /**
     * 查询平台订单详情
     *
     * @param orderId 订单id
     * @return
     */
    OrderDetailVO getPlatformOrderDetail(Long orderId);

    /**
     * 查询需要导出的平台订单数据
     *
     * @param param
     * @return
     */
    List<OrderListExportVO> getExportPlatformOrderList(OrderSearchParam param);

    /**
     * 判断指定商品、合作伙伴是否有已完成的订单
     *
     * @param partnerId
     * @param productId
     * @return
     */
    Boolean hasCompleteOrder4Product(Long partnerId, Long productId);

    /**
     * 查询待收货的订单
     * @return
     */
    List<Long> getReceiveOrderList();

    /**
     * 创建采购单详情和商品快照信息
     *
     * @param cartProduct 购物车商品
     * @param partnerTokenInfo partnerTokenInfo
     * @param supplierId supplierId
     * @param price 单价
     * @param priceType 价格类型（未税|含税）
     * @param tClass class
     * @return 采购单详情或商品快照信息对象
     */
    <T> T createSaveInfo(CartInfoVO.CartProduct cartProduct, PartnerTokenInfo partnerTokenInfo,
                         Long supplierId, BigDecimal price, Integer priceType, Class<T> tClass);
}

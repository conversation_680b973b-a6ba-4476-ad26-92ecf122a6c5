package com.jiuji.pick.service.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.service.common.entity.OperateLogInfo;
import com.jiuji.pick.service.common.param.ConditionalQueryLogInfoParam;
import com.jiuji.pick.service.common.vo.QueryOperateLogVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
public interface OperateLogInfoMapper extends BaseMapper<OperateLogInfo> {


    Page<QueryOperateLogVo> queryAdmLogInfo(@Param("page") Page<QueryOperateLogVo> page,
                                            @Param("param") ConditionalQueryLogInfoParam param);

    Page<QueryOperateLogVo> querySupplierLogInfo(@Param("page") Page<QueryOperateLogVo> page,
                                                 @Param("param") ConditionalQueryLogInfoParam param,
                                                 @Param("supplierId") Long supplierId);
}

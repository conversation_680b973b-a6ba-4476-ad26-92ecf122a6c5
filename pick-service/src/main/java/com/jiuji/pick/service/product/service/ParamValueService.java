/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.entity.ParamValue;
import com.jiuji.pick.service.product.vo.SearchResultVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
public interface ParamValueService extends IService<ParamValue> {
    /**
     * 获取列表需要显示的参数标签
     *
     * @param ppids ppid对应pid的map
     */
    Map<Long, List<String>> getSpecParamsByMap(List<Long> ppids);

    /**
     * 获取分类的选择项（单选、多选）参数
     */
    List<SearchResultVo.Screening> getCateSelectParamsScreening(@Param("cid") int category);
}

package com.jiuji.pick.service.common.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.enums.LogComparison.LogDifferences;
import com.jiuji.pick.common.enums.LogComparison.SupplierAddOrUpdateProductParamEnum;
import com.jiuji.pick.common.utils.ReflexUtils;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.service.LogService;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.product.param.SupplierAddOrUpdateProductParam;
import com.jiuji.pick.service.product.param.SupplierAddOrUpdateProductParamOld;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("SupplyManagementLogServiceImpl")
public class SupplyManagementLogServiceImpl implements LogService {

    @Resource
    private NewOperateLogInfoService logInfoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Override
    public void systemSaveLog(Object param, Integer type) {
        Long userId = null;
        String userName = null;
        NewOperateLogInfo newOperateLogInfo = new NewOperateLogInfo();
        if (NewOperateLogInfoTypeEnum.SUPPLY_MANAGEMENT.getCode().equals(type)) {
            SupplierTokenInfo tokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck())
                    .orElse(new SupplierTokenInfo());
            userId = tokenInfo.getId();
            userName = tokenInfo.getLoginName();
            newOperateLogInfo.setShowType(LogShowTypeEnum.SUPPLIER.getCode())
                    .setType(type);

        }
        if (NewOperateLogInfoTypeEnum.SUPPLIER_BOUND_GOODS_QUERY.getCode().equals(type)) {
            OATokenInfo oaTokenInfo = Optional.ofNullable(currentRequestComponent.getOATokenInfoBoWithoutCheck())
                    .orElse(new OATokenInfo());
            userId = oaTokenInfo.getUserId().longValue();
            userName = oaTokenInfo.getName();
            newOperateLogInfo.setShowType(LogShowTypeEnum.ADMIN.getCode())
                    .setType(type);

        }
        newOperateLogInfo.setOptUserName(userName)
                .setOptUserId(userId);
        CompletableFuture.runAsync(() -> {
            SupplierAddOrUpdateProductParam req = JSONUtil.toBean(JSONUtil.toJsonStr(param), SupplierAddOrUpdateProductParam.class);
            SupplierAddOrUpdateProductParamOld paramOld = req.getParamOld();
            SupplierAddOrUpdateProductParamOld paramNew = new SupplierAddOrUpdateProductParamOld();
            BeanUtils.copyProperties(param, paramNew);
            LogDifferences logDifferences = getLogDifferences(paramOld, paramNew);
            String comment = null;
            try {
                comment = ReflexUtils.entityComparison(logDifferences);
            } catch (Exception e) {
                log.error("可供应商品管理操作日志异常{}", e.getMessage(), e);
            }
            //特殊字段处理
            String connent = specialField(paramOld, paramNew, comment);
            newOperateLogInfo.setContent(connent).setCreateTime(LocalDateTime.now())
                    .setRelateId(req.getPpid() + "");
            logInfoService.saveSystemLog(newOperateLogInfo);
        });

    }

    /**
     * 特殊字段处理
     *
     * @param paramOld
     * @param paramNew
     * @param comment
     * @return
     */
    private String specialField(SupplierAddOrUpdateProductParamOld paramOld, SupplierAddOrUpdateProductParamOld paramNew, String comment) {
        //MATERIAL("material","物料")进行特殊处理
        String materialNew = Optional.ofNullable(paramNew.getMaterial()).orElse("");
        String materialOld = Optional.ofNullable(paramOld.getMaterial()).orElse("");
        if (materialNew.equals(materialOld)) {
            return comment;
        }

        StringJoiner joinerNew = new StringJoiner("，");
        StringJoiner joinerOld = new StringJoiner("，");

        if (!"".equals(materialNew)) {
            List<String> stringsNew = Arrays.asList(materialNew.split(","));
            if (CollectionUtils.isNotEmpty(stringsNew)) {
                stringsNew.forEach((String item) -> {
                    String nameByValue = MaterialEnum.getNameByValue(Integer.parseInt(item));
                    joinerNew.add(nameByValue);
                });
            }
        }

        if (!"".equals(materialOld)) {
            List<String> stringsOld = Arrays.asList(materialOld.split(","));
            if (CollectionUtils.isNotEmpty(stringsOld)) {
                stringsOld.forEach((String item) -> {
                    String nameByValue = MaterialEnum.getNameByValue(Integer.parseInt(item));
                    joinerOld.add(nameByValue);
                });
            }
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(comment).append(SupplierAddOrUpdateProductParamEnum.MATERIAL.getMessage())
                .append("由【").append(joinerOld).append("】修改为【").append(joinerNew).append("】。");

        return stringBuilder.toString();
    }

    /**
     * 获取日志的参数
     *
     * @param oldParam
     * @param newParam
     * @return
     */
    private LogDifferences getLogDifferences(Object oldParam, Object newParam) {
        LogDifferences logDifferences = new LogDifferences();
        logDifferences.setOldEntity(oldParam).setNewEntity(newParam).setParamMap(SupplierAddOrUpdateProductParamEnum.getMap());
        HashMap<String, Map<String, String>> map = new HashMap<>(6);
        map.put("material", MaterialEnum.getMap());
        map.put("noReasonReturn", NoReasonReturnEnum.getMap());
        map.put("badPay", BadPayEnum.getMap());
        map.put("lackPay", LackPayEnum.getMap());
        map.put("remoteDeliveryFee", RemoteDeliveryFeeEnum.getMap());
        logDifferences.setTransformationMap(map);
        logDifferences.setExcludeParams(Arrays.asList(SupplierAddOrUpdateProductParamEnum.MATERIAL.getCode()));
        return logDifferences;
    }

}

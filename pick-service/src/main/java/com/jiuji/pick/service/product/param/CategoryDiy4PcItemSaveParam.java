package com.jiuji.pick.service.product.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CategoryDiy4PcItemSaveParam {
    private Integer id;
    @NotNull(message = "请选择父分类")
    private Integer parentId;
    @NotBlank(message = "请填写名称")
    private String name;
    private String link=" ";
    @NotNull(message = "请选择分类级别")
    private Integer level;

}

package com.jiuji.pick.service.rpc.fallback;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.rpc.cloud.OaUserCloud;
import com.jiuji.pick.service.rpc.vo.OaUserVo;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @function:
 * @description: OaUserCloudFallbackFactory.java
 * @date: 2021/07/01
 * @author: sunfayun
 * @version: 1.0
 */
@Component
@Slf4j
public class OaUserCloudFallbackFactory implements FallbackFactory<OaUserCloud> {

    private final static String ERROR_LOG = "调用oaCloud出错，url:{},params:{}";

    @Override
    public OaUserCloud create(Throwable throwable) {
        return new OaUserCloud() {
            @Override
            public Result<OaUserVo> getUserByPhone(String phone, long xtenant) {
                log.error(ERROR_LOG, "/api/user/getUserByPhone", phone);
                return Result.error();
            }
        };
    }
}

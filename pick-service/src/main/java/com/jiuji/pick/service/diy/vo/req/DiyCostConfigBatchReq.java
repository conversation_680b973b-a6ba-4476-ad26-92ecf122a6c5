package com.jiuji.pick.service.diy.vo.req;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
/**
 * 订单列表查询参数
 *
 * <AUTHOR>
 * @since 2021-6-2
 */
@Data
public class DiyCostConfigBatchReq  {

    List<DiyCostConfigIdReq> updateList;


    @Data
    public static class DiyCostConfigIdReq  {
        /**
         * ppid
         */
        private Integer ppriceid;

        private BigDecimal pickwebCost;

        private BigDecimal saleCost;

    }

}

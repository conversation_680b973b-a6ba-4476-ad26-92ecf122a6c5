/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.entity.ProductImage;
import com.jiuji.pick.service.product.vo.ProductImageUpdateParam;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> @since 2017-08-25
 */
public interface ProductImageService extends IService<ProductImage> {


    /**
     * 提交修改商品图片图
     *
     * @param param 商品图片集合
     * @return 执行结果
     */
    boolean updateProductImage(ProductImageUpdateParam param, boolean isSynchronize);
}

package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 产品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_product_info")
public class ProductInfo extends Model<ProductInfo> {

    private static final long serialVersionUID=1L;

    /**
     * ppid
     */
    private Long ppriceid;

    /**
     * 商品id
     */
    private Long productid;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String productColor;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 小店价
     */
    private BigDecimal vipPrice;

    /**
     * 分类
     */
    private Long cid;

    /**
     * 是否是大件
     */
    private Boolean isMobile;

    /**
     * 会员价
     */
    private BigDecimal memberPrice;

    /**
     *  ppid 状态
     */
    private Integer que;

    /**
     * 上下架
     */
    private Boolean display;

    /**
     * 是否删除
     */
    @TableLogic
    private Boolean isDel;

    private Integer viewsWeek;

    @TableField("ppriceid1")
    private Long pPriceId1;

    /**
     * 配置
     */
    private String config;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 分类树
     */
    private String cidFamily;

    @TableField("views_weekr")
    private Integer viewsweekr;

    /**
     * sku排序值
     */
    @TableField("`rank`")
    private Integer rank;

    /**
     * 特价商品
     */
    private Integer noPromotion;

    /**
     * 69码
     */
    private String barCode;

    /**
     * 是否启用条码
     */
    private Boolean isBarCode;

    /**
     * 规格id拼接的字符串
     */
    private String productColorIds;

    /**
     * 商品排序值
     */
    private Integer psort;

    /**
     * 商品添加时间
     */
    private LocalDate addDate;

    /**
     * 商品规格id字符串
     */
    private String productColorId;

    private String bPic;

    private Integer buyLimit;

    /**
     * OA是否启用
     */
    private Boolean oaEnable;

    /**
     * 商品条码【69码的条数】
     */
    private Integer barCodeCount;


    @Override
    protected Serializable pkVal() {
        return this.ppriceid;
    }


    public static final String P_PRICE_ID = "ppriceid";

    public static final String PRODUCT_ID = "productid";

    public static final String PRODUCT_NAME = "product_name";

    public static final String PRODUCT_COLOR = "product_color";

    public static final String COST_PRICE = "cost_price";

    public static final String VIP_PRICE = "vip_price";

    public static final String CID = "cid";

    public static final String IS_MOBILE = "is_mobile";

    public static final String B_PIC = "b_pic";

    public static final String MEMBER_PRICE = "member_price";

    public static final String QUE = "que";

    public static final String DISPLAY = "display";

    public static final String IS_DEL = "is_del";

    public static final String VIEWSWEEK = "views_week";

    public static final String P_PRICE_ID1 = "pprice_id1";

    public static final String CONFIG = "config";

    public static final String BRAND_ID = "brand_id";

    public static final String CID_FAMILY = "cid_family";

    public static final String VIEWSWEEKR = "views_weekr";

    public static final String RANK = "`rank`";

    public static final String NO_PROMOTION = "no_promotion";

    public static final String BARCODE = "bar_code";

    public static final String IS_BARCODE = "is_bar_code";

    public static final String OA_ENABLE = "oa_enable";

    public static final String BARCODE_COUNT = "bar_code_count";

}

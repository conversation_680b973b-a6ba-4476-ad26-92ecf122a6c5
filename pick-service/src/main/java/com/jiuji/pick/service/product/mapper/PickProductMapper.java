package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.service.product.entity.PickProduct;
import com.jiuji.pick.service.product.param.QueryProductExamineListParam;
import com.jiuji.pick.service.product.param.QueryProductListParam;
import com.jiuji.pick.service.product.vo.QueryProductExamineListVo;
import com.jiuji.pick.service.product.vo.QueryProductListVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface PickProductMapper extends BaseMapper<PickProduct> {

    Page<QueryProductListVo> queryProductListInfo(@Param("page") Page<QueryProductListVo> page, @Param("param") QueryProductListParam param);

    Page<QueryProductExamineListVo> getProductExamineList(@Param("page") Page<QueryProductExamineListVo> page, @Param("param") QueryProductExamineListParam param);


}

package com.jiuji.pick.service.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.vo.SupplierChannelCountVO;
import com.jiuji.pick.service.user.entity.SupplierChannel;
import com.jiuji.pick.service.user.vo.SupplierChannelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商渠道关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
public interface SupplierChannelService extends IService<SupplierChannel> {

    String getChannelIdTest(Long xTenant, Long supplierId, String addressId, Integer authId, Integer orderType);

    /**
     * 获取OA渠道ID
     * @param xTenant 租户ID
     * @param supplierId 供应商ID
     * @param authId 授权ID 用于子租户隔离
     * @param addressId 门店ID
     * @param orderType 订单类型
     * @return 渠道ID
     */
    String getChannelId(Long xTenant, Long supplierId, String addressId, Integer authId, Integer orderType);

    /***
     * 获取渠道id  v2
     * @date: 2021/11/16 12:00
     */
    String getChannelIdV2(Long xTenant, Long supplierId, String addressId, Integer authId);

    /**
     * 获取NEO渠道ID
     * @param xtenant 租户ID
     * @param supplierId 供应商ID
     * @param host 租户域名
     * @param token token
     * @return 渠道ID
     */
    String getNeoChannelId(Long xtenant, Long supplierId, String host, String token);

    /***
     * @description: 统计供应商绑定的渠道数量
     * @Param: [supplierIdList]
     * @author: Lbj
     * @date: 2021/5/7 14:22
     */
    List<SupplierChannelCountVO> supplierChannelCount(List<Long> supplierIdList);

    /***
     * @description: 供应商绑定的渠道列表
     * @Param: [supplierIdList]
     * @author: Lbj
     * @date: 2021/5/7 14:22
     */
    List<SupplierChannelVO> listBySupplierId(Long supplierId);
}

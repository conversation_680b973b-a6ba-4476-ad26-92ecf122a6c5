package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/06
 * @Time 11:23
 */
@Data
public class ProductDetailsVo {

   private Long id;
   private Long supplierProductId;
   private String productFuture;
   private BigDecimal advicePrice;
   private String productConfig;
   private String attachmentIds;
   private BigDecimal buyNoTaxPrice;
   private String material;
   private Integer deliveryDay;
   private Integer noReasonReturn;
   private Integer changeDay;
   private Integer badPay;
   private Integer lackPay;
   private String afterSalePolicy;
   private String otherPolicy;
   private Integer qualityDate;
   private String boxRule;
   private String detail;
   private String decription;
   private String companyNme;
   private String leaderPhone;
   private BigDecimal predictProfit;
   private String address;
   private Long supplierUserId;
   private String categoryName;


}

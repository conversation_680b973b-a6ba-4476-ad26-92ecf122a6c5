/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
@Data
@TableName("t_brand_category")
public class BrandCategory extends Model<BrandCategory> {

    private static final long serialVersionUID = 1L;

    public static final String BRANDID = "brand_id";
    public static final String CATEGORYID = "category_id";
    public static final String RANK = "`rank`";
    public static final String ID = "id";
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer brandId;

    private Integer categoryId;

    @TableField("`rank`")
    private Integer rank;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }


}

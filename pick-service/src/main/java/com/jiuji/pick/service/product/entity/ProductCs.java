/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
@Data
@TableName("t_product_cs")
public class ProductCs extends Model<ProductCs> {

    public static final String ID = "id";
    public static final String PRODUCTID = "product_id";
    public static final String CSID = "csid";
    public static final String CSVALUE = "cs_value";
    public static final String ISDISPLAY = "is_display";
    public static final String TYPE = "type";
    public static final String CATE_TYPE = "cate_type";
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 商品ID
     */
    private Long productId;

    /**
     * product_cs_basic对应ID
     */
    private Integer csid;

    /**
     * 商品属性值
     */
    private String csValue;

    private Boolean isDisplay;

    /**
     * 1--productid是商品id ；0--productid是ppid
     */
    private Integer type;

    /**
     * 0---主分类参数 1-- 扩展分类参数
     */
    private Integer cateType;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

package com.jiuji.pick.service.diy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @function:
 * @description: OrderTypeEnum.java
 * @date: 2021/10/09
 * @author: sunfayun
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum ProductInfoQueEnum {

    /***
     *
     */
    ZERO(0, "正常"),
    ONE(1, "缺货"),
    TWO(2, "下市"),
    THREE(3, "预约"),
    FOUR(4, "有货");

    private int code;
    private String desc;


    public static String getDescByCode(Integer code) {
        for (ProductInfoQueEnum value : values()) {
            if (value.getCode()==code) {
                return value.getDesc();
            }
        }
        return "";
    }

}

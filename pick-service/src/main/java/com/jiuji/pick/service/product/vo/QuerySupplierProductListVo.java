package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @function:
 * @description: QuerySupplierProductListVo.java
 * @date: 2021/05/07
 * @author: sun<PERSON>yun
 * @version: 1.0
 */
@Data
public class QuerySupplierProductListVo {

    private Long productId;
    private Long ppid;
    private String productName;
    private String brandName;
    private Long categoryId;
    private String categoryName;
    private LocalDateTime productUpTime;
    private BigDecimal advicePrice;
    private Integer productStatus;
    private String productStatusName;
    private LocalDateTime createTime;
    private Integer saleCount;

}

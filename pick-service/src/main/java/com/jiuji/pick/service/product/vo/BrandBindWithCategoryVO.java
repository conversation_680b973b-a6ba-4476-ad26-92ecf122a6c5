package com.jiuji.pick.service.product.vo;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.pick.service.product.entity.BrandCategory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 绑定再分类上的品牌的VO
 * @since 2019-12-19 09:54
 */
@EqualsAndHashCode(callSuper = false)
@Data
@NoArgsConstructor
public class BrandBindWithCategoryVO extends Model<BrandCategory> implements Serializable {

    private static final long serialVersionUID = 2959393389377157078L;

    private Integer brandId;
    private Integer rank;

    public BrandBindWithCategoryVO(BrandCategory brandCategory) {
        this.brandId = brandCategory.getBrandId();
        this.rank = brandCategory.getRank();
    }

    public static BrandCategory toSuper(Integer categoryId,
            BrandBindWithCategoryVO brandBindWithCategoryVO) {
        BrandCategory brandCategory = new BrandCategory();
        brandCategory.setCategoryId(categoryId);
        brandCategory.setBrandId(brandBindWithCategoryVO.getBrandId());
        brandCategory.setRank(brandBindWithCategoryVO.getRank());
        return brandCategory;
    }
}

package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @function:
 * @description: QueryProductListVo.java
 * @date: 2021/05/06
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class QueryProductListVo {

    private Long productId;
    private Long jiuJiProductId;
    private Long ppid;
    private String productName;
    private Long categoryId;
    private String categoryName;
    private LocalDateTime productUpTime;
    private BigDecimal advicePrice;
    private Integer productStatus;
    private String productStatusName;
    private LocalDateTime createTime;

    /**
     * 采购未税单价
     */
    private BigDecimal buyNoTaxPrice;

    /**
     * 采购含税单价
     */
    private BigDecimal buyTaxPrice;

}

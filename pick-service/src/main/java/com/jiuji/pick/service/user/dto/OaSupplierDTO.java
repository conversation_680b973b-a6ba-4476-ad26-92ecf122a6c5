package com.jiuji.pick.service.user.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * oa新增渠道商实体类
 *
 * <AUTHOR>
 * @since 2021-5-21
 */
@Data
public class OaSupplierDTO {

    /**
     * 公司名称
     */
    private String company;

    /**
     * 公司简称
     */
    private String companyJc;

    /**
     * 城市id(对应公司地址)
     */
    @JSONField(name = "cityid")
    private Integer cityId;

    /**
     * 省份id(对应公司地址)
     */
    private Integer pid;

    /**
     * 市级id(对应公司地址)
     */
    private Integer zid;

    /**
     * 县级ID(对应公司地址)
     */
    private Integer did;

    /**
     * 地址
     */
    private String address;

    /**
     * 注册资金
     */
    @JSONField(name = "registeredcapital")
    private BigDecimal registeredFund;

    /**
     * 企业性质
     */
    @JSONField(name = "companynature")
    private Integer companyNature;

    /**
     * 法人代表
     */
    @JSONField(name = "legalrepresent")
    private String legalPerson;

    /**
     * 负责人联系手机
     */
    private String mobile;

    /**
     * 商品分类
     */
    @JSONField(name = "classification")
    private String classification;

    /**
     * 售后联系人
     */
    @JSONField(name = "shouhoucontacts")
    private String afterContacts;

    /**
     * 售后联系电话
     */
    @JSONField(name = "shouhoumobile")
    private String afterMobile;

    /**
     * 售后联系地址
     */
    private String afterAddress;

    /**
     * 售后收货地址cityId(城市id)
     */
    @JSONField(name = "afterCityid")
    private Integer afterCityId;

    /**
     * 售后收货地址provinceId（省份id）
     */
    @JSONField(name = "afterProvinceid")
    private Integer afterProvinceId;

    /**
     * 售后收货地址countyId(县区id)
     */
    @JSONField(name = "afterCountyid")
    private Integer afterCountyId;

    /**
     * 财务负责人
     */
    @JSONField(name = "cwFzr")
    private String financeName;

    /**
     * 财务负责人联系方式
     */
    @JSONField(name = "cwLxfs")
    private String financePhone;

    /**
     * 主营业务备注
     */
    private String comment1;

    /**
     * 签约合同
     */
    private List<OaFileBO> attachments;

    /**
     * 业务联系人列表
     */
    private List<OaContactsDTO> contactsList;

    /**
     * 财务信息
     */
    private List<OaFinanceDTO> financeList;

    /**
     * 0: 手机配件；1：维修配件；2:行政物品;3:手机;4:媒介; 5:营销物料，9:运营商工号 10：订单商品发货商渠道
     */
    private List<Integer> kinds;

    /**
     * 渠道规模
     * LESS_THAN_HALF_MILLION(1, "小于50万元"),
     * HALF_MILLION_TO_FIVE_MILLION(2,"50万元至500万元"),
     * FIVE_MILLION_TO_TEN_MILLION(3,"500万元至1000万元"),
     * MORE_THEN_TEN_MILLION(4,"1000万以上")
     */
    @JSONField(name = "channelscale")
    private Integer channelScale;

    private Integer xTenant;

    private Integer areaId;

    @JSONField(name = "authorizeid")
    private Integer authId;
}

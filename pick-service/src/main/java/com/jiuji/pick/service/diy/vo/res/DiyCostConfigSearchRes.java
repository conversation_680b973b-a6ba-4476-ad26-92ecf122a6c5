package com.jiuji.pick.service.diy.vo.res;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DiyCostConfigSearchRes implements Serializable {



    /**
     * 商品id
     */
    private Long productId;

    /**
     * ppid
     */
    private Long ppriceid;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String productColor;


    /**
     * 状态
     *
     */
    private Integer que;
    /**
     * 状态
     *
     */
    private String queName;


    private BigDecimal pickwebCost;

    private BigDecimal saleCost;

}

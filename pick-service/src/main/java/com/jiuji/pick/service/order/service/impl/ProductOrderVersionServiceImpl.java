package com.jiuji.pick.service.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.enums.OrderPriceTypeEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.service.order.entity.ProductOrderVersion;
import com.jiuji.pick.service.order.mapper.ProductOrderVersionMapper;
import com.jiuji.pick.service.order.service.ProductOrderVersionService;
import com.jiuji.pick.service.order.vo.UpdateOrderPriceVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品下单快照表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Service
public class ProductOrderVersionServiceImpl extends ServiceImpl<ProductOrderVersionMapper, ProductOrderVersion> implements ProductOrderVersionService {

    @Override
    public void checkOrderUpdatePrice(UpdateOrderPriceVo updateOrderPriceVoList) {
        Long orderId = updateOrderPriceVoList.getOrderId();
        List<UpdateOrderPriceVo.UpdateOrderPriceDetailVo> detailVoList = updateOrderPriceVoList.getDetailVoList();
        //交易快照获取原始
        List<ProductOrderVersion> list = this.lambdaQuery().eq(ProductOrderVersion::getOrderId, orderId).list();
        Map<Long, ProductOrderVersion> map = new HashMap<>(list.size());
        if (CollectionUtils.isNotEmpty(list)) {
            map = list.stream().collect(Collectors.toMap(ProductOrderVersion::getPpid, Function.identity()));
        }
        for (UpdateOrderPriceVo.UpdateOrderPriceDetailVo item : detailVoList) {
            String productName = Optional.ofNullable(item.getProductName()).orElseThrow(() -> new BizException("商品名称不能为空"));
            if (item.getBuyAmount() == null) {
                throw new BizException("购买数量不能为空");
            }
            //交易快照获取原始价格(根据订单id和ppid在数据确认)
            BigDecimal productOldPrice;
            BigDecimal productNewPrice = Optional.ofNullable(item.getProductNewPrice()).orElseThrow(() -> new BizException("商品新价格不能为空"));
            ProductOrderVersion productOrderVersion = map.get(item.getPpriceid().longValue());
            if (productOrderVersion != null) {
                Integer priceType = Optional.ofNullable(productOrderVersion.getPriceType()).orElse(Integer.MAX_VALUE);
                //判断价格选择含税加或这个非含税加
                if (OrderPriceTypeEnum.TAX_INCLUDED.getCode().equals(priceType)) {
                    productOldPrice = Optional.ofNullable(productOrderVersion.getBuyTaxPrice()).orElse(BigDecimal.ZERO);
                } else if (OrderPriceTypeEnum.UNTAXED.getCode().equals(priceType)) {
                    productOldPrice = Optional.ofNullable(productOrderVersion.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO);
                } else {
                    productOldPrice = Optional.ofNullable(item.getProductOldPrice()).orElseThrow(() -> new BizException("商品老价格不能为空"));
                }
            } else {
                productOldPrice = Optional.ofNullable(item.getProductOldPrice()).orElseThrow(() -> new BizException("商品老价格不能为空"));
            }
            //如果过新的价格大于老的价格 新价格不能高于老价格的110%
            BigDecimal benefit = new BigDecimal("2.10");
            BigDecimal productMultiply = productOldPrice.multiply(benefit);
            if (productNewPrice.compareTo(productMultiply) > 0) {
                throw new BizException("保存失败，" +productName + "单价高于售价210%，请核对");
            }
        }
    }
}

package com.jiuji.pick.service.diy.vo.res;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DiyCostConfigRes implements Serializable {

    private BigDecimal pickwebCost;

    private BigDecimal saleCost;

    private Integer ppriceid;

}

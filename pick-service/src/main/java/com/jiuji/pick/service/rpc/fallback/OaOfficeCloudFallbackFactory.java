package com.jiuji.pick.service.rpc.fallback;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.rpc.cloud.OaOfficeCloud;
import com.jiuji.pick.service.rpc.vo.PickInfoVo;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @function:
 * @description: OaStockCloudFallbackFactory.java
 * @date: 2021/08/09
 * @author: sunfayun
 * @version: 1.0
 */
@Component
@Slf4j
public class OaOfficeCloudFallbackFactory implements FallbackFactory<OaOfficeCloud> {

    private final static String ERROR_LOG = "调用OaOfficeCloud获取合作伙伴档案出错，url:{},params:{}";

    @Override
    public OaOfficeCloud create(Throwable throwable) {
        return new OaOfficeCloud() {
            @Override
            public Result<List<PickInfoVo>> list9XunPick(List<Long> xtenantIdList) {
                log.error(ERROR_LOG, "/office/api/partner/finance/list9XunPick/v1", xtenantIdList);
                return Result.error();
            }

            @Override
            public Result<List<PickInfoVo>> listDiyShell(List<Long> xtenantIdList) {
                log.error(ERROR_LOG, "/office/api/partner/finance/listDiyShell/v1", xtenantIdList);
                return Result.error();
            }
        };
    }
}

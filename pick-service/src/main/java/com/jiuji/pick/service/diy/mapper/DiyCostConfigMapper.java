package com.jiuji.pick.service.diy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.service.diy.entity.DiyCostConfig;
import com.jiuji.pick.service.diy.vo.req.DiyCostConfigSearchReq;
import com.jiuji.pick.service.diy.vo.res.DiyCostConfigSearchRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-06-16
 */
@Mapper
public interface DiyCostConfigMapper extends BaseMapper<DiyCostConfig> {

    Page<DiyCostConfigSearchRes> pageList(@Param("page") Page<DiyCostConfigSearchRes> page,
                                          @Param("req")DiyCostConfigSearchReq req);


}

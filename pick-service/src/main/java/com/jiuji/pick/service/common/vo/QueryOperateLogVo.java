package com.jiuji.pick.service.common.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @function:
 * @description: QueryOperateLogVo.java
 * @date: 2021/05/27
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class QueryOperateLogVo {

    private Long id;

    /**
     * 关联业务单号
     */
    private String relateId;

    /**
     * 操作人id
     */
    private Long userId;

    /**
     * 操作人姓名
     */
    private String userName;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}

package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_pick_product")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PickProduct extends Model<PickProduct> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 来源，0九机，1供应商
     */
    private Integer source;

    /**
     * 九机产品ID
     */
    @TableField("jiu_ji_product_id")
    private Long jiuJiProductId;

    /**
     * 商品ppid
     */
    private Long ppid;

    /**
     * 产品类型，0：小件，1：大件，2：虚拟商品，3：固定资产，4：虚拟资产
     */
    private Integer productType;

    /**
     * 商品状态,0待上架 1上架中 2下架
     */
    private Integer productStatus;

    /**
     * 建议售价
     */
    private BigDecimal advicePrice;

    /**
     * 商品卖点
     */
    private String productFuture;

    /**
     * 默认配置
     */
    private String productConfig;

    /**
     * 默认专区 0否，1是
     */
    private Integer defaultArea;

    /**
     * 九机爆品 0否，1是
     */
    private Integer hotArea;

    /**
     * 乐物专区 0否，1是
     */
    private Integer happyArea;

    /**
     * 新品推荐 0否，1是
     */
    private Integer recommendArea;

    /**
     * 九机销量
     */
    private Integer saleCount;

    /**
     * 培训资料
     */
    private String attachmentIds;

    /**
     * 商品上架时间
     */
    private LocalDateTime productUpTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

package com.jiuji.pick.service.order.service;

import com.jiuji.pick.service.order.bo.OrderDeliveryBO;
import com.jiuji.pick.service.order.dto.OaChannelCheckDTO;
import com.jiuji.pick.service.order.dto.OaOrderDTO;
import com.jiuji.pick.service.order.dto.OaOrderResultDTO;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.param.OaChannelCheckParam;
import com.jiuji.pick.service.order.vo.OrderAreaInfoVO;
import com.jiuji.pick.service.user.dto.OaSupplierDTO;
import com.jiuji.pick.service.user.dto.OaSupplierResultDTO;

import java.util.List;

/**
 * <p>
 * oa服务调用 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
public interface OaService {

    /**
     * 获取订单门店信息
     *
     * @param xTenant
     * @return
     */
    List<OrderAreaInfoVO> getAreaInfo(Long xTenant);

    /**
     * 生成采购单
     *
     * @param oaOrderDTO
     * @param xTenant
     * @param orderType 订单类型 OrderTypeEnum
     * @return
     */
    OaOrderResultDTO<OaOrderResultDTO.OaOrderData> createOaOrder(OaOrderDTO oaOrderDTO, Long xTenant, Integer orderType);

    /**
     * 查询大小件信息
     *
     * @param ppid
     * @return
     */
    Integer queryIsMobile(Long ppid) ;


        /**
         * 生成渠道商id
         *
         * @param perfectSupplierDTO
         * @param xTenant
         * @return
         */
    OaSupplierResultDTO createOaChannelId(OaSupplierDTO perfectSupplierDTO, Long xTenant);

    /**
     * 取消订单
     *
     * @param orderNo
     * @param xTenant
     * @param orderType 订单类型 OrderTypeEnum
     * @return
     */
    OaOrderResultDTO cancelOrder(Long orderNo, Long xTenant, Integer orderType);

    /**
     * oa分批发货
     * @param orderDeliveryBO
     * @param orderType 订单类型 OrderTypeEnum
     * @return
     */
    OaOrderResultDTO orderDeliveryForProduct(OrderInfo orderInfo, OrderDeliveryBO orderDeliveryBO, Integer orderType);

    /**
     * OA渠道校验
     * @param param
     * @return
     */
    OaChannelCheckDTO channelCheck(OaChannelCheckParam param);

    /**
     * OA渠道校验 v2
     * @param param
     * @return
     */
    OaChannelCheckDTO channelCheckV2(OaChannelCheckParam param);
}

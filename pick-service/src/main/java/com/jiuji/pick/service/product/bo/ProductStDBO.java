package com.jiuji.pick.service.product.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 对应数据库视图 VIEW_PRODUCT_ST
 */
@Data
@EqualsAndHashCode()
public class ProductStDBO {

    private Long ppid;
    private Long productid;
    private Long supplierProductId;
    /**
     * 规格名称 例如：颜色
     */
    private String name;
    /**
     * 规格明细内容 例如：蓝色
     */
    private String value;
    /**
     * 规格排序值，例如：颜色排序值
     */
    private Integer firstOrder;
    /**
     * 规格明细排序值，例如：蓝色排序值
     */
    private Integer secondOrder;
    /**
     * 0显示为文字，1 显示为图片
     */
    private String showtype;
    /**
     * 规格明细id
     */
    private Integer standardDetailId;
    /**
     * sku的排序值
     */
    @TableField("`rank`")
    private Integer rank;
    /**
     * 默认图
     */
    private String bpic;
    /**
     * 规格id
     */
    private Long standId;
    private Double memberprice;
    private Long cid;
    private Long que;
    private Boolean limitselect;


    /**
     * 临时记录能不能选
     */
    @JsonIgnore
    private boolean enable = true;

}

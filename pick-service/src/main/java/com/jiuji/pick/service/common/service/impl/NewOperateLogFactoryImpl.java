package com.jiuji.pick.service.common.service.impl;

import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.service.common.service.LogService;
import com.jiuji.pick.service.common.service.NewOperateLogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class NewOperateLogFactoryImpl implements NewOperateLogFactory {

    @Resource(name = "SupplyManagementLogServiceImpl")
    private LogService supplyManagementLogService;

    @Resource(name = "SupplierInformationLogServiceImpl")
    private LogService supplierInformationLogService;

    @Resource(name = "GoodsOnAndOffShelvesServiceImpl")
    private LogService goodsOnAndOffShelvesService;

    @Resource(name = "CommodityWarehouseManagementServiceImpl")
    private LogService commodityWarehouseManagementService;

    //暂时不用
    @Resource(name = "SupplierBoundGoodsQueryServiceImpl")
    private LogService supplierBoundGoodsQueryService;

    @Resource(name = "BlackAndWhiteListManagementServiceImpl")
    private LogService blackAndWhiteListManagementService;

    @Resource(name = "PartnerListServiceImpl")
    private LogService partnerListService;

    @Resource(name = "PartnerListOrderServiceImpl")
    private LogService partnerListOrderService;


    @Override
    public void systemSaveLog(Object param, Integer type) {
        //可供应商品管理(供应商)
        if(NewOperateLogInfoTypeEnum.SUPPLY_MANAGEMENT.getCode().equals(type)){
            supplyManagementLogService.systemSaveLog(param,NewOperateLogInfoTypeEnum.SUPPLY_MANAGEMENT.getCode());
        }
        //供应商绑定商品查询(平台方)
        if(NewOperateLogInfoTypeEnum.SUPPLIER_BOUND_GOODS_QUERY.getCode().equals(type)){
            supplyManagementLogService.systemSaveLog(param,NewOperateLogInfoTypeEnum.SUPPLIER_BOUND_GOODS_QUERY.getCode());
        }
        //供应商信息(供应商)
        if(NewOperateLogInfoTypeEnum.SUPPLIER_INFORMATION.getCode().equals(type)){
            supplierInformationLogService.systemSaveLog(param,NewOperateLogInfoTypeEnum.SUPPLIER_INFORMATION.getCode());
        }
        //供应商列表(平台方)
        if(NewOperateLogInfoTypeEnum.SUPPLIER_LIST.getCode().equals(type)){
            supplierInformationLogService.systemSaveLog(param,NewOperateLogInfoTypeEnum.SUPPLIER_LIST.getCode());
        }
        //商品库管理上下架(平台方)
        if(NewOperateLogInfoTypeEnum.GOODS_ON_AND_OFF_SHELVES.getCode().equals(type)){
            goodsOnAndOffShelvesService.systemSaveLog(param);
        }
        //商品库管理(平台方)
        if(NewOperateLogInfoTypeEnum.COMMODITY_WAREHOUSE_MANAGEMENT.getCode().equals(type)){
            commodityWarehouseManagementService.systemSaveLog(param);
        }

        //黑白名单管理(平台方)
        if(NewOperateLogInfoTypeEnum.BLACK_AND_WHITE_LIST_MANAGEMENT.getCode().equals(type)){
            blackAndWhiteListManagementService.systemSaveLog(param);
        }
        //合作伙伴列表(平台方)
        if(NewOperateLogInfoTypeEnum.PARTNER_LIST.getCode().equals(type)){
            partnerListService.systemSaveLog(param);
        }
        //合伙伙伴列表下单(平台方)
        if(NewOperateLogInfoTypeEnum.PARTNER_LIST_ORDER.getCode().equals(type)){
            partnerListOrderService.systemSaveLog(param);
        }


    }

}

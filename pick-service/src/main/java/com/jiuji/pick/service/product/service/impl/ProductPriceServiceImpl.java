package com.jiuji.pick.service.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.DateTimeUtils;
import com.ch999.common.util.vo.Result;
import com.jiuji.pick.common.constant.RedisKey;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.service.product.bo.AdmSkuBO;
import com.jiuji.pick.service.product.bo.ProductInRedis;
import com.jiuji.pick.service.product.entity.*;
import com.jiuji.pick.service.product.mapper.ProductInfoMapper;
import com.jiuji.pick.service.product.mapper.ProductMapper;
import com.jiuji.pick.service.product.mapper.ProductPriceMapper;
import com.jiuji.pick.service.product.service.*;
import com.jiuji.pick.service.product.vo.ProductSpecUpdateVO;
import com.jiuji.pick.service.product.vo.StandardsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Service
public class ProductPriceServiceImpl extends ServiceImpl<ProductPriceMapper, ProductPrice> implements ProductPriceService {

    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ProductService productService;
    @Resource
    private ProductInfoMapper productInfoMapper;
    @Resource
    private ProductPriceMapper productPriceMapper;
    @Autowired
    private ProductStInfoService productStInfoService;
    @Autowired
    private ProductSearchService productSearchService;
    @Autowired
    private ProductImageService productImageService;
    @Autowired
    private ProductIndexService productIndexService;


    @Override
    public Result<List<AdmSkuBO>> batchUpdateSku(List<AdmSkuBO> admSkuBOList, boolean isSync) {
        if (CollectionUtils.isEmpty(admSkuBOList)) {
            return Result.error("保存修改数据为空", "保存修改数据为空");
        }
        try {
            List<AdmSkuBO> synDataList = new ArrayList<>();
            for (AdmSkuBO admSkuBO : admSkuBOList) {
                if (admSkuBO == null) {
                    continue;
                }
                if (isSync) {
                    // 处理图片
                    if (StringUtils.isBlank(admSkuBO.getBpic()) && StringUtils.isNotBlank(admSkuBO.getBpicCache())) {
                        admSkuBO.setBpic(admSkuBO.getBpicCache());
                    }
                }
                Result result = ((ProductPriceServiceImpl) AopContext.currentProxy())
                        .updateSingleSku(admSkuBO, isSync, synDataList);
                if (result == null || result.getCode() != 0) {
                    return result;
                }
            }

            //最终数据库中的确认保存的数据构造到productinfo
            productInfoService.saveOrUpdateProductInfo(admSkuBOList.get(0).getProductid());
            //sku信息更新到搜索ES
            List<Long> ppidList = synDataList.stream().map(e->e.getPpriceid()).collect(Collectors.toList());
            productSearchService.addProductBatch(ppidList,false);
            return Result.success(synDataList);
        } catch (Exception e) {
            log.error("批量更新sku异常,入参:" + JSON.toJSONString(admSkuBOList), e);
            return Result.error("系统错误，保存失败！", "系统错误，保存失败！");
        }
    }


    /**
     * 修改单个sku，为了进行事务控制，拆分了这个方法
     */
    @Transactional(rollbackFor = Exception.class)
    public Result updateSingleSku(AdmSkuBO admSkuBO, boolean isSync, List<AdmSkuBO> synDataList) {
        List<ProductImage> images = admSkuBO.getPicList();
        //对象转换
        ProductPrice productPrice = admSkuBOConvertProductPrice(admSkuBO);
        if (isSync) {//如果是数据同步
            ProductPrice existSku = productPriceMapper.getProductPriceByPpid(admSkuBO.getPpriceid());
            //不存在就进行同步插入，新增获取程序异常会有遗漏，支持修改时也可以同步过去
            if (existSku != null) {
                //更新合作伙伴字段（主图 配置 排序 会员价）
                String bpic = productPrice.getBpic();
                String config = productPrice.getConfig();
                Integer rank = productPrice.getRank();
                Double memberPrice = productPrice.getMemberPrice();
                update(new LambdaUpdateWrapper<ProductPrice>()
                        .set(ProductPrice::getBpic, bpic)
                        .set(ProductPrice::getConfig, config)
                        .set(ProductPrice::getRank, rank)
                        .set(ProductPrice::getMemberPrice, memberPrice)
                        .eq(ProductPrice::getPpriceid, productPrice.getPpriceid()));
            } else {//不存在就进行同步插入，新增获取程序异常会有遗漏，支持修改时也可以同步过去
                productPrice.setBuyLimit(1);
                if (admSkuBO.getMainColor() == null) {
                    productPrice.setMainColor(false);
                }
                if (admSkuBO.getOEMPrice() == null) {
                    productPrice.setOemPrice(9999D);
                }
                productPriceMapper.insertProductPirce(productPrice);
            }
        } else {//九机插入或修改sku
            //保存或修改productprice
            productPrice.setBpic("");
            if(StringUtils.isNotBlank(admSkuBO.getBpic())){
                String path = admSkuBO.getBpic().replace("/newstatic/", "");
                productPrice.setBpic(path);
            } else if (CollectionUtils.isNotEmpty(images) && images.get(0) != null && StringUtils
                    .isNotBlank(images.get(0).getImagePath()) && !images.get(0).getDelFlag()) {
                String path = images.get(0).getImagePath().replace("/newstatic/", "");
                productPrice.setBpic(path);
                images.get(0).setDelFlag(true);
            }

            if(productPrice.getPpriceid()==null){
                this.save(productPrice);
            }else{
                baseMapper.updateDelFlag(false,productPrice.getPpriceid());
                this.updateById(productPrice);
            }
            //this.saveOrUpdate(productPrice);//批量更新，mybatis-plus的批量更新有误，数据不多的情况下，进行了循环插入

        }

        //更新product图片
        List<ProductImage> deleteImages=new ArrayList<>();//将删除的图片

        if(CollectionUtils.isNotEmpty(images)){
            Iterator<ProductImage> iterator = images.iterator();
            while (iterator.hasNext()){
                ProductImage image = iterator.next();
                if(image.getDelFlag()){
                    if(image.getId()!=null && image.getId()!=0){
                        deleteImages.add(image);
                    }
                    iterator.remove();
                    continue;
                }
                if(image.getId()!=null && image.getId()!=0){
                    iterator.remove();
                    continue;
                }

                image.setProductid(productPrice.getPpriceid());
                image.setDelFlag(false);
                image.setImgType(2);
                String path = image.getImagePath().replace("/newstatic/","");
                image.setImagePath(path);
            }
        }

        if(CollectionUtils.isNotEmpty(deleteImages)){
            productImageService.saveOrUpdateBatch(deleteImages);
        }
        if(CollectionUtils.isNotEmpty(images)){
            productImageService.saveBatch(images);
        }

        //规格更新
        List<Integer> specIds = admSkuBO.getSpecIds();
        if (CollectionUtils.isNotEmpty(specIds)) {
            ProductSpecUpdateVO productSpecUpdateVO = new ProductSpecUpdateVO();
            productSpecUpdateVO.setPpid(productPrice.getPpriceid());
            productSpecUpdateVO.setSpecIds(specIds);
            productStInfoService.updateProductSpec(productSpecUpdateVO);
        } else {
            QueryWrapper<ProductStInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(ProductStInfo.PPRICEID, productPrice.getPpriceid());
            productStInfoService.remove(queryWrapper);
        }
        //商品排序值
        saveOrUpdateProductIndex(productPrice.getPpriceid(), productPrice.getProductid(),admSkuBO.getSortValue());

        //放入缓存
        setProductInRedisToCache(productPrice.getPpriceid());

        //控制同步给智乐方、亿腾的初始化参数
        admSkuBO.setPpriceid(productPrice.getPpriceid());
        admSkuBO.setAdddate(null);
        admSkuBO.setBgSort(0);
        admSkuBO.setBgSortId(0);
        synDataList.add(admSkuBO);//（同步到智乐方、易腾.....）
        return Result.success();
    }

    @Override
    public void setProductInRedisToCache(long ppid) {
        final ProductInRedis productInRedis = productInfoService.getProductInRedisFromDB(ppid);
        if (productInRedis == null) {
            return;
        }
        stringRedisTemplate.opsForValue()
                .set(RedisKey.PRODUCTINFO + ppid, JSON.toJSONString(productInRedis));
    }

    /**
     * 前端对象转换为后端对象
     */
    private ProductPrice admSkuBOConvertProductPrice(AdmSkuBO admSkuBO) {
        ProductPrice productPrice = new ProductPrice();
        Long ppid = admSkuBO.getPpriceid();
        productPrice.setPpriceid(ppid != null && ppid == 0 ? null : ppid);
        productPrice.setBpic(admSkuBO.getBpic());
        productPrice.setConfig(admSkuBO.getConfig());
        if (admSkuBO.getPpriceid1() != null && admSkuBO.getPpriceid1() > 0) {
            productPrice.setPpriceid1(admSkuBO.getPpriceid1());
        }
        productPrice.setBuyLimit(admSkuBO.getBuyLimit());
        productPrice.setCues(admSkuBO.getCues());
        productPrice.setMainColor(admSkuBO.getMainColor());
        productPrice.setMarketPrice(
                admSkuBO.getMarketPrice() != null ? admSkuBO.getMarketPrice().doubleValue() : 0);
        double costPrice =
                admSkuBO.getCostprice() != null ? admSkuBO.getCostprice().doubleValue() : 0;
        double priceFd = admSkuBO.getPricefd() != null ? admSkuBO.getPricefd().doubleValue() : 0;
        //会员价和成本、幅度会有计算关系，真正的会员价以计算为准 --（已废除幅度计算公式 直接取）
        double memberPrice =
                admSkuBO.getMemberprice() != null ? admSkuBO.getMemberprice().doubleValue() : 0;
        productPrice.setMemberPrice(memberPrice);
        productPrice.setCostPrice(costPrice);
        //干预小店价计算（0-已页面传入为准 1-自动计算）
        int interveneShopPrice =
                admSkuBO.getInterveneShopPrice() != null ? admSkuBO.getInterveneShopPrice() : 0;
        productPrice.setVipPrice(
                admSkuBO.getVipPrice() != null ? admSkuBO.getVipPrice().doubleValue() : 0);
        productPrice.setOemPrice(
                admSkuBO.getOEMPrice() != null ? admSkuBO.getOEMPrice().doubleValue() : 0);
        productPrice.setVip2Price(
                admSkuBO.getVip2Price() != null ? admSkuBO.getVip2Price().doubleValue() : 0);
        productPrice.setQue(admSkuBO.getQue());
        productPrice.setRank(admSkuBO.getSkuRank());
        productPrice.setProductid(admSkuBO.getProductid());
        productPrice.setPricedp(
                admSkuBO.getPricedp() != null ? admSkuBO.getPricedp().doubleValue() : 0);
        productPrice.setPricefd(priceFd);
        productPrice.setNoPromotion(admSkuBO.getNoPromotion());
        productPrice.setAttributes(
                admSkuBO.getSupportRecover() != null && admSkuBO.getSupportRecover() ? 2 : 0);
        productPrice.setIsdel(false);
        if (admSkuBO.getPpriceid() != null && admSkuBO.getPpriceid() > 0) {//更新的情况
            double memberPriceCache =
                    admSkuBO.getMemberpriceCache() != null ? admSkuBO.getMemberpriceCache()
                            .doubleValue() : 0;
            if (memberPriceCache > 0 && memberPrice > 0
                    && String.valueOf(memberPriceCache)
                    .equals(String.valueOf(memberPrice))) {//会员价发生了变化
                productPrice.setUpdatePriceDate(new Date());//会员价变化时间
                productPrice.setPriceflag(memberPrice - memberPriceCache);
            }
        } else {//新增的情况
            productPrice.setPriceflag(0.0);
        }
        productPrice.setDestock(admSkuBO.getDestock() != null ? admSkuBO.getDestock() : 0);
        productPrice.setInterveneShopPrice(interveneShopPrice);
        productPrice.setUpdateTime(LocalDateTime.now());
        if (StringUtils.isNotBlank(admSkuBO.getSaleStartTime())) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            productPrice.setSaleStartTime(LocalDateTime.parse(admSkuBO.getSaleStartTime(), df));
        }
        return productPrice;

    }


    public Boolean saveOrUpdateProductIndex(Long ppid,Long productid,Integer sort) {
        if(sort == null || sort < 0){
            sort = 0;
        }
        ProductIndex productIndex = new ProductIndex();
        if (ppid != null) {
            productIndex.setPpriceid(ppid);
        }
        if (productid != null) {
            productIndex.setProductid(productid);
        }
        productIndex.setSortValue(sort);
        return productIndexService.saveOrUpdate(productIndex);
    }
}

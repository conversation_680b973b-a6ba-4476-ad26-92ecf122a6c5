package com.jiuji.pick.service.user.dto;

import lombok.Data;

/**
 * @Description NEO渠道实体类
 * <AUTHOR>
 * @Date 2021/11/4
 */
@Data
public class NeoSupplierDTO {

    /**
     * 渠道名称
     */
    private String name;
    /**
     * 联系人
     */
    private String contactName;
    /**
     * 联系人电话
     */
    private String contactMobile;
    /**
     * 售后联系人姓名
     */
    private String afterSaleName;
    /**
     * 售后联系人电话
     */
    private String afterSalePhone;
    /**
     * 售后地址
     */
    private String afterSaleAddress;
    /**
     * 城市编码
     */
    private Integer addressCityId;
    /**
     * 地址详细信息
     */
    private String addressCityDetail;
    /**
     * 公司名称(供应商名称)
     */
    private String companyName;

}

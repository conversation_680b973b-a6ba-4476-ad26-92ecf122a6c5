package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 专区商品VO
 * <AUTHOR>
 * @since 2021年11月16日
 */
@Data
public class AreaProductVO {

    private Long id;

    private Long ppid;

    private Long supplierId;

    private Long supplierProductId;

    private String companyName;

    /**
     * 含税价格
     */
    private BigDecimal buyTaxPrice;

    private String productName;

    /**
     * 九机销量
     */
    private Integer saleCount;

    /**
     * 上架时间
     */
    private String productUpTime;

    /**
     * 排序
     */
    private Integer sort;
}

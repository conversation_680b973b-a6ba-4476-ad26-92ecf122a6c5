package com.jiuji.pick.service.product.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.entity.CategoryDiyPcType;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
public interface CategoryDiyPcTypeMapper extends BaseMapper<CategoryDiyPcType> {
    /**
     * 批量保存
     * @param list
     * @return
     */
    int batchInsert(List<CategoryDiyPcType> list);

    /**
     * 批量保存
     * @param list
     * @return
     */
    int batchUpdate(List<CategoryDiyPcType> list);

    /**
     * 批量逻辑删除
     * @param ids
     * @return
     */
    int batchDelete(List<Integer> ids);
}

package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.pick.service.product.entity.ProductPrice;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface ProductPriceMapper extends BaseMapper<ProductPrice> {

    /**
     * 通过id查询sku，可以查看删除的sku （mybatis-plus查询不到删除的sku）
     */
    ProductPrice getProductPriceByPpid(@Param("ppid") Long ppid);

    /**
     * 插入
     */
    @SqlParser(filter = true)
    int insertProductPirce(ProductPrice productPrice);

    Boolean updateDelFlag(@Param("delFlag")Boolean delFlag,@Param("ppriceId")Long ppriceId);
}

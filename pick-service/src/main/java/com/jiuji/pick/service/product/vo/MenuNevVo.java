package com.jiuji.pick.service.product.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class MenuNevVo {

    /**
     * image : //img2.ch999img.com/static/images/icon.png
     * item : [{"link":"","title":"手机通讯"},{"link":"","title":"电脑办公"}]
     * level2 : [{"link":"","title":"iPhone X"},{"link":"","title":"荣耀10"},{"link":"","title":"R15"}]
     * level3 : [{"link":"","title":"热门品牌","children":[{"link":"","title":"iPhone X"},{"link":"","title":"荣耀10"},{"link":"","title":"R15"}]},{"link":"","title":"价位","children":[{"link":"","title":"iPhone X"},{"link":"","title":"荣耀10"},{"link":"","title":"R15"}]},{"link":"","title":"网络制式","children":[{"link":"","title":"iPhone X"},{"link":"","title":"荣耀10"},{"link":"","title":"R15"}]}]
     */

    private String image;
    private List<Item> item;
    private List<Item> level2;
    private List<Level3> level3;

    @NoArgsConstructor
    @Data
    public static class Item {
        /**
         * link :
         * title : 手机通讯
         */

        private String link;
        private String title;
    }

    @NoArgsConstructor
    @Data
    public static class Level3 {
        /**
         * link :
         * title : 热门品牌
         * children : [{"link":"","title":"iPhone X"},{"link":"","title":"荣耀10"},{"link":"","title":"R15"}]
         */

        private String link;
        private String title;
        private List<Item> children;
    }
}

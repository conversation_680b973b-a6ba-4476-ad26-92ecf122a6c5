package com.jiuji.pick.service.user.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车VO
 *
 * <AUTHOR>
 * @since 2021-05-08
 */
@Data
public class FavoriteVO {

    /**
     * 收藏夹
     */
    private List<FavoriteData> favoriteDataList;

    private List<FavoriteProduct> invalidFavoriteProductList;


    @Data
    public static class FavoriteData {
        /**
         * 供应商名称
          */
        private String supplierName;

        /**
         * 供应商id
          */
        private Long supplierId;

        /**
         * 购物车商品列表
         */
        private List<FavoriteProduct> productList;
    }

    @Data
    public static class FavoriteProduct {

        private Long favoriteId;

        /**
         * 商品详情id
          */
        private Long productDetailId;

        /**
         * ppid
         */
        private Long ppid;

        /**
         * 未税
         */
        private BigDecimal buyNoTaxPrice;

        /**
         * 含税
         */
        private BigDecimal buyTaxPrice;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 商品规格
         */
        private String productColor;

        /**
         * 商品图片
         */
        private String productImage;

        /**
         * 是否失效
         */
        private Boolean invalid;

    }

}

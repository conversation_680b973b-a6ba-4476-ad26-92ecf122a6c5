package com.jiuji.pick.service.rpc.cloud;

import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.rpc.fallback.OaOfficeCloudFallbackFactory;
import com.jiuji.pick.service.rpc.vo.PickInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @function:
 * @description: OaStockCloud.java
 * @date: 2021/08/09
 * @author: sunfayun
 * @version: 1.0
 */
@FeignClient(value = "oa-office-service", fallbackFactory = OaOfficeCloudFallbackFactory.class)
public interface OaOfficeCloud {

    @PostMapping("/office/api/partner/finance/list9XunPick/v1")
    Result<List<PickInfoVo>> list9XunPick(@RequestBody List<Long> xtenantIdList);


    @PostMapping("/office/api/partner/finance/listDiyShell/v1")
    Result<List<PickInfoVo>> listDiyShell(@RequestBody List<Long> xtenantIdList);


}

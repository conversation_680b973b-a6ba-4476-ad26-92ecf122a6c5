package com.jiuji.pick.service.order.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DomainsVO {
    private Long xtenant;
    private String tenantName;
    private String businessName;
    private DomainInfo domainInfo;


    @Data
    public static class DomainInfo {
        private String pc;
        private String m;
        private String img;
        private String oa;
        private String moa;
        private String tool;
        private String job;
        private String inwcf;
    }

}

package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @function:
 * @description: IndexProductDetailVo.java
 * @date: 2021/05/24
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class IndexProductDetailVo {

    private Long jiuJiProductId;
    private Long productId;
    private Long ppid;
    private Long supplierProductId;
    private String productName;
    private Integer paymentDay;
    private String imagePath;
    private String bPic;
    private Integer categoryId;
    private String categoryName;
    private Integer brandId;
    private String brandName;
    private String barCode;
    private String productFuture;
    private BigDecimal advicePrice;
    private String productConfig;
    private String attachmentIds;
    private BigDecimal buyNoTaxPrice;
    private BigDecimal buyTaxPrice;
    private String material;
    private Integer deliveryDay;
    private Integer noReasonReturn;
    private Integer changeDay;
    private Integer badPay;
    private Integer lackPay;
    private String afterSalePolicy;
    private String otherPolicy;
    private Integer qualityDate;
    private String boxRule;
    private String detail;
    private String description;
    private String companyNme;
    private String leaderPhone;
    private BigDecimal predictProfit;
    private String address;
    private String productColor;
    private Long supplierUserId;
    private IndexProductDetailSpecVo specList;
    /**
     * 版本
     */
    private IndexProductDetailSpecVo associationList;
    private List<SimilarProductVo> similarProductList;
    private List<FileInfo> fileList;
    private List<FileInfo> imageList;
    /**
     * 九机销量
     */
    private Integer saleCount;
    /**
     * 库存
     */
    private Integer stockCount;
    /**
     * 库存状态，0为预定，1为现货,3为缺货
     */
    private Integer stockStatus;
    /**
     * 商品类型
     */
    private Integer productType;


    /**
     * 收藏id
     */
    private Long favoriteId;

    /**
     * 是否收藏
     */
    private Boolean favorite;

    /**
    * 浮动价格(税前)
    */
    private BigDecimal floatPrice;

    /**
     * 质保政策VO
     */
    private WarrantyPolicyVO warrantyPolicyVO;

    /**
     * 起订量
     */
    private Integer minimumOrderQuantity;

    @Data
    public static class FileInfo {
        private String filePath;
        private String fileName;
        private String fid;
    }

}

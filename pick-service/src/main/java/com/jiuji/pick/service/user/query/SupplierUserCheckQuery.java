package com.jiuji.pick.service.user.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.common.param.BasePageParam;
import lombok.Data;

/**
 * <AUTHOR>
 * @description：
 * @date ：2021/5/6 14:35
 */
@Data
public class SupplierUserCheckQuery extends BasePageParam {

    /***
     * 搜索类型 1 名字 2 id
     */
    private String searchType;

    /***
     * 关键字
     */
    private String keyWord;

    /***
     * 名称
     */
    private Long id;

    /***
     * 名称
     */
    private String name;

    /**
     * 供应商状态
     */
    private Integer status;


    /***
     * 开始时间
     */
    private String createTimeStart;
    /***
     * 结束时间
     */
    private String createTimeEnd;

}

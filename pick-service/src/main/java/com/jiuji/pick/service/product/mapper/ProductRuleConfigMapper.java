package com.jiuji.pick.service.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.service.product.entity.ProductRuleConfig;
import com.jiuji.pick.service.product.param.SearchProductWhiteConfigParam;
import com.jiuji.pick.service.product.vo.QueryIndexWhiteConfigVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
public interface ProductRuleConfigMapper extends BaseMapper<ProductRuleConfig> {


    Page<QueryIndexWhiteConfigVo> conditionSearchWhiteConfig (@Param("page") Page<QueryIndexWhiteConfigVo> page, @Param("param") SearchProductWhiteConfigParam param);



}

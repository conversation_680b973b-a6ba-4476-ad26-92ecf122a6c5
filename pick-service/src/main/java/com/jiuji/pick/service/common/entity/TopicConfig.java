package com.jiuji.pick.service.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;

import com.jiuji.pick.common.annotation.LogFieldInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 专题配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_topic_config")
public class TopicConfig extends Model<TopicConfig> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 专题页名称
     */
    @LogFieldInfo(fieldName = "专题页名称")
    private String name;

    /**
     * 排序
     */
    @LogFieldInfo(fieldName = "排序")
    private Integer sort;

    /**
     * 类型,1：轮播图，2：爆品，3：乐物，4：大件，5：虚拟商品
     */
    private Integer type;

    /**
     * 跳转链接
     */
    @LogFieldInfo(fieldName = "跳转链接")
    private String link;

    /**
     * banner图
     */
    @LogFieldInfo(fieldName = "banner图")
    private String bannerPic;

    /**
     * banner背景色
     */
    @LogFieldInfo(fieldName = "banner背景色")
    private String bannerColor;

    /**
     * 是否生效
     */
    @LogFieldInfo(fieldName = "是否生效")
    private Boolean enabled;

    @TableLogic
    private Boolean isDelete;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

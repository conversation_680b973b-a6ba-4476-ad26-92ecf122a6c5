package com.jiuji.pick.service.product.vo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Author:zuofei
 * @Description
 * @Date 2020-09-14
 */
@Setter
@Getter
@NoArgsConstructor
public class ProductCsBasicVo {
    private Integer id;
    /**
     * 名称
     */
    private String name;
    /**
     * ParamGroup对应ID
     */
    private Integer groups;
    /**
     * 0单行输入1多行输入2单选3多选4图片5日期6数字
     */
    private Integer inputtype;
    /**
     * 排序
     */
    private Integer rank;
    /**
     * 是否纳入前台筛选
     */
    private Boolean isSelect;
    /**
     * 是否商详展示
     */
    private Boolean isDetailShow;
    /**
     * 同类推荐
     */
    private Boolean commonRec;

    /**
     * 是否作为卖点显示
     */
    private Boolean isSellPoint;
    /**
     * 排除对比
     */
    private Boolean excludePK;
}

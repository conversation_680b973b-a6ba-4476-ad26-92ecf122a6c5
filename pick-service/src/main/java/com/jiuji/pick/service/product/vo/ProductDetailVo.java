package com.jiuji.pick.service.product.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class ProductDetailVo {


    /**
     * introduction : <p>许多图文详情</p> params : [{"title":"基本参数","arguments":[{"key":"商品名称","value":"OPPO
     * R11 全网通版"},{"key":"品牌","value":"OPPO"}]}] afterService : {"warranteeInfo":"本产品包换期为：15天，质保期为：12个月","description":"","brandUrl":"http://www.oppo.com/cn/","brandPhone":"************","serviceContent":"<p>富文本内容<\/p>"}
     */

    private String introduction;
    private String introductionUrl;

    public static class Params {

        /**
         * title : 基本参数 arguments : [{"key":"商品名称","value":"OPPO R11 全网通版"},{"key":"品牌","value":"OPPO"}]
         */

        private String title;
        private List<Arguments> arguments;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public List<Arguments> getArguments() {
            return arguments;
        }

        public void setArguments(List<Arguments> arguments) {
            this.arguments = arguments;
        }


        @Data
        public static class Arguments {

            /**
             * key : 商品名称 value : OPPO R11 全网通版
             */

            private String key;
            private String value;
            private String link;

            private Integer inputtype;

            private Boolean detailShow = false;
            /**
             * 图片 新加
             */
            private String imgPath;
        }
    }
}

package com.jiuji.pick.service.order.bo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @function:
 * @description: OrderDeliveryBO.java
 * @date: 2021/06/30
 * @author: sun<PERSON>yun
 * @version: 1.0
 */
@Data
public class OrderDeliveryBO {

    private String expressCode;
    private String expressName;
    private String expressNum;
    @JSONField(name = "sub_id")
    private String subId;
    private String txtDay;
    private String inUser;
    private List<CaigouDetailVo> caigouDetailVoList;

    /***
     *  只有大件用
     */
    private String channelName;

    @JSONField(name = "subId")
    private String subId4Bulk;
    // 收货电话号码
    private String phone;

    @Data
    public static class CaigouDetailVo {
        private Long ppriceid;
        private Integer number;
    }

}

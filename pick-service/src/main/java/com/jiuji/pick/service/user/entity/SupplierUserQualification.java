package com.jiuji.pick.service.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_supplier_user_qualification")
public class SupplierUserQualification extends Model<SupplierUserQualification> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 供应商id
     */
    private Long supplierUserId;

    /**
     * 注册资金
     */
    private String registeredFund;

    /**
     * 企业性质
     */
    private String nature;


    /**
     * 法人
     */
    private String legalPerson;

    /**
     * 商品分类id
     */
    private String categoryId;

    /**
     * 售后联系人姓名
     */
    private String afterSaleName;

    /**
     * 售后联系人电话
     */
    private String afterSalePhone;

    /**
     * 售后地址
     */
    private String afterSaleAddress;

    /**
     * 售后省名称
     */
    private String afterSaleProvinceName;

    /**
     * 售后省id
     */
    private Integer afterSaleProvinceId;


    /**
     * 售后城市名称
     */
    private String afterSaleCityName;

    /**
     * 售后城市id
     */
    private Integer afterSaleCityId;

    /**
     * 售后区名称
     */
    private String afterSaleDistrictName;

    /**
     * 售后区id
     */
    private Integer afterSaleDistrictId;

    /**
     * 大小件商品负责人手机号
     */
    private String leaderPhone;
    /**
     * 虚拟商品负责人手机号
     */
    private String virtualPhone;

    /**
     * 资产商品负责人手机号
     */
    private String assetsPhone;

    /**
     * 财务负责人姓名
     */
    private String financeName;

    /**
     * 财务负责人电话
     */
    private String financePhone;

    /**
     * 主营业务
     */
    private String majorBusinesses;

    /**
     * 合同url
     */
    private String contractUrl;

    private String contractName;


    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

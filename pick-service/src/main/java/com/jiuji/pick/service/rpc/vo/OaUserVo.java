package com.jiuji.pick.service.rpc.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @function:
 * @description: OaUserVo.java
 * @date: 2021/07/01
 * @author: sunfayun
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OaUserVo {

    private Integer id;

    private String userName;

    private String pwdSign;

    private String mobile;

    private String tel;

    private String userMail;

    private Integer userSex;

    private String userLastIP;

    private Date userRegTime;

    private Date userLandTime;

    private Date userBuyTime;

    private Date firstAppLoginTime;

    private String realName;

    private Integer userClass;

    private Integer totalPoInteger;

    private Integer poIntegers;

    private Boolean blackList;

    private Integer erDu;

    private BigDecimal saveMoney;

    private String lastArea;

    private Integer chengZhangZhi;

    private Date birthday;

    private String salt;

    @JsonProperty(value = "isValidate")
    private Boolean validate;


}

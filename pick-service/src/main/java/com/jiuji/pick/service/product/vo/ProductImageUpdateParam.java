package com.jiuji.pick.service.product.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProductImageUpdateParam {

    @NotNull(message = "productId不能为空")
    private Integer productId;

    @NotNull(message = "type不能为空")
    private Integer type;

    @NotNull(message = "list不能传null")
    private List<ProductImageVO> list;

    private String ppids;
}

package com.jiuji.pick.service.order.dto;

import lombok.Data;

import java.util.List;

/**
 * @Description NEO物流单DTO
 * <AUTHOR>
 * @Date 2021/12/17
 */
@Data
public class NeoOrderDeliveryDTO {

    /**
     * 采购单id
     */
    private Long id;

    /**
     * 物流单号
     */
    private String deliveryId;

    /**
     * 快递公司名称
     */
    private String deliveryCompany;

    /**
     * 发货人
     */
    private String sender;

    /**
     * 收货人姓名
     */
    private String receiveName;

    /**
     * 收货人电话
     */
    private String receivePhone;

    /**
     * 预计到达时间
     */
    private String arriveTime;

    /**
     * 发货明细
     */
    private List<GoodsInfo> goodsInfoList;

    @Data
    public static class GoodsInfo {

        // ppid
        private Long ppId;

        // 发货数量
        private Integer goodsCounts;
    }

}

package com.jiuji.pick.service.order.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.service.LargeAndMiddleService;
import com.jiuji.pick.service.order.service.OrderInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service("LargeAndMiddleServiceDevImpl")
public class LargeAndMiddleServiceDevImpl implements LargeAndMiddleService {

    private static final Long SASS=50000L;

    @Resource
    private OrderInfoService orderInfoService;

    @Override
    public String getJumpUrl(PartnerTokenInfo tokenInfo, Long orderNumber) {
        List<OrderInfo> infoList = orderInfoService.lambdaQuery().eq(OrderInfo::getOrderNo, orderNumber)
                .eq(OrderInfo::getChannelType, 0).list();
        if(CollectionUtils.isEmpty(infoList)){
            throw new BizException("订单无效");
        }
        Long xtenantId = infoList.get(0).getXtenantId();
        if(SASS.equals(xtenantId)){
            return "https://test01.oa.saas.ch999.cn/productKC/caigouDetail?sub_id=" + orderNumber;
        }
        return "https://oa.dev.9ji.com/productKC/caigouDetail?sub_id=" + orderNumber;
    }
}

package com.jiuji.pick.service.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.service.user.entity.SupplierUserContact;
import com.jiuji.pick.service.user.mapper.SupplierUserContactMapper;
import com.jiuji.pick.service.user.service.SupplierUserContactService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
@Service
public class SupplierUserContactServiceImpl extends ServiceImpl<SupplierUserContactMapper, SupplierUserContact> implements SupplierUserContactService {


    @Override
    public List<SupplierUserContact> listBySupplierUserId(Long supplierUserId) {
        LambdaQueryWrapper<SupplierUserContact> query = new LambdaQueryWrapper<>();
        query.eq(SupplierUserContact::getSupplierUserId, supplierUserId);
        return baseMapper.selectList(query);
    }
}

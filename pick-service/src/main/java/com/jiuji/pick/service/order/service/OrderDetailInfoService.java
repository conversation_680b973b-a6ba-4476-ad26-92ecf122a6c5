package com.jiuji.pick.service.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.vo.OrderProductVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 采购单详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
public interface OrderDetailInfoService extends IService<OrderDetailInfo> {

    /**
     * 获取订单商品集合
     *
     * @param orderId 订单id
     * @return
     */
    List<OrderProductVO> getOrderProduct(Long orderId);

    /**
     * 根据订单id获取订单详情
     *
     * @param orderId
     * @return
     */
    List<OrderDetailInfo> getOrderDetailByOrderId(Long orderId);

    /**
     * 计算订单总物流费
     * @param orderIdList
     * @return
     */
    Map<Long, BigDecimal> calculateTotalDeliveryFee(List<Long> orderIdList);

    /**
     * 获取指定单号的物流费
     * @param orderId
     * @return
     */
    BigDecimal calculateTotalDeliveryFee(Long orderId);
}

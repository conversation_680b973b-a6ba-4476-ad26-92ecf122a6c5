package com.jiuji.pick.service.common.param;

import com.jiuji.pick.common.param.BasePageParam;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: zhl
 * @Date 2021/05/18
 * @Time 19:50
 */
@Data
public class ConditionalQueryLogInfoParam extends BasePageParam {

    //1:操作人，2：ppid
    private Integer searchType;
    private String keyWord;
    private LocalDateTime startTime;
    private LocalDateTime endTime;

}

package com.jiuji.pick.service.product.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @function:
 * @description: AdmUpdateSupplierProductDetailParam.java
 * @date: 2021/05/19
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class AdmUpdateSupplierProductDetailParamOld extends SupplierAddOrUpdateProductParamOld {

    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    /**
     * 是否收取区域物流费 默认 0
     */
    private Integer remoteDeliveryFee;



}

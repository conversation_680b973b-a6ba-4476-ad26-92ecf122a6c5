package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.entity.CategoryDiy;
import com.jiuji.pick.service.product.param.CategoryDiy4PcItemSaveParam;
import com.jiuji.pick.service.product.param.CategoryDiy4PcTypeSaveParam;
import com.jiuji.pick.service.product.param.CategoryDiySortSaveParam;
import com.jiuji.pick.service.product.vo.MenuNevVo;

import java.util.List;

/**
 * <p>
 * 分类diy主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-31
 */
public interface CategoryDiyService extends IService<CategoryDiy> {

    /**
     * 添加主分类
     * @param diy4PcTypeSave
     * @param staffUserId
     * @return
     */
    boolean saveByPcType(CategoryDiy4PcTypeSaveParam diy4PcTypeSave, Integer staffUserId);

    /**
     * 查询商城diy分类
     * @param
     * @param currentCityId
     * @return
     */
    List<MenuNevVo> getAllClass(Integer currentCityId);

    List<CategoryDiy>  getAll();

    /**
     * 添加子分类
     * @param diy4PcItemSave
     * @param staffUserId
     * @return
     */
    boolean saveByPcItem(CategoryDiy4PcItemSaveParam diy4PcItemSave, Integer staffUserId);

    /**
     * 删除
     * @param id
     * @param type
     * @return
     */
    boolean removeCascade(Integer id, Integer type);

    /**
     * 保持楼层排序
     * @param categoryDiySortSaveParam
     * @param other
     * @param staffUserId
     * @return
     */
    boolean saveSort(List<CategoryDiySortSaveParam> categoryDiySortSaveParam, Integer other, Integer staffUserId);
}

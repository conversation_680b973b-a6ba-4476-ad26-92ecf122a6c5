package com.jiuji.pick.service.order.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.service.common.entity.NotifyLogInfo;
import com.jiuji.pick.service.common.service.NotifyLogInfoService;
import com.jiuji.pick.service.order.service.LargeAndMiddleService;
import com.jiuji.pick.service.order.vo.DomainsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("LargeAndMiddleServiceImpl")
public class LargeAndMiddleServiceImpl implements LargeAndMiddleService {

    @Resource
    private NotifyLogInfoService logInfoService;

    private static final Long SHENZHEN=2000L;
    private static final Long GUANGZHOU =3000L;
    private static final Long SHANGHAI=4000L;


    private Long getFinalTenant(Long tenant){
        Long finalTenant;
        //处理易腾的tenant
        if (SHENZHEN.equals(tenant)) {
            finalTenant = 2003L;
        } else if (GUANGZHOU.equals(tenant)) {
            finalTenant = 3012L;
        } else if (SHANGHAI.equals(tenant)) {
            finalTenant = 4001L;
        } else {
            finalTenant = tenant;
        }
        return finalTenant;
    }

    @Override
    public String getJumpUrl(PartnerTokenInfo tokenInfo, Long orderNumber) {
        Long tenant = tokenInfo.getXtenant();
        String url;
        Long finalTenant=getFinalTenant(tenant);
        //调用研发组接口获取到当前对应的域名
        LocalDate localDate = LocalDateTime.now().toLocalDate();
        NotifyLogInfo notifyLogInfo = new NotifyLogInfo();
        notifyLogInfo.setXTenant(tenant).setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now())
                .setNotifyName("研发组获取域名").setNotifyParam("xtenant:" + finalTenant);
        String res = "";
        try {
            res = HttpUtil.createGet("https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1")
                    .header("token", DigestUtils.md5Hex(localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))))
                    .form("xtenant", finalTenant)
                    .execute().body();
        } catch (Exception e) {
            log.error("调用研发组接口获取域名异常，传入参数[{}],返回结果[{}]", finalTenant, JSONUtil.toJsonStr(res), e);
            throw new BizException("获取域名异常,请稍后再试");
        } finally {
            notifyLogInfo.setNotifyResult(JSONUtil.toJsonStr(res));
            logInfoService.saveNotifyLogInfo(notifyLogInfo);
        }
        //获取结果格式解析
        JSONObject jsonObject = JSON.parseObject(res);
        Integer code = jsonObject.getInteger("code");
        if (code != 0) {
            throw new BizException("获取域名接口：" + jsonObject.getString("userMsg"));
        }
        String data = jsonObject.getString("data");
        if (StringUtils.isEmpty(data)) {
            throw new BizException("获取域名接口返回域名信息为空，请稍后再试");
        }
        List<DomainsVO> domainsList = JSON.parseObject(data, new TypeReference<List<DomainsVO>>() {
        });
        if (CollectionUtils.isEmpty(domainsList)) {
            throw new BizException("获取域名接口返回域名为空，请稍后再试");
        }
        String oaUrl = Optional.ofNullable(domainsList.get(0).getDomainInfo()).orElse(new DomainsVO.DomainInfo()).getOa();
        url = "https://" + oaUrl + "/productKC/caigouDetail?sub_id=" + orderNumber;
        return url;
    }
}

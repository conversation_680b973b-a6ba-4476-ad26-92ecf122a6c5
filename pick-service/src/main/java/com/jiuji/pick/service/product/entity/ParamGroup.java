/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
@Data
@TableName("t_param_group")
public class ParamGroup extends Model<ParamGroup> {

    public static final String ID = "id";
    public static final String CATEID = "cate_id";
    public static final String GROUPNAME = "group_name";
    public static final String RANK = "`rank`";
    public static final String ISDEL = "is_del";
    public static final String TMP = "tmp";
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer cateId;

    private String groupName;

    @TableField("`rank`")
    private Integer rank;

    @TableLogic
    private Integer isDel;

    private Integer tmp;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

package com.jiuji.pick.service.product.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @function:
 * @description: ProductUpOrDownParam.java
 * @date: 2021/05/08
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class ProductUpOrDownParam {

    /**
     * 操作类型，1：上架，2：下架
     */
    @NotNull(message = "操作类型不能为空")
    private Integer type;

    /**
     * 商品ppid列表
     */
    @NotEmpty(message = "商品ppid列表不能为空")
    private List<Long> ppidList;

}

package com.jiuji.pick.service.product.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.entity.CategorySearchWord;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *  分类所有关键字
 * <AUTHOR>
 * @since 2020-10-20
 */
public interface CategorySearchWordService extends IService<CategorySearchWord> {

    /**
     * 根绝关键字获取分类id
     *
     * @param keywords
     * @return
     */
    List<Integer> getCategoryIdsByKeyword(List<String> keywords);

    /**
     * 查询所有分类集合信息
     * @return
     */
    List<CategorySearchWord> listAll();
}

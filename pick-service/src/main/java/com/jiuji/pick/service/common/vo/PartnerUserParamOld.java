package com.jiuji.pick.service.common.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 合作伙伴信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PartnerUserParamOld {


    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 登录用户ID
     */
    private String outUserId;

    /**
     * 租户ID
     */
    private Long xtenant;

    /**
     * 租户来源，oa/小型SAAS
     */
    private Integer source;

    /**
     * 租户域名
     */
    private String host;

    /**
     * 名称
     */
    private String name;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 状态，0 正常 1 禁用
     */
    private Boolean status;

    /**
     * 业务联系人姓名
     */
    private String businessName;

    /**
     * 业务联系人电话
     */
    private String businessPhone;

    /**
     * 开票抬头
     */
    private String invoiceTitle;

    /**
     * 开票税号
     */
    private String invoiceNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */

    private Boolean delFlag;

}

package com.jiuji.pick.service.order.service.impl;

import com.jiuji.pick.common.enums.OrderLogTypeEnum;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.entity.OrderInfoLog;
import com.jiuji.pick.service.order.mapper.OrderInfoLogMapper;
import com.jiuji.pick.service.order.service.OrderInfoLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 订单操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-20
 */
@Service
@Slf4j
public class OrderInfoLogServiceImpl extends ServiceImpl<OrderInfoLogMapper, OrderInfoLog> implements OrderInfoLogService {

    @Override
    public void errorLog(boolean orderUpdate, boolean productVersionUpdate, boolean orderDetailUpdate, boolean remove, OrderInfo orderInfo, Long orderNo, List<Long> cartIdList) {
        if (!orderUpdate) {
            log.error("保存更新采购单失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!productVersionUpdate) {
            log.error("保存更新商品快照失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!orderDetailUpdate) {
            log.error("保存更新订单详情失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!remove) {
            log.error("删除购物车失败，cartIdList:{}", cartIdList);
        }
    }

    @Override
    public void saveOrderLog(String content, OrderLogTypeEnum type, Long orderId, Long userId, String userName) {
        OrderInfoLog orderInfoLog = new OrderInfoLog();
        orderInfoLog.setContent(content);
        orderInfoLog.setType(type.getCode());
        orderInfoLog.setOrderId(orderId);
        orderInfoLog.setOperationUserId(userId);
        orderInfoLog.setOperationUserName(userName);
        orderInfoLog.insert();
    }
}

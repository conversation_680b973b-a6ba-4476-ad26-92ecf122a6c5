package com.jiuji.pick.service.order.param;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 提交订单参数
 *
 * <AUTHOR>
 * @since 2021-5-12
 */
@Data
public class CartOrderParam {

    /**
     * 门店id
     */
    @NotBlank(message = "门店id不能为空")
    private String addressId;

    /**
     * 门店名称
     */
    @NotBlank(message = "门店名称不能为空")
    private String addressName;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空")
    private String contactPerson;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    @Size(max = 20, message = "联系电话长度太长")
    private String contactPhone;

    /**
     * 收货地址
     */
    @NotBlank(message = "收货地址不能为空")
    private String receiveAddress;

    /**
     * 收货cityId
     */
    @NotBlank(message = "收货地址cityId不能为空")
    private String cityId;

    /**
     * 授权隔离ID
     */
    @NotNull(message = "授权隔离ID不能为空")
    private Integer authId;

    /**
     * 购物车信息
     */
    @Valid
    @NotEmpty(message = "请选择要下单的商品")
    private List<CartOrderData> cartOrderDataList;

    @Data
    public static class CartOrderData {
        /**
         * 购物车id
         */
        @NotNull(message = "请选择要下单的商品")
        private Long cartId;

        /**
         * 商品数量
         */
        @NotNull(message = "商品数量不能为空")
        private Integer productCount;

        /**
         * 价格类型
         */
        @NotNull(message = "价格类型不能为空")
        private Integer priceType;
    }
}

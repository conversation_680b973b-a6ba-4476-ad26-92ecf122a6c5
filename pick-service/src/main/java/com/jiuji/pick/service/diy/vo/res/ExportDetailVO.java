package com.jiuji.pick.service.diy.vo.res;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ExportDetailVO {

    /**
     * 采购方id
     */
    private String userid;

    /**
     * 采购方
     */
    private String userName;

    /**
     * kind
     */
    private String kind;


    /**
     * 订单id
     */
    private String subId;
    /**
     * 商品ppid
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品规格
     */
    private String productColor;
    /**
     * 数量
     */
    private Integer count;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    /**
     * 时间
     */
    private String time;
    /**
     * 备注
     */
    private String remark;
}

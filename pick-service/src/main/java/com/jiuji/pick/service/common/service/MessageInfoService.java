package com.jiuji.pick.service.common.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.MessageInfo;
import com.jiuji.pick.service.common.param.MessageInfoQuery;
import com.jiuji.pick.service.common.param.MessageInfoSaveParam;
import com.jiuji.pick.service.common.param.QueryUserMessageParam;
import com.jiuji.pick.service.common.param.UserMessageDealParam;
import com.jiuji.pick.service.common.vo.*;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-08
 */
public interface MessageInfoService extends IService<MessageInfo> {

    /**
     * 告示牌(通知)
     * @return
     */
    QueryMessageStatusVo noticeSign();

    /**
     * 查询待上架的商品
     * @return
     */
    int queryPickProductStatus();

    /**
     * 查询待审核的供应商
     * @return
     */
    int querySupplierStatus();

    /**
     * 供应商供应商品审核
     */
    int queryBindStatus();

    /**
     * 新增站内信通知
     * @param param
     */
    Result saveMessageInfo(MessageInfoSaveParam param);

    /**
     * 查看通知详情
     * @return
     */
    Result<QueryMessageInfoDetailsVo> queryMessageDetails(Long id);

    /**
     * 查询用户消息列表
     * @param param
     * @return
     */
    Result<Page<UserMessageInfoVo>> queryUserMessageInfo(QueryUserMessageParam param);
    /***
     * @description: 列表 - 分页
     * @Param: [query]
     * @author: Lbj
     * @date: 2021/5/28 11:19
     */
    Page pageList(MessageInfoQuery messageInfoQuery);

    /**
     * 站内信处理，标记已读，全部已读
     * @param param
     * @return
     */
    Result<String> userMessageDeal(UserMessageDealParam param);

    /**
     * 查询未读消息记录条数
     * @param type
     * @return
     */
    Result<UserCountInfoReq> getUnreadMessageCount(Integer type);
}

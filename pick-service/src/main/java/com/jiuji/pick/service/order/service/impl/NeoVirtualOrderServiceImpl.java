package com.jiuji.pick.service.order.service.impl;

import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.constant.OrderTipConstant;
import com.jiuji.pick.common.enums.OrderLogTypeEnum;
import com.jiuji.pick.common.enums.OrderStatusEnum;
import com.jiuji.pick.common.enums.OrderTypeEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.GenerateNoUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.DeliveryFeeArea;
import com.jiuji.pick.service.common.service.DeliveryFeeAreaService;
import com.jiuji.pick.service.order.bo.DeliveryProductBO;
import com.jiuji.pick.service.order.dto.NeoOrderDTO;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.entity.ProductOrderVersion;
import com.jiuji.pick.service.order.param.CartOrderParam;
import com.jiuji.pick.service.order.param.OrderDeliveryParam;
import com.jiuji.pick.service.order.service.*;
import com.jiuji.pick.service.order.vo.CartInfoVO;
import com.jiuji.pick.service.order.vo.UpdateOrderPriceVo;
import com.jiuji.pick.service.user.service.SupplierChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service(value = "NeoVirtualOrderServiceImpl")
@Slf4j
public class NeoVirtualOrderServiceImpl extends NeoOrderServiceImpl implements NeoVirtualOrderService {
    @Resource
    private DeliveryFeeAreaService deliveryFeeAreaService;
    @Resource
    private OrderInfoService orderInfoService;
    @Resource
    private SupplierChannelService supplierChannelService;
    @Resource
    private OrderInfoLogService orderInfoLogService;
    @Resource
    private OrderDetailInfoService orderDetailInfoService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private ProductOrderVersionService productOrderVersionService;
    @Resource
    private CartInfoService cartInfoService;


    private void checkOrder(UpdateOrderPriceVo updateOrderPriceVoList) {
        Long orderId = Optional.ofNullable(updateOrderPriceVoList.getOrderId()).orElseThrow(() -> new BizException("订单id不能为空"));
        OrderInfo orderInfo = Optional.ofNullable(orderInfoService.getById(orderId)).orElseThrow(() -> new BizException("订单" + orderId + "无效"));
        Integer channelType = orderInfo.getChannelType();
        if(channelType==0){
            throw new BizException("该采购单为中型的采购单，暂不持支改价功能");
        }
        Integer orderType = orderInfo.getOrderType();
        Long orderNo = orderInfo.getOrderNo();
        updateOrderPriceVoList.setOrderNo(orderNo);
        if (OrderTypeEnum.VIRTUAL.getCode() != orderType) {
            throw new BizException("订单" + orderNo + "不属于虚拟商品类型订单，暂不支持改价功能");
        }
        //判断订单类型是不是虚拟商品
        Integer orderStatus = orderInfo.getOrderStatus();
        List<Integer> status = Arrays.asList(OrderStatusEnum.DEFAULT.getCode(), OrderStatusEnum.AUDITED.getCode());
        if (!status.contains(orderStatus)) {
            throw new BizException("订单" + orderNo + "状态不支持改价功能，（取消，已发货，完成状态下不持支该改价）");
        }
        List<UpdateOrderPriceVo.UpdateOrderPriceDetailVo> detailVoList = updateOrderPriceVoList.getDetailVoList();
        if (CollectionUtils.isEmpty(detailVoList)) {
            throw new BizException("改价详情不能为空");
        }
        //通过快照校验价格
        productOrderVersionService.checkOrderUpdatePrice(updateOrderPriceVoList);

    }


    /**
     * 小件订单改价
     *
     * @param updateOrderPriceVoList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPrice(UpdateOrderPriceVo updateOrderPriceVoList) {
        // 改价之前的校验
        checkOrder(updateOrderPriceVoList);
        StringBuilder comment = new StringBuilder();
        OrderInfo orderInfo = orderInfoService.getById(updateOrderPriceVoList.getOrderId());
        BigDecimal totalPriceOld = orderInfo.getTotalPrice();
        BigDecimal totalPriceNew = new BigDecimal("0.00");
        List<UpdateOrderPriceVo.UpdateOrderPriceDetailVo> detailVoList = updateOrderPriceVoList.getDetailVoList();
        for (UpdateOrderPriceVo.UpdateOrderPriceDetailVo item : detailVoList) {
            BigDecimal productNewPrice = item.getProductNewPrice();
            BigDecimal productOldPrice = item.getProductOldPrice();
            BigDecimal multiply = productNewPrice.multiply(new BigDecimal(item.getBuyAmount() + ""));
            totalPriceNew=totalPriceNew.add(multiply);
            //OrderDetailInfo表修改
            orderDetailInfoService.lambdaUpdate().eq(OrderDetailInfo::getId, item.getOrderDetailInfoId())
                    .set(OrderDetailInfo::getProductPrice, productNewPrice).update();

            //拼接修改详情日志
            if(productOldPrice.compareTo(productNewPrice)!=0){
                comment.append(item.getProductName()).append("价格由【").append(productOldPrice).append("】改为【").append(productNewPrice).append("】。");
            }

        }
        //OrderInfo表修改
        orderInfoService.lambdaUpdate().eq(OrderInfo::getId, orderInfo.getId())
                .eq(OrderInfo::getTotalPrice, totalPriceOld)
                .set(OrderInfo::getTotalPrice, totalPriceNew)
                .update();
        //拼接修改总价日志
        if(totalPriceOld.compareTo(totalPriceNew)!=0){
            comment.append("订单总价由【").append(totalPriceOld).append("】修改为【").append(totalPriceNew).append("】。");
        }
        SupplierTokenInfo tokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck())
                .orElseThrow(() -> new BizException("登录信息失效，请重新登录"));
        //操作日志记录
        orderInfoLogService.saveOrderLog(comment.toString(), OrderLogTypeEnum.OTHER, orderInfo.getId(),
                tokenInfo.getId(), tokenInfo.getLoginName());
    }

    @Override
    public String createOrder(CartInfoVO.CartInfoData cartInfoData, PartnerTokenInfo partnerTokenInfo, CartOrderParam cartOrderParam) {
        // 参数预设，区分含税未税
        List<NeoOrderDTO.PurchaseProduct> productListTax = Lists.newArrayList();
        List<NeoOrderDTO.PurchaseProduct> productListNoTax = Lists.newArrayList();
        List<OrderDetailInfo> orderDetailInfoListTax = Lists.newArrayList();
        List<OrderDetailInfo> orderDetailInfoListNoTax = Lists.newArrayList();
        List<ProductOrderVersion> productOrderVersionListTax = Lists.newArrayList();
        List<ProductOrderVersion> productOrderVersionListNoTax = Lists.newArrayList();
        List<Long> cartIdsTax = Lists.newArrayList();
        List<Long> cartIdsNoTax = Lists.newArrayList();
        BigDecimal totalPriceTax = BigDecimal.ZERO;
        BigDecimal totalPriceNoTax = BigDecimal.ZERO;
        Long supplierId = cartInfoData.getSupplierId();
        Integer productType = cartInfoData.getProductType();
        DeliveryFeeArea deliveryFeeArea = deliveryFeeAreaService.getFeeArea(cartOrderParam.getCityId());

        // 获取渠道ID
        String channelId = supplierChannelService.getNeoChannelId(partnerTokenInfo.getXtenant(), supplierId, partnerTokenInfo.getHost(), partnerTokenInfo.getToken());
        if (StringUtils.isBlank(channelId)) {
            return cartInfoData.getSupplierName() + " 渠道id获取失败!";
        }
        NeoOrderDTO neoOrderTaxDTO = createNeoOrderDTO(cartInfoData.getSupplierName(), channelId, cartOrderParam.getAddressId());
        // 构建商品&订单数据
        for (CartInfoVO.CartProduct cartProduct : cartInfoData.getProductList()) {

            // 采购价
            Integer priceType = cartProduct.getPriceType();
            BigDecimal price = Integer.valueOf(1).equals(priceType) ? Optional.ofNullable(cartProduct.getBuyTaxPrice()).orElse(BigDecimal.ZERO) : Optional.ofNullable(cartProduct.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO);

            // 订单详情
            OrderDetailInfo orderDetailInfo = orderInfoService.createSaveInfo(cartProduct, partnerTokenInfo, supplierId, price, null, OrderDetailInfo.class);
            // POV
            ProductOrderVersion productOrderVersion = orderInfoService.createSaveInfo(cartProduct, partnerTokenInfo, supplierId, null, priceType, ProductOrderVersion.class);
            // 采购商品
            NeoOrderDTO.PurchaseProduct purchaseProduct = createProductGroup(cartProduct, price);

            // 物流费
            if (cartProduct.getRemoteDeliveryFee() == 1 && deliveryFeeArea != null) {
                BigDecimal deliveryFee = deliveryFeeArea.getDeliveryFee().multiply(BigDecimal.valueOf(cartProduct.getProductCount()));
                purchaseProduct.setDeliveryFee(deliveryFeeArea.getDeliveryFee());
                orderDetailInfo.setDeliveryFee(deliveryFee);
                if (Integer.valueOf(1).equals(priceType)) {
                    totalPriceTax = totalPriceTax.add(deliveryFee);
                } else {
                    totalPriceNoTax = totalPriceNoTax.add(deliveryFee);
                }
            }

            // 这里根据NEO系统情况 进行含税和未税拆分
            if (Integer.valueOf(1).equals(priceType)) {
                cartIdsTax.add(cartProduct.getId());
                productListTax.add(purchaseProduct);
                orderDetailInfoListTax.add(orderDetailInfo);
                productOrderVersionListTax.add(productOrderVersion);
                totalPriceTax = totalPriceTax.add(BigDecimal.valueOf(cartProduct.getProductCount()).multiply(price));
            } else {
                cartIdsNoTax.add(cartProduct.getId());
                productListNoTax.add(purchaseProduct);
                orderDetailInfoListNoTax.add(orderDetailInfo);
                productOrderVersionListNoTax.add(productOrderVersion);
                totalPriceNoTax = totalPriceNoTax.add(BigDecimal.valueOf(cartProduct.getProductCount()).multiply(price));
            }
        }
        neoOrderTaxDTO.setProduct(productListTax);
        NeoOrderDTO neoOrderNoTaxDTO = new NeoOrderDTO();
        BeanUtils.copyProperties(neoOrderTaxDTO, neoOrderNoTaxDTO);
        neoOrderNoTaxDTO.setProduct(productListNoTax);
        neoOrderNoTaxDTO.setInvoiceFlag(false);

        OrderInfo orderInfoTax = createOrderInfo(partnerTokenInfo, channelId, cartOrderParam, supplierId, neoOrderTaxDTO, productType, totalPriceTax);
        OrderInfo orderInfoNoTax = new OrderInfo();
        BeanUtils.copyProperties(orderInfoTax, orderInfoNoTax);
        orderInfoNoTax.setTotalPrice(totalPriceNoTax);
        String taxResult = null;
        String noTaxResult = null;
        try {
            taxResult = saveAndSyncVirtual(neoOrderTaxDTO, partnerTokenInfo, orderInfoTax, orderDetailInfoListTax, productOrderVersionListTax,cartIdsTax);
            noTaxResult = saveAndSyncVirtual(neoOrderNoTaxDTO, partnerTokenInfo, orderInfoNoTax, orderDetailInfoListNoTax, productOrderVersionListNoTax,cartIdsNoTax);
        } catch (Exception e) {
            log.error("调用NEO创建订单异常,exception{}, result1:{}, result2:{}", e, taxResult, noTaxResult);
            Thread.currentThread().interrupt();
            return cartInfoData.getSupplierName() + " 的采购单生成失败！";
        }
        return Optional.ofNullable(taxResult).orElse(noTaxResult);
    }


    private String saveAndSyncVirtual(NeoOrderDTO neoOrderDTO, PartnerTokenInfo partnerTokenInfo, OrderInfo orderInfo, List<OrderDetailInfo> orderDetailInfoList,
                               List<ProductOrderVersion> productOrderVersionList,List<Long> cartIdList) {
        if (CollectionUtils.isEmpty(neoOrderDTO.getProduct())) {
            return StringUtils.EMPTY;
        }
        //虚拟商品内部流程
        orderInfo.setOrderStatus(OrderStatusEnum.AUDITED.getCode());
        long orderNo = GenerateNoUtil.generateOrderNo();
        orderInfo.setOrderNo(orderNo);
        // 更新
        productOrderVersionList.forEach(productOrderVersion -> productOrderVersion.setOrderNo(orderNo));
        orderDetailInfoList.forEach(orderDetailInfo -> orderDetailInfo.setOrderNo(orderNo));
        boolean saveResult = saveOrderInfo(orderInfo, orderDetailInfoList, productOrderVersionList);
        if (!saveResult) {
            return OrderTipConstant.SAVE_ERROR;
        }
        orderInfoLogService.saveOrderLog("订单已生成，等待生成采购单", OrderLogTypeEnum.CREATE, orderInfo.getId(),
                partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());

        // 保存操作日志
        orderInfoLogService.saveOrderLog("采购单已生成，采购单号：" + orderNo, OrderLogTypeEnum.GENERATE_NO, orderInfo.getId(),
                partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());

        boolean remove = cartInfoService.removeByIds(cartIdList);
        orderInfoLogService.errorLog(saveResult, true, true, remove, orderInfo, orderNo, cartIdList);
        return null;
    }


    @Override
    public Result<String> cancelOrder(Long orderNo, Long xTenant) {
        return Result.success();
    }

    @Override
    public Result<String> deliveryProduct(List<DeliveryProductBO> deliveryProductBOList, OrderInfo orderInfo, OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo) {
        return Result.success();
    }
}

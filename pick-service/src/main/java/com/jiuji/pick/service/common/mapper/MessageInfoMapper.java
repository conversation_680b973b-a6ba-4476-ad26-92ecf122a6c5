package com.jiuji.pick.service.common.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.service.common.entity.MessageInfo;
import com.jiuji.pick.service.common.vo.UserMessageInfoVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-08
 */
public interface MessageInfoMapper extends BaseMapper<MessageInfo> {

    Page<UserMessageInfoVo> queryUserMessageInfo(@Param("page") Page<UserMessageInfoVo> page,
                                                 @Param("userId") Long userId);


}

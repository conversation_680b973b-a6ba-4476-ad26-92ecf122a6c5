package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.product.entity.SupplierProductPriceModify;

/**
 *
 * <AUTHOR>
 * @Date 2021/8/23
 */
public interface SupplierProductPriceModifyService extends IService<SupplierProductPriceModify> {

    /**
     * 根据供应商商品详情Id 查询商品最新改价记录
     * @param supplierProductDetailId 供应商商品详情Id
     * @param queryTime 是否设置查询时间
     * @param enable 是否生效
     * @return com.jiuji.pick.service.product.entity.SupplierProductPriceModify
     */
    SupplierProductPriceModify getSupplierProductPriceModify(Long supplierProductDetailId, Boolean queryTime, Boolean enable);
}

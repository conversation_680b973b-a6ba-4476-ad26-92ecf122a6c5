/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
@Data
@TableName("t_param_value")
public class ParamValue extends Model<ParamValue> {

    public static final String ID = "id";
    public static final String PARAMID = "param_id";
    public static final String VALUE = "value";
    public static final String RANK = "`rank`";
    public static final String VALDES = "valdes";
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer paramId;

    private String value;
    @TableField("`rank`")
    private Integer rank;

    private String valdes;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

package com.jiuji.pick.service.order.bo;

import com.jiuji.pick.service.common.entity.MessageInfo;
import com.jiuji.pick.service.common.entity.MessageInfoUser;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.entity.OrderInfoLog;
import lombok.Data;

import java.util.List;

/**
 * @Description 订单收货信息BO
 * <AUTHOR>
 * @Date 2021/10/18
 */
@Data
public class OrderReceiveBO {
    /**
     * 订单更新list
     */
    private List<OrderInfo> orderInfoList;
    /**
     * 订单日志list
     */
    private List<OrderInfoLog> orderInfoLogList;
    /**
     * 站内信息list
     */
    private List<MessageInfo> messageInfoList;
    private List<MessageInfoUser> messageInfoUserList;
}

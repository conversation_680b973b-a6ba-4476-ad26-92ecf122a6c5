package com.jiuji.pick.service.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.NewOperateLogInfo;
import com.jiuji.pick.service.common.vo.SaveLogVo;
import com.jiuji.pick.service.common.vo.SelectLogVo;

import java.util.List;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
public interface NewOperateLogInfoService extends IService<NewOperateLogInfo> {

    /**
     * @param saveLogVo
     */
    void saveLog(SaveLogVo saveLogVo);
    /**
     * @param newOperateLogInfo
     */
    void saveSystemLog(NewOperateLogInfo newOperateLogInfo);

    Result<List<NewOperateLogInfo>> selectLog(SelectLogVo saveLogVo);
}

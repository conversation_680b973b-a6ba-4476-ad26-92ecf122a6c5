package com.jiuji.pick.service.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.jiuji.cloud.office.dto.AddAssetsSaleOrderDTO;
import com.jiuji.cloud.office.service.AssetsOrderCloud;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.bo.SupplierTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.constant.OrderTipConstant;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.DeliveryFeeArea;
import com.jiuji.pick.service.common.entity.MessageInfo;
import com.jiuji.pick.service.common.entity.MessageInfoUser;
import com.jiuji.pick.service.common.service.DeliveryFeeAreaService;
import com.jiuji.pick.service.common.service.MessageInfoService;
import com.jiuji.pick.service.common.service.MessageInfoUserService;
import com.jiuji.pick.service.order.bo.DeliveryProductBO;
import com.jiuji.pick.service.order.bo.OrderReceiveBO;
import com.jiuji.pick.service.order.entity.OrderDetailInfo;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.entity.OrderInfoLog;
import com.jiuji.pick.service.order.entity.ProductOrderVersion;
import com.jiuji.pick.service.order.param.CartOrderParam;
import com.jiuji.pick.service.order.param.OrderDeliveryParam;
import com.jiuji.pick.service.order.service.*;
import com.jiuji.pick.service.order.vo.CartInfoVO;
import com.jiuji.pick.service.order.vo.UpdateOrderPriceVo;
import com.jiuji.pick.service.product.entity.Category;
import com.jiuji.pick.service.product.entity.PickProduct;
import com.jiuji.pick.service.product.service.CategoryService;
import com.jiuji.pick.service.product.service.PickProductService;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.mapper.PartnerUserMapper;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产订单业务处理
 * @function:
 * @description: AssetsOrderServiceImpl.java
 * @date: 2021/10/09
 * @author: sunfayun
 * @version: 1.0
 */
@Service("AssetsOrderServiceImpl")
@Slf4j
public class AssetsOrderServiceImpl implements AssetsOrderService {

    /**
     * 默认发货时间
     */
    private static final int DEFAULT_DELIVERY_DAY = 7;

    /**
     * 商品备注
     */
    private static final String PRODUCT_COMMENT = "采货王商品";

    @Resource
    private CartInfoService cartInfoService;
    @Resource
    private ProductOrderVersionService productOrderVersionService;
    @Resource
    private OrderDetailInfoService orderDetailInfoService;
    @Resource
    private OaService oaService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private DeliveryFeeAreaService deliveryFeeAreaService;
    @Resource
    private OrderInfoLogService orderInfoLogService;
    @Resource
    private PickProductService pickProductService;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private PartnerUserMapper partnerUserMapper;
    @Resource
    private MessageInfoUserService messageInfoUserService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private OrderInfoService orderInfoService;
    @Resource
    private MessageInfoService messageInfoService;
    @Resource
    private AssetsOrderCloud assetsOrderClient;


    /**
     * 资产改价功能
     * @param updateOrderPriceVoList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPrice(UpdateOrderPriceVo updateOrderPriceVoList) {
        OrderInfo orderInfo = orderInfoService.getById(updateOrderPriceVoList.getOrderId());
        BigDecimal totalPriceOld = orderInfo.getTotalPrice();
        BigDecimal totalPriceNew = new BigDecimal("0.00");
        List<UpdateOrderPriceVo.UpdateOrderPriceDetailVo> detailVoList = updateOrderPriceVoList.getDetailVoList();
        StringBuilder comment = new StringBuilder();
        for (UpdateOrderPriceVo.UpdateOrderPriceDetailVo item : detailVoList) {
            BigDecimal productNewPrice = item.getProductNewPrice();
            BigDecimal productOldPrice = item.getProductOldPrice();
            BigDecimal multiply = productNewPrice.multiply(new BigDecimal(item.getBuyAmount() + ""));
            totalPriceNew=totalPriceNew.add(multiply);
            //OrderDetailInfo表修改
            orderDetailInfoService.lambdaUpdate().eq(OrderDetailInfo::getId, item.getOrderDetailInfoId())
                    .set(OrderDetailInfo::getProductPrice, productNewPrice).update();

            //拼接修改详情日志
            if(productOldPrice.compareTo(productNewPrice)!=0){
                comment.append(item.getProductName()).append("价格由【").append(productOldPrice).append("】改为【").append(productNewPrice).append("】。");
            }

        }
        //OrderInfo表修改
        orderInfoService.lambdaUpdate().eq(OrderInfo::getId, orderInfo.getId())
                .eq(OrderInfo::getTotalPrice, totalPriceOld)
                .set(OrderInfo::getTotalPrice, totalPriceNew)
                .update();
        //拼接修改总价日志
        if(totalPriceOld.compareTo(totalPriceNew)!=0){
            comment.append("订单总价由【").append(totalPriceOld).append("】修改为【").append(totalPriceNew).append("】。");
        }
        // 保存日志
        SupplierTokenInfo tokenInfo = Optional.ofNullable(currentRequestComponent.getSupplierTokenInfoWithoutCheck())
                .orElseThrow(() -> new BizException("登录信息失效，请重新登录"));
        orderInfoLogService.saveOrderLog(comment.toString(), OrderLogTypeEnum.OTHER, orderInfo.getId(),
                tokenInfo.getId(), tokenInfo.getLoginName());
        //todo 调用办公组接口改价同步功能 若办公返回接口code不为0 抛出办公的异常信息

        com.jiuji.cloud.office.vo.UpdateOrderPriceVo vo =new com.jiuji.cloud.office.vo.UpdateOrderPriceVo();
        BeanUtils.copyProperties(updateOrderPriceVoList,vo);
        R<Boolean> booleanR = assetsOrderClient.updateOrderPrice(vo);
        if (Objects.isNull(booleanR)
                ||booleanR.getCode()!=0){
            throw new BizException("调用办公组接口改价同步失败:"+booleanR.getUserMsg());
        }
    }


    @Override
    public String createOrder(CartInfoVO.CartInfoData cartInfoData, PartnerTokenInfo partnerTokenInfo, CartOrderParam cartOrderParam) {
        Long supplierId = cartInfoData.getSupplierId();
        // 获取渠道商id 资产没有渠道 给默认渠道
        String channelId = CommonConstant.DEFAULT_CHANNEL_ID;

        // 交易快照
        List<ProductOrderVersion> productOrderVersionList = new ArrayList<>();
        // 订单详情
        List<OrderDetailInfo> orderDetailInfoList = new ArrayList<>();
        // 购物车id集合
        List<Long> cartIdList = new ArrayList<>();
        // 总价
        BigDecimal totalPrice = BigDecimal.ZERO;

        DeliveryFeeArea deliveryFeeArea = deliveryFeeAreaService.getFeeArea(cartOrderParam.getCityId());

        Integer orderType = null;
        if(Integer.valueOf(ProductTypeEnum.FIX_ASSETS.getCode()).equals(cartInfoData.getProductType())){
            orderType = OrderTypeEnum.FIX_ASSETS.getCode();
        }else if(Integer.valueOf(ProductTypeEnum.COMMON_ASSETS.getCode()).equals(cartInfoData.getProductType())){
            orderType = OrderTypeEnum.COMMON_ASSETS.getCode();
        }else {
            log.error("生成资产订单失败，商品类型匹配错误 productType():{}", cartInfoData.getProductType());
            return null;
        }
        // oa采购单
        AddAssetsSaleOrderDTO addAssetsSaleOrderDTO = createSaleOrderDTO(channelId, cartOrderParam, partnerTokenInfo, cartInfoData);

        // 资产商品信息
        List<AddAssetsSaleOrderDTO.Product> assetsProductList = Lists.newArrayList();
        // 购物车商品信息
        List<CartInfoVO.CartProduct> productList = cartInfoData.getProductList();
        for (CartInfoVO.CartProduct cartProduct : productList) {

            // 使用的价格
            Integer priceType = cartProduct.getPriceType();
            BigDecimal price = Integer.valueOf(1).equals(priceType) ? Optional.ofNullable(cartProduct.getBuyTaxPrice()).orElse(BigDecimal.ZERO) : Optional.ofNullable(cartProduct.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO);

            // 资产商品
            AddAssetsSaleOrderDTO.Product assetProduct = createPurchaseGoods(cartProduct, price);
            // add
            assetsProductList.add(assetProduct);

            // 交易快照
            ProductOrderVersion productOrderVersion =
                    createProductOrderVersion(cartProduct, priceType, supplierId, partnerTokenInfo);
            // add
            productOrderVersionList.add(productOrderVersion);

            // 订单详情
            OrderDetailInfo orderDetailInfo = createOrderDetailInfo(cartProduct, partnerTokenInfo, price, supplierId);
            // 物流费计算
            if (cartProduct.getRemoteDeliveryFee() == 1 && deliveryFeeArea != null) {
                BigDecimal deliveryFee = deliveryFeeArea.getDeliveryFee().multiply(BigDecimal.valueOf(cartProduct.getProductCount()));
                orderDetailInfo.setDeliveryFee(deliveryFee);
                totalPrice = totalPrice.add(deliveryFee);
            }

            // add
            orderDetailInfoList.add(orderDetailInfo);

            // 购物车id
            cartIdList.add(cartProduct.getId());

            // 总价
            totalPrice = totalPrice.add(price.multiply(BigDecimal.valueOf(cartProduct.getProductCount())));
        }
        // 资产订单参数设置-商品列表
        addAssetsSaleOrderDTO.setProducts(assetsProductList);
        // 资产订单参数设置-总价格
        addAssetsSaleOrderDTO.setTotalAmount(totalPrice);

        // 订单信息
        String orderTitle = cartInfoData.getSupplierName() + "采购单";
        OrderInfo orderInfo = createOrderInfo(partnerTokenInfo, channelId, cartOrderParam, supplierId, orderTitle, orderType, totalPrice);

        // 保存订单信息
        Long orderId = saveOrderInfo(orderInfo, orderDetailInfoList, productOrderVersionList);
        if (null == orderId) {
            return OrderTipConstant.SAVE_ERROR;
        }
        // 资产订单参数设置-采购单号
        addAssetsSaleOrderDTO.setChwOrderNo(String.valueOf(orderId));
        addAssetsSaleOrderDTO.setOrderTime(orderInfo.getCreateTime());
        // 保存日志
        orderInfoLogService.saveOrderLog("订单已创建，等待生成资产采购单。", OrderLogTypeEnum.CREATE, orderInfo.getId(),
                partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());

        // 保存并生成oa采购单
        return saveOaOrder(addAssetsSaleOrderDTO, partnerTokenInfo, orderInfo, cartInfoData,
                productOrderVersionList, orderDetailInfoList, cartIdList);
    }

    @Override
    public Result<String> cancelOrder(Long orderNo, Long xTenant) {
        // 资产取消订单 调取办公组接口
        R<Boolean> delResult = assetsOrderClient.delOrder(String.valueOf(orderNo));
        if (!Integer.valueOf(0).equals(delResult.getCode())) {
            return Result.error("OA接口调取异常:" + delResult.getUserMsg());
        }
        if (!delResult.getData()) {
            return Result.error("OA接口调取结果:" + delResult.getData());
        }
        return Result.success();
    }

    @Override
    public Result<String> deliveryProduct(List<DeliveryProductBO> deliveryProductBOList, OrderInfo orderInfo, OrderDeliveryParam param, SupplierTokenInfo supplierTokenInfo) {
        // 资产不用发货
        return Result.success();
    }


    @Override
    public List<String> orderReceive(List<OrderInfo> orderInfoList) {
        // 参数预设
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        OrderReceiveBO orderReceiveBO = new OrderReceiveBO();
        // 设置订单id与合作伙伴名称对应关系
        Map<Long, Long> idsMap = orderInfoList.stream().collect(Collectors.toMap(OrderInfo::getId, OrderInfo::getPartnerId));
        Map<Long, String> nameMap = partnerUserMapper.selectBatchIds(idsMap.values()).stream().collect(Collectors.toMap(PartnerUser::getId, PartnerUser::getName));
        Map<Long, String> partnerNameMap = idsMap.keySet().stream()
                .collect(Collectors.toMap(orderInfoId -> orderInfoId, orderInfoId -> nameMap.get(idsMap.get(orderInfoId))));

        // 返回列表
        List<String> errorInfoList = Lists.newArrayList();
        List<OrderInfo> orderInfos = Lists.newArrayList();
        List<OrderInfoLog> orderInfoLogList  = Lists.newArrayList();
        List<MessageInfoUser> messageInfoUserList = Lists.newArrayList();

        for (OrderInfo orderInfo : orderInfoList) {
            orderInfo.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
            OrderInfoLog orderInfoLog;
            // 记录订单日志
            if (Objects.nonNull(partnerTokenInfo)) {
                orderInfoLog = new OrderInfoLog().setOrderId(orderInfo.getId()).setContent("订单已完成").setType(OrderLogTypeEnum.COMPLETED.getCode())
                        .setOperationUserId(partnerTokenInfo.getLoginOAUserId()).setOperationUserName(partnerTokenInfo.getLoginOAUserName());
            } else {
                orderInfoLog = new OrderInfoLog().setOrderId(orderInfo.getId()).setContent("订单已完成")
                        .setType(OrderLogTypeEnum.COMPLETED.getCode()).setOperationUserName("系统");
            }
            // 站内信息推送，只在自动收货时有推送
            if (Objects.isNull(partnerTokenInfo) && StringUtils.isNotBlank(partnerNameMap.get(orderInfo.getId()))) {
                MessageInfo messageInfo = new MessageInfo().setContent("您的订单：" + orderInfo.getOrderNo() + "已自动确认收货，订单已完成").setTitle("订单自动收货通知")
                        .setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode()).setUserType(MessagePushUserTypeEnum.PARTNER.getCode());
                // 需要获取messageID
                messageInfoService.saveOrUpdate(messageInfo);
                MessageInfoUser messageInfoUser = new MessageInfoUser().setMessageId(messageInfo.getId()).setUserId(orderInfo.getPartnerId())
                        .setHasRead(1).setUserName(partnerNameMap.get(orderInfo.getId()));
                messageInfoUserList.add(messageInfoUser);
            }
            orderInfos.add(orderInfo);
            orderInfoLogList.add(orderInfoLog);
        }
        // 保存订单收货确认信息
        orderReceiveBO.setOrderInfoList(orderInfos);
        orderReceiveBO.setOrderInfoLogList(orderInfoLogList);
        orderReceiveBO.setMessageInfoUserList(messageInfoUserList);
        boolean saveResult = this.saveOrderReceiveInfo(orderReceiveBO);
        if (!saveResult) {
            List<Long> orderNoList = orderInfoList.stream().map(OrderInfo::getOrderNo).collect(Collectors.toList());
            errorInfoList.add("资产商品订单确认收货信息保存失败, 订单号：" + JSON.toJSON(orderNoList));
            return errorInfoList;
        }

        // 通知OA完成订单
        for(OrderInfo orderInfo : orderInfoList){
            R<Boolean> result = assetsOrderClient.finishOrder(String.valueOf(orderInfo.getOrderNo()));
            if (!Integer.valueOf(0).equals(result.getCode())) {
                log.error("资产完成订单失败,接口调取异常" + result.getUserMsg());
                errorInfoList.add("资产完成订单失败,接口调取异常" + result.getUserMsg());
            }
            if (!result.getData()) {
                log.error("资产完成订单失败,返回消息:" + result.getData());
                errorInfoList.add("资产完成订单失败,返回消息:" + result.getData());
            }
        }
        return errorInfoList;
    }


    @Override
    public Result<Boolean> checkProductType(Long ppid, Integer productType) {
        if(null == ppid || null == productType){
            return Result.error("参数缺失！");
        }
        // 校验枚举值正确性
        boolean isLegal = ProductTypeEnum.checkLegal(productType);
        if(!isLegal){
            return Result.error("商品类型不合法");
        }
        PickProduct pickProduct = pickProductService.getByPpidAndProductType(ppid, productType);
        if(Objects.isNull(pickProduct)){
            return Result.success(Boolean.FALSE);
        }
        return Result.success(Boolean.TRUE);
    }


    /**
     * 创建oa采购单
     *
     * @param channelId
     * @param cartOrderParam
     * @param partnerTokenInfo
     * @param cartInfoData
     * @return
     */
    private static AddAssetsSaleOrderDTO createSaleOrderDTO(String channelId, CartOrderParam cartOrderParam, PartnerTokenInfo partnerTokenInfo, CartInfoVO.CartInfoData cartInfoData) {
        AddAssetsSaleOrderDTO addAssetsSaleOrderDTO = new AddAssetsSaleOrderDTO();
        addAssetsSaleOrderDTO.setAreaId(Integer.valueOf(String.valueOf(cartOrderParam.getAddressId())));
        if(Integer.valueOf(ProductTypeEnum.FIX_ASSETS.getCode()).equals(cartInfoData.getProductType())){
            addAssetsSaleOrderDTO.setAssetsType(0);
        }else if(Integer.valueOf(ProductTypeEnum.COMMON_ASSETS.getCode()).equals(cartInfoData.getProductType())){
            addAssetsSaleOrderDTO.setAssetsType(1);
        }else {
            log.error("生成资产订单失败，商品类型匹配错误 productType():{}", cartInfoData.getProductType());
            return null;
        }
        addAssetsSaleOrderDTO.setAddress(cartOrderParam.getReceiveAddress());
        addAssetsSaleOrderDTO.setPhone(cartOrderParam.getContactPhone());
        addAssetsSaleOrderDTO.setConsignee(cartOrderParam.getContactPerson());
        addAssetsSaleOrderDTO.setCreateUser(Integer.valueOf(String.valueOf(partnerTokenInfo.getLoginOAUserId())));
        addAssetsSaleOrderDTO.setCreateUserName(partnerTokenInfo.getLoginOAUserName());
        addAssetsSaleOrderDTO.setXtenant(Integer.valueOf(String.valueOf(partnerTokenInfo.getXtenant())));
        addAssetsSaleOrderDTO.setXtenantName(partnerTokenInfo.getName());
        return addAssetsSaleOrderDTO;
    }

    /**
     * 创建采购单商品
     *
     * @param cartProduct
     * @param price
     * @return
     */
    private static AddAssetsSaleOrderDTO.Product createPurchaseGoods(CartInfoVO.CartProduct cartProduct, BigDecimal price) {

        AddAssetsSaleOrderDTO.Product product = new AddAssetsSaleOrderDTO.Product();
        product.setPpid(cartProduct.getPpid());
        product.setModel(cartProduct.getProductColor());
        product.setPrice(price);
        product.setName(cartProduct.getProductName());
        product.setQuantity(cartProduct.getProductCount());
        return product;
    }

    /**
     * 创建交易快照
     *
     * @param cartProduct
     * @param priceType
     * @param supplierId
     * @param partnerTokenInfo
     * @return
     */
    private ProductOrderVersion createProductOrderVersion(CartInfoVO.CartProduct cartProduct, Integer priceType, Long supplierId,
                                                          PartnerTokenInfo partnerTokenInfo) {
        ProductOrderVersion productOrderVersion = new ProductOrderVersion();
        productOrderVersion.setBuyNoTaxPrice(cartProduct.getBuyNoTaxPrice());
        productOrderVersion.setBuyTaxPrice(cartProduct.getBuyTaxPrice());
        productOrderVersion.setPartnerId(partnerTokenInfo.getId());
        productOrderVersion.setPpid(cartProduct.getPpid());
        productOrderVersion.setPriceType(priceType);
        productOrderVersion.setProductColor(cartProduct.getProductColor());
        productOrderVersion.setProductName(cartProduct.getProductName());
        productOrderVersion.setProductDetailId(cartProduct.getProductDetailId());
        productOrderVersion.setProductId(cartProduct.getProductId());
        productOrderVersion.setProductImg(cartProduct.getProductImage());
        productOrderVersion.setProductCid(cartProduct.getProductCid());
        productOrderVersion.setProductBarCode(cartProduct.getProductBarCode());
        productOrderVersion.setSupplierUserId(supplierId);
        // 查询分类名称
        if(cartProduct.getProductCid() != null) {
            Category category = categoryService.getOneById(cartProduct.getProductCid().intValue());
            if(category != null) {
                productOrderVersion.setProductCidName(category.getName());
            }
        }

        return productOrderVersion;
    }

    /**
     * 创建采购单详情
     *
     * @param cartProduct
     * @param partnerTokenInfo
     * @param price
     * @param supplierId
     * @return
     */
    private static OrderDetailInfo createOrderDetailInfo(CartInfoVO.CartProduct cartProduct, PartnerTokenInfo partnerTokenInfo,
                                                         BigDecimal price, Long supplierId) {
        OrderDetailInfo orderDetailInfo = new OrderDetailInfo();
        orderDetailInfo.setBuyAmount(cartProduct.getProductCount());
        orderDetailInfo.setPartnerId(partnerTokenInfo.getId());
        orderDetailInfo.setProductId(cartProduct.getProductId());
        orderDetailInfo.setProductDetailId(cartProduct.getProductDetailId());
        orderDetailInfo.setProductPrice(price);
        orderDetailInfo.setSupplierId(supplierId);
        orderDetailInfo.setCartId(cartProduct.getId());
        return orderDetailInfo;
    }

    /**
     * 创建采货王订单信息
     *
     * @param partnerTokenInfo
     * @param channelId
     * @param cartOrderParam
     * @param supplierId
     * @param orderTitle
     * @param totalPrice
     * @return
     */
    private static OrderInfo createOrderInfo(PartnerTokenInfo partnerTokenInfo, String channelId, CartOrderParam cartOrderParam, Long supplierId, String orderTitle, Integer orderType, BigDecimal totalPrice) {
        OrderInfo orderInfo = new OrderInfo();
        // 默认采货王
        orderInfo.setChannelType(0);
        orderInfo.setOrderType(orderType);
        orderInfo.setOrderStatus(OrderStatusEnum.DEFAULT.getCode());
        orderInfo.setPartnerId(partnerTokenInfo.getId());
        orderInfo.setChannelId(channelId);
        orderInfo.setReceivePoiId(cartOrderParam.getAddressId());
        orderInfo.setReceivePoiName(cartOrderParam.getAddressName());
        orderInfo.setReceivePoiCityId(cartOrderParam.getCityId());
        orderInfo.setSupplierId(supplierId);
        orderInfo.setXtenantId(partnerTokenInfo.getXtenant());
        orderInfo.setOrderTitle(orderTitle);
        orderInfo.setTotalPrice(totalPrice);
        orderInfo.setContactPerson(cartOrderParam.getContactPerson());
        orderInfo.setContactPhone(cartOrderParam.getContactPhone());
        orderInfo.setReceiveAddress(cartOrderParam.getReceiveAddress());
        orderInfo.setDeliveryTime(LocalDateTime.now().plusDays(DEFAULT_DELIVERY_DAY));
        orderInfo.setCreateTime(LocalDateTime.now());
        return orderInfo;
    }

    /**
     * 保存订单信息
     *
     * @param orderInfo
     * @param orderDetailInfoList
     * @param productOrderVersionList
     * @return
     */
    private Long saveOrderInfo(OrderInfo orderInfo, List<OrderDetailInfo> orderDetailInfoList, List<ProductOrderVersion> productOrderVersionList) {
        // 保存信息
        boolean insert = orderInfo.insert();
        if (!insert) {
            return null;
        }
        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            orderDetailInfo.setOrderId(orderInfo.getId());
        }
        boolean insert1 = orderDetailInfoService.saveBatch(orderDetailInfoList);
        if (!insert1) {
            orderInfo.deleteById();
            return null;
        }
        for (ProductOrderVersion productOrderVersion : productOrderVersionList) {
            productOrderVersion.setOrderId(orderInfo.getId());
        }
        boolean insert2 = productOrderVersionService.saveBatch(productOrderVersionList);
        if (!insert2) {
            orderInfo.deleteById();
        }
        return orderInfo.getId();
    }

    /**
     * 生成oa采购单，并更新订单信息
     *
     * @param addAssetsSaleOrderDTO
     * @param partnerTokenInfo
     * @param orderInfo
     * @param cartInfoData
     * @param productOrderVersionList
     * @param orderDetailInfoList
     * @param cartIdList
     * @return
     */
    private String saveOaOrder(AddAssetsSaleOrderDTO addAssetsSaleOrderDTO, PartnerTokenInfo partnerTokenInfo, OrderInfo orderInfo,
                               CartInfoVO.CartInfoData cartInfoData,
                               List<ProductOrderVersion> productOrderVersionList,
                               List<OrderDetailInfo> orderDetailInfoList,
                               List<Long> cartIdList) {
        // 生成资产采购单
        R<Boolean> result = assetsOrderClient.addOrder(addAssetsSaleOrderDTO);
        if (result.getCode() != 0 || !result.getData()) {
            // 保存日志
            String errorLogInfo = String.format("生成资产采购单失败，返回结果:%s 入参：%s", JSON.toJSONString(result), JSON.toJSONString(addAssetsSaleOrderDTO));
            orderInfoLogService.saveOrderLog(errorLogInfo, OrderLogTypeEnum.GENERATE_NO, orderInfo.getId(),
                    partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());
            // 删除订单
            orderInfo.deleteById();
            return cartInfoData.getSupplierName() + " 的采购单生成失败!";
        }

        Long orderNo = orderInfo.getId();
        // 保存日志
        orderInfoLogService.saveOrderLog("采购单已生成，采购单号：" + orderInfo.getId(), OrderLogTypeEnum.GENERATE_NO, orderInfo.getId(),
                partnerTokenInfo.getLoginOAUserId(), partnerTokenInfo.getLoginOAUserName());

        // 更新订单状态
        orderInfo.setOrderStatus(OrderStatusEnum.AUDITED.getCode());
        orderInfo.setOrderNo(orderNo);
        // 保存信息
        boolean update = orderInfo.updateById();

        for (ProductOrderVersion productOrderVersion : productOrderVersionList) {
            productOrderVersion.setOrderNo(orderNo);
        }
        boolean update1 = productOrderVersionService.updateBatchById(productOrderVersionList);

        for (OrderDetailInfo orderDetailInfo : orderDetailInfoList) {
            orderDetailInfo.setOrderNo(orderNo);
        }
        boolean update2 = orderDetailInfoService.updateBatchById(orderDetailInfoList);

        // 删除购物车
        boolean remove = cartInfoService.removeByIds(cartIdList);

        // 错误日志统计
        errorLog(update, update1, update2, remove, orderInfo, orderNo, cartIdList);
        return null;
    }

    /**
     * 错误日志统计
     *
     * @param update
     * @param update1
     * @param update2
     * @param remove
     * @param orderInfo
     * @param orderNo
     * @param cartIdList
     */
    private void errorLog(boolean update, boolean update1, boolean update2, boolean remove,
                          OrderInfo orderInfo, Long orderNo, List<Long> cartIdList) {
        if (!update) {
            log.error("保存更新采购单失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!update1) {
            log.error("保存更新商品快照失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!update2) {
            log.error("保存更新订单详情失败，orderId:{}, orderNo:{}", orderInfo.getId(), orderNo);
        }
        if (!remove) {
            log.error("删除购物车失败，cartIdList:{}", cartIdList);
        }
    }

    /**
     * 保存订单确认收货信息
     */
    private Boolean saveOrderReceiveInfo(OrderReceiveBO orderReceiveBO) {
        return transactionTemplate.execute(transactionStatus -> {
            try {
                orderInfoService.updateBatchById(orderReceiveBO.getOrderInfoList());
                if (CollectionUtils.isNotEmpty(orderReceiveBO.getOrderInfoLogList())) {
                    orderInfoLogService.saveOrUpdateBatch(orderReceiveBO.getOrderInfoLogList());
                }
                if (CollectionUtils.isNotEmpty(orderReceiveBO.getMessageInfoList())) {
                    messageInfoService.saveOrUpdateBatch(orderReceiveBO.getMessageInfoList());
                }
                if (CollectionUtils.isNotEmpty(orderReceiveBO.getMessageInfoUserList())) {
                    messageInfoUserService.saveOrUpdateBatch(orderReceiveBO.getMessageInfoUserList());
                }
                return true;
            } catch (Exception e) {
                transactionStatus.setRollbackOnly();
                log.error("资产商品订单确认收货信息保存失败,orderReceiveBO:{},exception:{}", JSON.toJSON(orderReceiveBO), e);
                return false;
            }
        });
    }

}

package com.jiuji.pick.service.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.component.minifile.MiniFileConfig;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.constant.OrderTipConstant;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.utils.ImageUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.bo.SupplierProductInfoBO;
import com.jiuji.pick.service.order.entity.CartInfo;
import com.jiuji.pick.service.order.mapper.CartInfoMapper;
import com.jiuji.pick.service.order.service.CartInfoService;
import com.jiuji.pick.service.order.service.NeoService;
import com.jiuji.pick.service.order.service.OaService;
import com.jiuji.pick.service.order.vo.CartInfoVO;
import com.jiuji.pick.service.order.vo.ConfirmOrderInfoVO;
import com.jiuji.pick.service.order.vo.DeleteByInfo;
import com.jiuji.pick.service.order.vo.OrderAreaInfoVO;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.service.PartnerUserService;
import com.jiuji.pick.service.user.service.SupplierUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 购物车信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-06
 */
@Service
public class CartInfoServiceImpl extends ServiceImpl<CartInfoMapper, CartInfo> implements CartInfoService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Resource
    private SupplierUserService supplierUserService;
    @Resource
    private MiniFileConfig miniFileConfig;
    @Resource
    private PartnerUserService partnerUserService;
    @Resource
    private OaService oaService;
    @Resource
    private NeoService neoService;

    @Override
    public Result<Long> add(Long productId, Integer productCount, Long supplierUserId, Integer type,Integer priceType) {
        // 获取用户信息
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        Result<Long> checkPartnerInfo = checkPartnerInfo(partnerTokenInfo);
        if (!checkPartnerInfo.isSucceed()) {
            return checkPartnerInfo;
        }

        // 获取商品信息
        SupplierProductInfoBO supplierProductInfo = baseMapper.getSupplierProductInfo(productId, supplierUserId);
        // 检查商品信息
        String checkProductInfo = checkProductInfo(supplierProductInfo, supplierUserId);
        if (StringUtils.isNotBlank(checkProductInfo)) {
            return Result.error(OrderTipConstant.ADD_ERROR, checkProductInfo);
        }

        return saveCart(partnerTokenInfo, productId, supplierUserId, type, productCount, supplierProductInfo,priceType);
    }

    /**
     * 保存购物车
     *
     * @param partnerTokenInfo
     * @param productId
     * @param supplierUserId
     * @param type
     * @param productCount
     * @param supplierProductInfo
     * @return
     */
    private Result<Long> saveCart(PartnerTokenInfo partnerTokenInfo, Long productId, Long supplierUserId, Integer type, Integer productCount, SupplierProductInfoBO supplierProductInfo,Integer priceType) {
        // 获取购物车信息
        LambdaQueryWrapper<CartInfo> cartInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cartInfoLambdaQueryWrapper.eq(CartInfo::getPartnerId, partnerTokenInfo.getId());
        cartInfoLambdaQueryWrapper.eq(CartInfo::getProductId, productId);
        cartInfoLambdaQueryWrapper.eq(CartInfo::getSupplierUserId, supplierUserId);
        cartInfoLambdaQueryWrapper.eq(CartInfo::getCartType, type);
        cartInfoLambdaQueryWrapper.eq(CartInfo::getPriceType, priceType);
        CartInfo cartInfo = this.getOne(cartInfoLambdaQueryWrapper);
        if (cartInfo != null) {
            Integer productCount1 = cartInfo.getProductCount();
            cartInfo.setProductCount(productCount1 + productCount);
            cartInfo.setUpdateTime(LocalDateTime.now());
        } else {
            cartInfo = new CartInfo();
            cartInfo.setProductId(productId);
            cartInfo.setProductCount(productCount);
            cartInfo.setCartType(type);
            cartInfo.setPriceType(priceType);
            cartInfo.setProductDetailId(supplierProductInfo.getProductDetailId());
            cartInfo.setPpid(supplierProductInfo.getPpid());
            cartInfo.setSupplierUserId(supplierUserId);
            if(OrderPriceTypeEnum.TAX_INCLUDED.getCode().equals(priceType)){
                cartInfo.setPrice(supplierProductInfo.getBuyTaxPrice());
            } else {
                cartInfo.setPrice(supplierProductInfo.getBuyNoTaxPrice());
            }
            cartInfo.setPartnerId(partnerTokenInfo.getId());
            cartInfo.setUserId(partnerTokenInfo.getLoginOAUserId());
        }
        boolean b = cartInfo.insertOrUpdate();
        if (b) {
            return Result.success(cartInfo.getId());
        }
        return Result.error();
    }

    /**
     * 检查合作伙伴状态
     *
     * @param partnerTokenInfo
     * @return
     */
    private Result<Long> checkPartnerInfo(PartnerTokenInfo partnerTokenInfo) {
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null) {
            return Result.notLoginError();
        }
        // 查询用户状态
        PartnerUser partnerUser = partnerUserService.getById(partnerTokenInfo.getId());
        if (Boolean.TRUE.equals(partnerUser.getStatus())) {
            return Result.error(OrderTipConstant.ADD_ERROR, OrderTipConstant.SUBMIT_ORDER_LIMIT_ERROR);
        }
        return Result.success();
    }

    /**
     * 检查商品信息
     *
     * @param supplierProductInfo
     * @param supplierUserId
     * @return
     */
    private static String checkProductInfo(SupplierProductInfoBO supplierProductInfo, Long supplierUserId) {
        if (supplierProductInfo == null) {
            return OrderTipConstant.SELECT_PRODUCT_ERROR;
        }
        String errorMsg = null;
        // 非上架的
        if (!Integer.valueOf(ProductStatusEnum.UP.getCode()).equals(supplierProductInfo.getProductStatus())) {
            errorMsg = OrderTipConstant.PRODUCT_UP_ERROR;
        }
        // 非绑定的
        if (!Integer.valueOf(BindStatusEnum.BIND.getCode()).equals(supplierProductInfo.getBindStatus())) {
            errorMsg = OrderTipConstant.SUPPLIER_NO_PRODUCT_ERROR;
        }
        // 供应商状态是未审核的
        if (!Integer.valueOf(SupplierUserStatusEnum.PASS.getCode()).equals(supplierProductInfo.getSupplierStatus())) {
            errorMsg = OrderTipConstant.SUPPLIER_NO_PRODUCT;
        }

        // 商品黑名单
        if (StringUtils.isBlank(supplierProductInfo.getBlackUsers())) {
            return errorMsg;
        }
        List<Long> blackUserList = CommonUtil.covertIdStr2Long(supplierProductInfo.getBlackUsers());
        // 在商品黑名单上的
        if (blackUserList.contains(supplierUserId)) {
            errorMsg = OrderTipConstant.SUPPLIER_NO_PRODUCT;
        }
        return errorMsg;
    }

    @Override
    public Result<Long> directBuy(Long productId, Integer productCount, Long supplierUserId,Integer priceType) {

        // 获取用户信息
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        Result<Long> checkPartnerInfo = checkPartnerInfo(partnerTokenInfo);
        if (!checkPartnerInfo.isSucceed()) {
            return checkPartnerInfo;
        }

        // 获取商品信息
        SupplierProductInfoBO supplierProductInfo = baseMapper.getSupplierProductInfo(productId, supplierUserId);
        // 检查商品信息
        String checkProductInfo = checkProductInfo(supplierProductInfo, supplierUserId);
        if (StringUtils.isNotBlank(checkProductInfo)) {
            return Result.error(OrderTipConstant.ADD_ERROR, checkProductInfo);
        }

        return saveDirectBuyCart(partnerTokenInfo, productId, productCount, supplierProductInfo, supplierUserId,priceType);
    }

    /**
     * 保存直接购买的购物车
     *
     * @param partnerTokenInfo
     * @param productId
     * @param productCount
     * @param supplierProductInfo
     * @param supplierUserId
     * @return
     */
    private Result<Long> saveDirectBuyCart(PartnerTokenInfo partnerTokenInfo, Long productId, Integer productCount, SupplierProductInfoBO supplierProductInfo, Long supplierUserId,Integer priceType) {
        // 直接购买
        Integer type = CartTypeEnum.DIRECT_BUY.getCode();

        // 删除直接购买的
        LambdaQueryWrapper<CartInfo> cartInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cartInfoLambdaQueryWrapper.eq(CartInfo::getPartnerId, partnerTokenInfo.getId());
        cartInfoLambdaQueryWrapper.eq(CartInfo::getCartType, type);
        this.remove(cartInfoLambdaQueryWrapper);

        // 直接购买加入购物车
        CartInfo cartInfo = new CartInfo();
        cartInfo.setProductId(productId);
        cartInfo.setProductCount(productCount);
        cartInfo.setCartType(type);
        cartInfo.setPriceType(priceType);
        cartInfo.setProductDetailId(supplierProductInfo.getProductDetailId());
        cartInfo.setPpid(supplierProductInfo.getPpid());
        cartInfo.setSupplierUserId(supplierUserId);
        if(OrderPriceTypeEnum.TAX_INCLUDED.getCode().equals(priceType)){
            cartInfo.setPrice(supplierProductInfo.getBuyTaxPrice());
        } else {
            cartInfo.setPrice(supplierProductInfo.getBuyNoTaxPrice());
        }


        cartInfo.setPartnerId(partnerTokenInfo.getId());
        cartInfo.setUserId(partnerTokenInfo.getLoginOAUserId());
        boolean b = cartInfo.insert();
        if (b) {
            return Result.success(cartInfo.getId());
        }
        return Result.error();
    }

    @Override
    public Result<Boolean> deleteById(List<Long> idList) {
        // 获取用户信息
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null) {
            return Result.notLoginError();
        }
        return deleteCartByIdList(idList, partnerTokenInfo);
    }


    /**
     * 根据ppid以及含税类型来进行购物车的删除
     * @param deleteByInfo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByInfo(DeleteByInfo deleteByInfo) {
        QueryWrapper<CartInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("price_type",deleteByInfo.getPriceType())
                .eq("del_flag",Boolean.FALSE)
                .eq("ppid",deleteByInfo.getPpid());
        this.remove(wrapper);
    }

    /**
     * 删除购物车
     *
     * @param idList
     * @param partnerTokenInfo
     * @return
     */
    private Result<Boolean> deleteCartByIdList(List<Long> idList, PartnerTokenInfo partnerTokenInfo) {
        LambdaQueryWrapper<CartInfo> cartInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cartInfoLambdaQueryWrapper.in(CartInfo::getId, idList);
        cartInfoLambdaQueryWrapper.eq(CartInfo::getPartnerId, partnerTokenInfo.getId());
        List<CartInfo> cartInfoList = this.list(cartInfoLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(cartInfoList)) {
            return Result.error();
        }

        // 要删除的id集合
        List<Long> cartInfoIdList = cartInfoList.stream().map(CartInfo::getId).collect(Collectors.toList());
        boolean b = this.removeByIds(cartInfoIdList);
        if (b) {
            return Result.success(true);
        }
        return Result.error();
    }

    @Override
    public Result<Boolean> updateCart(Long id, Integer productCount) {
        // 获取用户信息
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null) {
            return Result.notLoginError();
        }
        return updateCartById(id, productCount, partnerTokenInfo);
    }

    /**
     * 更新购物车
     *
     * @param id
     * @param productCount
     * @param partnerTokenInfo
     * @return
     */
    private Result<Boolean> updateCartById(Long id, Integer productCount, PartnerTokenInfo partnerTokenInfo) {
        LambdaQueryWrapper<CartInfo> cartInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cartInfoLambdaQueryWrapper.eq(CartInfo::getId, id);
        cartInfoLambdaQueryWrapper.eq(CartInfo::getPartnerId, partnerTokenInfo.getId());
        CartInfo cartInfo = this.getOne(cartInfoLambdaQueryWrapper);
        if (cartInfo == null) {
            return Result.errorInfo("购物车信息不存在");
        }
        // 更新
        LambdaUpdateWrapper<CartInfo> cartInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        cartInfoLambdaUpdateWrapper.set(CartInfo::getProductCount, productCount);
        cartInfoLambdaUpdateWrapper.eq(CartInfo::getId, id);
        boolean update = this.update(cartInfoLambdaUpdateWrapper);
        if (update) {
            return Result.success();
        }
        return Result.errorInfo("更新购物车失败");
    }

    @Override
    public Result<CartInfoVO> listAll() {
        // 获取租户
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null) {
            return Result.success();
        }
        CartInfoVO cartInfo = getCartInfo(null, CartTypeEnum.NORMAL.getCode(), partnerTokenInfo.getId());
        if (cartInfo == null) {
            cartInfo = new CartInfoVO();
        }
        return Result.success(cartInfo);
    }

    @Override
    public Result<ConfirmOrderInfoVO> confirmOrderByCartIds(List<Long> cartIdList) {
        // 获取租户
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (partnerTokenInfo == null || partnerTokenInfo.getId() == null) {
            return Result.notLoginError();
        }

        CartInfoVO cartInfo = getCartInfo(cartIdList, null, partnerTokenInfo.getId());
        if (cartInfo == null) {
            return Result.noData();
        }
        ConfirmOrderInfoVO confirmOrderInfoVO = new ConfirmOrderInfoVO();
        confirmOrderInfoVO.setCartInfo(cartInfo);

        // 门店信息
        List<OrderAreaInfoVO> areaInfo = Integer.valueOf(MagicalValueConstant.INT_2).equals(partnerTokenInfo.getSource())
                ? neoService.getAreaInfo(partnerTokenInfo.getXtenant()) : oaService.getAreaInfo(partnerTokenInfo.getXtenant());

        confirmOrderInfoVO.setAreaInfo(areaInfo);

        return Result.success(confirmOrderInfoVO);
    }

    /**
     * 获取购物车信息
     *
     * @param cartIdList
     * @return
     */
    private CartInfoVO getCartInfo(List<Long> cartIdList, Integer type, Long partnerId) {
        if (partnerId == null) {
            return null;
        }
        // 查询购物车
        LambdaQueryWrapper<CartInfo> cartInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cartInfoLambdaQueryWrapper.eq(CartInfo::getPartnerId, partnerId);
        if (type != null) {
            cartInfoLambdaQueryWrapper.eq(CartInfo::getCartType, type);
        }
        if (CollectionUtils.isNotEmpty(cartIdList)) {
            cartInfoLambdaQueryWrapper.in(CartInfo::getId, cartIdList);
        }
        List<CartInfo> list = this.list(cartInfoLambdaQueryWrapper);

        // 构建购物车
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        // 供应商分组
        Map<Long, List<CartInfo>> supplierMap = list.stream().collect(Collectors.groupingBy(CartInfo::getSupplierUserId));
        // 查询供应商信息
        Set<Long> supplierUserIds = supplierMap.keySet();
        LambdaQueryWrapper<SupplierUser> supplierUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        supplierUserLambdaQueryWrapper.in(SupplierUser::getId, supplierUserIds);
        List<SupplierUser> supplierUserList = supplierUserService.list(supplierUserLambdaQueryWrapper);
        Map<Long, SupplierUser> supplierUserMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(supplierUserList)) {
            supplierUserMap = supplierUserList.stream().collect(Collectors.toMap(SupplierUser::getId, Function.identity()));
        }

        // 查询商品信息
        List<Long> productDetailIdList = list.stream().map(CartInfo::getProductDetailId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, SupplierProductInfoBO> supplierProductInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(productDetailIdList)) {
            List<SupplierProductInfoBO> supplierProductInfoByIdList =
                    this.getSupplierProductInfoByIdList(productDetailIdList);
            if (CollectionUtils.isNotEmpty(supplierProductInfoByIdList)) {
                supplierProductInfoMap = supplierProductInfoByIdList.stream()
                        .collect(Collectors.toMap(SupplierProductInfoBO::getProductDetailId, Function.identity()));
            }
        }

        // 供应商商品集合
        List<CartInfoVO.CartInfoData> cartInfoDataList = new ArrayList<>();
        // 失效商品集合
        List<CartInfoVO.CartProduct> invalidCartProductList = new ArrayList<>();

        for (Map.Entry<Long, List<CartInfo>> entry : supplierMap.entrySet()) {
            setCartProductInfo(cartInfoDataList, invalidCartProductList, supplierUserMap, supplierProductInfoMap, entry);
        }

        CartInfoVO cartInfoVO = new CartInfoVO();
        // 有效商品
        cartInfoVO.setCartInfoData(cartInfoDataList);
        // 有失效商品
        cartInfoVO.setInvalidProductList(invalidCartProductList);

        return cartInfoVO;
    }

    /**
     * 设置购物车商品
     *
     * @param cartInfoDataList
     * @param invalidCartProductList
     * @param supplierUserMap
     * @param supplierProductInfoMap
     * @param entry
     */
    private void setCartProductInfo(List<CartInfoVO.CartInfoData> cartInfoDataList, List<CartInfoVO.CartProduct> invalidCartProductList, Map<Long, SupplierUser> supplierUserMap, Map<Long, SupplierProductInfoBO> supplierProductInfoMap, Map.Entry<Long, List<CartInfo>> entry) {
        // 供应商商品
        CartInfoVO.CartInfoData cartInfoData = new CartInfoVO.CartInfoData();
        // 商品集合
        List<CartInfoVO.CartProduct> cartProductList = new ArrayList<>();

        Long supplierId = entry.getKey();
        cartInfoData.setSupplierId(supplierId);
        // 获取供应商信息
        SupplierUser supplierUser = supplierUserMap.get(supplierId);
        // 是否失效
        boolean invalid = false;
        // 没有供应商信息，获取不是通过的供应商
        if (supplierUser == null || supplierUser.getStatus() == null
                || !supplierUser.getStatus().equals(SupplierUserStatusEnum.PASS.getCode())) {
            invalid = true;
        } else {
            cartInfoData.setSupplierName(supplierUser.getName());
        }

        // 获取商品信息
        List<CartInfo> value = entry.getValue();
        for (CartInfo cartInfo : value) {
            addCartProductList(cartInfo, supplierProductInfoMap, invalidCartProductList, cartProductList, invalid, supplierId);
        }

        cartInfoData.setProductList(cartProductList);
        // 有有效商品
        if (!cartProductList.isEmpty()) {
            cartInfoDataList.add(cartInfoData);
        }
    }

    /**
     * 添加购物车商品
     *
     * @param cartInfo
     * @param supplierProductInfoMap
     * @param invalidCartProductList
     * @param cartProductList
     * @param invalid
     * @param supplierId
     */
    private void addCartProductList(CartInfo cartInfo, Map<Long, SupplierProductInfoBO> supplierProductInfoMap, List<CartInfoVO.CartProduct> invalidCartProductList, List<CartInfoVO.CartProduct> cartProductList, boolean invalid, Long supplierId) {
        Long productDetailId = cartInfo.getProductDetailId();
        SupplierProductInfoBO supplierProductInfoBO = supplierProductInfoMap.get(productDetailId);
        CartInfoVO.CartProduct cartProduct = new CartInfoVO.CartProduct();
        if (supplierProductInfoBO == null) {
            cartProduct.setId(cartInfo.getId());
            cartProduct.setProductCount(cartInfo.getProductCount());
            cartProduct.setProductDetailId(cartInfo.getProductDetailId());
            cartProduct.setProductId(cartInfo.getProductId());
            cartProduct.setPpid(cartInfo.getPpid());

            cartProduct.setBuyNoTaxPrice(cartInfo.getPrice());
            cartProduct.setBuyTaxPrice(cartInfo.getPrice());
            cartProduct.setInvalid(true);
            cartProduct.setProductName("未知商品");
            cartProduct.setProductImage("");
            cartProduct.setRemoteDeliveryFee(MagicalValueConstant.INT_0);
            // 加入失效商品
            invalidCartProductList.add(cartProduct);
            return;
        }
        //设置起订量
        cartProduct.setMinimumOrderQuantity(supplierProductInfoBO.getMinimumOrderQuantity());
        //设置箱规
        cartProduct.setBoxRule(supplierProductInfoBO.getBoxRule());
        cartProduct.setId(cartInfo.getId());
        cartProduct.setProductCount(cartInfo.getProductCount());
        cartProduct.setProductDetailId(productDetailId);
        cartProduct.setProductId(supplierProductInfoBO.getProductId());
        cartProduct.setPpid(supplierProductInfoBO.getPpid());

        cartProduct.setBuyNoTaxPrice(supplierProductInfoBO.getBuyNoTaxPrice());
        cartProduct.setBuyTaxPrice(supplierProductInfoBO.getBuyTaxPrice());

        cartProduct.setProductName(supplierProductInfoBO.getProductName());
        cartProduct.setProductColor(supplierProductInfoBO.getProductColor());

        cartProduct.setProductImage(ImageUtil.getProductImageUrl(miniFileConfig.getPathbase(),
                supplierProductInfoBO.getBPic(), WebConstant.PICTURE_SIZE.PIC_440x440));
        cartProduct.setProductCid(supplierProductInfoBO.getProductCid());
        cartProduct.setProductCidName(supplierProductInfoBO.getProductCidName());
        cartProduct.setProductBarCode(supplierProductInfoBO.getProductBarCode());
        Integer remoteDeliveryFee = Objects.isNull(supplierProductInfoBO.getRemoteDeliveryFee()) ? MagicalValueConstant.INT_0 : supplierProductInfoBO.getRemoteDeliveryFee();
        cartProduct.setRemoteDeliveryFee(remoteDeliveryFee);

        // 黑名单
        List<Long> blackUserList = CommonUtil.covertIdStr2Long(supplierProductInfoBO.getBlackUsers());
        // 绑定状态
        boolean bindStatus = Integer.valueOf(BindStatusEnum.BIND.getCode()).equals(supplierProductInfoBO.getBindStatus());
        // 商品状态
        boolean productStatus = Integer.valueOf(ProductStatusEnum.UP.getCode()).equals(supplierProductInfoBO.getProductStatus());
        // 失效的、未绑定的、未上架的、黑名单上的
        if (invalid || !bindStatus || !productStatus || blackUserList.contains(supplierId)) {
            cartProduct.setInvalid(true);
            // 加入失效商品
            invalidCartProductList.add(cartProduct);
            return;
        }

        // 默认未税
        Integer priceType = Optional.ofNullable(cartInfo.getPriceType()).orElse(0);
        cartProduct.setPriceType(priceType);
        cartProduct.setInvalid(false);
        // 加入商品集合
        cartProductList.add(cartProduct);

    }

    @Override
    public List<SupplierProductInfoBO> getSupplierProductInfoByIdList(List<Long> productDetailIdList) {
        if (CollectionUtils.isEmpty(productDetailIdList)) {
            return Collections.emptyList();
        }
        return baseMapper.getSupplierProductInfoByIdList(productDetailIdList);
    }

    @Override
    public List<CartInfo> listByCartIds(List<Long> cartIdList) {
        if (CollectionUtils.isEmpty(cartIdList)) {
            return Collections.emptyList();
        }
        return baseMapper.selectByIds(cartIdList);
    }

}

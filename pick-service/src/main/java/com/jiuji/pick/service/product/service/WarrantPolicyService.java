package com.jiuji.pick.service.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.service.product.entity.WarrantyPolicy;
import com.jiuji.pick.service.product.param.WarrantPolicySetParam;
import com.jiuji.pick.service.product.vo.WarrantyPolicyVO;
import com.jiuji.pick.service.product.vo.WarrantyTypeVO;

import java.util.List;

/**
 * @Description 质保政策服务类
 * <AUTHOR>
 * @Date 2021/11/10
 */
public interface WarrantPolicyService extends IService<WarrantyPolicy> {

    /**
     * 获取质保政策类型枚举
     * @return WarrantyTypeVO
     */
    List<WarrantyTypeVO> getAllWarrantyType();

    /**
     * 设置质保政策
     * @param param WarrantPolicySetParam
     * @param oaTokenInfo oaTokenInfo
     * @return boolean
     */
    Boolean setWarrantyPolicy(WarrantPolicySetParam param, OATokenInfo oaTokenInfo);

    /**
     * 根据商品类型查询质保政策
     * @param type 商品|质保政策类型
     * @param isMapping 是否映射处理
     * @return WarrantyPolicyVO
     */
    WarrantyPolicyVO getWarrantyPolicy(Integer type, boolean isMapping);

}

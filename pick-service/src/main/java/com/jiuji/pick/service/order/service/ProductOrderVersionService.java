package com.jiuji.pick.service.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.service.order.entity.ProductOrderVersion;
import com.jiuji.pick.service.order.vo.UpdateOrderPriceVo;

/**
 * <p>
 * 商品下单快照表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
public interface ProductOrderVersionService extends IService<ProductOrderVersion> {


    /**
     * 价格修改交易快照校验
     * @param updateOrderPriceVoList
     */
    void checkOrderUpdatePrice(UpdateOrderPriceVo updateOrderPriceVoList);

}

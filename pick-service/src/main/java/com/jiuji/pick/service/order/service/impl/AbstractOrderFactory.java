package com.jiuji.pick.service.order.service.impl;

import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.service.order.service.OrderCommonService;

/**
 * @function:
 * @description: AbstractOrderFactory.java
 * @date: 2021/10/13
 * @author: sunfayun
 * @version: 1.0
 */
public abstract class AbstractOrderFactory {

    public abstract OrderCommonService getOrderService(Integer orderType);

    /**
     * 获取到跳转地址
     * @param tokenInfo
     * @param orderNumber
     * @return
     */
    public abstract String getJumpUrl(PartnerTokenInfo tokenInfo,Long orderNumber);

}

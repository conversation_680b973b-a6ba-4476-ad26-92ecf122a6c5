package com.jiuji.pick.service.order.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 平台订单列表VO
 *
 * <AUTHOR>
 * @since 2021-5-20
 */
@Data
public class PlatformOrderListVO {

    /**
     * 订单号
     */
    private Long orderId;
    /**
     * 采购单号
     */
    private Long orderNo;
    /**
     * 订单标题
     */
    private String orderTitle;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 合作伙伴id
     */
    private Long partnerId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 合作伙伴名称
     */
    private String partnerName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 日志集合
     */
    private List<OrderLogVO> logs;

    /**
     * 商品总数
     */
    @JsonIgnore
    private Integer productCount;

    /**
     * 总物流费
     */
    private BigDecimal totalDeliveryFee;
}

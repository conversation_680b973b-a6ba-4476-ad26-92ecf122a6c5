package com.jiuji.pick.service.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.utils.HttpClientUtils;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.NotifyLogInfo;
import com.jiuji.pick.service.common.service.NotifyLogInfoService;
import com.jiuji.pick.service.order.dto.NeoAreaInfoDTO;
import com.jiuji.pick.service.order.dto.NeoOrderDTO;
import com.jiuji.pick.service.order.dto.NeoOrderDeliveryDTO;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.service.NeoService;
import com.jiuji.pick.service.order.vo.OrderAreaInfoVO;
import com.jiuji.pick.service.user.dto.NeoSupplierDTO;
import com.jiuji.pick.service.user.entity.PartnerUser;
import com.jiuji.pick.service.user.service.PartnerUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * NEO服务实现类
 * @Description
 * <AUTHOR>
 * @Date 2021/11/4
 */
@Slf4j
@Service
public class NeoServiceImpl implements NeoService {

    @Resource
    private NotifyLogInfoService notifyLogInfoService;
    @Resource
    private PartnerUserService partnerUserService;

    @Override
    public List<OrderAreaInfoVO> getAreaInfo(Long xtenant) {
        if (xtenant == null) {
            return Collections.emptyList();
        }
        PartnerUser partnerUser = partnerUserService.getByXtenant(xtenant);
        if (ObjectUtil.isNull(partnerUser) || StringUtils.isBlank(partnerUser.getHost())) {
            return Collections.emptyList();
        }

        String url = CommonConstant.HTTPS + partnerUser.getHost() + "/small-oa/api/purchaseKing/areaInfo/v1";
        Map<String, String> headMap = new HashMap<>(2);
        headMap.put(CommonConstant.XTENANT, partnerUser.getHost());
        String result = HttpClientUtils.get(url, headMap);
        if (StringUtils.isBlank(result)) {
            return Collections.emptyList();
        }
        log.info("调用NEO获取小型租户门店信息，url:{}, param:{}, res:{}", url, xtenant, result);
        notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(xtenant).notifyName("获取NEO租户门店").notifyUrl(url).notifyParam(url).notifyResult(result).build());
        Result<List<NeoAreaInfoDTO>> areaInfoResult = JSON.parseObject(result, new TypeReference<Result<List<NeoAreaInfoDTO>>>(){});

        if (!areaInfoResult.isSucceed() || CollectionUtils.isEmpty(areaInfoResult.getData())) {
            return Collections.emptyList();
        }

        List<OrderAreaInfoVO> orderAreaInfoList = new ArrayList<>();
        for (NeoAreaInfoDTO neoAreaInfo : areaInfoResult.getData()) {
            OrderAreaInfoVO orderAreaInfoVO = new OrderAreaInfoVO();
            orderAreaInfoVO.setId(String.valueOf(neoAreaInfo.getId()));
            orderAreaInfoVO.setLabel(neoAreaInfo.getArea() + "(" + neoAreaInfo.getAreaName() + ")");
            orderAreaInfoVO.setArea(neoAreaInfo.getArea());
            orderAreaInfoVO.setAddress(neoAreaInfo.getAddress());
            orderAreaInfoVO.setMobile(neoAreaInfo.getMobile());
            orderAreaInfoVO.setCityId(String.valueOf(neoAreaInfo.getCityId()));
            // NEO门店不需要授权ID，但采货王提交采购单时需要默认值
            orderAreaInfoVO.setAuthId(0);
            orderAreaInfoList.add(orderAreaInfoVO);
        }
        return orderAreaInfoList;
    }

    @Override
    public Result<String> createNeoChannelId(NeoSupplierDTO neoSupplierDTO, Long xtenant, String host, String token) {
        try {
            // 请求参数
            String url = CommonConstant.HTTPS + host + "/small-oa/api/purchaseKing/channel/add/v1";
            Map<String, String> headMap = new HashMap<>(2);
            headMap.put(CommonConstant.AUTHORIZATION, token);
            headMap.put(CommonConstant.XTENANT, host);

            String result = HttpClientUtils.postJsonWithHead(url, JSONUtil.toJsonStr(neoSupplierDTO), headMap);
            if (StringUtils.isBlank(result)) {
                return Result.noData();
            }
            // 日志
            log.info("调用NEO获取渠道ID，url:{}, param:{}, res:{}", url, JSONUtil.toJsonStr(neoSupplierDTO), result);
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(xtenant).notifyName("创建NEO渠道")
                    .notifyUrl(url).notifyParam(JSON.toJSONString(neoSupplierDTO)).notifyResult(result).build());

            return JSON.parseObject(result, new TypeReference<Result<String>>(){});
        } catch (Exception e) {
            log.error("调用NEO创建渠道异常,exception:", e);
            return null;
        }
    }

    @Override
    public Result<String> createNeoOrder(NeoOrderDTO neoOrderDTO, Long xtenant, String host, String token) {
        try {
            // 请求参数
            String url = CommonConstant.HTTPS + host + "/small-oa/api/purchaseKing/purchaseOrder/add/v1";
            Map<String, String> headMap = new HashMap<>(2);
            headMap.put(CommonConstant.XTENANT, host);
            headMap.put(CommonConstant.AUTHORIZATION, token);

            String result = HttpClientUtils.postJsonWithHead(url, JSONUtil.toJsonStr(neoOrderDTO), headMap);
            if (StringUtils.isBlank(result)) {
                return Result.noData();
            }
            // 日志
            log.info("调用NEO创建采购单，url:{}, param:{}, res:{}", url, JSONUtil.toJsonStr(neoOrderDTO), result);
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(xtenant).notifyName("创建订单")
                    .notifyUrl(url).notifyParam(JSON.toJSONString(neoOrderDTO)).notifyResult(result).build());
            return JSON.parseObject(result, new TypeReference<Result<String>>(){});
        } catch (Exception e) {
            log.error("调用NEO创建采购单异常,exception:", e);
            return Result.error("调用NEO创建采购单处理异常");
        }
    }

    @Override
    public Result<Boolean> cancelNeoOrder(Long orderNo, Long xtenant, String host, String token) {
        try {
            String url = CommonConstant.HTTPS + host + "/small-oa/api/purchaseKing/purchaseOrder/cancel/v1";
            HashMap<String, String> headMap = new HashMap<>(2);
            headMap.put(CommonConstant.AUTHORIZATION, token);
            headMap.put(CommonConstant.XTENANT, host);
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("orderNo", orderNo);

            String result = HttpClientUtils.postJsonWithHead(url, jsonObject.toString(), headMap);
            if (StringUtils.isBlank(result)) {
                return null;
            }

            // 日志
            log.info("调用NEO取消采购单，url:{}, orderNo:{}, res:{}", url, orderNo, result);
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(xtenant).notifyName("取消订单")
                    .notifyUrl(url).notifyParam(JSON.toJSONString(orderNo)).notifyResult(result).build());
            Result<com.alibaba.fastjson.JSONObject> re = com.alibaba.fastjson.JSONObject.parseObject(result, Result.class);
            if(Result.SUCCESS!=re.getCode()){
                return Result.error(Optional.ofNullable(re.getUserMsg()).orElse(re.getMsg()));
            }
            return Result.success();
        } catch (Exception e) {
            log.error("调用NEO取消订单异常,exception:" + e);
            return Result.error("调用NEO取消订单处理异常");
        }
    }

    @Override
    public Result<Boolean> neoOrderDelivery(OrderInfo orderInfo, NeoOrderDeliveryDTO neoOrderDeliveryDTO) {
        PartnerUser partnerUser = partnerUserService.getByXtenant(orderInfo.getXtenantId());
        if (ObjectUtil.isNull(partnerUser) || StringUtils.isBlank(partnerUser.getHost())) {
            return null;
        }
        try {
            // 请求参数
            String url = CommonConstant.HTTPS + partnerUser.getHost() + "/small-oa/api/purchaseKing/deliveryGoods/v1";
            Map<String, String> headMap = new HashMap<>(1);
            headMap.put(CommonConstant.XTENANT, partnerUser.getHost());
            String result = HttpClientUtils.postJsonWithHead(url, JSONUtil.toJsonStr(neoOrderDeliveryDTO), headMap);
            if (StringUtils.isBlank(result)) {
                return null;
            }
            log.info("调用NEO发货，url:{}, param:{}, res:{}", url, JSON.toJSONString(neoOrderDeliveryDTO), result);
            notifyLogInfoService.saveNotifyLogInfo(NotifyLogInfo.builder().xTenant(orderInfo.getXtenantId()).orderNo(orderInfo.getOrderNo()).notifyName("NEO订单发货")
                    .notifyUrl(url).notifyParam(JSON.toJSONString(neoOrderDeliveryDTO)).notifyResult(result).build());
            return JSON.parseObject(result, new TypeReference<Result<Boolean>>(){});
        } catch (Exception e) {
            log.error("调用NEO发货异常,exception:" + e);
            return Result.error("调用NEO发货处理异常");
        }
    }

}






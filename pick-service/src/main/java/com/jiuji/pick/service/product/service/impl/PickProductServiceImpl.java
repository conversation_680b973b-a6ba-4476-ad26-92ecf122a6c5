package com.jiuji.pick.service.product.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.constant.MagicalValueConstant;
import com.jiuji.pick.common.enums.*;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.AttachmentInfo;
import com.jiuji.pick.service.common.entity.OperateLogInfo;
import com.jiuji.pick.service.common.param.MessageInfoSaveParam;
import com.jiuji.pick.service.common.service.AttachmentInfoService;
import com.jiuji.pick.service.common.service.MessageInfoService;
import com.jiuji.pick.service.common.service.NewOperateLogFactory;
import com.jiuji.pick.service.common.service.OperateLogInfoService;
import com.jiuji.pick.service.order.service.CartInfoService;
import com.jiuji.pick.service.order.vo.DeleteByInfo;
import com.jiuji.pick.service.product.bo.AddOrUpdateProductParam;
import com.jiuji.pick.service.product.bo.AddOrUpdateProductParamOld;
import com.jiuji.pick.service.product.entity.*;
import com.jiuji.pick.service.product.mapper.PickProductMapper;
import com.jiuji.pick.service.product.mapper.ProductInfoMapper;
import com.jiuji.pick.service.product.param.*;
import com.jiuji.pick.service.product.service.*;
import com.jiuji.pick.service.product.vo.QueryProduct4AddVo;
import com.jiuji.pick.service.product.vo.QueryProductExamineListVo;
import com.jiuji.pick.service.product.vo.QueryProductListVo;
import com.jiuji.pick.service.product.vo.SynOtherProductVo;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.service.SupplierUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Service
@Slf4j
public class PickProductServiceImpl extends ServiceImpl<PickProductMapper, PickProduct> implements PickProductService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private OperateLogInfoService operateLogInfoService;
    @Resource
    private ProductBindService productBindService;
    @Resource
    private AttachmentInfoService attachmentInfoService;
    @Resource
    private MessageInfoService messageInfoService;
    @Resource
    private SupplierUserService supplierUserService;
    @Resource
    private ProductInfoService productInfoService;
    @Resource
    private ProductRuleConfigService productRuleConfigService;
    @Resource
    private SupplierProductDetailService supplierProductDetailService;
    @Resource
    private RefreshCacheDataService refreshCacheDataService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private SupplierProductPriceModifyService priceModifyService;
    @Resource
    private NewOperateLogFactory logFactory;
    @Resource
    private CartInfoService cartInfoService;
    @Resource
    private ProductInfoMapper productInfoMapper;

    @Override
    public Result<Boolean> addOrUpdateProduct(AddOrUpdateProductParam param) {
        if(param == null || param.getPpid() == null) {
            return Result.errorInfo("请求参数错误");
        }
        try {
            OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
            if(oaTokenInfo == null || StringUtils.isBlank(oaTokenInfo.getName())) {
                return Result.notLoginError();
            }
            // 文件处理
            this.productAttachmentInfoDeal(param);
            // 判断商品是否已经添加
            PickProduct pickProduct = this.getOne(new LambdaQueryWrapper<PickProduct>().eq(PickProduct::getPpid, param.getPpid()).last("limit 1"));
            if(pickProduct == null) {
                pickProduct = new PickProduct();
            }
            pickProduct = this.buildPickProduct(pickProduct, param);
            boolean isAdd = pickProduct.getId() == null ? Boolean.TRUE : Boolean.FALSE;
            if(isAdd) {
                pickProduct.setProductStatus(ProductStatusEnum.WAIT_UP.getCode());
            }
            // 查询商品文件
            List<AttachmentInfo> attachmentInfoList = attachmentInfoService.list(new LambdaQueryWrapper<AttachmentInfo>().eq(AttachmentInfo::getRelateId, param.getPpid()).eq(AttachmentInfo::getType, AttachTypeEnum.PRODUCT.getCode()));
            if(CollectionUtils.isNotEmpty(attachmentInfoList)) {
                List<Long> attachmentIdList = attachmentInfoList.stream().map(AttachmentInfo::getId).collect(Collectors.toList());
                pickProduct.setAttachmentIds(Joiner.on(",").join(attachmentIdList));
            }
            // 保存或者更新商品数据
            this.saveOrUpdate(pickProduct);

            // 新增 深圳九讯供应商默认绑定
            if(isAdd) {
                //供应商商品明细
                SupplierProductDetail supplierProductDetail = new SupplierProductDetail();
                supplierProductDetail.setSupplierId(CommonConstant.SHENZHEN_JIUXUN_SUPPLIER_ID);
                supplierProductDetail.setBuyNoTaxPrice(MagicalValueConstant.BD_9999);
                supplierProductDetail.setBuyTaxPrice(MagicalValueConstant.BD_9999);
                supplierProductDetail.setPpid(pickProduct.getPpid());
                supplierProductDetail.setProductId(pickProduct.getId());
                supplierProductDetail.setQualityDate(MagicalValueConstant.INT_0);
                supplierProductDetail.setAfterSalePolicy("--");
                supplierProductDetail.setOtherPolicy("--");
                supplierProductDetailService.save(supplierProductDetail);

                //供应商绑定关系
                ProductBind productBind = new ProductBind();
                productBind.setProductId(pickProduct.getId());
                productBind.setPpid(pickProduct.getPpid());
                productBind.setSupplierId(CommonConstant.SHENZHEN_JIUXUN_SUPPLIER_ID);
                productBind.setSupplierProductId(supplierProductDetail.getId());
                productBind.setBindStatus(BindStatusEnum.BIND.getCode());
                productBind.setBindTime(LocalDateTime.now());
                productBind.setCreateTime(LocalDateTime.now());
                productBindService.saveOrUpdate(productBind);
            }

            // 记录操作日志
            OperateLogInfo operateLogInfo = OperateLogInfo.builder()
                    .relateId(String.valueOf(pickProduct.getPpid()))
                    .type(ModuleEnum.PRODUCT.getCode())
                    .userId(Long.valueOf(oaTokenInfo.getUserId()))
                    .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                    .optUserName(oaTokenInfo.getName())
                    .content(isAdd ? "新增商品信息" : "修改商品信息")
                    .showType(LogShowTypeEnum.ADMIN.getCode())
                    .build();
            operateLogInfoService.save(operateLogInfo);
            // 异步通知供应商
            CompletableFuture.runAsync(() -> notifySupplierUserProductAdd(param.getPpid()));
            // 刷新ES数据
            refreshCacheDataService.refreshEsData(ImmutableList.of(pickProduct.getPpid()), null, CommonConstant.UPDATE_PRODUCT);
            //详细操作日志记录
            AddOrUpdateProductParamOld paramOld = param.getParamOld();
            if(paramOld!=null){
                logFactory.systemSaveLog(param,NewOperateLogInfoTypeEnum.COMMODITY_WAREHOUSE_MANAGEMENT.getCode());
            }
            return Result.successInfo("保存成功");
        } catch (Exception e) {
            log.error("保存用户数据发生异常，param:{}, exception:", JSONUtil.toJsonStr(param), e);
            return Result.errorInfo("服务异常，稍后再试");
        }
    }

    @Override
    public Result<String> synOtherProduct(SynOtherProductVo param) {
        Long jiuJiProductId = param.getJiuJiProductId();
        //查询出严选系统里所有的ppid
        List<Long> allPpidList = productInfoService.lambdaQuery().eq(ProductInfo::getProductid, jiuJiProductId)
                .select(ProductInfo::getPpriceid)
                .list().stream().map(ProductInfo::getPpriceid)
                .filter(Objects::nonNull).collect(Collectors.toList());
        //找到已经存在的ppid
        List<Long> havePpidList = this.lambdaQuery().in(PickProduct::getPpid, allPpidList).select(PickProduct::getPpid)
                .list().stream().map(PickProduct::getPpid)
                .filter(Objects::nonNull).collect(Collectors.toList());
        allPpidList.removeAll(havePpidList);
        if(CollectionUtils.isEmpty(allPpidList)){
            return Result.success("没有需要同步的商品");
        }
        allPpidList.forEach(item->{
            QueryProduct4AddVo queryProduct4AddVo = productInfoMapper.queryProductInfo4Add(item);
            AddOrUpdateProductParam productParam = new AddOrUpdateProductParam();
            productParam.setJiuJiProductId(jiuJiProductId);
            productParam.setPpid(item);
            productParam.setHappyArea(param.getHappyArea());
            productParam.setDefaultArea(param.getDefaultArea());
            productParam.setRecommendArea(param.getRecommendArea());
            productParam.setHotArea(param.getHotArea());
            //建议零售价
            productParam.setAdvicePrice(queryProduct4AddVo.getAdvicePrice());
            //商品卖点
            productParam.setProductFuture(queryProduct4AddVo.getProductFuture());
            //默认配置
            productParam.setProductConfig(queryProduct4AddVo.getProductConfig());
            Result<Boolean> booleanResult = addOrUpdateProduct(productParam);
            if(!booleanResult.isSucceed()){
              log.warn("同步其他商品功能异常：{}",JSONUtil.toJsonStr(booleanResult));
            }
        });
        return Result.success("操作成功");
    }

    /**
     * 新增商品消息通知
     */
    private void notifySupplierUserProductAdd(Long ppid) {
        // 获取审核通过的供应商
        List<SupplierUser> supplierUserList = supplierUserService.list(new LambdaQueryWrapper<SupplierUser>().eq(SupplierUser::getStatus, SupplierUserStatusEnum.PASS.getCode()));
        if(CollectionUtils.isEmpty(supplierUserList)) {
            return;
        }
        List<Long> supplierIdList = supplierUserList.stream().map(SupplierUser::getId).collect(Collectors.toList());
        MessageInfoSaveParam messageInfoSaveParam = new MessageInfoSaveParam();
        messageInfoSaveParam.setUserIdStr(Joiner.on(",").join(supplierIdList));
        messageInfoSaveParam.setUserType(MessagePushUserTypeEnum.SUPPLIER.getCode());
        messageInfoSaveParam.setTitle("商品新增通知");
        messageInfoSaveParam.setContent("采货王提醒您：商品库新增了新的商品ppid:"+ppid + " ，快去申请供货吧");
        messageInfoSaveParam.setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode());
        messageInfoService.saveMessageInfo(messageInfoSaveParam);
    }

    /**
     * 商品保存文件处理
     * @param param param
     */
    private void productAttachmentInfoDeal(AddOrUpdateProductParam param) {
        if(param == null) {
            return;
        }
        List<AttachmentInfo> attachmentInfoList = attachmentInfoService.list(new LambdaQueryWrapper<AttachmentInfo>().eq(AttachmentInfo::getRelateId, param.getPpid()).eq(AttachmentInfo::getType, AttachTypeEnum.PRODUCT.getCode()));
        // 处理并存储附件信息[根据fid 新增 更新 删除数据]
        attachmentInfoService.handleAndSaveAttachment(attachmentInfoList, param.getFileList(), param.getPpid(), AttachTypeEnum.PRODUCT.getCode());
        List<AttachmentInfo> imageList = attachmentInfoService.list(new LambdaQueryWrapper<AttachmentInfo>().eq(AttachmentInfo::getRelateId, param.getPpid()).eq(AttachmentInfo::getType, AttachTypeEnum.PRODUCT_IMAGE.getCode()));
        attachmentInfoService.handleAndSaveAttachment(imageList, param.getImageList(), param.getPpid(), AttachTypeEnum.PRODUCT_IMAGE.getCode());
    }

    @Override
    public Result<Page<QueryProductListVo>> queryProductListInfo(QueryProductListParam param) {
        if(param == null) {
            return Result.errorInfo("请求参数错误");
        }
        // 如果categoryId不为空，设置子分类
        if(param.getCategoryId() != null) {
            List<Long> categoryIdList = Lists.newArrayList();
            categoryIdList.add(param.getCategoryId());
            List<Category> categoryList = categoryService.getChildCategory(param.getCategoryId().intValue());
            if(CollectionUtils.isNotEmpty(categoryList)) {
                List<Long> childCategoryIdList = categoryList.stream().map(category -> Long.valueOf(category.getId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(childCategoryIdList)) {
                    categoryIdList.addAll(childCategoryIdList);
                }
            }
            param.setCategoryIdList(categoryIdList);
        }
        Page<QueryProductListVo> productListVoPage = baseMapper.queryProductListInfo(new Page<>(param.getCurrentPage(), param.getSize()), param);
        if(productListVoPage == null || CollectionUtils.isEmpty(productListVoPage.getRecords())) {
            return Result.noData();
        }

        List<Long> productIdList = productListVoPage.getRecords().stream().map(QueryProductListVo::getProductId).collect(Collectors.toList());
        // 只要绑定状态的商品
        List<ProductBind> productBindList = productBindService.list(Wrappers.<ProductBind>lambdaQuery().eq(ProductBind::getSupplierId,CommonConstant.SHENZHEN_JIUXUN_SUPPLIER_ID).eq(ProductBind::getBindStatus,BindStatusEnum.BIND.getCode()).in(ProductBind::getProductId,productIdList));
        productIdList = productBindList.stream().map(ProductBind::getProductId).collect(Collectors.toList());
        Map<Long, SupplierProductDetail> supplierProductDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productIdList)) {
            List<SupplierProductDetail> supplierProductDetailList = supplierProductDetailService.list(Wrappers.<SupplierProductDetail>lambdaQuery().eq(SupplierProductDetail::getSupplierId, CommonConstant.SHENZHEN_JIUXUN_SUPPLIER_ID).in(SupplierProductDetail::getProductId, productIdList));
            supplierProductDetailMap = supplierProductDetailList.stream().collect(Collectors.toMap(SupplierProductDetail::getProductId, Function.identity()));
        }
        Map<Long, SupplierProductDetail> finalSupplierProductDetailMap = supplierProductDetailMap;
        productListVoPage.getRecords().forEach(product -> {
            product.setProductStatusName(ProductStatusEnum.getProductStatusName(product.getProductStatus()));
            SupplierProductDetail supplierProductDetail = finalSupplierProductDetailMap.get(product.getProductId());
            if (supplierProductDetail != null) {
                product.setBuyTaxPrice(supplierProductDetail.getBuyTaxPrice());
                product.setBuyNoTaxPrice(supplierProductDetail.getBuyNoTaxPrice());
            }
        });
        return Result.success(productListVoPage);
    }

    @Override
    public Result<String> productUpOrDown(ProductUpOrDownParam param) {
        try {
            OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
            if(oaTokenInfo == null || StringUtils.isBlank(oaTokenInfo.getName())) {
                return Result.notLoginError();
            }
            List<PickProduct> pickProductList = this.list(new LambdaQueryWrapper<PickProduct>().in(PickProduct::getPpid, param.getPpidList()));
            if(CollectionUtils.isEmpty(pickProductList)) {
                return Result.errorInfo("商品信息不存在");
            }
            // 上架
            if(param.getType() == CommonConstant.PRODUCT_UP) {
                // 判断商品是否绑定供应商
                List<ProductBind> productBindList = productBindService.list(new LambdaQueryWrapper<ProductBind>().in(ProductBind::getPpid, param.getPpidList()).eq(ProductBind::getBindStatus, BindStatusEnum.BIND.getCode()));
                if(CollectionUtils.isEmpty(productBindList)) {
                    return Result.errorInfo("所选商品都未绑定供应商或者审核未通过");
                }
                List<Long> bindPpidList = productBindList.stream().map(ProductBind::getPpid).collect(Collectors.toList());
                for (Long ppid : param.getPpidList()) {
                    if(!bindPpidList.contains(ppid)) {
                        return Result.errorInfo("商品："+ppid+" 未绑定供应商或者审核未通过");
                    }
                }
                pickProductList.forEach(pickProduct -> {
                    pickProduct.setProductStatus(ProductStatusEnum.UP.getCode());
                    pickProduct.setProductUpTime(LocalDateTime.now());
                    pickProduct.setUpdateTime(LocalDateTime.now());
                });
                this.saveOrUpdateBatch(pickProductList);
                // 商品上下架通知
                CompletableFuture.runAsync(() -> notifySupplierUserProductDownOrUp(param, productBindList));
                // 刷新ES数据
                refreshCacheDataService.refreshEsData(param.getPpidList(), null, CommonConstant.UPDATE_PRODUCT);
            } else if(param.getType() == CommonConstant.PRODUCT_DOWN) {
                // 下架
                pickProductList.forEach(pickProduct -> {
                    pickProduct.setProductStatus(ProductStatusEnum.DOWN.getCode());
                    pickProduct.setUpdateTime(LocalDateTime.now());
                });
                this.saveOrUpdateBatch(pickProductList);
                List<ProductBind> productBindList = productBindService.list(new LambdaQueryWrapper<ProductBind>().in(ProductBind::getPpid, param.getPpidList()));
                if(CollectionUtils.isNotEmpty(productBindList)) {
                    List<Long> productBindIdList = productBindList.stream().map(ProductBind::getId).collect(Collectors.toList());
                    // 刷新ES数据
                    refreshCacheDataService.refreshEsData(null, productBindIdList, CommonConstant.DELETE_PRODUCT);
                }
            } else {
                return Result.errorInfo("操作类型错误");
            }
            // 记录操作日志
            List<OperateLogInfo> operateLogInfoList = Lists.newArrayList();
            for (PickProduct pickProduct : pickProductList) {
                OperateLogInfo operateLogInfo = OperateLogInfo.builder()
                        .relateId(String.valueOf(pickProduct.getPpid()))
                        .type(ModuleEnum.PRODUCT.getCode())
                        .userId(Long.valueOf(oaTokenInfo.getUserId()))
                        .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                        .optUserName(oaTokenInfo.getName())
                        .content(param.getType() == CommonConstant.PRODUCT_UP ? "商品上架" : "商品下架")
                        .showType(LogShowTypeEnum.ADMIN.getCode())
                        .build();
                operateLogInfoList.add(operateLogInfo);
            }
            operateLogInfoService.saveOrUpdateBatch(operateLogInfoList);
            //详细操作日志记录
            logFactory.systemSaveLog(param, NewOperateLogInfoTypeEnum.GOODS_ON_AND_OFF_SHELVES.getCode());
            return Result.successInfo(param.getType() == CommonConstant.PRODUCT_UP ? "上架成功" : "下架成功");
        } catch (Exception e) {
            log.error("商品上下架发生异常，param:{}, exception:", JSONUtil.toJsonStr(param), e);
            return Result.errorInfo("服务异常，稍后再试");
        }
    }

    private void notifySupplierUserProductDownOrUp(ProductUpOrDownParam param, List<ProductBind> productBindList) {
        if(param.getType() != CommonConstant.PRODUCT_UP) {
            return;
        }
        if(CollectionUtils.isEmpty(productBindList)) {
            return;
        }
        List<Long> ppidList = productBindList.stream().map(ProductBind::getPpid).collect(Collectors.toList());
        List<ProductInfo> productInfoList = productInfoService.list(new LambdaQueryWrapper<ProductInfo>().in(ProductInfo::getPpriceid, ppidList));
        if(CollectionUtils.isEmpty(productInfoList)) {
            return;
        }
        Map<Long, ProductInfo> productInfoMap = productInfoList.stream().collect(Collectors.toMap(ProductInfo::getPpriceid, Function.identity(), (p1, p2) -> p1));
        if(MapUtils.isEmpty(productInfoMap)) {
            return;
        }
        for (ProductBind productBind : productBindList) {
            ProductInfo productInfo = productInfoMap.get(productBind.getPpid());
            if(productInfo == null) {
                continue;
            }
            MessageInfoSaveParam messageInfoSaveParam = new MessageInfoSaveParam();
            messageInfoSaveParam.setUserIdStr(String.valueOf(productBind.getSupplierId()));
            messageInfoSaveParam.setUserType(MessagePushUserTypeEnum.SUPPLIER.getCode());
            messageInfoSaveParam.setTitle("商品上架通知");
            messageInfoSaveParam.setContent("采货王提醒您：商品:"+productBind.getPpid()+"，名称:"+productInfo.getProductName()+" 已经上架平台");
            messageInfoSaveParam.setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode());
            messageInfoService.saveMessageInfo(messageInfoSaveParam);
        }
    }

    @Override
    public Result<Page<QueryProductExamineListVo>> getProductExamineList(QueryProductExamineListParam param) {
        Page<QueryProductExamineListVo> productExamineListVoPage = baseMapper.getProductExamineList(new Page<>(param.getCurrentPage(), param.getSize()),param);
        if(productExamineListVoPage == null || CollectionUtils.isEmpty(productExamineListVoPage.getRecords())) {
            return Result.noData();
        }
        //设置绑定状态
        productExamineListVoPage.getRecords().forEach(product -> product.setBindStatusName(BindStatusEnum.getStatusName(product.getBindStatus())));
        // 设置商品状态
        productExamineListVoPage.getRecords().forEach(product -> product.setProductStatusName(ProductStatusEnum.getProductStatusName(product.getProductStatus())));
        // 设置预估利润
        productExamineListVoPage.getRecords().forEach(product -> product.setPredictProfit(product.getAdvicePrice().subtract(Optional.ofNullable(product.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO))));
        // 设置物料
        productExamineListVoPage.getRecords().forEach(product -> {
            if(StringUtils.isNotBlank(product.getMaterial())) {
                List<String> materialNameList = Lists.newArrayList();
                Splitter.on(",").splitToList(product.getMaterial()).forEach(material -> materialNameList.add(MaterialEnum.getNameByValue(Integer.parseInt(material))));
                product.setMaterialName(Joiner.on(";").join(materialNameList));
            }
        });
        return Result.success(productExamineListVoPage);
    }

    @Override
    public Result<String> bindOrUnboundProduct(ProductApplyDealParam param) {
        try {
            OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
            if(oaTokenInfo == null || StringUtils.isBlank(oaTokenInfo.getName())) {
                return Result.notLoginError();
            }
            ProductBind productBind = productBindService.getOne(new LambdaQueryWrapper<ProductBind>().eq(ProductBind::getPpid, param.getPpid()).eq(ProductBind::getSupplierId, param.getSupplierId()).last("limit 1"));
            if(productBind == null) {
                return Result.errorInfo("商品绑定关系不存在");
            }
            // 查询商品信息
            ProductInfo productInfo = productInfoService.getOne(new LambdaQueryWrapper<ProductInfo>().eq(ProductInfo::getPpriceid, productBind.getPpid()).last("limit 1"));
            if(productInfo == null) {
                return Result.errorInfo("商品基础信息不存在");
            }
            productBind.setRemark(param.getRemark());
            productBind.setUpdateTime(LocalDateTime.now());
            if(param.getOperateType() == CommonConstant.AGREE_BIND) {
                // 同意绑定
                productBind.setBindStatus(BindStatusEnum.BIND.getCode());
                productBind.setBindTime(LocalDateTime.now());
                productBindService.saveOrUpdate(productBind);

                // 查询商品改价记录
                SupplierProductPriceModify priceModify = priceModifyService.getSupplierProductPriceModify(productBind.getSupplierProductId(), false, false);
                // 修改商品改价记录
                if (ObjectUtil.isNotNull(priceModify)) {
                    priceModify.setEnabled(true);
                    priceModify.setUpdateTime(LocalDateTime.now());
                    priceModify.updateById();
                } else {
                    log.info("无对应商品改价信息记录");
                }

                // 刷新ES数据
                refreshCacheDataService.refreshEsData(ImmutableList.of(productBind.getPpid()), null, CommonConstant.UPDATE_PRODUCT);
                // 发送站内信
                MessageInfoSaveParam messageInfoSaveParam = new MessageInfoSaveParam();
                messageInfoSaveParam.setUserIdStr(String.valueOf(productBind.getSupplierId()));
                messageInfoSaveParam.setUserType(MessagePushUserTypeEnum.SUPPLIER.getCode());
                messageInfoSaveParam.setTitle("商品供应申请通过通知");
                messageInfoSaveParam.setContent(String.format("采货王提醒您：您的商品:%s,%s 供应申请已通过，可以接收到该商品的采购单了", productBind.getSupplierProductId(), productInfo.getProductName()));
                messageInfoSaveParam.setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode());
                messageInfoService.saveMessageInfo(messageInfoSaveParam);
            } else if (param.getOperateType() == CommonConstant.REJECT_BIND) {
                // 拒绝绑定
                productBind.setBindStatus(BindStatusEnum.REJECT.getCode());
                productBindService.saveOrUpdate(productBind);
                // 发送站内信
                MessageInfoSaveParam messageInfoSaveParam = new MessageInfoSaveParam();
                messageInfoSaveParam.setUserIdStr(String.valueOf(productBind.getSupplierId()));
                messageInfoSaveParam.setUserType(MessagePushUserTypeEnum.SUPPLIER.getCode());
                messageInfoSaveParam.setTitle("商品供应申请未通过");
                messageInfoSaveParam.setContent(String.format("采货王提醒您：您的商品:%s,%s 供应申请未通过，原因：%s", productBind.getSupplierProductId(), productInfo.getProductName(), param.getRemark()));
                messageInfoSaveParam.setPushType(MessagePushTypeEnum.SPECIFY_USER.getCode());
                messageInfoService.saveMessageInfo(messageInfoSaveParam);
            } else if (param.getOperateType() == CommonConstant.UNBOUND) {
                // 解绑前先查询商品是否需要下架
                Result<Boolean> judgeProductDown4Unbound = this.judgeProductDown4Unbound(param.getPpid());
                // 解绑 更新状态同时删除数据
                productBindService.update(new LambdaUpdateWrapper<ProductBind>()
                        .set(ProductBind::getBindStatus, BindStatusEnum.UNBOUND.getCode())
                        .set(ProductBind::getDelFlag, Boolean.TRUE)
                        .set(ProductBind::getRemark, param.getRemark())
                        .set(ProductBind::getUpdateTime, LocalDateTime.now())
                        .eq(ProductBind::getId, productBind.getId()));
                // 判断商品是否需要下架
                if(judgeProductDown4Unbound != null && judgeProductDown4Unbound.getData() != null && judgeProductDown4Unbound.getData()) {
                    PickProduct pickProduct = this.getOne(new LambdaQueryWrapper<PickProduct>().eq(PickProduct::getPpid, param.getPpid()).eq(PickProduct::getProductStatus, ProductStatusEnum.UP.getCode()).last("limit 1"));
                    if(pickProduct != null) {
                        pickProduct.setProductStatus(ProductStatusEnum.DOWN.getCode());
                        pickProduct.setUpdateTime(LocalDateTime.now());
                        this.saveOrUpdate(pickProduct);
                        // 记录商品下架操作日志
                        OperateLogInfo productDownOperateLogInfo = OperateLogInfo.builder()
                                .relateId(String.valueOf(param.getPpid()))
                                .type(ModuleEnum.PRODUCT.getCode())
                                .userId(0L)
                                .optUserId(0L)
                                .optUserName("系统")
                                .content("商品只绑定当前供应商，解除绑定商品同时下架产品")
                                .showType(LogShowTypeEnum.ADMIN.getCode())
                                .build();
                        operateLogInfoService.saveOrUpdate(productDownOperateLogInfo);
                    }
                }
                // 刷新ES数据
                refreshCacheDataService.refreshEsData(null, ImmutableList.of(productBind.getId()), CommonConstant.DELETE_PRODUCT);
                // 移除黑名单配置
                productRuleConfigService.removeUserFromProductRule(new RemoveUserFromProductRuleParam(param.getPpid(), String.valueOf(param.getSupplierId()), RestrictTargetEnum.SUPPLIER.getCode(), RestrictTypeEnum.BLACKLIST.getCode()));
            } else {
                return Result.errorInfo("操作类型错误");
            }
            // 记录操作日志
            OperateLogInfo bindOperateLogInfo = OperateLogInfo.builder()
                    .relateId(String.valueOf(param.getPpid()))
                    .type(ModuleEnum.PRODUCT.getCode())
                    .userId(productBind.getSupplierId())
                    .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                    .optUserName(oaTokenInfo.getName())
                    .content(param.getOperateType() == CommonConstant.AGREE_BIND ? "同意绑定商品" : param.getOperateType() == CommonConstant.REJECT_BIND ? "拒绝绑定商品" : "解除绑定商品")
                    .showType(LogShowTypeEnum.ADMIN.getCode())
                    .build();
            operateLogInfoService.saveOrUpdate(bindOperateLogInfo);
            return Result.successInfo("处理成功");
        } catch (Exception e) {
            log.error("商品绑定解绑发生异常，param:{},exception:", JSONUtil.toJsonStr(param), e);
            return Result.errorInfo("服务异常，稍后再试");
        }
    }

    @Override
    public Result<Boolean> judgeProductDown4Unbound(Long ppid) {
        if(ppid == null) {
            return Result.errorInfo("请求参数错误");
        }
        PickProduct pickProduct = this.getOne(new LambdaQueryWrapper<PickProduct>().eq(PickProduct::getPpid, ppid).eq(PickProduct::getProductStatus, ProductStatusEnum.UP.getCode()).last("limit 1"));
        if(pickProduct == null) {
            return Result.success(Boolean.FALSE);
        }
        int bindProductCount = productBindService.count(new LambdaQueryWrapper<ProductBind>().eq(ProductBind::getPpid, ppid).eq(ProductBind::getBindStatus, BindStatusEnum.BIND.getCode()));
        if(bindProductCount > 1) {
            return Result.success(Boolean.FALSE);
        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<SupplierProductDetail> getSupplierProductDetailInfo(AdmQuerySupplierProductDetailParam param) {
        if(param == null) {
            return Result.errorInfo("请求参数错误");
        }
        SupplierProductDetail supplierProductDetail = supplierProductDetailService.getOne(new LambdaQueryWrapper<SupplierProductDetail>().eq(SupplierProductDetail::getPpid, param.getPpid()).eq(SupplierProductDetail::getSupplierId, param.getSupplierId()));
        if(supplierProductDetail == null) {
            return Result.noData();
        }
        return Result.success(supplierProductDetail);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> updateSupplierProductDetailInfo(AdmUpdateSupplierProductDetailParam param) {
        try {
            OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
            if(oaTokenInfo == null) {
                return Result.notLoginError();
            }
            // 查询商品信息是否存在
            SupplierProductDetail supplierProductDetail = supplierProductDetailService.getOne(new LambdaQueryWrapper<SupplierProductDetail>().eq(SupplierProductDetail::getPpid, param.getPpid()).eq(SupplierProductDetail::getSupplierId, param.getSupplierId()));
            if(supplierProductDetail == null) {
                return Result.errorInfo("商品信息不存在或者已删除");
            }
            // 当前商品 税前税后单价
            BigDecimal buyNoTaxPrice = Optional.ofNullable(supplierProductDetail.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO);
            BigDecimal buyTaxPrice = Optional.ofNullable(supplierProductDetail.getBuyTaxPrice()).orElse(BigDecimal.ZERO);
            SupplierProductPriceModify currentPriceModify = priceModifyService.getSupplierProductPriceModify(supplierProductDetail.getId(), false, false);
            // 判断 是否是供应商新增商品 如果是则不改价进行记录
            LambdaQueryWrapper<ProductBind> wrapper = Wrappers.lambdaQuery(new ProductBind().setSupplierProductId(supplierProductDetail.getId()));
            LocalDateTime bindTime = productBindService.getOne(wrapper).getBindTime();
            log.info("供应商当前改价：{}", currentPriceModify);
            // 判断是否需要记录商品改价信息 此处为管理员修改，直接生效
            boolean isModify = buyNoTaxPrice.compareTo(Optional.ofNullable(param.getBuyNoTaxPrice()).orElse(BigDecimal.ZERO)) != 0
                    || buyTaxPrice.compareTo(Optional.ofNullable(param.getBuyTaxPrice()).orElse(BigDecimal.ZERO)) != 0;
            if (Boolean.TRUE.equals(isModify) && bindTime != null) {
                // 判断是否有供应商对当前商品进行改价
                if (ObjectUtil.isNull(currentPriceModify)) {
                    SupplierProductPriceModify priceModify = new SupplierProductPriceModify();
                    priceModify.setSupplierProductDetailId(supplierProductDetail.getId())
                            .setLastNoTaxPrice(supplierProductDetail.getBuyNoTaxPrice())
                            .setLastTaxPrice(supplierProductDetail.getBuyTaxPrice())
                            .setCurrentNoTaxPrice(param.getBuyNoTaxPrice())
                            .setCurrentTaxPrice(param.getBuyTaxPrice())
                            .setEnabled(true);
                    priceModifyService.save(priceModify);
                } else {
                    currentPriceModify
                            .setCurrentNoTaxPrice(param.getBuyNoTaxPrice())
                            .setLastTaxPrice(param.getBuyTaxPrice())
                            .setUpdateTime(LocalDateTime.now())
                            .setEnabled(true);
                    priceModifyService.updateById(currentPriceModify);
                }
            }
            supplierProductDetail.setPaymentDay(param.getPaymentDay());
            supplierProductDetail.setMaterial(param.getMaterial());
            supplierProductDetail.setBoxRule(param.getBoxRule());
            supplierProductDetail.setQualityDate(param.getQualityDate());
            supplierProductDetail.setBuyNoTaxPrice(param.getBuyNoTaxPrice());
            supplierProductDetail.setBuyTaxPrice(param.getBuyTaxPrice());
            supplierProductDetail.setDeliveryDay(param.getDeliveryDay());
            supplierProductDetail.setMinimumOrderQuantity(param.getMinimumOrderQuantity());
            supplierProductDetail.setNoReasonReturn(param.getNoReasonReturn());
            supplierProductDetail.setChangeDay(param.getChangeDay());
            supplierProductDetail.setBadPay(param.getBadPay());
            supplierProductDetail.setLackPay(param.getLackPay());
            supplierProductDetail.setAfterSalePolicy(param.getAfterSalePolicy());
            supplierProductDetail.setOtherPolicy(param.getOtherPolicy());
            supplierProductDetail.setUpdateTime(LocalDateTime.now());
            Integer remoteDeliveryFee = Objects.isNull(param.getRemoteDeliveryFee()) ? MagicalValueConstant.INT_0 : param.getRemoteDeliveryFee();
            supplierProductDetail.setRemoteDeliveryFee(remoteDeliveryFee);
            // 修改产品信息
            supplierProductDetailService.saveOrUpdate(supplierProductDetail);
            //价格修改之后判断是不是有修改为空的价格如果有需要在购物车删除
            handlePriceIsNull(supplierProductDetail);
            // 记录操作日志
            OperateLogInfo operateLogInfo = OperateLogInfo.builder()
                    .relateId(String.valueOf(param.getPpid()))
                    .type(ModuleEnum.PRODUCT.getCode())
                    .userId(supplierProductDetail.getSupplierId())
                    .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                    .optUserName(oaTokenInfo.getName())
                    .content("管理员修改供应商供货商品信息")
                    .showType(LogShowTypeEnum.ADMIN.getCode())
                    .build();
            operateLogInfoService.save(operateLogInfo);
            // 刷新ES数据
            refreshCacheDataService.refreshEsData(ImmutableList.of(supplierProductDetail.getPpid()), null, CommonConstant.UPDATE_PRODUCT);
            //操作日志记录详情记录
            if(param.getParamOld()!=null){
                logFactory.systemSaveLog(param,NewOperateLogInfoTypeEnum.SUPPLIER_BOUND_GOODS_QUERY.getCode());
            }
            return Result.successInfo("修改成功");
        } catch (Exception e) {
            log.error("管理员修改供应商商品信息发生异常，param:{},exception:", JSONUtil.toJsonStr(param), e);
            return Result.errorInfo("服务异常，稍后再试");
        }
    }

    /**
     *
     * @param supplierProductDetail
     */
    private void handlePriceIsNull(SupplierProductDetail supplierProductDetail){
        BigDecimal buyNoTaxPrice = supplierProductDetail.getBuyNoTaxPrice();
        BigDecimal buyTaxPrice = supplierProductDetail.getBuyTaxPrice();
        Long ppid = supplierProductDetail.getPpid();
        DeleteByInfo deleteByInfo = new DeleteByInfo();
        deleteByInfo.setPpid(ppid);
        if(buyTaxPrice==null){
            deleteByInfo.setPriceType(OrderPriceTypeEnum.TAX_INCLUDED.getCode());
            cartInfoService.deleteByInfo(deleteByInfo);
        }
        if(buyNoTaxPrice==null){
            deleteByInfo.setPriceType(OrderPriceTypeEnum.UNTAXED.getCode());
            cartInfoService.deleteByInfo(deleteByInfo);
        }

    }

    @Override
    public Boolean deleteProduct4DelSupplier(Long supplierId) {
        OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
        if(oaTokenInfo == null) {
            return Boolean.FALSE;
        }
        // 查询供应商绑定的商品
        List<SupplierProductDetail> supplierProductDetailList = supplierProductDetailService.list(new LambdaQueryWrapper<SupplierProductDetail>().eq(SupplierProductDetail::getSupplierId, supplierId));
        if(CollectionUtils.isEmpty(supplierProductDetailList)) {
            return Boolean.TRUE;
        }
        // 查询绑定关系
        List<ProductBind> needDeleteProductBindList = productBindService.list(new LambdaQueryWrapper<ProductBind>().eq(ProductBind::getSupplierId, supplierId));
        if(CollectionUtils.isEmpty(needDeleteProductBindList)) {
            return Boolean.TRUE;
        }
        List<Long> ppidList = supplierProductDetailList.stream().map(SupplierProductDetail::getPpid).collect(Collectors.toList());
        // 删除供应商的商品
        supplierProductDetailService.remove(new LambdaQueryWrapper<SupplierProductDetail>().eq(SupplierProductDetail::getSupplierId, supplierId));
        // 删除供应商绑定关系
        productBindService.remove(new LambdaQueryWrapper<ProductBind>().eq(ProductBind::getSupplierId, supplierId));
        // 判断商品绑定的供应商数量决定是否需要下架商品
        List<ProductBind> productBindList = productBindService.list(new LambdaQueryWrapper<ProductBind>().in(ProductBind::getPpid, ppidList).ne(ProductBind::getSupplierId, supplierId).eq(ProductBind::getBindStatus, BindStatusEnum.BIND.getCode()));
        List<Long> productDownPpidList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(productBindList)) {
            // 没有绑定的供应商，下架商品
            List<PickProduct> pickProductList = this.list(new LambdaQueryWrapper<PickProduct>().in(PickProduct::getPpid, ppidList));
            pickProductList.forEach(pickProduct -> {
                pickProduct.setProductStatus(ProductStatusEnum.DOWN.getCode());
                pickProduct.setUpdateTime(LocalDateTime.now());
            });
            this.saveOrUpdateBatch(pickProductList);
            productDownPpidList.addAll(ppidList);
        } else {
            List<Long> hasBindPpidList = productBindList.stream().map(ProductBind::getPpid).collect(Collectors.toList());
            for (Long ppid : ppidList) {
                if(hasBindPpidList.contains(ppid)) {
                    continue;
                }
                productDownPpidList.add(ppid);
            }
            if(CollectionUtils.isNotEmpty(productDownPpidList)) {
                List<PickProduct> pickProductList = this.list(new LambdaQueryWrapper<PickProduct>().in(PickProduct::getPpid, productDownPpidList));
                pickProductList.forEach(pickProduct -> {
                    pickProduct.setProductStatus(ProductStatusEnum.DOWN.getCode());
                    pickProduct.setUpdateTime(LocalDateTime.now());
                });
                this.saveOrUpdateBatch(pickProductList);
            }
        }
        // 刷新ES数据
        List<Long> needDeleteBindIdList = needDeleteProductBindList.stream().map(ProductBind::getId).collect(Collectors.toList());
        refreshCacheDataService.refreshEsData(null, needDeleteBindIdList, CommonConstant.DELETE_PRODUCT);
        List<OperateLogInfo> operateLogInfoList = Lists.newArrayList();
        // 记录删除供应商商品操作日志
        OperateLogInfo deleteSupplierProductLog = OperateLogInfo.builder()
                .relateId(String.valueOf(supplierId))
                .type(ModuleEnum.PRODUCT.getCode())
                .userId(supplierId)
                .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                .optUserName(oaTokenInfo.getName())
                .content("管理员删除供应商，同时删除供应商绑定的商品")
                .showType(LogShowTypeEnum.ADMIN.getCode())
                .build();
        operateLogInfoList.add(deleteSupplierProductLog);
        // 记录下架的商品
        if(CollectionUtils.isNotEmpty(productDownPpidList)) {
            for (Long ppid : productDownPpidList) {
                OperateLogInfo downProductLog = OperateLogInfo.builder()
                        .relateId(String.valueOf(ppid))
                        .type(ModuleEnum.PRODUCT.getCode())
                        .userId(supplierId)
                        .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                        .optUserName(oaTokenInfo.getName() + "系统下架")
                        .content("管理员删除供应商，商品ppid:" + ppid + " 没有其他绑定的供应商，下架商品")
                        .showType(LogShowTypeEnum.ADMIN.getCode())
                        .build();
                operateLogInfoList.add(downProductLog);
            }
        }
        operateLogInfoService.saveOrUpdateBatch(operateLogInfoList);
        return Boolean.TRUE;
    }

    @Override
    public PickProduct getByPpidAndProductType(Long ppid, Integer productType) {
        // 两个参数至少要传一个
        if(null == ppid && null == productType){
            return null;
        }
        LambdaQueryWrapper<PickProduct> query = new LambdaQueryWrapper<>();
        if(null != ppid){
            query.eq(PickProduct::getPpid, ppid);
        }
        if(null != productType){
            query.eq(PickProduct::getProductType, productType);
        }
        return baseMapper.selectOne(query);
    }

    private PickProduct buildPickProduct(PickProduct pickProduct, AddOrUpdateProductParam param) {
        pickProduct.setJiuJiProductId(param.getJiuJiProductId());
        pickProduct.setPpid(param.getPpid());
        pickProduct.setProductType(param.getProductType());
        pickProduct.setAdvicePrice(param.getAdvicePrice());
        pickProduct.setProductFuture(param.getProductFuture());
        pickProduct.setProductConfig(param.getProductConfig());
        pickProduct.setDefaultArea(param.getDefaultArea());
        pickProduct.setHotArea(param.getHotArea());
        pickProduct.setHappyArea(param.getHappyArea());
        pickProduct.setRecommendArea(param.getRecommendArea());
        return pickProduct;
    }

}

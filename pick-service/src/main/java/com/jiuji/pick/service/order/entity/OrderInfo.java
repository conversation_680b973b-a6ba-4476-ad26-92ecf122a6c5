package com.jiuji.pick.service.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.pick.common.enums.OrderStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 采购定单表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_order_info")
public class OrderInfo extends Model<OrderInfo> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号  不用创建oa订单情况下为id的负数，兼容orderNo不为空产生的异常情况
     */
    private Long orderNo;

    /**
     * 特殊逻辑标识  null或0是原来逻辑 1 不用创建oa订单
     */
    private Integer orderFlag = 0;

    /**
     * 销售人姓名
     */
    private String saleName;

    /**
     * 销售人工号
     */
    private Integer saleJobNumber;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 租户id
     */
    private Long xtenantId;

    /**
     * 合作伙伴ID
     */
    private Long partnerId;

    /**
     * 渠道商ID
     */
    private String channelId;

    /**
     * 订单金额
     */
    private BigDecimal totalPrice;

    /**
     * @see OrderStatusEnum
     * 订单状态，1已审核，2取消，3已发货，4完成
     */
    private Integer orderStatus;

    /**
     * 订单类型，0：小件，1：大件，2：虚拟商品，3：固定资产，4：虚拟资产
     */
    private Integer orderType;

    /**
     * 渠道，oa/小型SAAS  0-OA 1-小型
     */
    private Integer channelType;

    /**
     * 采购单名称
     */
    private String orderTitle;

    /**
     * 外部订单号
     */
    private String outOrderNo;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 收货地址
     */
    private String receiveAddress;

    /**
     * 收货门店ID
     */
    private String receivePoiId;

    /**
     * 收货门店名称
     */
    private String receivePoiName;

    /**
     * 收货门店cityId
     */
    private String receivePoiCityId;

    /**
     * 快递类型
     */
    private String deliveryType;

    /**
     * 快递单号
     */
    private String deliveryNo;

    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 销售单号
     */
    private String saleOrderNo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

package com.jiuji.pick.service.product.param;

import com.jiuji.pick.common.param.BasePageParam;
import lombok.Data;

import java.util.List;

/**
 * @function:
 * @description: QueryProductListParam.java
 * @date: 2021/05/06
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class QueryProductListParam extends BasePageParam {

    private Long categoryId;
    /**
     * 不需要前端传，根据categoryId获取他的所有子分类
     */
    private List<Long> categoryIdList;
    private Integer productStatus;
    // 搜索类型，1ppid,2产品名称
    private String searchType;
    private String keyWord;

}

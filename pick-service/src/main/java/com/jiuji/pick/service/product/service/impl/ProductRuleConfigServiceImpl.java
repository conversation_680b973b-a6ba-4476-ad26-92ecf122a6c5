package com.jiuji.pick.service.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.jiuji.pick.common.bo.OATokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.constant.CommonConstant;
import com.jiuji.pick.common.enums.BindStatusEnum;
import com.jiuji.pick.common.enums.LogShowTypeEnum;
import com.jiuji.pick.common.enums.ModuleEnum;
import com.jiuji.pick.common.enums.NewOperateLogInfoTypeEnum;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.common.entity.OperateLogInfo;
import com.jiuji.pick.service.common.service.LogService;
import com.jiuji.pick.service.common.service.NewOperateLogFactory;
import com.jiuji.pick.service.common.service.NewOperateLogInfoService;
import com.jiuji.pick.service.common.service.OperateLogInfoService;
import com.jiuji.pick.service.product.entity.PickProduct;
import com.jiuji.pick.service.product.entity.ProductBind;
import com.jiuji.pick.service.product.entity.ProductRuleConfig;
import com.jiuji.pick.service.product.mapper.PickProductMapper;
import com.jiuji.pick.service.product.mapper.ProductRuleConfigMapper;
import com.jiuji.pick.service.product.param.EditProductWhiteConfigParam;
import com.jiuji.pick.service.product.param.RemoveUserFromProductRuleParam;
import com.jiuji.pick.service.product.param.SaveProductWhiteConfigParam;
import com.jiuji.pick.service.product.param.SearchProductWhiteConfigParam;
import com.jiuji.pick.service.product.service.ProductBindService;
import com.jiuji.pick.service.product.service.ProductRuleConfigService;
import com.jiuji.pick.service.product.service.RefreshCacheDataService;
import com.jiuji.pick.service.product.vo.QueryIndexWhiteConfigVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Service
public class ProductRuleConfigServiceImpl extends ServiceImpl<ProductRuleConfigMapper, ProductRuleConfig> implements ProductRuleConfigService {

    @Resource
    private ProductRuleConfigMapper ruleConfigMapper;
    @Resource
    private PickProductMapper pickProductMapper;
    @Resource
    private CurrentRequestComponent currentRequestComponent;
    @Resource
    private OperateLogInfoService operateLogInfoService;
    @Resource
    private RefreshCacheDataService refreshCacheDataService;
    @Resource
    private ProductBindService productBindService;
    @Resource
    private NewOperateLogFactory logFactory;

    @Override
    public Result saveProductWhiteConfig(SaveProductWhiteConfigParam param) {
        if (param.getRestrictTarget() == null){
            return Result.error("请选择限制对象");
        }
        if (param.getRestrictType() == null){
            return Result.error("请选择限制类型");
        }
        if (StringUtils.isBlank(param.getPpid()) ){
            return Result.error("请输入商品PPID");
        }
        if (StringUtils.isBlank(param.getUserIds())){
            return Result.error("请输入用户ID");
        }
        OATokenInfo oaTokenInfo = currentRequestComponent.getOATokenInfoBoWithoutCheck();
        if(oaTokenInfo == null) {
            return Result.notLoginError();
        }
        // 过滤中文逗号
        param.setPpid(RegExUtils.replaceAll(param.getPpid(), "，", ","));
        List<Long> ppidList = Splitter.on(",").splitToList(param.getPpid()).stream().map(ppid -> Long.valueOf(ppid)).collect(Collectors.toList());
        List<ProductRuleConfig> productRuleConfigs = ruleConfigMapper.selectList(new LambdaQueryWrapper<ProductRuleConfig>().in(ProductRuleConfig::getPpid, ppidList).
                eq(ProductRuleConfig::getRestrictTarget, param.getRestrictTarget()).eq(ProductRuleConfig::getRestrictType, param.getRestrictType()));
        if (CollectionUtils.isNotEmpty(productRuleConfigs)){
            List<Long> hasPpIdList = productRuleConfigs.stream().map(productRuleConfig -> productRuleConfig.getPpid()).collect(Collectors.toList());
            return Result.errorInfo(Joiner.on(",").join(hasPpIdList) + " ppid已存在，请重新输入或者去编辑页面添加！");
        }

        // 保存数据
        String userIds = "," + param.getUserIds() + ",";
        List<PickProduct> pickProductList = pickProductMapper.selectList(new LambdaQueryWrapper<PickProduct>().in(PickProduct::getPpid, ppidList));
        if(CollectionUtils.isEmpty(pickProductList)) {
            return Result.errorInfo("ppid对应的商品不存在，请重新输入ppid");
        }
        Map<Long, PickProduct> pickProductMap = pickProductList.stream().collect(Collectors.toMap(PickProduct::getPpid, Function.identity(), (p1, p2) -> p1));
        List<Long> errorPpidList = Lists.newArrayList();
        List<ProductRuleConfig> productRuleConfigList = Lists.newArrayList();
        List<OperateLogInfo> operateLogInfoList = Lists.newArrayList();
        for (Long ppid : ppidList) {
            // 生成黑名单数据
            PickProduct pickProduct = pickProductMap.get(ppid);
            if(pickProduct == null) {
                errorPpidList.add(ppid);
                continue;
            }
            ProductRuleConfig productRuleConfig = new ProductRuleConfig();
            productRuleConfig.setProductId(pickProduct.getId());
            productRuleConfig.setPpid(ppid);
            productRuleConfig.setUserIds(userIds);
            productRuleConfig.setRestrictTarget(param.getRestrictTarget());
            productRuleConfig.setRestrictType(param.getRestrictType());
            productRuleConfigList.add(productRuleConfig);

            // 生成操作日志数据
            OperateLogInfo operateLogInfo = OperateLogInfo.builder()
                    .relateId(String.valueOf(pickProduct.getPpid()))
                    .type(ModuleEnum.COMMON.getCode())
                    .userId(Long.valueOf(oaTokenInfo.getUserId()))
                    .optUserId(Long.valueOf(oaTokenInfo.getUserId()))
                    .optUserName(oaTokenInfo.getName())
                    .content("管理员添加黑名单，ppid:" + ppid + "，黑名单用户：" + param.getUserIds())
                    .showType(LogShowTypeEnum.ADMIN.getCode())
                    .build();
            operateLogInfoList.add(operateLogInfo);
        }
        if(CollectionUtils.isNotEmpty(productRuleConfigList)) {
            this.saveOrUpdateBatch(productRuleConfigList);
        }
        if(CollectionUtils.isNotEmpty(operateLogInfoList)) {
            operateLogInfoService.saveOrUpdateBatch(operateLogInfoList);
        }
        // 刷新ES数据
        List<Long> supplierIdList = Splitter.on(",").splitToList(param.getUserIds()).stream().map(Long::new).collect(Collectors.toList());
        List<ProductBind> productBindList = productBindService.list(new LambdaQueryWrapper<ProductBind>().in(ProductBind::getPpid, ppidList).in(ProductBind::getSupplierId, supplierIdList).eq(ProductBind::getBindStatus, BindStatusEnum.BIND.getCode()));
        if(CollectionUtils.isNotEmpty(productBindList)) {
            List<Long> productBindIdList = productBindList.stream().map(ProductBind::getId).collect(Collectors.toList());
            refreshCacheDataService.refreshEsData(null, productBindIdList, CommonConstant.DELETE_PRODUCT);
        }
        if(CollectionUtils.isNotEmpty(errorPpidList)) {
            return Result.success(Joiner.on(",").join(errorPpidList) + " ppid对应的商品不存在，添加失败，其他ppid已添加成功");
        }
        return Result.success("保存成功");
    }

    @Override
    public Result removeWhiteConfig(Long id) {
        if(id==null){
            return Result.error("请传入需要删除的id");
        }
        ruleConfigMapper.deleteById(id);
        return Result.success("删除成功");
    }

    @Override
    public Result editSaveWhiteConfig(EditProductWhiteConfigParam param) {
        if (param.getId() == null || StringUtils.isBlank(param.getUserIds()) || StringUtils.isBlank(param.getPpid())){
            return Result.error("请求参数不能为空");
        }
        ProductRuleConfig productRuleConfig = ruleConfigMapper.selectById(param.getId());
        if(productRuleConfig == null) {
            return Result.errorInfo("数据不存在，请输入正确ID");
        }
        if(!Objects.equals(param.getPpid(), String.valueOf(productRuleConfig.getPpid()))) {
            return Result.errorInfo("ppid不能变更");
        }

        String userIds = ","+param.getUserIds() + ",";
        productRuleConfig.setUserIds(userIds);
        productRuleConfig.setUpdateTime(LocalDateTime.now());
        this.saveOrUpdate(productRuleConfig);
        if(param.getParamOld()!=null){
            logFactory.systemSaveLog(param, NewOperateLogInfoTypeEnum.BLACK_AND_WHITE_LIST_MANAGEMENT.getCode());
        }
        return Result.success("修改成功");
    }

    @Override
    public Result<ProductRuleConfig> getProductRuleConfigDetail(Long id) {
        if (id==null){
           return Result.error("参数错误");
        }
        ProductRuleConfig productRuleConfig = ruleConfigMapper.selectById(id);
        if(productRuleConfig == null) {
            return Result.errorInfo("数据不存在，请输入正确ID");
        }
        productRuleConfig.setUserIds(CommonUtil.trimFirstAndLastChar(productRuleConfig.getUserIds(), ','));
        return Result.success(productRuleConfig);
    }

    @Override
    public Result<Page<QueryIndexWhiteConfigVo>> conditionSearch(SearchProductWhiteConfigParam param) {
        Page<QueryIndexWhiteConfigVo> integerPage = new Page<>(param.getCurrentPage(), param.getSize());
        Page<QueryIndexWhiteConfigVo> whiteConfig = ruleConfigMapper.conditionSearchWhiteConfig(integerPage,param);
        if(whiteConfig == null || CollectionUtils.isEmpty(whiteConfig.getRecords())) {
            return Result.noData(new Page<>());
        }
        whiteConfig.getRecords().forEach(it->{
            String userId = CommonUtil.trimFirstAndLastChar(it.getUserIds(), ',');
            it.setUserIds(userId);
        });
        return Result.success(whiteConfig);
    }

    @Override
    public Boolean removeUserFromProductRule(RemoveUserFromProductRuleParam param) {
        if(param == null && param.getPpid() == null || StringUtils.isBlank(param.getUserId()) ||
                param.getRestrictTarget() == null || param.getRestrictType() == null) {
            return Boolean.FALSE;
        }
        ProductRuleConfig productRuleConfig = this.getOne(new LambdaQueryWrapper<ProductRuleConfig>().eq(ProductRuleConfig::getPpid, param.getPpid())
                .eq(ProductRuleConfig::getRestrictTarget, param.getRestrictTarget()).eq(ProductRuleConfig::getRestrictType, param.getRestrictType()));
        if(productRuleConfig == null || StringUtils.isBlank(productRuleConfig.getUserIds())) {
            return Boolean.TRUE;
        }
        String userIds = CommonUtil.trimFirstAndLastChar(productRuleConfig.getUserIds(), ',');
        if(StringUtils.isBlank(userIds)) {
            return Boolean.TRUE;
        }
        String[] userIdArray = userIds.split(",");
        if(userIdArray == null || userIdArray.length < 1) {
            return Boolean.TRUE;
        }
        List<String> userIdList = Lists.newArrayList();
        for (String userId : userIdArray) {
            userIdList.add(userId);
        }
        if(userIdList.contains(param.getUserId())) {
            userIdList.remove(param.getUserId());
        }
        if(CollectionUtils.isEmpty(userIdList)) {
            // 删除数据
            return this.removeWhiteConfig(productRuleConfig.getId()).isSucceed();
        }
        String newUserIds = "," + Joiner.on(",").join(userIdList) + ",";
        productRuleConfig.setUserIds(newUserIds);
        return this.saveOrUpdate(productRuleConfig);
    }
}

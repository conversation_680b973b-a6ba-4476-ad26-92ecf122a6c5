package com.jiuji.pick.service.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.pick.service.order.entity.OrderInfo;
import com.jiuji.pick.service.order.param.AutomaticPickupOrder;
import com.jiuji.pick.service.order.param.OrderSearchParam;
import com.jiuji.pick.service.order.vo.OrderDetailVO;
import com.jiuji.pick.service.order.vo.OrderListExportVO;
import com.jiuji.pick.service.order.vo.OrderListVO;
import com.jiuji.pick.service.order.vo.PlatformOrderListVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 采购定单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
public interface OrderInfoMapper extends BaseMapper<OrderInfo> {


    /**
     * 自动完成订单
     * @return
     */
    List<AutomaticPickupOrder> selectAutomaticPickupOrder();

    Page<OrderListVO> getOrderList(@Param("page") Page<OrderListVO> page,
                                          @Param("type") Integer type,
                                          @Param("partnerId") Long partnerId,
                                          @Param("supplierId") Long supplierId,
                                          @Param("param") OrderSearchParam param);


    Page<OrderListExportVO> getOrderListV2(@Param("page") Page<OrderListVO> page,
                                           @Param("type") Integer type,
                                           @Param("partnerId") Long partnerId,
                                           @Param("supplierId") Long supplierId,
                                           @Param("param") OrderSearchParam param);
    /**
     * @param type
     * @param partnerId
     * @param supplierId
     * @param orderId
     * @return
     */
    OrderDetailVO getOrderDetail(@Param("type") Integer type,
                                 @Param("partnerId") Long partnerId,
                                 @Param("supplierId") Long supplierId,
                                 @Param("orderId") Long orderId);

    int countPlatformOrderList(@Param("orderStatus") Integer orderStatus,
                               @Param("keyType") Integer keyType,
                               @Param("keyValue") String keyValue,
                               @Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime,
                               @Param("orderType") Integer orderType);

    List<PlatformOrderListVO> getPlatformOrderList(@Param("orderStatus") Integer orderStatus,
                                                   @Param("keyType") Integer keyType,
                                                   @Param("keyValue") String keyValue,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime,
                                                   @Param("orderType") Integer orderType,
                                                   @Param("current") Integer current,
                                                   @Param("size") Integer size,
                                                   @Param("needLimit") boolean needLimit);

    List<OrderListExportVO> getPlatformOrderExportList(@Param("orderStatus") Integer orderStatus,
                                                   @Param("keyType") Integer keyType,
                                                   @Param("keyValue") String keyValue,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime,
                                                   @Param("orderType") Integer orderType,
                                                   @Param("current") Integer current,
                                                   @Param("size") Integer size,
                                                   @Param("needLimit") boolean needLimit);

    List<Long> getReceiveOrderList();


    String getProductName(@Param("id") Long id);

    Long getOrderIdBySaleNo(@Param("saleNo") Integer saleNo);

}

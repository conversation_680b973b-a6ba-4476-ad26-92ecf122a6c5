package com.jiuji.pick.service.product.vo;

import lombok.Data;

import java.util.List;

/**
 * @function:
 * @description: IndexMiniProductSpecVo.java
 * @date: 2021/05/25
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class IndexMiniProductSpecVo {

    private Long jiuJiProductId;
    private String productColor;
    private Long productId;
    private Long supplierProductId;
    private Long ppid;
    private Boolean selected;
    private String bPic;
    private String edition;
    private Integer showSort;
    /**
     * 该productId下所有的ppid
     */
    private List<Long> ppidList;


}

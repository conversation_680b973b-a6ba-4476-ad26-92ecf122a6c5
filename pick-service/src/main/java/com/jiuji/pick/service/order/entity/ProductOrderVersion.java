package com.jiuji.pick.service.order.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 商品下单快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_product_order_version")
public class ProductOrderVersion extends Model<ProductOrderVersion> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品详情ID
     */
    private Long productDetailId;

    /**
     * 商品ppid
     */
    private Long ppid;

    /**
     * 合作伙伴表主键ID
     */
    private Long partnerId;

    /**
     * 订单号
     */
    private Long orderNo;

    /**
     * 订单表主键id
     */
    private Long orderId;

    /**
     * 供应商id
     */
    private Long supplierUserId;

    /**
     * 采购未税单价
     */
    private BigDecimal buyNoTaxPrice;

    /**
     * 采购含税单价
     */
    private BigDecimal buyTaxPrice;

    /**
     * 使用的价格类型,0:未税,1:含税
     */
    private Integer priceType;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String productColor;

    /**
     * 商品图片
     */
    private String productImg;

    /**
     * 商品分类名称
     */
    private String productCidName;

    /**
     * 商品分类
     */
    private Long productCid;

    /**
     * 商品条码
     */
    private String productBarCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    private Boolean delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

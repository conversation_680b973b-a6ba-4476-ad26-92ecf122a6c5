package com.jiuji.pick.service.product.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.service.product.entity.ProductBind;
import com.jiuji.pick.service.product.mapper.ProductBindMapper;
import com.jiuji.pick.service.product.service.ProductBindService;
import com.jiuji.pick.service.product.vo.SupplierChannelCountVO;
import com.jiuji.pick.service.product.vo.SupplierProductCountVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.sql.Wrapper;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Service
public class ProductBindServiceImpl extends ServiceImpl<ProductBindMapper, ProductBind> implements ProductBindService {

    @Override
    public List<SupplierProductCountVO> supplierProductCount(List<Long> supplierIdList) {
        if(CollectionUtils.isEmpty(supplierIdList)){
            return Lists.newArrayList();
        }
        List<SupplierProductCountVO> countVOList = baseMapper.supplierProductCount(supplierIdList);
        List<SupplierProductCountVO> list = countVOList.stream().filter(e -> null != e.getSupplierId()).collect(Collectors.toList());
        return list;
    }

    @Override
    public List<ProductBind> listProductBind(Collection<Long> supplierProductIdList) {
        if (CollectionUtils.isEmpty(supplierProductIdList)) {
            return Lists.newArrayList();
        }
        return this.list(Wrappers.<ProductBind>lambdaQuery().in(ProductBind::getSupplierProductId, supplierProductIdList));
    }
}

package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_category_diy")
public class CategoryDiy extends Model<CategoryDiy> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 链接
     */
    private String link;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 样式：0.列表 1. 瀑布
     */
    private Integer style;

    /**
     * 平台 0-pc 1-app
     */
    private Integer platform;

    private Integer xtenant;

    private Integer updateUserId;

    /**
     * 逻辑删除标识
     */
    private Boolean deleted;

    /**
     * 所有父id，按顺序逗号分隔，方便查询
     */
    private String parentIds;

    /**
     * 父级id，默认为0，代表没有父级
     */
    private Integer parentId;

    /**
     * 右侧标签名称
     */
    private String rightTagName;

    /**
     * 右侧标签链接
     */
    private String rightTagLink;

    /**
     * 树的层级，默认为1
     */
    private Integer level;

    /**
     * 图标
     */
    private String fid;

    /**
     * 商品类型 0--普通商品 1--样品特卖商品
     */
    private Integer productType;

    /**
     * 广告位id
     */
    private String adId;

    /**
     * 分类图片（1级分类使用图片）
     */
    private String cateImage;

    /**
     * 分类小标签（1级分类使用标签）
     */
    private String cateSmallTag;

    /**
     * 子分类标题透明图
     */
    private String titleImage;

    /**
     * 子分类背景图
     */
    private String backgroundImage;

    /**
     * 底部标签名称
     */
    private String bottomTagName;

    /**
     * 底部标签链接
     */
    private String bottomTagLink;

    /**
     * 热销商品更换方式：0-手动  1-自动
     */
    private Boolean hotProductAutoChange;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

package com.jiuji.pick.service.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.order.bo.SupplierProductInfoBO;
import com.jiuji.pick.service.order.entity.CartInfo;
import com.jiuji.pick.service.order.vo.CartInfoVO;
import com.jiuji.pick.service.order.vo.ConfirmOrderInfoVO;
import com.jiuji.pick.service.order.vo.DeleteByInfo;

import java.util.List;

/**
 * <p>
 * 购物车信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-06
 */
public interface CartInfoService extends IService<CartInfo> {

    /**
     * 添加购物车
     *
     * @param productId      商品id
     * @param productCount   商品数量
     * @param supplierUserId 供应商id
     * @param type           type
     * @return
     */
    Result<Long> add(Long productId, Integer productCount, Long supplierUserId, Integer type,Integer priceType);

    /**
     * 直接购买加入购物车
     *
     * @param productId      商品id
     * @param productCount   商品数量
     * @param supplierUserId 供应商id
     * @return
     */
    Result<Long> directBuy(Long productId, Integer productCount, Long supplierUserId,Integer priceType);

    /**
     * 根据id删除购物车，多个用","分隔
     *
     * @param idList  idList
     * @return
     */
    Result<Boolean> deleteById(List<Long> idList);


    /**
     * 根据ppid以及含税类型来进行购物车的删除
     * @param deleteByInfo
     * @return
     */
    void deleteByInfo(DeleteByInfo deleteByInfo);


    /**
     * 更新购物车数量
     *
     * @param id           购物车id
     * @param productCount 数量
     * @return
     */
    Result<Boolean> updateCart(Long id, Integer productCount);

    /**
     * 查询购物车
     *
     * @return
     */
    Result<CartInfoVO> listAll();

    /**
     * 根据购物车进行订单确认页
     *
     * @param cartIdList 购物车id集合
     * @return
     */
    Result<ConfirmOrderInfoVO> confirmOrderByCartIds(List<Long> cartIdList);

    /**
     * 获取供应商商品数据
     *
     * @param productDetailIdList 商品详情id集合
     * @return
     */
    List<SupplierProductInfoBO> getSupplierProductInfoByIdList(List<Long> productDetailIdList);

    /**
     * 查询购物车列表
     *
     * @param cartIdList
     * @return
     */
    List<CartInfo> listByCartIds(List<Long> cartIdList);

}

package com.jiuji.pick.service.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.query.SupplierUserCheckQuery;
import com.jiuji.pick.service.user.vo.SupplierUserDetailVO;
import com.jiuji.pick.service.user.vo.SupplierUserVO;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
public interface SupplierUserService extends IService<SupplierUser> {

    /***
     * @description: 详情信息
     * @Param: [id]
     * @author: Lbj
     * @date: 2021/4/30 15:59
     */
    SupplierUserDetailVO getDetailInfo(Long id);


    /***
     * @description: 添加或者修改
     * @Param: [detailVO]
     * @return: boolean
     * @author: Lbj
     * @date: 2021/5/6 13:59
     */
    boolean saveOrUpdateDetail(SupplierUserDetailVO detailVO);

    /***
     * @description: 修改密码
     * @Param: [id, password]
     * @return: boolean
     * @author: Lbj
     * @date: 2021/5/6 13:58
     */
    Result<String> changePassword(Long id, String newPassword, String oldPassword, Long userId, String userName);

    /***
     * @description: 审核通过
     * @Param: [id]
     * @author: Lbj
     * @date: 2021/5/6 14:00
     */
    boolean pass(Long id, String remark, Long userId, String userName);

    /***
     * @description: 审核通驳回
     * @Param: [id]
     * @author: Lbj
     * @date: 2021/5/6 14:00
     */
    boolean reject(Long id, String remark, Long userId, String userName);

    /***
     * @description: 删除
     * @Param: [id]
     * @return: boolean
     * @author: Lbj
     * @date: 2021/5/6 13:58
     */
    boolean delete(Long id, Long userId, String userName);

    /***
     * @description: 审核列表
     * @Param: [checkQuery]
     * @author: Lbj
     * @date: 2021/5/6 14:40
     */
    IPage<SupplierUserVO> listPageCheck(SupplierUserCheckQuery checkQuery);

    /***
     * @description: 后台供应商列表
     * @Param: [checkQuery]
     * @author: Lbj
     * @date: 2021/5/6 14:40
     */
    IPage<SupplierUserVO> listPageAdmin(SupplierUserCheckQuery checkQuery);

    /***
     * @description: 通过登录名查询
     * @Param: [longinName]
     * @author: Lbj
     * @date: 2021/5/7 14:57
     */
    SupplierUser getByLoginName(String loginName);

    /***
     * @description: 通过名名称查询
     * @Param: [longinName]
     * @author: Lbj
     * @date: 2021/5/7 14:57
     */
    SupplierUser getByName(String name);

    /***
     * @description: 登陆
     * @Param: [loginName, password]
     * @return: boolean
     * @author: Lbj
     * @date: 2021/5/7 14:53
     */
    Result<String> login(String loginName, String password);

    /**
     * 供应商模拟登录
     */
    String simulateLogin();

    /***
     * @description: 退出登陆
     * @author: Lbj
     * @date: 2021/5/7 14:53
     */
    Result<String> logout(String token);

    /***
     * @description: 根据名称和登录名 检查是否有重复的数据
     * @Param: [id, loginName, name]
     * @author: Lbj
     * @date: 2021/5/7 17:37
     */
    Result<String> hasRepeatData(Long id, String loginName, String name);


    /***
     * @description: 导入供应商数据
     * @Param: [idStr]  多个以逗号间隔
     * @author: Lbj
     * @date: 2021/5/8 16:30
     */
    Result<String> importSupplierByIds(String idStr);
}

/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.constant.RedisKey;
import com.jiuji.pick.service.product.entity.ParamValue;
import com.jiuji.pick.service.product.mapper.ParamValueMapper;
import com.jiuji.pick.service.product.service.ParamValueService;
import com.jiuji.pick.service.product.vo.SearchResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-09-01
 */
@Service
@Slf4j
public class ParamValueServiceImpl extends ServiceImpl<ParamValueMapper, ParamValue> implements ParamValueService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public Map<Long, List<String>> getSpecParamsByMap(List<Long> ppids) {
        Map<Long, List<String>> result = new HashMap<>();
        if (ppids.isEmpty()) {
            return result;
        }
        final List<Object> keys = ppids.stream().map(String::valueOf).collect(Collectors.toList());
        try {
            Boolean keyExist = stringRedisTemplate.hasKey(RedisKey.PRODUCT_TAG_V2);
            // 如果redis中存在，直接取redis中的返回，redis数据每20分钟刷新一次
            // 刷新逻辑：ScheduleRefreshCacheApi.refreshProductTagCache()
            if (keyExist != null && keyExist) {
                final List<Object> tags = stringRedisTemplate.opsForHash()
                        .multiGet(RedisKey.PRODUCT_TAG_V2, keys);
                for (int i = 0; i < ppids.size(); i++) {
                    final Object o = tags.get(i);
                    final Long key = ppids.get(i);
                    if (o == null) {
                        result.put(key, new ArrayList<>());
                    } else {
                        result.put(key, Arrays.stream(StringUtils.split(o.toString(), ","))
                                .collect(Collectors.toList()));
                    }
                }
            } else {  // 讲道理不会进入该分支，redis数据是每20分钟定时刷新一次的
                log.info("商品规格数据缓存过期, ppids: {}", ppids);
                for (Long ppid : ppids) {
                    result.put(ppid, new ArrayList<>());
                }
            }
        } catch (Exception e) {
            log.error("获取参数缓存失败", e);
        }
        return result;
    }

    @Override
    public List<SearchResultVo.Screening> getCateSelectParamsScreening(int category) {
        return baseMapper.getCateSelectParamsScreening(category);
    }
}

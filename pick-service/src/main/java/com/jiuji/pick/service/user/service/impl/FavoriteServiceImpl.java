package com.jiuji.pick.service.user.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.component.CurrentRequestComponent;
import com.jiuji.pick.common.component.minifile.MiniFileConfig;
import com.jiuji.pick.common.constant.WebConstant;
import com.jiuji.pick.common.enums.BindStatusEnum;
import com.jiuji.pick.common.enums.ProductStatusEnum;
import com.jiuji.pick.common.utils.CommonUtil;
import com.jiuji.pick.common.utils.ImageUtil;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.entity.*;
import com.jiuji.pick.service.product.service.*;
import com.jiuji.pick.service.user.entity.Favorite;
import com.jiuji.pick.service.user.entity.SupplierUser;
import com.jiuji.pick.service.user.mapper.FavoriteMapper;
import com.jiuji.pick.service.user.service.FavoriteService;
import com.jiuji.pick.service.user.service.SupplierUserService;
import com.jiuji.pick.service.user.vo.FavoriteVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 收藏夹 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
@Service
public class FavoriteServiceImpl extends ServiceImpl<FavoriteMapper, Favorite> implements FavoriteService {

    @Resource
    private CurrentRequestComponent currentRequestComponent;

    @Resource
    private SupplierProductDetailService supplierProductDetailService;

    @Resource
    private SupplierUserService supplierUserService;

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private MiniFileConfig miniFileConfig;

    @Resource
    private ProductBindService productBindService;

    @Resource
    private PickProductService pickProductService;

    @Resource
    private ProductRuleConfigService productRuleConfigService;

    @Override
    public Result<Boolean> add(Integer supplierProductId) {
        if (Objects.isNull(supplierProductId)) {
            return Result.errorInfo("供应商商品id不能为空");
        }
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (Objects.isNull(partnerTokenInfo)) {
            return Result.notLoginError();
        }

        Favorite existFavorite = this.getOne(Wrappers.<Favorite>lambdaQuery().eq(Favorite::getSupplierProductId,supplierProductId).eq(Favorite::getPartnerUserId,partnerTokenInfo.getId()),false);
        if (existFavorite != null) {
            return Result.errorInfo("收藏夹已存在该商品");
        }

        SupplierProductDetail supplierProductDetail = supplierProductDetailService.getById(supplierProductId);
        Favorite favorite = new Favorite();
        favorite.setSupplierProductId(supplierProductId.longValue());
        favorite.setSupplierUserId(supplierProductDetail.getSupplierId());
        favorite.setPartnerUserId(partnerTokenInfo.getId());
        return Result.success(this.save(favorite));
    }

    @Override
    public Result<Boolean> del(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return Result.errorInfo("ids，不能为空");
        }
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (Objects.isNull(partnerTokenInfo)) {
            return Result.notLoginError();
        }
        List<Long> idList = CommonUtil.covertIdStr2Long(ids);
        if (CollectionUtils.isEmpty(idList)) {
            return Result.noData();
        }
        return Result.success(this.removeByIds(idList));
    }

    @Override
    public Result<FavoriteVO> getList() {
        PartnerTokenInfo partnerTokenInfo = currentRequestComponent.getPartnerTokenInfoWithoutCheck();
        if (Objects.isNull(partnerTokenInfo)) {
            return Result.notLoginError();
        }

        List<Favorite> list = this.list(Wrappers.<Favorite>lambdaQuery().eq(Favorite::getPartnerUserId, partnerTokenInfo.getId()));
        if (CollectionUtils.isEmpty(list)) {
            return Result.noData();
        }

        Map<Long, List<Favorite>> favoriteMap = list.stream().collect(Collectors.groupingBy(Favorite::getSupplierUserId));

        Set<Long> supplierProductIdSet = list.stream().map(Favorite::getSupplierProductId).collect(Collectors.toSet());
        Set<Long> supplierUserIdSet = list.stream().map(Favorite::getSupplierUserId).collect(Collectors.toSet());

        List<SupplierProductDetail> productDetailList = supplierProductDetailService.noLogicList(supplierProductIdSet);
        List<Long> ppidList = productDetailList.stream().map(SupplierProductDetail::getPpid).collect(Collectors.toList());
        List<ProductInfo> productInfoList = productInfoService.list(Wrappers.<ProductInfo>lambdaQuery().in(ProductInfo::getPpriceid, ppidList));
        Map<Long,ProductInfo> productInfoMap = productInfoList.stream().collect(Collectors.toMap(ProductInfo::getPpriceid, Function.identity()));
        Map<Long, SupplierProductDetail> productDetailMap = productDetailList.stream().collect(Collectors.toMap(SupplierProductDetail::getId, Function.identity()));

        List<PickProduct> pickProductList = pickProductService.list(Wrappers.<PickProduct>lambdaQuery().in(PickProduct::getPpid, ppidList));
        Map<Long, PickProduct> pickProductMap = pickProductList.stream().collect(Collectors.toMap(PickProduct::getPpid, Function.identity()));

        List<ProductBind> productBindList = productBindService.listProductBind(supplierProductIdSet);
        Map<Long, ProductBind> productBindMap = productBindList.stream().collect(Collectors.toMap(ProductBind::getSupplierProductId, Function.identity()));

        List<ProductRuleConfig> productRuleConfigList = productRuleConfigService.list(Wrappers.<ProductRuleConfig>lambdaQuery().in(ProductRuleConfig::getPpid, ppidList));
        Map<Long, ProductRuleConfig> productRuleConfigMap = productRuleConfigList.stream().collect(Collectors.toMap(ProductRuleConfig::getPpid, Function.identity()));

        List<SupplierUser> supplierUserList = supplierUserService.list(Wrappers.<SupplierUser>lambdaQuery().in(SupplierUser::getId, supplierUserIdSet));
        Map<Long, SupplierUser> supplierUserMap = supplierUserList.stream().collect(Collectors.toMap(SupplierUser::getId, Function.identity()));


        FavoriteVO vo = new FavoriteVO();
        List<FavoriteVO.FavoriteData> favoriteDataList = new ArrayList<>();
        List<FavoriteVO.FavoriteProduct> invalidFavoriteProductList = new ArrayList<>();
        favoriteMap.forEach((key,value)->{
            FavoriteVO.FavoriteData favoriteData = new FavoriteVO.FavoriteData();
            SupplierUser supplierUser = supplierUserMap.get(key);
            favoriteData.setSupplierId(supplierUser.getId());
            favoriteData.setSupplierName(supplierUser.getName());
            List<FavoriteVO.FavoriteProduct> favoriteProductList = new ArrayList<>();
            value.forEach(e->{
                FavoriteVO.FavoriteProduct product = new FavoriteVO.FavoriteProduct();
                product.setFavoriteId(e.getId());
                SupplierProductDetail productDetail = productDetailMap.get(e.getSupplierProductId());
                product.setProductDetailId(productDetail.getId());
                product.setPpid(productDetail.getPpid());
                product.setBuyTaxPrice(productDetail.getBuyTaxPrice());
                product.setBuyNoTaxPrice(productDetail.getBuyNoTaxPrice());
                ProductInfo productInfo = productInfoMap.get(productDetail.getPpid());
                product.setProductName(productInfo.getProductName());
                String productImage = ImageUtil.getProductImageUrl(miniFileConfig.getPathbase(),
                        productInfo.getBPic(), WebConstant.PICTURE_SIZE.PIC_440x440);
                product.setProductImage(productImage);
                product.setProductColor(productInfo.getProductColor());
                product.setInvalid(false);
                // 绑定状态
                ProductBind productBind = productBindMap.get(productDetail.getId());
                boolean bindStatus = !Objects.isNull(productBind) && Integer.valueOf(BindStatusEnum.BIND.getCode()).equals(productBind.getBindStatus());
                // 商品状态
                PickProduct pickProduct = pickProductMap.get(productDetail.getPpid());
                boolean productStatus = Integer.valueOf(ProductStatusEnum.UP.getCode()).equals(pickProduct.getProductStatus());
                // 黑名单
                ProductRuleConfig productRuleConfig = productRuleConfigMap.get(productDetail.getPpid());
                List<Long> blackUserList = Objects.isNull(productRuleConfig) ? Collections.emptyList() : CommonUtil.covertIdStr2Long(productRuleConfig.getUserIds());
                // 失效的、未绑定的、未上架的、黑名单上的
                if (productDetail.getDelFlag() || !bindStatus || !productStatus || blackUserList.contains(productDetail.getSupplierId())) {
                    product.setInvalid(true);
                    invalidFavoriteProductList.add(product);
                } else {
                    favoriteProductList.add(product);
                }
            });
            favoriteData.setProductList(favoriteProductList);
            favoriteDataList.add(favoriteData);
        });
        vo.setFavoriteDataList(favoriteDataList);
        vo.setInvalidFavoriteProductList(invalidFavoriteProductList);
        return Result.success(vo);
    }

    @Override
    public Boolean isFavorite(Long supplierProductId, Long partnerId) {
        if (Objects.isNull(supplierProductId) || Objects.isNull(partnerId)) {
            return false;
        }
        Favorite existFavorite = this.getOne(Wrappers.<Favorite>lambdaQuery().eq(Favorite::getSupplierProductId,supplierProductId).eq(Favorite::getPartnerUserId,partnerId),false);
        return !Objects.isNull(existFavorite);
    }
}

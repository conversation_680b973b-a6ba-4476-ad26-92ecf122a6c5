package com.jiuji.pick.service.order.service.impl;


import com.google.common.collect.Maps;
import com.jiuji.pick.common.bo.PartnerTokenInfo;
import com.jiuji.pick.common.enums.OrderTypeEnum;
import com.jiuji.pick.service.order.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;


/**
 * @function:
 * @description: OrderServiceFactory.java
 * @date: 2021/10/09
 * @author: sunfayun
 * @version: 1.0
 */
@Slf4j
@Component
public class OrderServiceFactory extends AbstractOrderFactory {

    private static final Map<Integer, OrderCommonService> serviceMap = Maps.newHashMap();

    @Resource
    private SmallOrderService smallOrderService;
    @Resource
    private BulkyOrderService bulkyOrderService;
    @Resource
    private VirtualOrderService virtualOrderService;
    @Resource(name="AssetsOrderServiceImpl")
    private AssetsOrderService assetsOrderService;

    @Value("${getDomainsBy.isDev}")
    private Boolean isDev;
    @Resource(name = "LargeAndMiddleServiceDevImpl")
    private LargeAndMiddleService largeAndMiddleServiceDev;
    @Resource(name = "LargeAndMiddleServiceImpl")
    private LargeAndMiddleService largeAndMiddleService;


    @PostConstruct
    private void factoryInit() {
        this.initServiceMap();
    }

    @Override
    public OrderCommonService getOrderService(Integer orderType) {
        return serviceMap.get(orderType);
    }


    @Override
    public String getJumpUrl(PartnerTokenInfo tokenInfo,Long orderNumber) {
       //测试环境的情况
        if(isDev){
            return largeAndMiddleServiceDev.getJumpUrl(tokenInfo, orderNumber);
        }else {
            //正式环境输出以及输出测试
            return largeAndMiddleService.getJumpUrl(tokenInfo, orderNumber);
        }
    }


    public SmallOrderService getSmallOrderService() {
        return smallOrderService;
    }

    public VirtualOrderService getVirtualOrderService() {
        return virtualOrderService;
    }

    public AssetsOrderService getAssetsOrderService() {
        return assetsOrderService;
    }

    private void initServiceMap() {
        serviceMap.put(OrderTypeEnum.SMALL.getCode(), smallOrderService);
        serviceMap.put(OrderTypeEnum.BULKY.getCode(), bulkyOrderService);
        serviceMap.put(OrderTypeEnum.VIRTUAL.getCode(), virtualOrderService);
        serviceMap.put(OrderTypeEnum.FIX_ASSETS.getCode(), assetsOrderService);
        serviceMap.put(OrderTypeEnum.COMMON_ASSETS.getCode(), assetsOrderService);
        // 维修配件走小件流程
        serviceMap.put(OrderTypeEnum.REPAIR_PART.getCode(), smallOrderService);
    }

}

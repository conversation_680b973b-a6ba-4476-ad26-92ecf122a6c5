/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.pick.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.annotation.Transient;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR> @since 2017-08-25
 */
@Data
@TableName("t_product_image")
public class ProductImage extends Model<ProductImage> {

    public static final String ID = "id";
    public static final String IMAGENAME = "image_name";
    public static final String RANK = "`rank`";
    public static final String IMAGEPATH = "image_path";
    public static final String PRODUCTID = "productid";
    public static final String IMGTYPE = "img_type";
    public static final String SELECT_FLAG = "select_flag";
    public static final String AREA_CODE = "area_code";
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String imageName;
    @TableField("`rank`")
    private Integer rank;
    private String imagePath;
    private Long productid;
    private Integer imgType;

    @TableField("select_flag")
    private Integer selectFlag;

    @TableField("area_code")
    private String areaCode;
    @TableField("is_delete")
    private Boolean delFlag;
    /**
     * 图片展示路径
     */
    @Transient
    private transient String imageDisplay;

    public String getImagePath() {
        if (StringUtils.isNotBlank(imagePath) && imagePath.contains("\\")) {
            imagePath = imagePath.replace("\\", "/");
        }
        return imagePath;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

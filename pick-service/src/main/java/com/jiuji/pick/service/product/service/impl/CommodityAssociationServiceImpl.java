package com.jiuji.pick.service.product.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.pick.common.enums.ProductStatusEnum;
import com.jiuji.pick.common.exception.BizException;
import com.jiuji.pick.common.utils.RegexUtils;
import com.jiuji.pick.common.vo.Result;
import com.jiuji.pick.service.product.entity.CommodityAssociation;
import com.jiuji.pick.service.product.entity.PickProduct;
import com.jiuji.pick.service.product.mapper.CommodityAssociationServiceMapper;
import com.jiuji.pick.service.product.service.CommodityAssociationService;
import com.jiuji.pick.service.product.service.PickProductService;
import com.jiuji.pick.service.product.vo.AssociationTerms;
import com.jiuji.pick.service.product.vo.AssociationVo;
import com.jiuji.pick.service.product.vo.DeleteInfoVo;
import com.jiuji.pick.service.product.vo.PageInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommodityAssociationServiceImpl extends ServiceImpl<CommodityAssociationServiceMapper, CommodityAssociation> implements CommodityAssociationService {

    public static final Long DEFAULT_SIZE = 10L;
    public static final Long CURRENT = 1L;
    private static final int SIZE = 2000;

    @Resource
    private PickProductService pickProductService;
    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> saveOrModify(AssociationVo associationVo) {
        //修改之前的数据校验
        Long id = associationVo.getId();
        if(id!=null){
            //修改的情况
            List<CommodityAssociation> list = this.lambdaQuery().eq(CommodityAssociation::getProductId, associationVo.getProductId())
                    .ne(CommodityAssociation::getId,id).list();
            if(CollectionUtils.isNotEmpty(list)){
                throw new BizException("该商品id已经存在关联信息不可在添加");
            }
        } else {
            //新增的情况
            List<CommodityAssociation> list = this.lambdaQuery().eq(CommodityAssociation::getProductId, associationVo.getProductId()).list();
            if(CollectionUtils.isNotEmpty(list)){
                throw new BizException("该商品id已经存在关联信息不可在添加");
            }
        }
        CommodityAssociation commodityAssociation = checkAndTransformationData(associationVo);
        boolean b = this.saveOrUpdate(commodityAssociation);
        if (b) {
            return Result.success("操作成功");
        }
        return Result.error("操作失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> deleteInfo(DeleteInfoVo deleteInfoVo) {
        List<Integer> associationIdList = deleteInfoVo.getAssociationIdList();
        List<Long> idList = deleteInfoVo.getIdList();
        boolean flag = Boolean.FALSE;
        if (CollectionUtils.isNotEmpty(idList)) {
            flag = this.removeByIds(idList);
        }
        if (CollectionUtils.isNotEmpty(associationIdList)) {
            QueryWrapper<CommodityAssociation> wrapper = new QueryWrapper<>();
            wrapper.in("association_id", associationIdList);
            flag = this.remove(wrapper);
        }
        if (!flag) {
            throw new BizException("删除失败");
        }
        return Result.success("操作成功");
    }

    @Override
    public Result<IPage<CommodityAssociation>> pageInfo(PageInfoVo pageInfoVo) {
        Page<CommodityAssociation> page = new Page<>();
        page.setSize(Optional.ofNullable(pageInfoVo.getSize()).orElse(DEFAULT_SIZE))
                .setCurrent(Optional.ofNullable(pageInfoVo.getCurrent()).orElse(CURRENT));
        QueryWrapper<CommodityAssociation> wrapper = new QueryWrapper<>();
        String searchKey = pageInfoVo.getSearchKey();
        if (StringUtils.isNotEmpty(searchKey)) {
            if (RegexUtils.checkPositiveRealNumber(searchKey)) {
                wrapper.and(item -> item.eq("productId", pageInfoVo.getSearchKey()).or().like("product_name", searchKey));
            } else {
                wrapper.like("product_name", searchKey);
            }
        }
        wrapper.orderByAsc("association_id","id");
        IPage<CommodityAssociation> result = this.page(page, wrapper);
        return Result.success(result);
    }

    @Override
    public Result<List<AssociationTerms>> getAssociationTerms(String key) {
        QueryWrapper<AssociationTerms> wrapper = new QueryWrapper<>();
        wrapper.eq("pick.product_status", ProductStatusEnum.UP.getCode());
        if (StringUtils.isNotEmpty(key)) {
            if (RegexUtils.checkPositiveRealNumber(key)) {
                wrapper.and(item -> item.like("info.productid", key).or().like("info.product_name", key));
            } else {
                wrapper.like("info.product_name", key);
            }
        }
        List<AssociationTerms> associationTerms = this.baseMapper.selectAssociationTerms(wrapper);
        return Result.success(associationTerms);
    }


    /**
     * 进行数据的转换
     *
     * @param associationVo
     * @return
     */
    private CommodityAssociation checkAndTransformationData(AssociationVo associationVo) {
        CommodityAssociation commodityAssociation = new CommodityAssociation();
        BeanUtils.copyProperties(associationVo, commodityAssociation);
        //获取productId然后找出九讯严选平台上架商品
        Integer productId = associationVo.getProductId();
        List<PickProduct> list = pickProductService.lambdaQuery()
                .eq(PickProduct::getJiuJiProductId, productId)
                .eq(PickProduct::getProductStatus, ProductStatusEnum.UP.getCode())
                .orderByDesc(PickProduct::getSaleCount)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException("该商品id九讯严选平台不存在或没有上架商品");
        }
        //显示版本关联的推荐ppid
        PickProduct pickProduct = list.get(0);
        Long ppid = pickProduct.getPpid();
        commodityAssociation.setPpid(ppid.intValue());
        //判断如果过是新增的情况
        Integer associationId = associationVo.getAssociationId();
        Long id = associationVo.getId();
        if (id == null & associationId == null) {
            //获取已存在的最大的associationId
            Integer newestAssociationId = Optional.ofNullable(this.baseMapper.getNewestAssociationId()).orElse(1);
            commodityAssociation.setAssociationId(newestAssociationId);
        } else {
            commodityAssociation.setUpdateTime(LocalDateTime.now());
        }
        return commodityAssociation;
    }

    /**
     * 搜索引擎联想词搜索
     *
     * @param key
     * @return
     */
    @Override
    public Result<List<AssociationTerms>> getAssociationTermsV2(String key) {
        List<AssociationTerms> associationTermsList = new ArrayList<>();
        if (StringUtils.isEmpty(key)) {
            return Result.success(associationTermsList);
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (RegexUtils.checkPositiveRealNumber(key)) {
            boolQueryBuilder
                    .should(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("productId", key)).must(QueryBuilders.termQuery("state", ProductStatusEnum.UP.getCode())))
                    .should(QueryBuilders.boolQuery().must(QueryBuilders.matchQuery("searchKey", key)).must(QueryBuilders.termQuery("state", ProductStatusEnum.UP.getCode())));
        } else {
            boolQueryBuilder.must(QueryBuilders.matchQuery("searchKey", key))
                    .must(QueryBuilders.termQuery("state", ProductStatusEnum.UP.getCode()));
        }
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(SIZE);
        searchSourceBuilder.from(0);
        SearchRequest request = new SearchRequest(ProductSearchServiceImpl.ES_INDEX_ASSOCIATION);
        request.source(searchSourceBuilder);
        try {
            final SearchResponse searchResponse = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            for (SearchHit hit : searchResponse.getHits()) {
                AssociationTerms p = new AssociationTerms();
                BeanUtilsBean.getInstance().populate(p, hit.getSourceAsMap());
                associationTermsList.add(p);
            }
            //如果es查询不到数据那就走数据库
            if(CollectionUtils.isEmpty(associationTermsList)){
                return getAssociationTerms(key);
            }
        } catch (Exception e) {
            log.error("[searchProductFromEs] 从ES查询联想数据异常", e);
            return getAssociationTerms(key);
        }
        return Result.success(associationTermsList);
    }
}

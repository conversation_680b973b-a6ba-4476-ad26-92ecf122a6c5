package com.jiuji.pick.service.common.vo;

import lombok.Data;

import java.util.List;

/**
 * @function:
 * @description: QueryUserMessageInfoVo.java
 * @date: 2021/05/28
 * @author: sunfayun
 * @version: 1.0
 */
@Data
public class QueryUserMessageInfoVo {

    /**
     * 未读消息条数
     */
    private Integer unreadCount;
    /**
     * 记录条数
     */
    private long total = 0;
    /**
     * 每页显示条数，默认 10
     */
    private long size = 10;

    /**
     * 当前页
     */
    private long current = 1;
    private List<UserMessageInfoVo> userMessageInfoVoList;

}

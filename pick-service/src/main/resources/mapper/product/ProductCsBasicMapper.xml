<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
  ~
  ~ All rights reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ProductCsBasicMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ProductCsBasic">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="`groups`" property="groups"/>
    <result column="inputtype" property="inputtype"/>
    <result column="`rank`" property="rank"/>
    <result column="is_del" property="isDel"/>
    <result column="is_select" property="isSelect"/>
    <result column="is_detail_show" property="isDetailShow"/>
    <result column="img_path" property="imgPath"/>
    <result column="common_rec" property="commonRec"/>
  </resultMap>

  <insert id="insertProductCsBasic">
    INSERT INTO `t_product_cs_basic`(`id`, `name`, `groups`, `inputtype`, `rank`, `is_del`, `is_select`, `is_detail_show`, `is_sell_point`, `img_path`, `exclude_pk`, `common_rec`, `create_time`, `update_time`, `is_delete`)
    VALUES (#{id}, #{name}, #{groups}, #{inputtype}, #{rank}, #{isDel}, #{isSelect}, #{isDetailShow}, #{isSellPoint}, #{imgPath}, #{excludePk}, #{commonRec}, NOW(), NOW(), #{isDel});

  </insert>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
  ~
  ~ All rights reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ParamValueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ParamValue">
        <id column="id" property="id"/>
        <result column="param_id" property="paramId"/>
        <result column="value" property="value"/>
        <result column="rank" property="rank"/>
        <result column="valdes" property="valdes"/>
    </resultMap>

    <resultMap id="ScreeningMap" type="com.jiuji.pick.service.product.vo.SearchResultVo$Screening">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <collection property="value" javaType="List"
                    ofType="com.jiuji.pick.service.product.vo.SearchResultVo$Screening$Value">
            <id column="v_id" property="id"/>
            <result column="v_name" property="name"/>
            <result column="v_imagePath" property="imagePath"/>
        </collection>
    </resultMap>

    <insert id="insertParamValue">
        INSERT INTO `t_param_value`(`id`, `param_id`, `value`, `rank`, `valdes`, `create_time`, `update_time`)
        VALUES (#{id}, #{paramId}, #{value}, #{rank}, #{valdes}, NOW(),NOW());
    </insert>

    <!--获取分类的选择项（单选、多选）参数-->
    <select id="getCateSelectParamsScreening" resultMap="ScreeningMap">
        SELECT pcb.id,
               pcb.name,
               pv.id    v_id,
               pv.value v_name
        FROM t_param_group pg
               INNER JOIN t_product_cs_basic AS pcb ON pg.id = pcb.groups
               LEFT JOIN t_param_value pv ON pcb.id = pv.param_id
        WHERE pcb.is_select = 1
          AND pcb.is_del = 0
          AND pv.id IS NOT NULL
          AND pg.cate_id = #{cid}
        ORDER BY pcb.`rank`, pv.`rank`,pv.id
    </select>

    <!--获取商品的多选项参数-->
    <select id="getProductSelectParam" resultType="java.lang.String">
    SELECT c.cs_value
    FROM t_product_cs c
           RIGHT JOIN t_product_cs_basic b ON b.id = c.csid
           INNER JOIN t_product p ON p.id = c.product_id
    WHERE product_id = #{productId}
      AND inputtype IN (3)
      AND is_del = 0
  </select>

</mapper>

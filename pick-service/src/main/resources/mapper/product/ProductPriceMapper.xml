<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ProductPriceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ProductPrice">
        <id column="ppriceid" property="ppriceid" />
        <result column="productid" property="productid" />
        <result column="member_price" property="memberPrice" />
        <result column="vip_price" property="vipPrice" />
        <result column="cost_price" property="costPrice" />
        <result column="config" property="config" />
        <result column="que" property="que" />
        <result column="rank" property="rank" />
        <result column="bpic" property="bpic" />
        <result column="pricefd" property="pricefd" />
        <result column="update_price_date" property="updatePriceDate" />
        <result column="priceflag" property="priceflag" />
        <result column="isdel" property="isdel" />
        <result column="ppriceid1" property="ppriceid1" />
        <result column="viewsr" property="viewsr" />
        <result column="views_weekr" property="viewsWeekr" />
        <result column="market_price" property="marketPrice" />
        <result column="cues" property="cues" />
        <result column="no_promotion" property="noPromotion" />
        <result column="buy_limit" property="buyLimit" />
        <result column="proid" property="proid" />
        <result column="old_ppid" property="oldPpid" />
        <result column="pricedp" property="pricedp" />
        <result column="pricedptime" property="pricedptime" />
        <result column="feature_title" property="featureTitle" />
        <result column="feature_desc" property="featureDesc" />
        <result column="markpic" property="markpic" />
        <result column="rgbcolor" property="rgbcolor" />
        <result column="initvalue" property="initvalue" />
        <result column="times" property="times" />
        <result column="main_color" property="mainColor" />
        <result column="oem_price" property="oemPrice" />
        <result column="bar_code" property="barCode" />
        <result column="vip2_price" property="vip2Price" />
        <result column="attributes" property="attributes" />
        <result column="scarcity" property="scarcity" />
        <result column="update_time" property="updateTime" />
        <result column="destock" property="destock" />
        <result column="intervene_shop_price" property="interveneShopPrice" />
        <result column="sale_channel" property="saleChannel" />
        <result column="is_bar_code" property="isBarCode" />
        <result column="p_label" property="pLabel" />
        <result column="product_color" property="productColor" />
        <result column="sale_start_time" property="saleStartTime" />
    </resultMap>

    <sql id="columns">
    pp.ppriceid,
        pp.productid,
        pp.member_price,
        pp.vip_price,
        pp.cost_price,
        pp.config,
        pp.que,
        pp.`rank`,
        pp.bpic,
        pp.pricefd,
        pp.update_price_date,
        pp.priceflag,
        pp.isdel,
        pp.ppriceid1,
        pp.viewsr,
        pp.views_weekr,
        pp.market_price,
        pp.cues,
        pp.no_promotion,
        pp.buy_limit,
        pp.proid,
        pp.old_ppid,
        pp.pricedp,
        pp.pricedptime,
        pp.feature_desc,
        pp.markpic,
        pp.rgbcolor,
        pp.initvalue,
        pp.times,
        pp.main_color,
        pp.oem_price,
        pp.bar_code,
        pp.vip2_price,
        pp.attributes,
        pp.scarcity,
        pp.update_time,
        pp.destock,
        pp.intervene_shop_price
    </sql>

    <select id="getProductPriceByPpid" resultType="com.jiuji.pick.service.product.entity.ProductPrice">
        select
        <include refid="columns"/>
        from t_product_price pp where ppriceid = #{ppid}
    </select>

    <!--数据同步到第三方公司商品插入（有初始值）除了会员价其他价格都是0，该sql不要随意调用，也不要随意改动，需要特殊处理，自行重写-->
    <insert id="insertProductPirce" parameterType="com.jiuji.pick.service.product.entity.ProductPrice">
        <!--允许指定id插入-->
        INSERT INTO t_product_price (ppriceid, productid, member_price, vip_price, cost_price, config,
        que, `rank`, bpic,
        pricefd, update_price_date, priceflag, isdel, ppriceid1, viewsr, views_weekr, market_price,
        cues,
        buy_limit, proid, old_ppid, pricedp, pricedptime, feature_title, feature_desc, markpic,
        rgbcolor, initvalue,
        main_color, oem_price, bar_code, vip2_price, attributes)
        VALUES (#{ppriceid}, #{productid}, #{memberPrice}, 0, 0, #{config}, #{que}, #{rank}, #{bpic},
        #{pricefd}, #{updatePriceDate}, #{priceflag}, 0, #{ppriceid1}, #{viewsr}, #{viewsWeekr}, 0,
        #{cues},
        #{buyLimit}, #{proid}, #{oldPpid}, #{pricedp}, #{pricedptime}, #{featureTitle}, #{featureDesc},
        #{markpic}, #{rgbcolor}, #{initvalue},
        #{mainColor}, #{oemPrice}, #{barCode}, 0, #{attributes});
    </insert>

    <update id="updateDelFlag">
        update t_product_price
        <if test="delFlag">
            set isdel = 1
        </if>
        <if test="!delFlag">
            set isdel = 0
        </if>
        where ppriceid = #{ppriceId}
    </update>

</mapper>

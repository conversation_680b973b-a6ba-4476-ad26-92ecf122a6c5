<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.PickProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.PickProduct">
        <id column="id" property="id" />
        <result column="source" property="source" />
        <result column="ppid" property="ppid" />
        <result column="product_status" property="productStatus" />
        <result column="advice_price" property="advicePrice" />
        <result column="product_future" property="productFuture" />
        <result column="product_config" property="productConfig" />
        <result column="default_area" property="defaultArea" />
        <result column="hot_area" property="hotArea" />
        <result column="happy_area" property="happyArea" />
        <result column="recommend_area" property="recommendArea" />
        <result column="attachment_ids" property="attachmentIds" />
        <result column="product_up_time" property="productUpTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, source, ppid, product_status, advice_price, product_future, product_config, default_area, hot_area, happy_area, recommend_area,
        attachment_ids, product_up_time, create_time, update_time, del_flag
    </sql>

    <select id="queryProductListInfo" resultType="com.jiuji.pick.service.product.vo.QueryProductListVo">
        SELECT
            pp.id as productId,
            pp.jiu_ji_product_id as jiuJiProductId,
            pp.ppid,
            pi.product_name,
            c.id as categoryId,
            c. `name` as categoryName,
            pp.product_up_time,
            pp.advice_price,
            pp.product_status,
            pp.create_time
        FROM
            t_pick_product pp
            LEFT JOIN t_product_info pi ON pp.ppid = pi.ppriceid
            LEFT JOIN t_category c ON pi.cid = c.id
        WHERE
            pp.del_flag = 0
            <if test="param.searchType != null and param.searchType == 1 and param.keyWord != null and param.keyWord != ''">
                and pp.ppid = #{param.keyWord}
            </if>
            <if test="param.productStatus != null">
                and pp.product_status = #{param.productStatus}
            </if>
            <if test="param.searchType != null and param.searchType == 2 and param.keyWord != null and param.keyWord != ''">
                AND pi.product_name LIKE CONCAT('%',#{param.keyWord},'%')
            </if>
            <if test="param.categoryIdList != null and param.categoryIdList.size() > 0">
                AND c.id in
                <foreach item="item" index="index" collection="param.categoryIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        order by pp.ppid desc
    </select>

    <select id="getProductExamineList" resultType="com.jiuji.pick.service.product.vo.QueryProductExamineListVo">
        SELECT
            pp.id AS productId,
            pp.ppid,
            pi.product_name,
            c.id AS categoryId,
            c. `name` AS categoryName,
            pp.product_up_time,
            pp.advice_price,
            pp.product_status,
            pd.id AS supplierProductId,
            pd.material,
            pd.payment_day,
            pd.buy_no_tax_price,
            pd.buy_tax_price,
            su.`name` as supplierName,
            pb.supplier_id,
            pb.bind_time,
            pb.bind_status
        FROM
            t_product_bind pb
            LEFT JOIN t_product_info pi ON pb.ppid = pi.ppriceid
            INNER JOIN t_pick_product pp ON pb.ppid = pp.ppid and pp.del_flag = 0
            INNER JOIN t_supplier_product_detail pd ON pb.ppid = pd.ppid and pb.supplier_id = pd.supplier_id and pd.del_flag = 0
            LEFT JOIN t_category c ON pi.cid = c.id
            INNER JOIN t_supplier_user su ON pb.supplier_id = su.id and su.del_flag = 0
        WHERE
            pb.del_flag = 0
            <if test="param.searchType != null and param.searchType == 1 and param.keyWord != null and param.keyWord != ''">
                AND pp.ppid = #{param.keyWord}
            </if>
            <if test="param.searchType != null and param.searchType == 2 and param.keyWord != null and param.keyWord != ''">
                AND pi.product_name LIKE CONCAT('%',#{param.keyWord},'%')
            </if>
            <if test="param.searchType != null and param.searchType == 3 and param.keyWord != null and param.keyWord != ''">
                AND su.`name` LIKE  CONCAT('%',#{param.keyWord},'%')
            </if>
            <if test="param.searchType != null and param.searchType == 4 and param.keyWord != null and param.keyWord != ''">
                AND pb.supplier_id = #{param.keyWord}
            </if>
            <if test="param.bindStatusList != null and param.bindStatusList.size() > 0">
                AND pb.bind_status in
                <foreach item="item" index="index" collection="param.bindStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.startTime != null">
                and pb.create_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and pb.create_time &lt;= #{param.endTime}
            </if>
    </select>
</mapper>

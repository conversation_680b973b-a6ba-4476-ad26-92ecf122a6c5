<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ProductStDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ProductStDetail">
        <id column="id" property="id" />
        <result column="stand_id" property="standId" />
        <result column="value" property="value" />
        <result column="order_id" property="orderId" />
        <result column="is_del" property="isDel" />
        <result column="news_id" property="newsId" />
    </resultMap>

    <!--后台同步用的新增方法，指定唯一id进行插入-->
    <insert id="addProductStDetail" parameterType="com.jiuji.pick.service.product.entity.ProductStDetail">
    INSERT INTO t_product_st_detail(id, stand_id, `value`, order_id, is_del)
    VALUES (#{id}, #{standId}, #{value}, #{orderId}, 0);
  </insert>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.Product">
        <id column="id" property="id" />
        <result column="cid" property="cid" />
        <result column="cid_family" property="cidFamily" />
        <result column="cid_extend" property="cidExtend" />
        <result column="name" property="name" />
        <result column="pro_key" property="proKey" />
        <result column="decription" property="decription" />
        <result column="decription2" property="decription2" />
        <result column="detail" property="detail" />
        <result column="views" property="views" />
        <result column="buys" property="buys" />
        <result column="adddate" property="adddate" />
        <result column="display" property="display" />
        <result column="comment" property="comment" />
        <result column="views_week" property="viewsWeek" />
        <result column="inuser" property="inuser" />
        <result column="rank1" property="rank1" />
        <result column="brand_id" property="brandId" />
        <result column="is_new" property="isNew" />
        <result column="is_recommend" property="isRecommend" />
        <result column="is_popular" property="isPopular" />
        <result column="is_special" property="isSpecial" />
        <result column="comment_count" property="commentCount" />
        <result column="search_key" property="searchKey" />
        <result column="is_mobile" property="isMobile" />
        <result column="is_services" property="isServices" />
        <result column="des_link_name" property="desLinkName" />
        <result column="des_link_url" property="desLinkUrl" />
        <result column="last_modify_time" property="lastModifyTime" />
        <result column="shot_name" property="shotName" />
        <result column="old_pid" property="oldPid" />
        <result column="psort" property="psort" />
        <result column="ultimate" property="ultimate" />
        <result column="pc_des_link_name" property="pcDesLinkName" />
        <result column="pc_des_link_url" property="pcDesLinkUrl" />
        <result column="qkk_des_link_name" property="qkkDesLinkName" />
        <result column="qkk_des_link_url" property="qkkDesLinkUrl" />
        <result column="selling_point" property="sellingPoint" />
        <result column="is_recommend_by_like_product" property="isRecommendByLikeProduct" />
        <result column="seven_day_return" property="sevenDayReturn" />
        <result column="support_service" property="supportService" />
        <result column="sort_start_time" property="sortStartTime" />
        <result column="sort_end_time" property="sortEndTime" />
        <result column="service_introduce_link" property="serviceIntroduceLink" />
        <result column="param_search_key" property="paramSearchKey" />
        <result column="display_time" property="displayTime" />
        <result column="other_limit" property="otherLimit" />
        <result column="number_pan_dian" property="numberPanDian" />
        <result column="oa_enable" property="oaEnable" />
    </resultMap>


    <!--后台管理同步插入商品-->
    <insert id="insertProduct">
        <!--允许对自增列id插入指定数据-->
        INSERT INTO t_product (id, cid, cid_family, cid_extend, name, decription, decription2, detail,
        display, comment,
        inuser, brand_id, pro_key, is_new, is_recommend, is_popular, is_special, search_key, is_mobile,
        is_services,
        des_link_url, des_link_name, pc_des_link_name, pc_des_link_url, qkk_des_link_name,
        qkk_des_link_url, selling_point,
        last_modify_time, shot_name, ultimate, seven_day_return, is_recommend_by_like_product,
        support_service,number_pan_dian,other_limit)
        VALUES(#{product.id}, #{product.cid}, #{product.cidFamily}, #{product.cidExtend},
        #{product.name},
        #{product.decription}, #{product.decription2}, #{product.detail}, #{product.display},
        #{product.comment},
        #{product.inuser}, #{product.brandId}, #{product.proKey}, #{product.isNew},
        #{product.isRecommend},
        #{product.isPopular}, #{product.isSpecial}, #{product.searchKey}, #{product.isMobile},
        #{product.isServices},
        #{product.desLinkUrl}, #{product.desLinkName}, #{product.pcDesLinkName},
        #{product.pcDesLinkUrl},
        #{product.qkkDesLinkName}, #{product.qkkDesLinkUrl}, #{product.sellingPoint},
        now(), #{product.shotName}, #{product.ultimate}, #{product.sevenDayReturn},
        #{product.isRecommendByLikeProduct},
        #{product.supportService},#{product.numberPanDian},#{product.otherLimit})
    </insert>

    <!--根据商品ID更新-->
    <update id="updateProductById">
    UPDATE t_product
    SET cid                          = #{product.cid},
        cid_family                   = #{product.cidFamily},
        cid_extend                   = #{product.cidExtend},
        `name`                       = #{product.name},
        decription                   = #{product.decription},
        decription2                  = #{product.decription2},
        detail                       = #{product.detail},
        display                      = #{product.display},
        `comment`                    = #{product.comment},
        inuser                       = #{product.inuser},
        brand_id                     = #{product.brandId},
        pro_key                      = #{product.proKey},
        is_new                       = #{product.isNew},
        is_recommend                 = #{product.isRecommend},
        is_popular                   = #{product.isPopular},
        is_special                   = #{product.isSpecial},
        search_key                   = #{product.searchKey},
        is_mobile                    = #{product.isMobile},
        is_services                  = #{product.isServices},
        des_link_url                 = #{product.desLinkUrl},
        des_link_name                = #{product.desLinkName},
        pc_des_link_name             = #{product.pcDesLinkName},
        pc_des_link_url              = #{product.pcDesLinkUrl},
        qkk_des_link_name            = #{product.qkkDesLinkName},
        qkk_des_link_url             = #{product.qkkDesLinkUrl},
        selling_point                = #{product.sellingPoint},
        last_modify_time             = now(),
        shot_name                    = #{product.shotName},
        ultimate                     = #{product.ultimate},
        seven_day_return             = #{product.sevenDayReturn},
        is_recommend_by_like_product = #{product.isRecommendByLikeProduct},
        support_service              = #{product.supportService},
        service_introduce_link       = #{product.serviceIntroduceLink},
        number_pan_dian              = #{product.numberPanDian},
        other_limit                  = #{product.otherLimit},
        oa_enable                    = #{product.oaEnable}
    WHERE id = #{product.id}
  </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
  ~
  ~ All rights reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ParamGroupMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ParamGroup">
    <id column="ID" property="id"/>
    <result column="cate_id" property="cateId"/>
    <result column="group_name" property="groupName"/>
    <result column="rank" property="rank"/>
    <result column="is_del" property="isDel"/>
    <result column="tmp" property="tmp"/>
  </resultMap>

  <insert id="insertParamGroup">
    INSERT INTO `t_param_group`(`id`, `cate_id`, `group_name`, `rank`, `is_del`, `tmp`)
    VALUES (#{id}, #{cateId}, #{groupName}, #{rank}, #{isDel}, #{tmp});
  </insert>
</mapper>

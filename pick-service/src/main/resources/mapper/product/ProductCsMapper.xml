<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
  ~
  ~ All rights reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ProductCsMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ProductCs">
    <id column="id" property="id"/>
    <result column="product_id" property="productId"/>
    <result column="csid" property="csid"/>
    <result column="cs_value" property="csValue"/>
    <result column="is_display" property="isDisplay"/>
    <result column="type" property="type"/>
  </resultMap>


  <insert id="insertProductCs">
    insert into t_product_cs(id, product_id, csid, cs_value, is_display, `type`, cate_type,
                             create_time, update_time, is_delete)
    values (#{id}, #{productId}, #{csid}, #{csValue}, #{isDisplay}, #{type}, #{cateType}, NOW(),
            NOW(), 0)
  </insert>


    <select id="getParamByGoodsIdAndType" resultType="com.jiuji.pick.service.product.bo.ProductCsBO">
    SELECT pc.id,
           pc.product_id,
           pc.csid,
           pc.cs_value,
           pc.is_display,
           pc.type,
           pc.cate_type,
           pcb.inputtype          AS inputType,
           pcb.name               AS paramName,
           CASE
             WHEN pcb.inputtype = 2 THEN
               (SELECT pv.value FROM t_param_value pv where pv.id = pc.cs_value)
             ELSE pc.cs_value END AS paramValue
    FROM t_product_cs pc
           LEFT JOIN t_product_cs_basic pcb ON pc.csid = pcb.id
    WHERE pc.product_id = #{goodsId}
      AND pc.type = #{type}
      AND pc.cate_type = #{cateType}
  </select>

    <select id="getProductAllType" resultType="com.jiuji.pick.service.product.vo.ProductCsVO">
        SELECT
        cs.csid,
        cs.cs_value,
        cs.product_id,
        cs.type,
        pp.ppriceid
        FROM
        t_product_info pp
        LEFT JOIN t_product_cs cs ON pp.productid = cs.product_id
        LEFT JOIN t_product_cs_basic csb ON cs.csid = csb.id
        WHERE
        pp.display = 1
        AND csb.inputtype IN (2,3) and cs.cs_value != '' and cs.type = 1
        <if test="ppids != null and ppids.size>0">
            and pp.ppriceid in
            <foreach collection="ppids" item="ppid" open="(" close=")" separator=",">
                #{ppid}
            </foreach>
        </if>
    </select>

    <select id="getProductCsByPpid" resultType="com.jiuji.pick.service.product.vo.ProductCsVO">
        SELECT
        cs.csid,
        cs.cs_value,
        cs.product_id,
        cs.type,
        pp.ppriceid
        FROM
        t_product_info pp
        LEFT JOIN t_product_cs cs ON pp.ppriceid = cs.product_id
        LEFT JOIN t_product_cs_basic csb ON cs.csid = csb.id
        WHERE
        pp.display = 1
        AND csb.inputtype IN (2,3) and cs.cs_value !='' and cs.type = 0
        <if test="ppids != null and ppids.size>0">
            and pp.ppriceid in
            <foreach collection="ppids" item="ppid" open="(" close=")" separator=",">
                #{ppid}
            </foreach>
        </if>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ProductStandardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ProductStandard">
        <id column="id" property="id" />
        <result column="cid" property="cid" />
        <result column="name" property="name" />
        <result column="show_type" property="showType" />
        <result column="order_id" property="orderId" />
        <result column="isdel" property="isdel" />
        <result column="limit_select" property="limitSelect" />
    </resultMap>

    <!--后台同步用的新增方法，指定唯一id进行插入-->
    <insert id="addProductStandard" parameterType="com.jiuji.pick.service.product.entity.ProductStandard">
    INSERT INTO t_product_standard(id, cid, `name`, show_type, order_id, isdel)
    VALUES (#{id}, #{cid}, #{name}, #{showType}, #{orderId}, 0);
  </insert>

</mapper>

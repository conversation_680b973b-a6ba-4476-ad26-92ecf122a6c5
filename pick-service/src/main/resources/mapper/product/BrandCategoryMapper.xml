<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
  ~
  ~ All rights reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.BrandCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.BrandCategory">
        <id column="id" property="id"/>
        <result column="brand_id" property="brandId"/>
        <result column="category_id" property="categoryId"/>
        <result column="rank" property="rank"/>
    </resultMap>

    <select id="getRecommendSearch"
            resultType="com.jiuji.pick.service.product.vo.RecommendSearchVo">
        SELECT
        p.NAME,
        p.id AS productId,
        p.is_mobile AS isMobile
        FROM
        t_product p
        WHERE
        REPLACE ( p.NAME, ' ', '' ) LIKE concat( '%', #{searchName}, '%' )
        AND p.display = 1
        <if test="brandId!=null">
            AND p.brand_id = #{brandId}
        </if>
        AND EXISTS ( SELECT 1 FROM t_product_price pp WHERE pp.isdel = 0 AND pp.productid = p.id )
        ORDER BY
        p.is_mobile DESC
    </select>


    <select id="listAllWithName" resultType="com.jiuji.pick.service.product.vo.BrandCategoryVO">
        select bc.id,
               bc.brand_id,
               bc.category_id,
               bc.`rank`,
               c.name as categoryName,
               b.name as brandName
        from t_brand_category bc
                 left join t_category c on c.id = bc.category_id
                 left join t_brand b on b.id = bc.brand_id
        where c.display = 1
          and c.is_show = 1
          and b.display = 1
    </select>


    <select id="selectCateIdbyBrandId" resultType="integer">
        select category_id
        from t_brand_category
        where brand_id = #{brandId}
    </select>


    <select id="getBrandIdsByCid" resultType="integer">
        select distinct brand_id
        from t_brand_category
        where category_id in
        <foreach collection="ids" index="index" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>
    <select id="getProductName" resultType="java.lang.String">
        SELECT DISTINCT p.name
        FROM t_product p
                 LEFT JOIN t_pick_product pick on pick.jiu_ji_product_id = p.id
        where pick.product_status = 1
          and p.name LIKE CONCAT('%', #{searchName}, '%')
        LIMIT 10
    </select>


</mapper>

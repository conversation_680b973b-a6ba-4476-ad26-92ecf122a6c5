<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.CategorySearchWordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.CategorySearchWord">
        <id column="id" property="id"/>
        <result column="category_id" property="categoryId"/>
        <result column="search_word" property="searchWord"/>
    </resultMap>

    <select id="getCategoryIdsByKeyword" resultType="java.lang.Integer">
        select distinct (cs.category_id)
        from t_category_search_word cs
        left join t_category c  on cs.category_id = c.id
        where c.display	= 1 and c.is_show = 1
        and cs.search_word in
        <foreach collection="keywords" open="(" close=")" separator="," item="keyword">
            #{keyword}
        </foreach>

    </select>
</mapper>
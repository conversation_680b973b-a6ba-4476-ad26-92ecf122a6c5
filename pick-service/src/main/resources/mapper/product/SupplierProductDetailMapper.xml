<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.SupplierProductDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.SupplierProductDetail">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="ppid" property="ppid" />
        <result column="supplier_id" property="supplierId" />
        <result column="payment_day" property="paymentDay" />
        <result column="material" property="material" />
        <result column="box_rule" property="boxRule" />
        <result column="quality_date" property="qualityDate" />
        <result column="buy_no_tax_price" property="buyNoTaxPrice" />
        <result column="buy_tax_price" property="buyTaxPrice" />
        <result column="delivery_day" property="deliveryDay" />
        <result column="no_reason_return" property="noReasonReturn" />
        <result column="change_day" property="changeDay" />
        <result column="bad_pay" property="badPay" />
        <result column="lack_pay" property="lackPay" />
        <result column="after_sale_policy" property="afterSalePolicy" />
        <result column="other_policy" property="otherPolicy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
        <result column="is_remote_delivery_fee" property="remoteDeliveryFee" />
        <result column="sort" property="sort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_id, ppid, supplier_id, payment_day, material, box_rule, quality_date, buy_no_tax_price, buy_tax_price, delivery_day, no_reason_return, change_day, bad_pay, lack_pay, after_sale_policy, other_policy, create_time, update_time, del_flag
    </sql>

    <select id="querySupplierProductList" resultType="com.jiuji.pick.service.product.vo.QuerySupplierProductListVo">
        SELECT
            pp.id AS productId,
            pp.ppid,
            pi.product_name,
            c.id AS categoryId,
            c. `name` AS categoryName,
            b.`name` AS brandName,
            pp.product_up_time,
            pp.advice_price,
            pp.product_status,
            pp.create_time,
            pp.sale_count
        FROM
            t_pick_product pp
            LEFT JOIN t_product_info pi ON pp.ppid = pi.ppriceid
            LEFT JOIN t_category c ON pi.cid = c.id
            LEFT JOIN t_brand b ON pi.brand_id = b.id
        WHERE
            pp.del_flag = 0
            AND NOT EXISTS (SELECT pb.ppid FROM t_product_bind pb WHERE pb.ppid = pp.ppid AND pb.supplier_id = #{param.supplierId} AND pb.del_flag = 0)
            AND NOT EXISTS (SELECT rc.ppid FROM t_product_rule_config rc WHERE rc.ppid = pp.ppid AND rc.restrict_target = 1 AND rc.restrict_type = 1 AND rc.del_flag = 0 AND rc.user_Ids LIKE CONCAT('%,',#{param.supplierId},',%'))
            <if test="param.categoryIdList != null and param.categoryIdList.size() > 0">
                AND pi.cid in
                <foreach item="item" index="index" collection="param.categoryIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.searchType != null and param.searchType == 1 and param.keyWord != null and param.keyWord != ''">
                AND pp.ppid = #{param.keyWord}
            </if>
            <if test="param.searchType != null and param.searchType == 2 and param.keyWord != null and param.keyWord != ''">
                AND pi.product_name LIKE CONCAT('%',#{param.keyWord},'%')
            </if>
        order by pp.create_time desc
    </select>

    <select id="querySupplierApplyProductList" resultType="com.jiuji.pick.service.product.vo.QuerySupplierApplyProductListVo">
        SELECT
            pp.id AS productId,
            pp.ppid,
            pi.product_name,
            c.id AS categoryId,
            c. `name` AS categoryName,
            pp.product_up_time,
            pp.advice_price,
            pd.material,
            pd.buy_no_tax_price,
            pd.buy_tax_price,
            pd.stock_count AS stockCount,
            pb.bind_status,
            pb.remark,
            pb.create_time
        FROM
            t_product_bind pb
            LEFT JOIN t_product_info pi ON pb.ppid = pi.ppriceid
            INNER JOIN t_pick_product pp ON pb.ppid = pp.ppid and pp.del_flag = 0
            INNER JOIN t_supplier_product_detail pd ON pb.ppid = pd.ppid and pb.supplier_id = pd.supplier_id and pd.del_flag = 0
            LEFT JOIN t_category c ON pi.cid = c.id
        WHERE
            pb.del_flag = 0
            AND pd.supplier_id = #{param.supplierId}
            <if test="param.categoryIdList != null and param.categoryIdList.size() > 0">
                AND pi.cid in
                <foreach item="item" index="index" collection="param.categoryIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.searchType != null and param.searchType == 1 and param.keyWord != null and param.keyWord != ''">
                AND pp.ppid = #{param.keyWord}
            </if>
            <if test="param.searchType != null and param.searchType == 2 and param.keyWord != null and param.keyWord != ''">
                AND pi.product_name LIKE CONCAT('%',#{param.keyWord},'%')
            </if>
            <if test="param.bindStatus != null">
                AND pb.bind_status = #{param.bindStatus}
            </if>
        order by pb.create_time desc
    </select>

    <select id="noLogicList" resultType="com.jiuji.pick.service.product.entity.SupplierProductDetail">
        SELECT <include refid="Base_Column_List"></include> from t_supplier_product_detail pd where id in
        <foreach item="item" index="index" collection="supplierProductIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>

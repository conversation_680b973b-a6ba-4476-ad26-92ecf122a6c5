<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ProductInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ProductInfo">
        <id column="ppriceid" property="ppriceid" />
        <result column="productid" property="productid" />
        <result column="product_name" property="productName" />
        <result column="product_color" property="productColor" />
        <result column="cost_price" property="costPrice" />
        <result column="vip_price" property="vipPrice" />
        <result column="cid" property="cid" />
        <result column="is_mobile" property="isMobile" />
        <result column="member_price" property="memberPrice" />
        <result column="que" property="que" />
        <result column="display" property="display" />
        <result column="is_del" property="isDel" />
        <result column="views_week" property="viewsWeek" />
        <result column="ppriceid1" property="pPriceId1" />
        <result column="config" property="config" />
        <result column="brand_id" property="brandId" />
        <result column="cid_family" property="cidFamily" />
        <result column="views_weekr" property="viewsweekr" />
        <result column="rank" property="rank" />
        <result column="no_promotion" property="noPromotion" />
        <result column="bar_code" property="barCode" />
        <result column="is_bar_code" property="isBarCode" />
        <result column="product_color_ids" property="productColorIds" />
        <result column="psort" property="psort" />
        <result column="add_date" property="addDate" />
        <result column="product_color_id" property="productColorId" />
        <result column="b_pic" property="bPic" />
        <result column="buy_limit" property="buyLimit" />
        <result column="oa_enable" property="oaEnable" />
        <result column="bar_code_count" property="barCodeCount" />
    </resultMap>

    <select id="queryProductInfo4Add" resultType="com.jiuji.pick.service.product.vo.QueryProduct4AddVo">
        SELECT
            cy. `name` AS categoryName,
            bd. `name` AS brandName,
            pi.cid as cid,
            pi.product_color AS specName,
            pi.productid as jiuJiProductId,
            pi.product_name,
            pi.bar_code,
            pi.is_mobile as isMobile,
            ifnull(pp.advice_price,pi.member_price) as advicePrice,
            ifnull(pp.product_future,tp.selling_point) as productFuture,
            ifnull(pp.product_config,pi.config) as productConfig,
            pp.default_area,
            pp.hot_area,
            pp.happy_area,
            pp.recommend_area,
            pp.id AS pickProductId,
            pp.attachment_ids
        FROM
            t_product_info pi
        LEFT JOIN t_category cy ON pi.cid = cy.id
        LEFT JOIN t_product tp ON pi.productid = tp.id
        LEFT JOIN t_brand bd ON pi.brand_id = bd.id
        LEFT JOIN t_pick_product pp ON pi.ppriceid = pp.ppid
        WHERE
            pi.ppriceid = #{ppid}
        limit 1
    </select>

    <select id="getAllProductInfoByPid" resultType="com.jiuji.pick.service.product.entity.ProductInfo">
        SELECT
            pp.ppriceid,
            pp.productid,
            p.`name` as product_name,
            pp.cost_price,
            pp.vip_price,
            p.cid,
            p.is_mobile,
            pp.member_price,
            pp.que,
            p.display,
            pp.isdel,
            p.views_week,
            pp.ppriceid1,
            pp.config,
            p.brand_id,
            p.cid_family,
            pp.views_weekr,
            pp.rank,
            pp.no_promotion,
            pp.bar_code,
            pp.is_bar_code,
            p.psort,
            p.adddate,
            p.oa_enable,
            pp.bpic,
            pp.buy_limit
        FROM
	        t_product_price pp
	    INNER JOIN t_product p ON pp.productid = p.id
        where p.id = #{pid}
    </select>
    <select id="listProductColor" resultType="com.jiuji.pick.service.product.vo.ProductColorInfoVo">
        SELECT
            info.ppriceid,
            GROUP_CONCAT( detail.id ) AS product_color_ids,
            REPLACE ( GROUP_CONCAT( detail.`value` ), ',', ' ' ) AS product_color
        FROM
            t_product_st_detail detail
                JOIN t_product_st_info info ON detail.id = info.standard_detail_id
        <!--info.ppriceid in(ppids)-->
        WHERE
            info.ppriceid in
            <foreach collection="ppids" item="ppid" open="(" close=")" separator=",">
                #{ppid}
            </foreach>

        GROUP BY info.ppriceid
    </select>

    <select id="getProductInfoByPpid" resultType="com.jiuji.pick.service.product.entity.ProductInfo">
        SELECT *
        FROM t_product_info
        WHERE ppriceid = #{ppid}
    </select>


    <insert id="insertProductInfo" parameterType="com.jiuji.pick.service.product.entity.ProductInfo">
        INSERT INTO t_product_info(ppriceid, productid, product_name, product_color,
                                   cost_price, vip_price, cid, is_mobile, b_pic, member_price, que,
                                   display, is_del,
                                   ppriceid1, config, brand_id, cid_family, `rank`, no_promotion,
                                   bar_code, product_color_ids,buy_limit,oa_enable)
        VALUES (#{ppriceid}, #{productid}, #{productName}, #{productColor},
                #{costPrice}, #{vipPrice}, #{cid}, #{isMobile}, #{bPic}, #{memberPrice}, #{que},
                #{display}, #{isDel},
                #{pPriceId1}, #{config}, #{brandId}, #{cidFamily}, #{rank}, #{noPromotion},
                #{barCode}, #{productColorIds},#{buyLimit},#{oaEnable});
    </insert>

    <update id="updateByPid">
        update t_product_info set
        productid = #{productid},
        product_name = #{productName},
        product_color = #{productColor},
        cost_price = #{costPrice},
        vip_price = #{vipPrice},
        cid = #{cid},
        is_mobile = #{isMobile},
        member_price = #{memberPrice},
        que = #{que},
        display = #{display},
        is_del = #{isDel},
        views_week = #{viewsWeek},
        ppriceid1 = #{pPriceId1},
        config = #{config},
        brand_id = #{brandId},
        cid_family = #{cidFamily},
        views_weekr = #{viewsweekr},
        `rank` = #{rank},
        no_promotion = #{noPromotion},
        bar_code = #{barCode},
        is_bar_code = #{isBarCode},
        product_color_ids = #{productColorIds},
        psort = #{psort},
        add_date = #{addDate},
        b_pic = #{bPic},
        buy_limit = #{buyLimit},
        oa_enable = #{oaEnable}
        where ppriceid = #{ppriceid}
    </update>

    <select id="getProductInRedisFromDB" resultType="com.jiuji.pick.service.product.bo.ProductInRedis">
    SELECT pp.ppriceid       AS pPriceId,
           p.id              AS productId,
           p.name            AS productName,
           p.shot_name       AS shortName,
           tpi.product_color AS productColor,
           pp.cost_price     AS costPrice,
           pp.vip_price      AS vipPrice,
           p.cid             AS cid,
           p.is_mobile       AS ismobile,
           p.display         AS display,
           pp.bpic           AS bPic,
           pp.member_price   AS memberPrice,
           pp.que            AS que,
           pp.isdel          AS isDel,
           pp.ppriceid1      AS pPriceId1,
           pp.config         AS config,
           p.brand_id        AS brandID,
           p.cid_family      AS cidFamily,
           p.decription      AS decription,
           p.selling_point   AS sellingpoint,
           pp.no_promotion   AS noPromotion,
           pp.bar_code       AS barCode,
           pp.buy_limit      AS buyLimit
    FROM t_product_info tpi
           INNER JOIN t_product p ON tpi.productid = p.id
           INNER JOIN t_product_price pp ON tpi.ppriceid = pp.ppriceid
    WHERE tpi.ppriceid = #{ppid}
  </select>

    <select id="getProductSearchInES" resultType="com.jiuji.pick.service.product.bo.ProductSearchInEsV2">
        SELECT
            pb.id as bindId,
            spd.id as suppllierDetailId,
            p.ppriceid ppid,
            p.productid productId,
            p.product_name as `name`,
            ifnull(p.product_color, '') color,
            ifnull(pro.search_key, '') searchKey,
            pro.decription description,
            ifnull(pi.sort_value, 0) defaultSort,
            pro.adddate addTime,
            p.member_price price,
            p.cid categoryId,
            p.brand_id brandId,
            p.bar_code,
            p.cid_family,
            p.is_mobile,
            p.cost_price,
            p.display,
            p.que,
            p.oa_enable,
            pro.cid_extend cidExtend,
            pro.param_search_key paramSearchKey,
            su.`name` as supplierName,
            su.province_name as supplierProvinceName,
            su.city_name as supplierCityName,
            suq.after_sale_province_name,
            suq.after_sale_city_name,
            suq.after_sale_district_name,
            spd.buy_no_tax_price as buyNoTaxPrice,
            spd.buy_tax_price as buyTaxPrice,
            spd.sort as pickSort,
            pp.product_up_time as productUpTime,
            pp.sale_count as sales,
            pp.advice_price as advicePrice,
            pp.product_type as productType
        FROM t_product_bind pb
        INNER JOIN t_supplier_user su ON pb.supplier_id = su.id and su.status=2 and su.del_flag = 0
        LEFT JOIN t_supplier_user_qualification suq on su.id = suq.supplier_user_id
        LEFT JOIN t_product_info p ON pb.ppid = p.ppriceid
        INNER JOIN t_pick_product pp ON pp.ppid = p.ppriceid and pp.product_status = 1 and pp.del_flag = 0
        INNER JOIN t_supplier_product_detail spd ON spd.ppid = pb.ppid and spd.supplier_id = pb.supplier_id and spd.del_flag = 0
        LEFT JOIN t_product_index pi ON pi.ppriceid = p.ppriceid
        left join t_product pro on p.productid = pro.id
        WHERE pb.del_flag = 0 and pb.bind_status = 1
        and not EXISTS (select rc.ppid from t_product_rule_config rc where
        rc.ppid = pp.ppid and rc.restrict_target = 1 and rc.restrict_type = 1 and rc.del_flag = 0 and rc.user_Ids LIKE CONCAT('%,',su.id,',%'))
        <if test="needAllPpid == null or needAllPpid == 0 ">
            AND p.ppriceid in
            <foreach collection="ppids" item="ppid" open="(" close=")" separator=",">
                #{ppid}
            </foreach>
        </if>


    </select>

</mapper>

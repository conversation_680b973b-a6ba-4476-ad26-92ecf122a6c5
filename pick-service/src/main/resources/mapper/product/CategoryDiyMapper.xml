<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.CategoryDiyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.CategoryDiy">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="link" property="link" />
        <result column="sort" property="sort" />
        <result column="style" property="style" />
        <result column="platform" property="platform" />
        <result column="xtenant" property="xtenant" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="deleted" property="deleted" />
        <result column="parent_ids" property="parentIds" />
        <result column="parent_id" property="parentId" />
        <result column="level" property="level" />
        <result column="right_tag_name" property="rightTagName" />
        <result column="right_tag_link" property="rightTagLink" />
        <result column="fid" property="fid" />
        <result column="product_type" property="productType" />
        <result column="ad_id" property="adId" />
        <result column="cate_image" property="cateImage" />
        <result column="cate_small_tag" property="cateSmallTag" />
        <result column="title_image" property="titleImage" />
        <result column="background_image" property="backgroundImage" />
        <result column="bottom_tag_name" property="bottomTagName" />
        <result column="bottom_tag_link" property="bottomTagLink" />
        <result column="hot_product_auto_change" property="hotProductAutoChange" />
    </resultMap>

    <insert id="batchInsert"  useGeneratedKeys="true" keyProperty="id">
        insert into t_category_diy ( name, link, update_time,update_user_id,category_diy_id,kinds,fid,sort)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.name},
            #{item.link},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.categoryDiyId},
            #{item.kinds},
            #{item.fid},
            #{item.sort}
            )
        </foreach>
    </insert>


    <update id="batchDelete" parameterType="arraylist">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update t_category_diy set deleted = 1
            where id=#{item}
        </foreach>
    </update>

    <update id="batchUpdate" parameterType="arraylist">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update t_category_diy
            <trim prefix="set" suffixOverrides=",">
                <if test="item.name!=null">name = #{item.name},</if>
                link = #{item.link},
                <if test="item.updateTime!=null">update_time = #{item.updateTime},</if>
                <if test="item.updateUserId!=null">update_user_id = #{item.updateUserId},</if>
                <if test="item.categoryDiyId!=null">category_diy_id = #{item.categoryDiyId},</if>
                <if test="item.kinds!=null"> kinds = #{item.kinds},</if>
                <if test="item.fid!=null">fid = #{item.fid},</if>
                <if test="item.sort!=null">sort = #{item.sort},</if>
            </trim>
            where id=#{item.id}
        </foreach>
    </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
        <mapper namespace="com.jiuji.pick.service.product.mapper.ProductRuleConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ProductRuleConfig">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="ppid" property="ppid" />
        <result column="user_Ids" property="userIds" />
        <result column="restrict_target" property="restrictTarget" />
        <result column="restrict_type" property="restrictType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_id, ppid, user_Ids, restrict_target, restrict_type, create_time, update_time, del_flag
    </sql>


    <select id="conditionSearchWhiteConfig" resultType="com.jiuji.pick.service.product.vo.QueryIndexWhiteConfigVo">
        select
            prc.id,
            prc.ppid,
            prc.restrict_target as restrictTarget,
            prc.restrict_type as restrictType,
            prc.user_Ids as userIds
        from t_product_rule_config prc
        left join t_product_info pi on prc.ppid = pi.ppriceid
        <where>
            prc.del_flag=0
          <if test="param.searchType != null and param.searchType == 1 and param.keyWord != null  and param.keyWord != ''">
            and prc.ppid=#{param.keyWord}
          </if>
          <if test="param.searchType != null and param.searchType == 2 and param.keyWord != null  and param.keyWord != ''">
            and pi.product_name like CONCAT('%', #{param.keyWord}, '%')
          </if>
        </where>
    </select>

</mapper>

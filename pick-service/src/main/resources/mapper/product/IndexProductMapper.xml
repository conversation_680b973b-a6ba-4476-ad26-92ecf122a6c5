<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.IndexProductMapper">

    <select id="queryIndexProduct" resultType="com.jiuji.pick.service.product.vo.IndexProductVo">
        select t.id,
               t.ppid,
               t.jiu_ji_product_id as jiuJiProductId,
               tsu.id as supplierId,
               d.id as supplierProductId,
               d.sort as sort,
               t.source,
               t.product_status as productStatus,
               t.product_type as productType ,
               t.sale_count as saleCount,
               t.advice_price as advicePrice,
               t.product_up_time as productUpTime,
               d.buy_no_tax_price as buyNoTaxPrice,
               d.buy_tax_price as buyTaxPrice,
               tsu.name as companyName,
               tsu.address,
               suq.after_sale_province_name,
               suq.after_sale_city_name,
               suq.after_sale_district_name,
               pi.product_name as productName,
               p.bpic
        from t_supplier_product_detail d
                     join t_pick_product t on t.id=d.product_id and d.del_flag = 0
                     join t_product_bind pb on t.ppid=pb.ppid and pb.supplier_id=d.supplier_id and pb.bind_status = 1 and pb.del_flag = 0
                     join t_supplier_user tsu on tsu.id=d.supplier_id and tsu.status=2 and tsu.del_flag = 0
                     join t_supplier_user_qualification suq on tsu.id = suq.supplier_user_id
                     join t_product_price p on d.ppid=p.ppriceid
                     join t_product_info pi ON p.ppriceid = pi.ppriceid
        <where>
            t.del_flag = 0
            <if test="type != null and type == 1">
                and t.default_area=1
            </if>
            <if test="type != null and type == 2">
                and t.hot_area=1
            </if>
            <if test="type != null and type == 3">
                and t.happy_area=1
            </if>
            <if test="type != null and type == 4">
                and t.recommend_area=1
            </if>
            <if test="type != null and type == 5">
                and t.product_type=1
            </if>
            <if test="type != null and type == 6">
                and t.product_type=2
            </if>
            <if test="type != null and type == 7">
                and t.product_type in (3,4)
            </if>
            <if test="type != 5 and type != 6 and partnerType != null and partnerType == 2">
                and t.product_type not in (3,4)
            </if>
            and t.product_status=1
            and not EXISTS (select rc.ppid from t_product_rule_config rc where
                rc.ppid = t.ppid and rc.restrict_target = 1 and rc.restrict_type = 1 and rc.del_flag = 0 and rc.user_Ids LIKE CONCAT('%,',tsu.id,',%'))
            and tsu.status=2
            and pb.bind_status=1
            <choose>
                <when test="type != null and type == 4">
                    order by d.sort desc, t.product_up_time desc
                </when>
                <otherwise>
                    order by d.sort desc, t.sale_count desc
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="querySimilarProduct" resultType="com.jiuji.pick.service.product.vo.SimilarProductVo">
        select tu.name as companyName,
               d.buy_no_tax_price as buyNoTaxPrice,
               pi.product_name as productName,
               t.id as productId,
               d.id as supplierProductId
        from t_pick_product t
                 join t_supplier_product_detail d on t.id=d.product_id and d.del_flag = 0
                 join t_product_bind pb on t.ppid=pb.ppid and pb.supplier_id=d.supplier_id and pb.del_flag = 0
                 join t_supplier_user tu on d.supplier_id=tu.id and tu.status=2 and tu.del_flag = 0
                 join t_product_info pi ON t.ppid = pi.ppriceid
        where t.del_flag = 0
          and t.id=#{id}
          and d.id != #{supplierProductId}
          and pb.bind_status=1
          and not EXISTS (select rc.ppid from t_product_rule_config rc where
            rc.ppid = t.ppid and rc.restrict_target = 1 and rc.restrict_type = 1 and rc.del_flag = 0 and rc.user_Ids LIKE CONCAT('%,',tu.id,',%'))
          and t.product_status=1;
    </select>

    <sql id="queryAreaProductPre">
        select t.id,
               t.source,
               tsu.id as supplierId,
               t.ppid,
               t.sale_count as saleCount,
               d.id as supplierProductId,
               t.product_status as productStatus,
               t.product_type as productType ,
               t.advice_price as advicePrice,
               d.buy_no_tax_price as buyNoTaxPrice,
               d.buy_tax_price as buyTaxPrice,
               tsu.name as companyName,
               tsu.address ,
               suq.after_sale_province_name,
               suq.after_sale_city_name,
               suq.after_sale_district_name,
               p.bpic,
               i.productid as productId,
               i.product_name as productName
        from t_pick_product t
                 join t_supplier_product_detail d on t.id=d.product_id and d.del_flag = 0
                 join t_product_bind pb on t.ppid=pb.ppid and pb.supplier_id=d.supplier_id and pb.del_flag = 0
                 join t_supplier_user tsu on tsu.id=d.supplier_id and tsu.status=2 and tsu.del_flag = 0
                 join t_supplier_user_qualification suq on tsu.id = suq.supplier_user_id
                 join t_product_price p on d.ppid=p.ppriceid
                 join t_product_info i on t.ppid=i.ppriceid
        where
            t.del_flag = 0
          and t.product_status=1
          and pb.bind_status=1
          and not EXISTS (select rc.ppid from t_product_rule_config rc where
            rc.ppid = t.ppid and rc.restrict_target = 1 and rc.restrict_type = 1 and rc.del_flag = 0 and rc.user_Ids LIKE CONCAT('%,',tsu.id,',%'))
    </sql>

    <select id="queryAreaProduct"  resultType="com.jiuji.pick.service.product.vo.IndexProductVo">
          <include refid="queryAreaProductPre"/>
          <if test="param.type != null and param.type != '' and param.type == 'hotArea'">
          and t.hot_area=1
          </if>
          <if test="param.filterPpidList !=null and param.filterPpidList.size()!=0">
          and t.ppid in
              <foreach collection="param.filterPpidList" index="index" item="item" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
          <if test="param.type != null and param.type != '' and param.type == 'happyArea'">
          and t.happy_area=1
          </if>
          <if test="param.type != null and param.type != '' and param.type == 'recommendArea'">
          and t.recommend_area=1
          </if>
          <if test="param.type != null and param.type != '' and param.type == 'defaultArea'">
          and t.default_area=1
          </if>
          <if test="param.type != null and param.type != '' and param.type == 'bulkyArea'">
          and t.product_type=1
          </if>
          <if test="param.type != null and param.type != '' and param.type == 'virtualArea'">
          and t.product_type=2
          </if>
          <if test="param.type != null and param.type != '' and param.type == 'assetsArea'">
          and t.product_type in (3,4)
          </if>
          <if test="param.minPrice != null">
          and  d.buy_no_tax_price &gt;= #{param.minPrice}
          </if>
          <if test="param.maxPrice != null">
          and d.buy_no_tax_price &lt;= #{param.maxPrice}
          </if>
          <choose>
            <when test="param.sortSaleCount != null">
                order by t.sale_count desc, t.product_up_time desc
            </when>
            <otherwise>
                <choose>
                    <when test="param.sortPrice !=null and param.sortPrice == 1">
                        order by d.buy_no_tax_price desc
                    </when>
                    <when test="param.sortPrice !=null and param.sortPrice == 2">
                        order by d.buy_no_tax_price asc
                    </when>
                    <otherwise>
                        order by d.sort ,t.sale_count DESC
                    </otherwise>
                </choose>
            </otherwise>
          </choose>

    </select>

    <select id="getProductDetailInfo" resultType="com.jiuji.pick.service.product.vo.IndexProductDetailVo">
        SELECT
                t.id AS productId,
                t.ppid,
                t.sale_count as saleCount,
                d.id AS supplierProductId,
                t.jiu_ji_product_id as jiuJiProductId,
                t.product_future AS productFuture,
                t.advice_price AS advicePrice,
                t.product_config AS productConfig,
                t.attachment_ids AS attachmentIds,
                t.product_type AS productType,
                d.buy_no_tax_price AS buyNoTaxPrice,
                d.buy_tax_price AS buyTaxPrice,
                d.material AS material,
                d.delivery_day AS deliveryDay,
                d.no_reason_return AS noReasonReturn,
                d.change_day AS changeDay,
                d.bad_pay AS badPay,
                d.lack_pay AS lackPay,
                d.after_sale_policy AS afterSalePolicy,
                d.other_policy AS otherPolicy,
                d.quality_date AS qualityDate,
                d.box_rule AS boxRule,
                d.payment_day AS paymentDay,
                d.stock_count AS stockCount,
                d.minimum_order_quantity AS minimumOrderQuantity,
                tsu.id AS supplierId,
                i.product_name AS productName,
                i.bar_code AS barCode,
                tsu.name AS companyNme,
                tsu.address,
                tsu.id AS supplierUserId,
                tsu.leader_phone AS leaderPhone,
                p.cost_price AS costPrice,
                tp.detail,
                tp.decription,
                i.cid AS categoryId,
                c. `name` AS categoryName,
                i.brand_id AS brandId,
                b. `name` AS brandName,
                i.product_name AS productName,
                tp.shot_name AS shortName,
                i.product_color AS productColor,
                p.bpic AS bPic,
                p.no_promotion AS noPromotion,
                i.bar_code AS barCode
        FROM
                t_supplier_product_detail d
                JOIN t_pick_product t ON t.ppid = d.ppid and t.del_flag = 0
                JOIN t_supplier_user tsu ON tsu.id = d.supplier_id and tsu.del_flag = 0
                JOIN t_product_price p ON d.ppid = p.ppriceid
                JOIN t_product_info i ON t.ppid = i.ppriceid
                JOIN t_product tp ON tp.id = i.productid
                LEFT JOIN t_category c ON i.cid = c.id
                LEFT JOIN t_brand b ON b.id = i.brand_id
        WHERE
                d.del_flag = 0
                AND d.id = #{supplierProductId}
    </select>

    <select id="getProductDetailSpecInfo" resultType="com.jiuji.pick.service.product.vo.IndexMiniProductSpecVo">
        SELECT
                pi.productid as jiuJiProductId,
                pi.product_color,
                pp.id as productId,
                spd.id as supplierProductId,
                p.bpic AS bPic,
                pp.ppid
        FROM
                t_product_info pi

                INNER JOIN t_pick_product pp ON pi.productid = pp.jiu_ji_product_id and pi.ppriceid = pp.ppid and pp.product_status=1
                LEFT JOIN t_product_price p ON p.ppriceid =  pi.ppriceid
                INNER JOIN t_supplier_product_detail spd ON pp.ppid = spd.ppid and spd.del_flag = 0
                INNER JOIN t_product_bind pb ON spd.ppid = pb.ppid AND spd.supplier_id = pb.supplier_id AND pb.bind_status = 1 and pb.del_flag = 0
                INNER JOIN t_supplier_user tu on spd.supplier_id=tu.id and tu.status=2 and tu.del_flag = 0
        WHERE
                pp.del_flag = 0
                AND pi.product_color is not null
                AND pi.productid = #{jiuJiProductId}
                AND spd.supplier_id = #{supplierId}
                <if test="ppid!=null">
                AND  pi.ppriceid=#{ppid}
                </if>
	            AND NOT EXISTS (SELECT rc.ppid FROM t_product_rule_config rc WHERE rc.ppid = pp.ppid AND rc.restrict_target = 1 AND rc.restrict_type = 1 AND rc.del_flag = 0 AND rc.user_Ids LIKE CONCAT('%,', spd.supplier_id, ',%'))
                order by pp.ppid
    </select>


    <select id="getProductDetailSpecInfoV2" resultType="com.jiuji.pick.service.product.vo.IndexMiniProductSpecVo">
        SELECT
        pi.productid as jiuJiProductId,
        pp.id as productId,
        spd.id as supplierProductId,
        p.bpic AS bPic,
        pp.ppid
        FROM
        t_product_info pi

        INNER JOIN t_pick_product pp ON pi.productid = pp.jiu_ji_product_id and pi.ppriceid = pp.ppid and pp.product_status=1
        LEFT JOIN t_product_price p ON p.ppriceid =  pi.ppriceid
        INNER JOIN t_supplier_product_detail spd ON pp.ppid = spd.ppid and spd.del_flag = 0
        INNER JOIN t_product_bind pb ON spd.ppid = pb.ppid AND spd.supplier_id = pb.supplier_id AND pb.bind_status = 1 and pb.del_flag = 0
        INNER JOIN t_supplier_user tu on spd.supplier_id=tu.id and tu.status=2 and tu.del_flag = 0
        WHERE
        pp.del_flag = 0
        AND pi.productid = #{jiuJiProductId}
        AND spd.supplier_id = #{supplierId}
        <if test="ppid!=null">
            AND  pi.ppriceid=#{ppid}
        </if>
        AND NOT EXISTS (SELECT rc.ppid FROM t_product_rule_config rc WHERE rc.ppid = pp.ppid AND rc.restrict_target = 1 AND rc.restrict_type = 1 AND rc.del_flag = 0 AND rc.user_Ids LIKE CONCAT('%,', spd.supplier_id, ',%'))
        order by pp.ppid
    </select>



    <select id="pageAreaProductVO" resultType="com.jiuji.pick.service.product.vo.AreaProductVO">
        SELECT spd.id, spd.id supplierProductId, spd.ppid, pi.product_name productName, su.`name` companyName, spd.buy_tax_price buyTaxPrice,
               pp.sale_count saleCount, pp.product_up_time productUpTime, spd.sort, spd.supplier_id supplierId
            FROM t_supplier_product_detail spd
            LEFT JOIN t_product_bind pb ON spd.product_id=pb.product_id AND spd.supplier_id = pb.supplier_id AND pb.bind_status = 1 AND spd.del_flag=0
            LEFT JOIN t_pick_product pp ON pp.id = spd.product_id AND pp.del_flag=0
            LEFT JOIN t_product_info pi on pi.ppriceid = spd.ppid
            LEFT JOIN t_supplier_user su ON su.id = spd.supplier_id
        <where>
            spd.del_flag = 0
            <if test="param.areaCode != null and param.areaCode == 1">
                and pp.default_area=1
            </if>
            <if test="param.areaCode != null and param.areaCode == 2">
                and pp.hot_area=1
            </if>
            <if test="param.areaCode != null and param.areaCode == 3">
                and pp.happy_area=1
            </if>
            <if test="param.areaCode != null and param.areaCode == 4">
                and pp.recommend_area=1
            </if>
            <if test="param.areaCode != null and param.areaCode == 5">
                and pp.product_type=1
            </if>
            <if test="param.areaCode != null and param.areaCode == 6">
                and pp.product_type=2
            </if>
            <if test="param.ppid != null and param.ppid != ''">
                and spd.ppid = #{param.ppid}
            </if>
            <if test="param.productName != null and param.productName != ''">
                and pi.product_name like CONCAT('%',#{param.productName},'%')
            </if>
            <if test="param.companyName != null and param.companyName != ''">
                and su.`name` like CONCAT('%',#{param.companyName},'%')
            </if>
            <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
                and pp.product_up_time BETWEEN #{param.startTime} and #{param.endTime}
            </if>
        </where>
        ORDER BY spd.sort desc, pp.sale_count desc
    </select>
    <select id="queryAreaProductAll" resultType="com.jiuji.pick.service.product.vo.IndexProductVo">
        <include refid="queryAreaProductPre"/>
    </select>

</mapper>

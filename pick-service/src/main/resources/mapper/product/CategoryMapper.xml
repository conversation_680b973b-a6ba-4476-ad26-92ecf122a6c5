<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.CategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.Category">
        <id column="id" property="id"/>
        <result column="is_statistics" property="isStatistics"/>
        <result column="keywords" property="keywords"/>
        <result column="name" property="name"/>
        <result column="prices" property="prices"/>
        <result column="rank" property="rank"/>
        <result column="is_show" property="isShow"/>
        <result column="path" property="path"/>
        <result column="service_ids" property="serviceIds"/>
        <result column="ppids" property="ppids"/>
        <result column="description" property="description"/>
        <result column="level" property="level"/>
        <result column="page_title" property="pageTitle"/>
        <result column="product_sort_value" property="productSortValue"/>
        <result column="is_mobile" property="isMobile"/>
        <result column="child" property="child"/>
        <result column="adid" property="adid"/>
        <result column="parent_id" property="parentId"/>
        <result column="newsid2" property="newsid2"/>
        <result column="display" property="display"/>
        <result column="is_service" property="isService"/>
        <result column="newsid" property="newsid"/>
        <result column="pic" property="pic"/>
        <result column="not_support_delivery" property="notSupportDelivery"/>
    </resultMap>

    <select id="getChildCount" resultType="integer">
    select count(*)
    from t_category
    where parent_id = #{parentId}
      and display = 1
  </select>

    <select id="getChildIdList" resultType="integer">
    select id
    from t_category
    where LOCATE(CONCAT(',', #{id}, ','), path) > 0
    order by level desc
  </select>

    <select id="resetSubCategory">

    UPDATE t_category
    SET path  = #{path},
        level =#{level}
    WHERE id = #{id}
  </select>

    <select id="getNeedResetPath" resultType="string">
    SELECT (SELECT CONCAT(path, pnode.id, ',')
            FROM t_category pnode
            WHERE pnode.id =
                  c.parent_id)
    FROM t_category c
    WHERE id = #{id}
  </select>


    <update id="resetCategoryChild">
    update t_category A, (SELECT parent_id, COUNT(1) as childNum
                          FROM t_category
                          WHERE display = 1
                          GROUP BY parent_id) B
    set A.child =B.childNum
    where A.id = B.parent_id
  </update>

    <update id="resetProductCatgs">
    update t_product
    set cid_family = (select CONCAT(path, #{id}, ',') from t_category where id = #{id})
    where cid = #{id}
  </update>

    <update id="updateParentChild">
    UPDATE t_category
    SET child=#{child}
    WHERE id = #{parentId}
  </update>
    <delete id="deleteCategorySearchKeyword">
    DELETE
    FROM t_category_search_word
    WHERE category_id = #{cid}
  </delete>

    <insert id="insertCategorySearchWord">
        insert into t_category_search_word (category_id, search_word) values
        <foreach collection="searchWordList" item="item" index="index" separator=",">
            (#{item.categoryId}, #{item.searchWord})
        </foreach>
    </insert>

    <update id="deleteAllChild">
    UPDATE t_category
    SET display=0
    WHERE LOCATE(CONCAT(',', #{cid}, ','), path) > 0
  </update>

    <update id="decreaseParent">
    UPDATE t_category
    SET child=#{child}-1
    WHERE id = #{parentId}
  </update>

    <select id="getAllChild" resultType="java.lang.Long">
    select id
    from t_category c
    where LOCATE(CONCAT(',', #{id}, ','), path) > 0
  </select>

    <select id="getMultipleParentAllChild" resultType="java.lang.Long">
        select id
        from t_category c
        where
        <foreach collection="ids" item="id" index="index" separator="OR">
            LOCATE(CONCAT(',', #{id}, ','), path) > 0
        </foreach>
    </select>
    <select id="getAllChildByIdList" resultType="java.lang.Long">
        select id
        from t_category c
        <where>
            <foreach collection="idList" item="cid" separator=" ">
                OR `path` like CONCAT('%,', #{cid}, ',%')
            </foreach>
        </where>
    </select>

    <update id="displayForAllChild">
    UPDATE t_category
    SET display=1
    WHERE LOCATE(CONCAT(',', #{id}, ','), path) > 0
  </update>

    <insert id="insertCategory">
    INSERT INTO `t_category`(`id`, `parent_id`, `name`, `level`, `child`, `rank`, `path`, `pic`, `keywords`, `description`, `display`, `prices`, `is_mobile`, `newsid`, `newsid2`, `ppids`, `page_title`, `is_service`, `is_show`, `adid`, `is_statistics`, `service_ids`, `product_sort_value`, `not_support_delivery`, `sys_sort`, `is_output_category`, `is_virtual_goods`)
    VALUES (#{id}, #{parentId}, #{name}, #{level}, #{child}, #{rank}, #{path}, #{pic}, #{keywords}, #{description}, #{display}, #{prices},#{isMobile},#{newsid},#{newsid2},#{ppids}, #{pageTitle}, #{isService}, #{isShow}, #{adid}, #{isStatistics}, #{serviceIds}, #{productSortValue}, #{notSupportDelivery}, #{sysSort}, #{isOutputCategory}, #{isVirtualGoods});
  </insert>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ProductBindMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ProductBind">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="supplier_id" property="supplierId" />
        <result column="ppid" property="ppid" />
        <result column="bind_status" property="bindStatus" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_id, supplier_id, ppid, bind_status, remark, create_time, update_time, del_flag
    </sql>

    <select id="supplierProductCount" resultType="com.jiuji.pick.service.product.vo.SupplierProductCountVO">
        select count(product_id) as ProductNum, any_value(supplier_id) as supplierId
        from t_product_bind
        <where>
            del_flag = 0 and bind_status = 1

            <if test="supplierIdList != null">
                and supplier_id in
                <foreach collection="supplierIdList" index="index" item="item" open="("  separator="," close=")"> #{item} </foreach>
            </if>
        </where>
        GROUP BY supplier_id
    </select>

</mapper>

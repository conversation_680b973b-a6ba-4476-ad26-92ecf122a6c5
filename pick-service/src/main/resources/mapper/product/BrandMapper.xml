<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.BrandMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.Brand">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="url" property="url" />
        <result column="rank" property="rank" />
        <result column="info" property="info" />
        <result column="display" property="display" />
        <result column="seo_key" property="seoKey" />
        <result column="picforapp" property="picforapp" />
        <result column="web_site" property="webSite" />
        <result column="tel" property="tel" />
        <result column="product_sort_value" property="productSortValue" />
        <result column="short_name" property="shortName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, url, rank, info, display, seo_key, picforapp, web_site, tel, product_sort_value, short_name
    </sql>


    <select id="listWithCategorySort" resultMap="BaseResultMap">
        select
        b.id,
        b.name,
        b.url,
        b.rank,
        b.info,
        b.display,
        b.seo_key,
        b.picforapp,
        b.web_site,
        b.tel,
        b.product_sort_value,
        b.short_name
        from t_brand b
        left join t_brand_category bc on b.id=bc.brand_id
        where bc.category_id = #{categoryId}
        <if test="brandIds!=null and brandIds.size()>0">
            and b.id in
            <foreach collection="brandIds" item="brandId" open="(" close=")" separator=",">
                #{brandId}
            </foreach>
        </if>
        order by bc.rank
    </select>

    <insert id="insertBrand">
        INSERT INTO `t_brand`(`id`, `name`, `url`, `rank`, `info`, `display`, `seo_key`, `picforapp`, `web_site`, `tel`, `product_sort_value`, `short_name`)
        VALUES (#{id}, #{name}, #{url}, #{rank}, #{info}, #{display}, #{seoKey}, #{picforapp},#{webSite}, #{tel}, #{productSortValue}, #{shortName});
    </insert>

</mapper>

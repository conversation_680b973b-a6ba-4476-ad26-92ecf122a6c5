<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.ProductStInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.ProductStInfo">
        <id column="id" property="id" />
        <result column="standard_detail_id" property="standardDetailId" />
        <result column="ppriceid" property="ppriceid" />
    </resultMap>

    <select id="getProductAllSpecByPid" resultType="com.jiuji.pick.service.product.bo.ProductStDBO">
        SELECT
            A.ppriceid AS ppid,
            D.productid,
            pd.id as supplierProductId,
            C.NAME,
            B.VALUE,
            IFNULL(C.order_id,0) AS firstOrder,
            IFNULL(B.order_id,0) AS secondOrder,
            C.show_type,
            B.id AS standardDetailId,
            D.rank,
            D.b_pic AS bpic,
            C.id AS standId,
            D.member_price,
            C.cid,
            ifnull(C.limit_select,false) limit_select
        FROM
            t_pick_product t
                JOIN t_supplier_product_detail pd ON t.ppid = pd.ppid
                JOIN t_product_bind pb ON t.ppid = pb.ppid AND pb.supplier_id = pd.supplier_id
                JOIN t_supplier_user tsu ON tsu.id = pd.supplier_id
                JOIN t_product_st_info AS A ON t.ppid = A.ppriceid
                INNER JOIN t_product_st_detail AS B ON A.standard_detail_id = B.id
                INNER JOIN t_product_standard AS C ON B.stand_id = C.id
                INNER JOIN t_product_info AS D ON D.ppriceid = A.ppriceid
        WHERE
              D.productid = #{productId}
          and D.is_del = 0
    </select>
</mapper>

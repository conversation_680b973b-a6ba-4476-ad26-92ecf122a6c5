<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.product.mapper.CategoryDiyPcTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.product.entity.CategoryDiyPcType">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="link" property="link" />
        <result column="sort" property="sort" />
        <result column="fid" property="fid" />
        <result column="kinds" property="kinds" />
        <result column="xtenant" property="xtenant" />
        <result column="category_diy_id" property="categoryDiyId" />
        <result column="update_user_id" property="updateUserId" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, link, sort, fid, kinds, xtenant, category_diy_id, update_user_id, deleted, create_time, update_time
    </sql>
    <update id="batchUpdate" parameterType="arraylist">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update t_category_diy_pc_type
            <trim prefix="set" suffixOverrides=",">
                <if test="item.name!=null">name = #{item.name},</if>
                link = #{item.link},
                <if test="item.updateTime!=null">update_time = #{item.updateTime},</if>
                <if test="item.updateUserId!=null">update_user_id = #{item.updateUserId},</if>
                <if test="item.categoryDiyId!=null">category_diy_id = #{item.categoryDiyId},</if>
                <if test="item.kinds!=null"> kinds = #{item.kinds},</if>
                <if test="item.fid!=null">fid = #{item.fid},</if>
                <if test="item.sort!=null">sort = #{item.sort},</if>
            </trim>
            where id=#{item.id}
        </foreach>
    </update>

    <insert id="batchInsert"  useGeneratedKeys="true" keyProperty="id">
        insert into t_category_diy_pc_type ( name, link, update_time,update_user_id,category_diy_id,kinds,fid,sort)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (

            #{item.name},
            #{item.link},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.categoryDiyId},
            #{item.kinds},
            #{item.fid},
            #{item.sort}
            )
        </foreach>
    </insert>

    <update id="batchDelete" parameterType="arraylist">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update t_category_diy_pc_type set deleted = 1
            where id=#{item}
        </foreach>
    </update>

</mapper>

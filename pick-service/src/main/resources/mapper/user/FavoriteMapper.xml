<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.user.mapper.FavoriteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.user.entity.Favorite">
        <id column="id" property="id" />
        <result column="supplier_product_id" property="supplierProductId" />
        <result column="supplier_user_id" property="supplierUserId" />
        <result column="partner_user_id" property="partnerUserId" />
        <result column="is_delete" property="isDelete" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, supplier_product_id, supplier_user_id, partner_user_id, is_delete, create_time, update_time
    </sql>

</mapper>

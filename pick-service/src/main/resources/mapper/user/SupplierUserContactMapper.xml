<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.user.mapper.SupplierUserContactMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.user.entity.SupplierUserContact">
        <id column="id" property="id" />
        <result column="supplier_user_id" property="supplierUserId" />
        <result column="type" property="type" />
        <result column="name" property="name" />
        <result column="phone" property="phone" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, supplier_user_id, type, name, phone, remark, create_time, update_time, del_flag
    </sql>

</mapper>

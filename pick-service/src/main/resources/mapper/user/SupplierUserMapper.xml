<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.user.mapper.SupplierUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.user.entity.SupplierUser">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="short_name" property="shortName" />
        <result column="login_name" property="loginName" />
        <result column="password" property="password" />
        <result column="status" property="status" />
        <result column="type" property="type" />
        <result column="province_id" property="provinceId" />
        <result column="province_name" property="provinceName" />
        <result column="city_id" property="cityId" />
        <result column="city_name" property="cityName" />
        <result column="district_id" property="districtId" />
        <result column="district_name" property="districtName" />
        <result column="address" property="address" />
        <result column="remark" property="remark" />
        <result column="import_id" property="importId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, login_name, password, status, type, province_id, province_name, city_id, city_name, district_id, district_name, address, remark,import_id, create_time, update_time, del_flag
    </sql>

    <select id="getSupplierInfo4Neo" resultType="com.jiuji.pick.service.user.dto.NeoSupplierDTO">
        select su.name as name, su.city_id as addressCityId, su.address as addressCityDetail, su.name as companyName,
            suq.after_sale_name as afterSaleName, suq.after_sale_phone as afterSalePhone, suq.after_sale_address as afterSaleAddress,
            suc.name as contactName, suc.phone as contactMobile
        from t_supplier_user as su
        inner join t_supplier_user_contact as suc on su.id = suc.supplier_user_id
        inner join t_supplier_user_qualification as suq on su.id = suq.supplier_user_id
        where su.del_flag = 0 and su.id = #{supplierId} LIMIT 1
    </select>

</mapper>

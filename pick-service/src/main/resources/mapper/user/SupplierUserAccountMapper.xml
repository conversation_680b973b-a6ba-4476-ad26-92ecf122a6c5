<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.user.mapper.SupplierUserAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.user.entity.SupplierUserAccount">
        <id column="id" property="id" />
        <result column="supplier_user_id" property="supplierUserId" />
        <result column="type" property="type" />
        <result column="account_name" property="accountName" />
        <result column="account_num" property="accountNum" />
        <result column="bank_deposit" property="bankDeposit" />
        <result column="province_id" property="provinceId" />
        <result column="province_name" property="provinceName" />
        <result column="city_id" property="cityId" />
        <result column="city_name" property="cityName" />
        <result column="district_id" property="districtId" />
        <result column="district_name" property="districtName" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, supplier_user_id, type, account_name, public_account_num, bank_deposit, province_id, province_name, city_id, city_name, district_id, district_name, remark, create_time, update_time, del_flag
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.user.mapper.SupplierUserQualificationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.user.entity.SupplierUserQualification">
        <id column="id" property="id" />
        <result column="supplier_user_id" property="supplierUserId" />
        <result column="registered_fund" property="registeredFund" />
        <result column="nature" property="nature" />
        <result column="legal_person" property="legalPerson" />
        <result column="category_id" property="categoryId" />
        <result column="after_sale_name" property="afterSaleName" />
        <result column="after_sale_phone" property="afterSalePhone" />
        <result column="after_sale_address" property="afterSaleAddress" />
        <result column="after_sale_province_name" property="afterSaleProvinceName" />
        <result column="after_sale_province_id" property="afterSaleProvinceId" />
        <result column="after_sale_city_name" property="afterSaleCityName" />
        <result column="after_sale_city_id" property="afterSaleCityId" />
        <result column="after_sale_district_name" property="afterSaleDistrictName" />
        <result column="after_sale_district_id" property="afterSaleDistrictId" />
        <result column="leader_phone" property="leaderPhone" />
        <result column="finance_name" property="financeName" />
        <result column="finance_phone" property="financePhone" />
        <result column="major_businesses" property="majorBusinesses" />
        <result column="contract_url" property="contractUrl" />
        <result column="contract_name" property="contractName" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, supplier_user_id, registered_fund, nature, legal_person, category_id, address, after_sale_name, after_sale_phone, after_sale_address,
        after_sale_province_name,after_sale_province_id, after_sale_city_name, after_sale_city_id, after_sale_district_id, after_sale_district_name, leader_phone, finance_name, finance_phone, major_businesses, contract_url, contract_name, remark, create_time, update_time, del_flag
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.mapper.PartnerUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.user.entity.PartnerUser">
        <id column="id" property="id" />
        <result column="out_user_id" property="outUserId" />
        <result column="xtenant" property="xtenant" />
        <result column="source" property="source" />
        <result column="host" property="host" />
        <result column="name" property="name" />
        <result column="short_name" property="shortName" />
        <result column="phone" property="phone" />
        <result column="register_time" property="registerTime" />
        <result column="status" property="status" />
        <result column="business_name" property="businessName" />
        <result column="business_phone" property="businessPhone" />
        <result column="invoice_title" property="invoiceTitle" />
        <result column="invoice_num" property="invoiceNum" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, out_user_id, xtenant, source, host, name, shortName, phone, register_time, status, business_name, business_phone, invoice_title, invoice_num, remark, create_time, update_time, del_flag
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.user.mapper.SupplierChannelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.user.entity.SupplierChannel">
        <id column="id" property="id" />
        <result column="supplier_id" property="supplierId" />
        <result column="xtenant_id" property="xtenantId" />
        <result column="channel_id" property="channelId" />
        <result column="channel_type" property="channelType" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, supplier_id, channel_id, channel_type, remark, create_time, update_time, del_flag
    </sql>

    <select id="supplierChannelCount" resultType="com.jiuji.pick.service.product.vo.SupplierChannelCountVO">
        select count(channel_id) as channelNum, any_value(supplier_id) as supplierId
        from t_supplier_channel
        <where>
        del_flag = 0

            <if test="supplierIdList != null">
                and supplier_id in
                <foreach collection="supplierIdList" index="index" item="item" open="("  separator="," close=")"> #{item} </foreach>
            </if>
        </where>
        GROUP BY supplier_id

    </select>

    <select id="listBySupplierId" parameterType="java.lang.Long" resultType="com.jiuji.pick.service.user.vo.SupplierChannelVO">
        select
        su.name as supplierName, c.* from t_supplier_channel c
        LEFT JOIN  t_supplier_user su on su.id = c.supplier_id
        where c.del_flag = 0 and c.supplier_id = #{supplierId}
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.order.mapper.OrderInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.order.entity.OrderInfo">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="xtenant_id" property="xtenantId"/>
        <result column="partner_id" property="partnerId"/>
        <result column="channel_id" property="channelId"/>
        <result column="total_price" property="totalPrice"/>
        <result column="order_status" property="orderStatus"/>
        <result column="channel_type" property="channelType"/>
        <result column="order_title" property="orderTitle"/>
        <result column="out_order_no" property="outOrderNo"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="receive_address" property="receiveAddress"/>
        <result column="receive_poi_id" property="receivePoiId"/>
        <result column="receive_poi_name" property="receivePoiName"/>
        <result column="receive_poi_city_id" property="receivePoiCityId"/>
        <result column="delivery_type" property="deliveryType"/>
        <result column="delivery_no" property="deliveryNo"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="finish_time" property="finishTime"/>
        <result column="sale_order_no" property="saleOrderNo"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, supplier_id, xtenant_id, partner_id, channel_id, total_price, order_status, channel_type, order_title,
        out_order_no, receive_address, receive_poi_id, receive_poi_name, delivery_type,
        delivery_no, delivery_time, finish_time, sale_order_no, create_time, update_time, del_flag
    </sql>


    <sql id="commonWhere">
        o.del_flag = 0
        <if test="param.partialDelivery !=null and param.partialDelivery == 1">
            and detail.buy_amount > delivery_count
            and detail.delivery_count >= 0
            and o.order_status = 3
        </if>
        <if test="param.isOverOneDay != null and param.isOverOneDay == 1">
            AND  CURRENT_TIMESTAMP() > DATE_ADD(o.create_time, INTERVAL 1 DAY) and o.order_status = 1
        </if>

        <if test="type == 1 and partnerId != null">
            and o.partner_id = #{partnerId}
        </if>
        <if test="type == 2 and supplierId != null">
            and o.supplier_id = #{supplierId}
        </if>
        <if test="param.orderStatus != null">
            and o.order_status = #{param.orderStatus}
        </if>
        <if test="param.orderStatus == null">
            and o.order_status > 0
        </if>
        <if test="param.priceType != null">
            and version.price_type=#{param.priceType}
        </if>
        <if test="param.orderType != null">
            and o.order_type = #{param.orderType}
        </if>
        <if test="param.areaId != null">
            and o.receive_poi_id = #{param.areaId}
        </if>
        <if test="param.keyType != null and param.keyType == 3">
            and o.partner_id = #{param.keyValue}
        </if>
        <if test="param.keyType != null and param.keyType == 4">
            and p.`name` like concat('%', #{param.keyValue}, '%')
        </if>
        <if test="param.keyType != null and param.keyType == 5">
            and o.order_no = #{param.keyValue}
        </if>
        <if test="param.keyType != null and param.keyType == 6">
            and o.order_title like concat('%', #{param.keyValue}, '%')
        </if>
        <if test="param.keyType != null and param.keyType == 7">
            and o.sale_order_no = #{param.keyValue}
        </if>
        <if test="param.keyType != null and param.keyType == 8">
            and pick.ppid = #{param.keyValue}
        </if>
        <if test="param.keyType != null and param.keyType == 9">
            and pro.name like concat('%', #{param.keyValue}, '%')
        </if>
        <if test="param.keyType != null and param.keyType == 10">
            and o.sale_name = #{param.keyValue}
        </if>
        <if test="param.keyType != null and param.keyType == 11">
            and o.sale_job_number=#{param.keyValue}
        </if>
        <if test="param.startTime != null">
            and o.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and o.create_time <![CDATA[ <= ]]> #{param.endTime}
        </if>

    </sql>

    <select id="getOrderListV2" resultType="com.jiuji.pick.service.order.vo.OrderListExportVO">
        select distinct
        o.order_no as orderNo,
        o.sale_order_no as saleOrderNo,
        o.order_status as orderStatus,
        o.create_time as createTime,
        p.`name` as partnerName,
        pick.ppid as ppid,
        pro.name as productName,
        detail.buy_amount as buyAmount,
        detail.product_price as productPrice,
        version.price_type as priceType ,
        (detail.buy_amount*detail.product_price) as totalAmount
        from t_order_info o
        LEFT JOIN t_order_detail_info detail on detail.order_id=o.id
        LEFT JOIN t_pick_product pick on pick.id=detail.product_id
        LEFT JOIN t_product pro on pro.id=pick.jiu_ji_product_id
        LEFT JOIN t_partner_user p on o.partner_id = p.id
        LEFT JOIN t_product_order_version version on version.order_id = o.id
        <where>
            <include refid="commonWhere"/>
        </where>
        order by o.create_time desc
    </select>

    <select id="getOrderList" resultType="com.jiuji.pick.service.order.vo.OrderListVO">
        select distinct o.id as orderId,
        o.order_no as orderNo,
        o.sale_name as saleName,
        o.sale_job_number as saleJobNumber,
        o.order_title as orderTitle,
        o.receive_poi_name as receivePoiName,
        o.create_time as createTime,
        o.finish_time as finishTime,
        o.total_price as totalPrice,
        o.order_status as orderStatus,
        o.order_type as orderType,
        p.`name` as partnerName,
        version.price_type as priceType ,
        o.sale_order_no as saleOrderNo
        from t_order_info o
        LEFT JOIN t_order_detail_info detail on detail.order_id=o.id
        LEFT JOIN t_pick_product pick on pick.id=detail.product_id
        LEFT JOIN t_product pro on pro.id=pick.jiu_ji_product_id
        LEFT JOIN t_partner_user p on o.partner_id = p.id
        LEFT JOIN t_product_order_version version on version.order_id = o.id
        <where>
            <include refid="commonWhere"/>
        </where>
        order by o.create_time desc
    </select>

    <select id="getOrderDetail" resultType="com.jiuji.pick.service.order.vo.OrderDetailVO">
        select o.channel_type as channelType,
               o.create_time as createTime,
               o.partner_id as partnerId,
			   o.delivery_time as deliveryTime,
			   o.finish_time as finishTime,
			   o.order_status as orderStatus,
               o.id as orderId,
			   o.order_no as orderNo,
			   o.contact_person as contactPerson,
			   o.receive_address as receiveAddress,
			   o.contact_phone as contactPhone,
               su.id as supplierId,
			   su.`name` as supplierName,
			   suq.leader_phone as leaderPhone,
               suq.virtual_phone as virtualPhone,
               suq.assets_phone as assetsPhone,
			   o.receive_poi_id as receivePoiId,
			   o.receive_poi_name as receivePoiName,
			   o.total_price as totalPrice,
               o.delivery_type as deliveryType,
               o.delivery_no as deliveryNo,
               o.sale_order_no as saleOrderNo,
               o.order_type as orderType
          from t_order_info o
          left join t_supplier_user su on o.supplier_id = su.id
          left join t_supplier_user_qualification suq on o.supplier_id = suq.supplier_user_id
        where o.del_flag = 0
           and o.id = #{orderId}
        <if test="type == 1 and partnerId != null">
            and o.partner_id = #{partnerId}
        </if>
        <if test="type == 2 and supplierId != null">
            and o.supplier_id = #{supplierId}
        </if>
    </select>

    <select id="countPlatformOrderList" resultType="int">
        select count(1)
          from t_order_info o
          left join t_supplier_user su on o.supplier_id = su.id
          left join t_partner_user pu on o.partner_id = pu.id
        <where>
            o.del_flag = 0
            <if test="orderStatus != null">
                and o.order_status = #{orderStatus}
            </if>
            <if test="startTime != null">
                and o.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and o.create_time <![CDATA[ <= ]]> #{endTime}
            </if>
            <if test="keyType != null and keyType == 1">
                and o.supplier_id = #{keyValue}
            </if>
            <if test="keyType != null and keyType == 2">
                and su.`name` like concat('%', #{keyValue}, '%')
            </if>
            <if test="keyType != null and keyType == 3">
                and o.partner_id = #{keyValue}
            </if>
            <if test="keyType != null and keyType == 4">
                and pu.`name` like concat('%', #{keyValue}, '%')
            </if>
            <if test="keyType != null and keyType == 5">
                and o.order_no like concat('%', #{keyValue}, '%')
            </if>
            <if test="keyType != null and keyType == 6">
                and o.order_title like concat('%', #{keyValue}, '%')
            </if>
            <if test="keyType != null and keyType == 7">
                and o.sale_order_no = #{keyValue}
            </if>
            <if test="orderType != null">
                and o.order_type = #{orderType}
            </if>
        </where>
    </select>

    <select id="getPlatformOrderList" resultType="com.jiuji.pick.service.order.vo.PlatformOrderListVO">
        select distinct o.id as orderId,
               o.order_no as orderNo,
               o.order_title as orderTitle,
               o.order_status as orderStatus,
               o.order_type as orderType,
               o.supplier_id as supplierId,
               o.partner_id as partnerId,
               su.`name` as supplierName,
               pu.`name` as partnerName,
               o.create_time as createTime,
               o.total_price as totalPrice
        <if test="needLimit == false">
            ,(select sum(odi.buy_amount) from t_order_detail_info odi WHERE odi.order_id = o.id) as productCount
        </if>
          from t_order_info o
          left join t_order_detail_info detail on detail.order_id=o.id
          left join t_pick_product pick on pick.id=detail.product_id
          left join t_product pro on pro.id=pick.jiu_ji_product_id
          left join t_supplier_user su on o.supplier_id = su.id
          left join t_partner_user pu on o.partner_id = pu.id
         <include refid="commonWhereTwo"/>
    </select>

    <select id="getReceiveOrderList" resultType="java.lang.Long">
        SELECT
            distinct oi.id
        FROM t_order_info oi
        LEFT JOIN t_order_detail_info odi ON odi.order_id = oi.id
        WHERE
            oi.del_flag = 0
            AND oi.order_status = 3
            AND oi.order_type in(2, 3, 4)
            AND odi.buy_amount = odi.delivery_count
    </select>
    <select id="getProductName" resultType="java.lang.String">
        SELECT jiuji.name  FROM t_order_detail_info info
                                    left join  t_pick_product product on product.id =info.product_id
                                    left join t_product jiuji on jiuji.id =product.jiu_ji_product_id
        WHERE info.id =#{id}

    </select>
    <select id="getPlatformOrderExportList" resultType="com.jiuji.pick.service.order.vo.OrderListExportVO">
        select
        o.order_no as orderNo,
        o.sale_order_no as saleOrderNo,
        o.order_status as orderStatus,
        pu.`name` as partnerName,
        su.`name` as supplierName,
        pick.ppid as ppid,
        pro.name as productName,
        detail.buy_amount as buyAmount,
        detail.product_price as productPrice,
        (detail.buy_amount*detail.product_price) as totalAmount
        <if test="needLimit == false">
            ,(select sum(odi.buy_amount) from t_order_detail_info odi WHERE odi.order_id = o.id) as productCount
        </if>
        from t_order_info o
        left join t_order_detail_info detail on detail.order_id=o.id
        left join t_pick_product pick on pick.id=detail.product_id
        left join t_product pro on pro.id=pick.jiu_ji_product_id
        left join t_supplier_user su on o.supplier_id = su.id
        left join t_partner_user pu on o.partner_id = pu.id
        <include refid="commonWhereTwo"/>

    </select>
    <sql id="commonWhereTwo">
        <where>
            o.del_flag = 0
            <if test="orderStatus != null">
                and o.order_status = #{orderStatus}
            </if>
            <if test="startTime != null">
                and o.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and o.create_time <![CDATA[ <= ]]> #{endTime}
            </if>
            <if test="orderType != null">
                and o.order_type = #{orderType}
            </if>
            <if test="keyType != null and keyType == 1">
                and o.supplier_id = #{keyValue}
            </if>
            <if test="keyType != null and keyType == 2">
                and su.`name` like concat('%', #{keyValue}, '%')
            </if>
            <if test="keyType != null and keyType == 3">
                and o.partner_id = #{keyValue}
            </if>
            <if test="keyType != null and keyType == 4">
                and pu.`name` like concat('%', #{keyValue}, '%')
            </if>
            <if test="keyType != null and keyType == 5">
                and o.order_no = #{keyValue}
            </if>
            <if test="keyType != null and keyType == 6">
                and o.order_title like concat('%', #{keyValue}, '%')
            </if>
            <if test="keyType != null and keyType == 7">
                and o.sale_order_no = #{keyValue}
            </if>
            <if test="keyType != null and keyType == 8">
                and pick.ppid = #{keyValue}
            </if>
            <if test="keyType != null and keyType == 9">
                and pro.name = #{keyValue}
            </if>
        </where>
        order by o.create_time desc
        <if test="needLimit == true and current != null and size != null">
            limit ${current}, ${size}
        </if>
    </sql>

    <select id="getOrderIdBySaleNo" resultType="Long">
        select toi.order_no from t_order_info toi where toi.sale_order_no = #{saleNo} and toi.order_status &lt;&gt; 2 and ifnull(toi.del_flag,0) = 0
    </select>
    <select id="selectAutomaticPickupOrder" resultType="com.jiuji.pick.service.order.param.AutomaticPickupOrder">
        select o.id
        from t_order_info o
        where o.order_status = 3
        AND del_flag = 0
        AND o.delivery_time &lt; DATE_SUB(NOW(), INTERVAL 1 DAY)
        AND not EXISTS( select 1 from t_order_detail_info d where d.order_id = o.id and d.del_flag =0  and d.buy_amount > delivery_count
        and d.delivery_count >= 0 )
        order by id desc

    </select>

</mapper>

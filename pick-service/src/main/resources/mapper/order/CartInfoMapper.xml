<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.order.mapper.CartInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.order.entity.CartInfo">
        <id column="id" property="id"/>
        <result column="product_id" property="productId"/>
        <result column="product_detail_id" property="productDetailId"/>
        <result column="ppid" property="ppid"/>
        <result column="partner_id" property="partnerId"/>
        <result column="user_id" property="userId"/>
        <result column="supplier_user_id" property="supplierUserId"/>
        <result column="product_count" property="productCount"/>
        <result column="price" property="price"/>
        <result column="cart_type" property="cartType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_detail_id, product_id, ppid, partner_id, supplier_user_id, user_id, product_count,
        price, cart_type, create_time, update_time, del_flag, price_type as priceType
    </sql>

    <select id="getSupplierProductInfo" resultType="com.jiuji.pick.service.order.bo.SupplierProductInfoBO">
        select spd.id as productDetailId,
               spd.product_id as productId,
               spd.ppid as ppid,
               spd.supplier_id as supplierId,
               spd.buy_no_tax_price as buyNoTaxPrice,
               spd.buy_tax_price as buyTaxPrice,
               pp.product_status as productStatus,
               pb.bind_status as bindStatus,
               prc.user_Ids as blackUsers,
			   su.`status` as supplierStatus
          from t_supplier_product_detail spd
          left join t_pick_product pp on spd.product_id = pp.id and pp.del_flag = 0
          left join t_product_bind pb on spd.supplier_id = pb.supplier_id and spd.product_id = pb.product_id and pb.del_flag = 0
          left join t_product_rule_config prc on spd.ppid = prc.ppid and prc.restrict_target = 1
                    and prc.restrict_type = 1 and prc.del_flag = 0
          left join t_supplier_user su on su.id = spd.supplier_id
         where spd.del_flag = 0 and spd.product_id = #{productId} and spd.supplier_id = #{supplierUserId}
    </select>

    <select id="getSupplierProductInfoByIdList" resultType="com.jiuji.pick.service.order.bo.SupplierProductInfoBO">
        select spd.id as productDetailId,
               spd.product_id as productId,
               spd.ppid as ppid,
               spd.supplier_id as supplierId,
               spd.buy_no_tax_price as buyNoTaxPrice,
               spd.buy_tax_price as buyTaxPrice,
               spd.is_remote_delivery_fee as remoteDeliveryFee,
               spd.box_rule as boxRule,
               spd.minimum_order_quantity as minimumOrderQuantity,
               pp.product_status as productStatus,
               pp.product_type as productType,
               pb.bind_status as bindStatus,
               pi.product_name as productName,
               pi.product_color as productColor,
               pp2.bpic as bPic,
               prc.user_Ids as blackUsers,
               pi.cid as productCid,
               pi.bar_code as productBarCode,
               pi.is_mobile as isMobile,
               c.`name` as productCidName
          from t_supplier_product_detail spd
          left join t_pick_product pp on spd.product_id = pp.id and pp.del_flag = 0
          left join t_product_bind pb on spd.supplier_id = pb.supplier_id and spd.product_id = pb.product_id and pb.del_flag = 0
          left join t_product_info pi on spd.ppid = pi.ppriceid
          left join t_product_price pp2 on spd.ppid = pp2.ppriceid
          left join t_product_rule_config prc on spd.ppid = prc.ppid and prc.restrict_target = 1
                    and prc.restrict_type = 1 and prc.del_flag = 0
          left join t_category c on c.id = pi.cid
         where spd.del_flag = 0
           and spd.id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
          from t_cart_info
         where id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>

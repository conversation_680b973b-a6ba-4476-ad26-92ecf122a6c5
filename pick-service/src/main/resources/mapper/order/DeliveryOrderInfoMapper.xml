<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.order.mapper.DeliveryOrderInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.order.entity.DeliveryOrderInfo">
        <id column="id" property="id" />
        <result column="supplier_id" property="supplierId" />
        <result column="order_no" property="orderNo" />
        <result column="order_id" property="orderId" />
        <result column="order_detail_id" property="orderDetailId" />
        <result column="product_id" property="productId" />
        <result column="product_detail_id" property="productDetailId" />
        <result column="product_count" property="productCount" />
        <result column="delivery_status" property="deliveryStatus" />
        <result column="delivery_name" property="deliveryName" />
        <result column="delivery_no" property="deliveryNo" />
        <result column="delivery_time" property="deliveryTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, supplier_id, order_no, order_id, order_detail_id, product_id, product_detail_id, product_count, delivery_status, delivery_name, delivery_no, delivery_time, create_time, update_time, del_flag
    </sql>

</mapper>

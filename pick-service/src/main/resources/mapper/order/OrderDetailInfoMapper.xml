<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.order.mapper.OrderDetailInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.order.entity.OrderDetailInfo">
        <id column="id" property="id"/>
        <result column="partner_id" property="partnerId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="cart_id" property="cartId"/>
        <result column="order_id" property="orderId"/>
        <result column="order_no" property="orderNo"/>
        <result column="product_id" property="productId"/>
        <result column="product_detail_id" property="productDetailId"/>
        <result column="product_price" property="productPrice"/>
        <result column="buy_amount" property="buyAmount"/>
        <result column="delivery_count" property="deliveryCount"/>
        <result column="delivery_fee" property="deliveryFee"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, partner_id, supplier_id, cart_id, order_id, order_no, product_id, product_detail_id, product_price,
        buy_amount, delivery_count, delivery_fee, create_time, update_time, del_flag
    </sql>

    <select id="getOrderProduct" resultType="com.jiuji.pick.service.order.vo.OrderProductVO">
        select odi.id as orderDetailInfoId,
               pov.product_img as productImg,
               pov.product_name as productName,
               pov.product_color as productColor,
               pov.product_cid as productCid,
			   pov.product_cid_name as productCidName,
               pov.product_bar_code as productBarCode,
               pov.product_id as productId,
               pov.price_type as priceType,
               pov.product_detail_id as supplierProductId,
               pov.ppid as ppid,
               odi.product_price as productPrice,
               odi.buy_amount as buyAmount,
               odi.delivery_count as deliveryCount,
               IFNULL(odi.delivery_fee,0) as deliveryFee,
               pp.product_type as productType,
               pp.attachment_ids as attachmentIds
          from t_order_detail_info odi
          left join t_product_order_version pov on odi.order_id = pov.order_id and odi.product_detail_id = pov.product_detail_id
          left join t_pick_product pp on odi.product_id = pp.id and pp.del_flag = 0
         where odi.del_flag = 0 and odi.order_id = #{orderId}
    </select>
    <select id="selectPreviousOrderPrice" resultType="com.jiuji.pick.service.order.dto.PreviousProductDTO">
        select pov.price_type as priceType,d.product_price as productPrice,d.product_id as productId
        from t_order_detail_info d
        left join t_order_info t on t.id = d.order_id
        left join t_product_order_version pov ON pov.product_detail_id = d.product_detail_id and t.id=pov.order_id
        where d.partner_id = #{req.partnerId}
        and d.order_id &lt; #{req.orderId}
        and t.order_status &lt;&gt;2
        AND d.product_id in
        <foreach collection="req.productIdList" item="ppid" open="(" close=")" separator=",">
            #{ppid}
        </foreach>
        order by d.create_time desc
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.mapper.ProductOrderVersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.order.entity.ProductOrderVersion">
        <id column="id" property="id"/>
        <result column="product_detail_id" property="productDetailId"/>
        <result column="product_id" property="productId"/>
        <result column="ppid" property="ppid"/>
        <result column="partner_id" property="partnerId"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_id" property="orderId"/>
        <result column="supplier_user_id" property="supplierUserId"/>
        <result column="buy_no_tax_price" property="buyNoTaxPrice"/>
        <result column="buy_tax_price" property="buyTaxPrice"/>
        <result column="price_type" property="priceType"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="product_img" property="productImg"/>
        <result column="product_cid" property="productCid"/>
        <result column="product_bar_code" property="productBarCode"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_detail_id, product_id, ppid, partner_id, order_no, order_id, supplier_user_id, buy_no_tax_price,
        buy_tax_price, price_type, product_name, product_color, product_img, product_cid, product_bar_code,
        create_time, update_time, del_flag
    </sql>

</mapper>

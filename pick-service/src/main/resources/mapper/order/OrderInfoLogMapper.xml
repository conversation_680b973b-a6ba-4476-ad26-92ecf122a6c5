<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.order.mapper.OrderInfoLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.order.entity.OrderInfoLog">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="content" property="content" />
        <result column="type" property="type" />
        <result column="operation_user_id" property="operationUserId" />
        <result column="operation_user_name" property="operationUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, content, type, operation_user_id, operation_user_name, create_time, update_time, del_flag
    </sql>

</mapper>

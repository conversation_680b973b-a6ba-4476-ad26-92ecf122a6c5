<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.diy.mapper.DiyCostConfigMapper">

    <select id="pageList" resultType="com.jiuji.pick.service.diy.vo.res.DiyCostConfigSearchRes">
        SELECT
        p.ppriceid,
        c.pickweb_cost,
        c.sale_cost,
        p.productid,
        p.product_name ,
        p.product_color ,
        p.que
        FROM
        t_product_info p
        left join diy_cost_config c on p.ppriceid  = c.ppriceid
        <where> p.productid in (65684)
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 1 ">
                and p.productid = #{req.searchValue}
            </if>
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 2 ">
                and p.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 3 ">
                and p.product_name LIKE CONCAT('%',#{req.searchValue},'%')
            </if>
        </where>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.common.mapper.NotifyLogInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.common.entity.NotifyLogInfo">
        <id column="id" property="id" />
        <result column="x_tenant" property="xTenant" />
        <result column="order_no" property="orderNo" />
        <result column="notify_type" property="notifyType" />
        <result column="notify_name" property="notifyName" />
        <result column="notify_url" property="notifyUrl" />
        <result column="notify_param" property="notifyParam" />
        <result column="notify_result" property="notifyResult" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, x_tenant, order_no, notify_type, notify_name, notify_url, notify_param, notify_result, create_time, update_time, del_flag
    </sql>

</mapper>

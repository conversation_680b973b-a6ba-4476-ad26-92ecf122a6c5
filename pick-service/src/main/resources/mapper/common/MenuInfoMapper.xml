<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.common.mapper.MenuInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.common.entity.MenuInfo">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="title" property="title" />
        <result column="path" property="path" />
        <result column="menu_type" property="menuType" />
        <result column="parent_id" property="parentId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, title, path, menu_type, parent_id, create_time, update_time, del_flag
    </sql>

</mapper>

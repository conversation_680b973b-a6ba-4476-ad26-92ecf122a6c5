<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.common.mapper.TopicConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.common.entity.TopicConfig">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="sort" property="sort" />
        <result column="type" property="type" />
        <result column="link" property="link" />
        <result column="banner_pic" property="bannerPic" />
        <result column="banner_color" property="bannerColor" />
        <result column="enabled" property="enabled" />
        <result column="is_delete" property="isDelete" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, sort, `type`, link, banner_pic, enabled, is_delete, create_time, update_time
    </sql>

</mapper>

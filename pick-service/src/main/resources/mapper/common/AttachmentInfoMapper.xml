<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.common.mapper.AttachmentInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.common.entity.AttachmentInfo">
        <id column="id" property="id" />
        <result column="relate_id" property="relateId" />
        <result column="type" property="type" />
        <result column="fid" property="fid" />
        <result column="file_name" property="fileName" />
        <result column="file_path" property="filePath" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, relate_id, type, fid, file_name, file_path, create_time, update_time, del_flag
    </sql>

</mapper>

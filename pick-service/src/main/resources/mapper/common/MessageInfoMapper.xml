<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.common.mapper.MessageInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.common.entity.MessageInfo">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="user_type" property="userType" />
        <result column="push_type" property="pushType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, title, content, img, type, message_type, url, has_read, create_time, update_time, del_flag
    </sql>

    <select id="queryUserMessageInfo" resultType="com.jiuji.pick.service.common.vo.UserMessageInfoVo">
        SELECT mi.id  AS messageId,
               miu.id AS userMessageId,
               miu.has_read,
               mi.title,
               mi.content,
               mi.create_time
        FROM t_message_info_user miu
        INNER JOIN t_message_info mi ON miu.message_id = mi.id
        WHERE miu.del_flag = 0
          AND miu.user_id = #{userId}
        ORDER BY miu.has_read DESC, mi.create_time DESC
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.common.entity.MessageInfoUser">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.common.entity.MessageInfoUser">
        <id column="id" property="id" />
        <result column="message_id" property="messageId" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="has_read" property="hasRead" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_id, user_id, has_read,user_name, create_time, update_time, del_flag
    </sql>

</mapper>

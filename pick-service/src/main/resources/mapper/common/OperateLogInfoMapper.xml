<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.pick.service.common.mapper.OperateLogInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.pick.service.common.entity.OperateLogInfo">
        <id column="id" property="id" />
        <result column="relate_id" property="relateId" />
        <result column="type" property="type" />
        <result column="user_id" property="userId" />
        <result column="opt_user_id" property="optUserId" />
        <result column="opt_user_name" property="optUserName" />
        <result column="content" property="content" />
        <result column="show_type" property="showType" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, relate_id, type, opt_user_id, opt_user_name, user_id, content, show_type, create_time
    </sql>

    <select id="queryAdmLogInfo" resultType="com.jiuji.pick.service.common.vo.QueryOperateLogVo">
        SELECT
            opt_user_name as userName,
            create_time as createTime,
            content,
            user_id as userId,
            relate_id as relateId,
            id
        FROM
        `t_operate_log_info`
        <where>
            del_flag=0
            <if test="param.searchType !=null and param.searchType!='' and param.searchType==1  and param.keyWord !=null and param.keyWord !=''">
                and opt_user_name LIKE CONCAT('%',#{param.keyWord},'%')
            </if>
            <if test="param.searchType !=null and param.searchType!='' and param.searchType==2  and param.keyWord !=null and param.keyWord !=''">
                and relate_id = #{param.keyWord}
            </if>
            <if test="param.startTime != null">
                and create_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and create_time &lt;= #{param.endTime}
            </if>
            order by create_time desc
        </where>
    </select>

    <select id="querySupplierLogInfo" resultType="com.jiuji.pick.service.common.vo.QueryOperateLogVo">
        SELECT opt_user_name   AS userName,
               create_time AS createTime,
               content,
               user_id     AS userId,
               relate_id   AS relateId,
               id
        FROM t_operate_log_info
        WHERE del_flag = 0
            AND show_type IN (1, 3)
            AND user_id = #{supplierId}
            <if test="param.searchType !=null and param.searchType!='' and param.searchType==1  and param.keyWord !=null and param.keyWord !=''">
                and opt_user_name LIKE CONCAT('%',#{param.keyWord},'%')
            </if>
            <if test="param.searchType !=null and param.searchType!='' and param.searchType==2  and param.keyWord !=null and param.keyWord !=''">
                and relate_id = #{param.keyWord}
            </if>
            <if test="param.startTime != null">
                and create_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and create_time &lt;= #{param.endTime}
            </if>
        order by create_time desc
    </select>
</mapper>
